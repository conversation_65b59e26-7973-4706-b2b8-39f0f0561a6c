package com.socialplay.gpark.app

import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Environment
import android.provider.Settings
import android.webkit.WebView
import androidx.fragment.app.FragmentActivity
import androidx.paging.ExperimentalPagingApi
import com.airbnb.mvrx.Mavericks
import com.bin.cpbus.CpEventBus
import com.bin.stetho.WrapStetho
import com.meta.audio.agora.AgoraAudio
import com.meta.biz.mgs.MgsBiz
import com.meta.biz.mgs.api.config.MgsConfig
import com.meta.biz.ugc.UGCProtocolBiz
import com.meta.biz.ugc.exception.EditorException
import com.meta.biz.ugc.listener.IEditorLocalFunction
import com.meta.ipc.IPC
import com.meta.ipc.Options
import com.meta.verse.lib.util.hash.HashUtil
import com.mw.develop.mwDevelopModule
import com.socialplay.gpark.AppStartupInit
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.app.initialize.AppTimeInit
import com.socialplay.gpark.app.initialize.AppUseDataInit
import com.socialplay.gpark.app.initialize.FileManagerInit
import com.socialplay.gpark.app.initialize.LibBuildConfigInit
import com.socialplay.gpark.app.initialize.PlayedGameInit
import com.socialplay.gpark.app.initialize.PushInit
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.DeviceInteractor
import com.socialplay.gpark.data.interactor.EditorGameLoadInteractor
import com.socialplay.gpark.data.interactor.EditorInteractor
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.interactor.PingInteractor
import com.socialplay.gpark.data.interactor.PublishPostInteractor
import com.socialplay.gpark.data.interactor.SystemNoticeInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.interactor.TabConfigInteractor
import com.socialplay.gpark.data.interactor.UploadFileInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.di.apiModule
import com.socialplay.gpark.di.databaseModule
import com.socialplay.gpark.di.interactorModule
import com.socialplay.gpark.di.mapperModule
import com.socialplay.gpark.di.networkModule
import com.socialplay.gpark.di.playerModule
import com.socialplay.gpark.di.repositoryModule
import com.socialplay.gpark.di.useCaseModule
import com.socialplay.gpark.di.viewModelsModule
import com.socialplay.gpark.di.webModule
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.observer.GameCrashHostObserve
import com.socialplay.gpark.function.apm.PageMonitor
import com.socialplay.gpark.function.apm.leak.LeakCanaryConfig
import com.socialplay.gpark.function.bizinit.MWDevelopProject
import com.socialplay.gpark.function.deeplink.ShareActiveHelper
import com.socialplay.gpark.function.download.DownloadFileProvider
import com.socialplay.gpark.function.editor.EditorGameInteractHelper
import com.socialplay.gpark.function.editor.PlayedDatabaseTask
import com.socialplay.gpark.function.friend.FriendInit
import com.socialplay.gpark.function.im.RongImHelper
import com.socialplay.gpark.function.ipc.IPCFunctionRegister
import com.socialplay.gpark.function.locale.MetaLanguages
import com.socialplay.gpark.function.member.UserMemberInit
import com.socialplay.gpark.function.mgs.MgsPlayGameTask
import com.socialplay.gpark.function.mw.MWAutoDeleteStorageSpaceManager
import com.socialplay.gpark.function.mw.MWInitialize
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.quitgame.GameQuitObserver
import com.socialplay.gpark.function.startup.StartupProcessType.AUTO
import com.socialplay.gpark.function.startup.StartupProcessType.AWO
import com.socialplay.gpark.function.startup.StartupProcessType.H
import com.socialplay.gpark.function.startup.StartupProcessType.M
import com.socialplay.gpark.function.startup.StartupProcessType.O
import com.socialplay.gpark.function.startup.StartupProcessType.R
import com.socialplay.gpark.function.startup.core.plus
import com.socialplay.gpark.function.startup.core.project.Project
import com.socialplay.gpark.function.startup.dsl.ProjectLauncher
import com.socialplay.gpark.function.startup.dsl.project
import com.socialplay.gpark.function.startup.dsl.task
import com.socialplay.gpark.function.startup.dsl.taskAsync
import com.socialplay.gpark.function.store.StoreCommentGuide
import com.socialplay.gpark.ui.compliance.MetaProtocol
import com.socialplay.gpark.ui.developer.restart.CleanRestartWrapper
import com.socialplay.gpark.ui.dialog.DialogManagerInfo
import com.socialplay.gpark.ui.dialog.DialogScene
import com.socialplay.gpark.ui.dialog.DialogShowCount
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.dialog.ListDialog
import com.socialplay.gpark.ui.dialog.UnpublishedProjectReminderDialog
import com.socialplay.gpark.ui.editor.module.guide.UgcModuleAddGuideDialog
import com.socialplay.gpark.ui.gamedetail.cache.GameDetailBindingCache
import com.socialplay.gpark.ui.gamedetail.sendflower.SendFlowerConditionDialog
import com.socialplay.gpark.ui.main.GuideDialog
import com.socialplay.gpark.ui.suggestion.NewGameSuggestionDialog
import com.socialplay.gpark.util.ActivityLifecycleCallbacksAdapter
import com.socialplay.gpark.util.StorageUtils
import com.socialplay.gpark.util.WebViewDataDirFixer
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.Dispatchers
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.context.GlobalContext
import org.koin.core.context.startKoin
import org.koin.core.logger.Level
import retrofit2.Retrofit
import timber.log.Timber
import java.io.File
import kotlin.system.exitProcess

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/06/11
 * desc   :
 * </pre>
 */
// 是否需要同意用户协议
private var isNeedAgreeProtocol = false

@ExperimentalPagingApi
val attachContextProject = project("attachContext") {
    timber()
    val mmkv = mmkv()
    mmkv.await()
    initBuildConfig()
    val koin = koin()
    val language = language()
    appTimeInit()
    koin.await() + language.await()
    val pandora = analyticsPreInit()
    if (noSpace()) {
        return@project
    }
    leakCanaryInit()
    val hermesEventBus = hermesEventBus()
    installIPC()
    pandora.await()
    hermesEventBus.await()
    fixAndroidPWebView()
    // TODO 国内先去掉这个功能了
//    copyPlayerAsset()
    maverick()
    isNeedAgreeProtocol = MetaProtocol.needLegal()
    if (!isNeedAgreeProtocol) {
        deviceId()
    }
    AppStartupInit.onAttachContext(this)
}

val onCreateProject = project("onCreate") {
    if (StorageUtils.isFullSDWhenStart) return@project
    observeGameCrash()
    observeGameQuit()
    editorLocal()
    //提前检测一下用户信息
    accountCheck()
    h5PageConfig()
    lifecycleInteractor()
    tTai()
    if (!isNeedAgreeProtocol) {
        metaWord().await()
        agreeProtocolOnCreate(this)
    }
    userMember()
    initAccountUpdate()
    WrapStetho.init(application)
    cleanPlayedDatabase()
    pageMonitor()
    mwDevelopBiz()
    refreshMyPlayedGame()
    // 初始化ViewBinding缓存预加载
    gameDetailBindingCacheInit()
}

val onAgreeProtocolProject = project("onAgreeProtocol") {
    if (StorageUtils.isFullSDWhenStart) return@project
    if (!isNeedAgreeProtocol) {
        return@project
    }
    metaWord().await()
    agreeProtocolOnCreate(this)
}

private val agreeProtocolOnCreate: ProjectLauncher = {
    MetaProtocol.agreedProtocol = true
    developer()
    deviceId()
    AppStartupInit.onCreateAnalyticsBefore(this)
    analytics()
    AppStartupInit.onCreateAnalyticsAfter(this)
    appUseData()
    trackMainProcessStart()
    googlePay()
    RongIm()
    friendRegisterHermes()
    editorChoiceConfig()
    mgsBiz()
    mgsPlayGame()
    autoDeleteMWStorageSpace()
    initPush()
    uploadInit()
    publishPostInit()
    pingService()
    AppStartupInit.onCreate(this)
    dialogManager()
    editorGameInit()
    fileManager()
}


private fun isDebugFileExists(): Boolean {
    return runCatching {
        return File(
            Environment.getExternalStorageDirectory(), "debug.txt"
        ).exists()
    }.getOrElse { false }
}

private fun Project.noSpace(): Boolean {
    if (processType != H) return false
    val freeSize = StorageUtils.getDataFreeSize(application)
    val enoughSpace = 100L * 1024 * 1024
    application.registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacksAdapter() {
        override fun onActivityResumed(activity: Activity) {
            super.onActivityResumed(activity)
            if (StorageUtils.getDataFreeSize(application) < enoughSpace) { // 每次检测
                if (activity is FragmentActivity) {
                    showNoSpaceDialog(activity)
                }
            }
        }
    })
    if (freeSize < enoughSpace) {
        StorageUtils.isFullSDWhenStart = true
        return true
    }
    return false
}

private fun showNoSpaceDialog(activity: FragmentActivity) {
    ListDialog().title(activity.getString(com.socialplay.gpark.R.string.no_space_title)).content(activity.getString(com.socialplay.gpark.R.string.no_space_content)).clickCallback {
        val i = Intent(Settings.ACTION_APPLICATION_SETTINGS)
        activity.startActivity(i)
        exitProcess(0)
    }.list(mutableListOf(SimpleListData(activity.getString(com.socialplay.gpark.R.string.text_confirm), com.socialplay.gpark.R.drawable.selector_button_warn))).image(com.socialplay.gpark.R.drawable.icon_no_space).setBackPressedDismiss(false)
        .setOutsideDismiss(false).show(activity.supportFragmentManager, "no space")
}

fun Project.timber() = task("timber", AWO) {
    if (com.socialplay.gpark.BuildConfig.DEBUG || com.socialplay.gpark.BuildConfig.LOG_DEBUG || isDebugFileExists()) {
        Timber.uprootAll()
        Timber.plant(Timber.DebugTree())
    }
}

fun Project.mmkv() = taskAsync("mmkv", AWO, Dispatchers.IO) {
    MMKV.initialize(application)
}

fun Project.language() = task("language", H + M) {
    if (processType == H) {
        MetaLanguages.initByH(application)
    } else if (processType == M) {
        MetaLanguages.initByM(application)
    }
}

@ExperimentalPagingApi
fun Project.koin() = taskAsync("koin", AWO, Dispatchers.IO) {
    startKoin {
        androidContext(application)
        //No such Method 'Duration.toDouble-impl' error
        //fix:[https://youtrack.jetbrains.com/issue/KTOR-3575]
        androidLogger(Level.ERROR)
        val flavorModules = AppStartupInit.koinModules()

        val initModules = listOf(
            viewModelsModule,
            repositoryModule,
            networkModule,
            apiModule,
            databaseModule,
            interactorModule,
            playerModule,
            mapperModule,
            useCaseModule,
            mwDevelopModule,
            webModule
        )
        modules(flavorModules + initModules)
    }
}

//开发者模式
fun Project.developer() = task("developer", AWO) {
    if (processType == H) {
        CleanRestartWrapper.checkExternalSdCardEnvConfig()
    }
}

fun Project.initBuildConfig() = task("initBuildConfig", AWO) {
    LibBuildConfigInit.initConfig()
}

fun Project.analyticsPreInit() = taskAsync("analyticsPreInit", AWO, Dispatchers.IO) {
    Analytics.preInit(application, processType)
}

fun Project.analytics() = task("analytics", AWO) {
    Analytics.init(application, isMainProcess, processName, processType)
}

fun Project.appUseData() = task("appUseData", H) {
    AppUseDataInit.init()
}

fun Project.deviceId() = taskAsync("deviceId", AWO) {
    GlobalContext.get().get<DeviceInteractor>().init()
}

fun Project.maverick() = task("maverick", H + M) {
    Mavericks.initialize(application)
}

fun Project.observeGameCrash() = task("observeGameCrash", H) {
    GameCrashHostObserve.initHostLifeCycleCallbacks(application)
}

fun Project.observeGameQuit() = task("observeGameQuit", H) {
    GameQuitObserver.initHostLifecycleCallbacks(application)
}

fun Project.accountCheck() = taskAsync("accountCheck", AWO) {
    GlobalContext.get().get<AccountInteractor>().checkUserInfo(matchProcess(H))
}

fun Project.friendRegisterHermes() = taskAsync("friendRegisterHermes", AWO) {
    FriendInit.init(processType)
}


fun Project.RongIm() = task("rongIm", H) {
    RongImHelper.init(application)
    // 新需求，启动应用就链接IM
    RongImHelper.needConnect()
}

//MW
fun Project.metaWord() = task("metaVerseCore", H + M + R + AUTO) {
    MWInitialize.init(application)
}

//fun Project.metaVerseCore() = task("metaVerseCore", H + M) {
//    MetaVerseStartup.startup(application, processName)
//}

fun Project.mgsPlayGame() = taskAsync("mgsPlayGame", H) {
    MgsPlayGameTask.init()
}

fun Project.googlePay() = taskAsync("googlePay", H + M) {
    val payInteractor: IPayInteractor = GlobalContext.get().get()
    payInteractor.init(processType)
}

fun Project.mgsBiz() = task("mgsBiz", H + M + R + AUTO) {
    val retrofit = GlobalContext.get().get<Retrofit>()
    val isOversea = !EnvConfig.isParty()
    MgsBiz.init(
        application, retrofit, MgsConfig(
            com.socialplay.gpark.BuildConfig.ENV_TYPE.lowercase(),
            isEnableAudio = (isOversea && !PandoraToggle.isMWMgs),
            audio = if (isOversea && processType == M) AgoraAudio() else null,
            isOversea,
            false,
            com.socialplay.gpark.BuildConfig.DEBUG,
            false,
            PandoraToggle.isMWMgs
        )
    )
}

fun Project.hermesEventBus() = taskAsync("hermesEventBus", AWO, Dispatchers.IO) {
    CpEventBus.init(application)
}

fun Project.uploadInit() = taskAsync("uploadInit", AWO, Dispatchers.IO) {
    GlobalContext.get().get<UploadFileInteractor>().init()
}

fun Project.publishPostInit() = taskAsync("publishPostInit", AWO, Dispatchers.IO) {
    GlobalContext.get().get<PublishPostInteractor>().init(isMainProcess, processName)
}

fun Project.tTai() = taskAsync("TTaiInit", H, Dispatchers.IO) {
    GlobalContext.get().get<TTaiInteractor>().init()
}

fun Project.fixAndroidPWebView() = task("fixAndroidPWebView", H + O + M + R) {
    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.P) {
        return@task
    }

    WebViewDataDirFixer.fix(application, application.packageName, processName) {
        WebView.setDataDirectorySuffix(it)
    }
}


fun Project.installIPC() = task("ipc", AWO) {
    IPC.getInstance().init(
        application, Options.Builder().debug(com.socialplay.gpark.BuildConfig.LOG_DEBUG).standaloneServer(false).threadConformanceEnabled(false).build()
    )

    IPCFunctionRegister.register(application, processType)
}

fun Project.trackMainProcessStart() = taskAsync("trackMainProcessStart", H, Dispatchers.IO) {
    Analytics.track(EventConstants.EVENT_MAIN_PROCESS_START) {
        put("sd_space", StorageUtils.getInternalMemoryTotalSize())
        put("sd_free_space", StorageUtils.getInternalMemoryFreeSize())
        put("meta_use_space", StorageUtils.getMetaSpace(application))
    }
}

fun Project.userMember() = task("userMember", H + M + R) {
    UserMemberInit.init(processType)
}

fun Project.h5PageConfig() = taskAsync("h5PageConfig", AWO, Dispatchers.IO) {
    GlobalContext.get().get<H5PageConfigInteractor>().init(isMainProcess)
}

fun Project.pingService() = taskAsync("pingService", H, Dispatchers.IO) {
    GlobalContext.get().get<PingInteractor>().init()
}

fun Project.appTimeInit() = taskAsync("appTimeInit", H, Dispatchers.IO) {
    AppTimeInit.init(application)
}

fun Project.initPush() = taskAsync("pingService", H, Dispatchers.Main) {
    PushInit.init()
}

fun Project.cleanPlayedDatabase() = taskAsync("cleanPlayedDatabase", H, Dispatchers.IO) {
    PlayedDatabaseTask.startupClean()
}

fun Project.initAccountUpdate() = task("initAccountUpdate", H) {
    GlobalContext.get().get<EditorInteractor>().init()
    GlobalContext.get().get<SystemNoticeInteractor>().init()
}

fun Project.lifecycleInteractor() = taskAsync("lifecycleInteract", AWO, Dispatchers.IO) {
    LifecycleInteractor.init(application)
}


fun Project.pageMonitor() = task("pageMonitor", H) {
    PageMonitor.init()
}

fun Project.leakCanaryInit() = taskAsync("leakCanaryInit", H + M + R, Dispatchers.IO) {
    LeakCanaryConfig.init(application, processType)
}


private fun editorLocal() {
    UGCProtocolBiz.setEditorLocalListener(object : IEditorLocalFunction {
        override fun getCurrentUuid(): String {
            return GlobalContext.get().get<MetaKV>().account.uuid
        }

        override suspend fun getFileSHA1(file: File): String? {
            return HashUtil.SHA1.get(file)
        }

        override fun getTemplateUnZipFolder(): File {
            return DownloadFileProvider.editorTemplateUnZip
        }

        override fun getTemplateZipFolder(): File {
            return DownloadFileProvider.editorTemplateZip
        }

        override fun getUgcConsumptionStateZipFolder(): File {
            return File("./")
        }

        override fun getUserUnzipFolder(): File {
            return DownloadFileProvider.getEditorUserUnzipLocal()
        }

        override fun onException(exception: EditorException) {
            EditorGameInteractHelper.analyticsLoaderException(exception)
        }
    })
}

fun Project.autoDeleteMWStorageSpace() = taskAsync("autoDeleteMWStorageSpace", H, Dispatchers.IO) {
    MWAutoDeleteStorageSpaceManager.start(application)
}

fun Project.refreshMyPlayedGame() = taskAsync("refreshMyPlayedGame", H, Dispatchers.IO) {
    PlayedGameInit.refreshPlayedGame()
}

fun Project.mwDevelopBiz() = taskAsync("mwDevelopBiz", H, Dispatchers.Main) {
    MWDevelopProject.initialize()
}

fun Project.dialogManager() = taskAsync("dialogManager", H, Dispatchers.IO) {
    // 更新弹框在里面
    AppStartupInit.dialogManager()
    // 建造引导弹框
    DialogShowManager.registerDialog(GuideDialog(), DialogManagerInfo(900, listOf(DialogScene.MAIN_PAGE), showCount = DialogShowCount.MULTI))
    // 未发布项目提醒弹框
    DialogShowManager.registerDialog(UnpublishedProjectReminderDialog(), DialogManagerInfo(800, listOf(DialogScene.MAIN_PAGE), showCount = DialogShowCount.ONCE))
    // UGC模组引导弹框
    DialogShowManager.registerDialog(UgcModuleAddGuideDialog(), DialogManagerInfo(700, listOf(DialogScene.MAIN_PAGE), showCount = DialogShowCount.ONCE, runCount = 1))
    // 超级推荐位弹框
    DialogShowManager.registerDialog(NewGameSuggestionDialog(), DialogManagerInfo(600, listOf(DialogScene.MAIN_PAGE)))
    // 商店评分引导弹框
    DialogShowManager.registerDialog(StoreCommentGuide, DialogManagerInfo(500, listOf(DialogScene.MAIN_PAGE, DialogScene.GAME_SUCCESS, DialogScene.LIKE_OPT), showCount = DialogShowCount.MULTI))
    // 分享跳转控制器
    DialogShowManager.registerDialog(ShareActiveHelper, DialogManagerInfo(400, listOf(DialogScene.MAIN_PAGE), showCount = DialogShowCount.MULTI))
    // 送花功能首次弹窗
    DialogShowManager.registerDialog(SendFlowerConditionDialog(), DialogManagerInfo(300, listOf(DialogScene.GAME_DETAIL_PAGE), showCount = DialogShowCount.ONCE))
}

fun Project.editorGameInit() = task("editorGame", H) {
    GlobalContext.get().get<EditorGameLoadInteractor>().init()
}


fun Project.fileManager() = taskAsync("fileManager", H, Dispatchers.IO) {
    FileManagerInit().init(application, isMainProcess, processName)
}

fun Project.editorChoiceConfig() = task("editorChoiceConfig", H) {
    GlobalContext.get().get<TabConfigInteractor>().init()
}

fun Project.gameDetailBindingCacheInit() = task("gameDetailBindingCacheInit", H) {
    // 只初始化缓存管理器，不立即预加载（避免主题问题）
    // 预加载将在首次使用Fragment时进行
    GameDetailBindingCache.getInstance()
    Timber.d("GameDetail binding cache manager initialized")
}