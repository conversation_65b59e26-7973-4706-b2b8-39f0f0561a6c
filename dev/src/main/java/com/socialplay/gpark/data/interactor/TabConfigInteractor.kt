package com.socialplay.gpark.data.interactor

import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.LoginStatusEvent
import com.socialplay.gpark.data.model.choice.ChoiceTabInfo
import com.socialplay.gpark.data.model.choice.CommunityTabTargetType
import com.socialplay.gpark.data.model.choice.CommunityTabType
import com.socialplay.gpark.data.model.choice.HomeTabType
import com.socialplay.gpark.data.model.choice.TabTargetType
import com.socialplay.gpark.util.extension.enumContains
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * @des:精选tab
 * @author: lijun<PERSON>a
 * @date: 2023/5/15 14:57
 */
class TabConfigInteractor(
    val metaRepository: IMetaRepository,
    private val metaKV: MetaKV,
    private val context: Context
) {
    //精选底栏顶部tab配置
    private val _choiceTabInfoLiveData = MutableLiveData<List<ChoiceTabInfo>>()

    //首页顶部tab配置
    private val _homeTabInfoLiveData = MutableLiveData<List<ChoiceTabInfo>>()

    private val _mineTabInfoLiveData = MutableLiveData<List<ChoiceTabInfo>>()

    private val _communityTabInfoLiveData = MutableLiveData<List<ChoiceTabInfo>>()

    //    private val _mineToolList = MutableLiveData<List<MineToolInfo>?>()
//    private val _mineActivityInfo = MutableLiveData<SimpleActivityInfo?>()
    private val mainScope = MainScope()

    fun init() {
        CpEventBus.register(this)
        getConfigTabInfo()
//        getMineConfig()
    }

    companion object {
        //        const val KEY_TAB_EDITORS_CHOICE = "choice"
        const val KEY_TAB_HOME_PAGE = TabConfigInteractorWrapper.KEY_TAB_HOME_PAGE

        //        const val KEY_TAB_MINE_BOTTOM = "userHomePage"
        const val KEY_TAB_COMMUNITY = TabConfigInteractorWrapper.KEY_TAB_COMMUNITY

        const val KEY_CODE_USER_HOME_JOIN = "userHomeJoinLy"
        const val KEY_CODE_USER_HOME_TOOL = "userHomeToolBar"
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(loginStatusEvent: LoginStatusEvent) {
        if (loginStatusEvent == LoginStatusEvent.LOGIN_SUCCESS || loginStatusEvent == LoginStatusEvent.LOGOUT_SUCCESS) {
            getConfigTabInfo()
        }
    }

//    fun getMineActivityInfo() = _mineActivityInfo.value
//    fun getMineTools() = _mineToolList.value

//    /**
//     * 提前获取我的底栏的配置，方便图片预加载
//     */
//    private fun getMineConfig() {
//        mainScope.launch {
//            metaRepository.getMineConfig("$KEY_CODE_USER_HOME_JOIN,${KEY_CODE_USER_HOME_TOOL}")
//                .collect {
//                    if (it.succeeded) {
//                        it.data?.items?.forEach { config ->
//                            val tabList = config.confList ?: mutableListOf()
//                            when (config.biz) {
//                                KEY_CODE_USER_HOME_JOIN -> {
//                                    val choiceTab = tabList.getOrNull(0)
//                                    _mineActivityInfo.value =
//                                        if (choiceTab == null) null else SimpleActivityInfo.fromChoiceTab(
//                                            choiceTab
//                                        )
//                                }
//
//                                KEY_CODE_USER_HOME_TOOL -> {
//                                    Timber.d("tabList:${tabList.size}")
//                                    val listNative = MineToolInfo.getMineNativeTypeList()
//                                    val listTab =
//                                        tabList?.map { tab -> MineToolInfo.fromChoiceTab(tab) }
//                                            ?.filter { tab ->
//                                                if (NewMineTabType.NATIVE.name == tab.type) {
//                                                    listNative.contains(tab.target)
//                                                } else {
//                                                    enumContains<NewMineTabType>(tab.type)
//                                                }
//                                            }
//                                    Timber.d("listTab:${listTab?.size}")
//                                    _mineToolList.value = listTab
//
//                                }
//                            }
//                        }
//                    }
//                }
//        }
//    }

    private fun getConfigTabInfo() {
        mainScope.launch {
            metaRepository.getConfigTabInfo("${KEY_TAB_HOME_PAGE},${KEY_TAB_COMMUNITY}")
                .collect {
                    if (it.succeeded) {
                        it.data?.items?.forEach { config ->
                            val tabList = config.confList ?: mutableListOf()
                            when (config.biz) {
                                KEY_TAB_HOME_PAGE -> {
                                    updateHomeTabInfo(tabList)
                                }

                                KEY_TAB_COMMUNITY -> {
                                    updateCommunityTabInfo(tabList)
                                }
                            }
                        }
                    }
                }
        }
    }

//    private fun updateMineBottomTab(listTab: List<ChoiceTabInfo>) {
//        val listTabInfo = listTab.filter {
//            enumContains<MineTabType>(it.type)
//        }
//        _mineTabInfoLiveData.value = listTabInfo
//        mainScope.launch(Dispatchers.IO) {
//            metaKV.editorsChoiceKV.saveMineConfigTabInfo(listTabInfo)
//        }
//    }

    private fun updateHomeTabInfo(listTab: List<ChoiceTabInfo>) {
        val listTabInfo = listTab.filter {
            if (HomeTabType.NATIVE.name == it.type) {
                enumContains<TabTargetType>(it.target)
            } else {
                enumContains<HomeTabType>(it.type)
            }
        }
        _homeTabInfoLiveData.value = listTabInfo
        mainScope.launch(Dispatchers.IO) {
            metaKV.editorsChoiceKV.saveHomeConfigTabInfo(listTabInfo)
        }
    }

    private fun updateCommunityTabInfo(listTab: List<ChoiceTabInfo>) {
        val listTabInfo = listTab.filter {
            if (CommunityTabType.NATIVE.name == it.type) {
                enumContains<CommunityTabTargetType>(it.target)
            } else {
                enumContains<CommunityTabType>(it.type)
            }
        }
        _communityTabInfoLiveData.value = listTabInfo
        mainScope.launch(Dispatchers.IO) {
            metaKV.editorsChoiceKV.saveCommunityConfigTabInfo(listTabInfo)
        }
    }

//    private fun updateEditorsChoiceTabInfo(listTab: List<ChoiceTabInfo>) {
//        val listTabInfo = listTab.filter {
//            if (TabType.NATIVE.name == it.type) {
//                enumContains<TabTarget>(it.target)
//            } else {
//                enumContains<TabType>(it.type)
//            }
//        }
//        _choiceTabInfoLiveData.value = listTabInfo
//        mainScope.launch(Dispatchers.IO) {
//            metaKV.editorsChoiceKV.saveConfigTabInfo(listTabInfo)
//        }
//    }

//    fun getEditorsConfigTab(): List<ChoiceTabInfo>? {
//        return _choiceTabInfoLiveData.value ?: metaKV.editorsChoiceKV.getConfigTabInfo()
//    }
//
//    /**
//     * 旧版的我的底栏的配置
//     */
//    fun getMineConfigTab(): List<ChoiceTabInfo>? {
//        return _mineTabInfoLiveData.value ?: metaKV.editorsChoiceKV.getMineConfigTabInfo()
//    }


    fun getCommunityConfigTab(): List<ChoiceTabInfo>? {
        val list =
            _communityTabInfoLiveData.value ?: metaKV.editorsChoiceKV.getCommunityConfigTabInfo()
        return list
//        return list?.mapNotNull {
//            if (it.isCommunityCircleTab()) {
//                if (!PandoraToggle.isOpenCommunityCircleTab) {
//                    null
//                } else {
//                    it
//                }
//            } else {
//                it
//            }
//        }
    }

    fun getHomeConfigTab(): List<ChoiceTabInfo>? {
        val list = _homeTabInfoLiveData.value ?: metaKV.editorsChoiceKV.getHomeConfigTabInfo()
        return list
//        return list?.mapNotNull {
//            if (it.isOldHotGame()) {
//                null
//            } else if (it.isCommunityTab() && !PandoraToggle.isOpenHomeCommunityTab) {
//                null
//            } else if (it.isSchool()) {
//                if (!PandoraToggle.isMgsFriendJoin) {
//                    null
//                } else {
//                    it.updateSchoolCircleTabColor(
//                        GlobalContext.get().get<AccountInteractor>().joinedSchool
//                    )
//                }
//            } else {
//                it
//            }
//        }
    }
}