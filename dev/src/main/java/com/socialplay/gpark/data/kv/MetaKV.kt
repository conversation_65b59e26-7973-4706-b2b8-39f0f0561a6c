package com.socialplay.gpark.data.kv

import com.tencent.mmkv.MMKV

class MetaKV {
    private val commonMmkv = MMKV.mmkvWithID("id_common", MMKV.MULTI_PROCESS_MODE)!!
    private val metaAppMmkv = MMKV.mmkvWithID("id_meta_app", MMKV.MULTI_PROCESS_MODE)!!
    private val analyticsMmkv = MMKV.mmkvWithID("analytics", MMKV.MULTI_PROCESS_MODE)!!
    private val analyticsGameMmkv = MMKV.mmkvWithID("id_analytics_game", MMKV.MULTI_PROCESS_MODE)!!
    private val developerMmkv = MMKV.mmkvWithID("id_developer", MMKV.MULTI_PROCESS_MODE)!!
    private val metaGameMmkv = MMKV.mmkvWithID("id_meta_game", MMKV.MULTI_PROCESS_MODE)!!
    private val webMmkv = MMKV.mmkvWithID("id_web_data", MMKV.MULTI_PROCESS_MODE)!!
    val time by lazy { TimeKV(commonMmkv) }
    val appKV by lazy { AppCommonKV(metaAppMmkv, time) }
    val account by lazy { AccountKV(commonMmkv, metaAppMmkv) }
    val accountWrapper by lazy { AccountKVWrapper(commonMmkv, metaAppMmkv) }
    val device by lazy { DeviceKV(commonMmkv) }
    val deviceWrapper by lazy { DeviceKVWrapper(commonMmkv) }
    val download by lazy { DownloadKV(metaAppMmkv, this) }
    val gameDetail by lazy { GameDetailKV(metaAppMmkv, account) }
    val analytic by lazy { AnalyticKV(analyticsMmkv, analyticsGameMmkv) }
    val developer by lazy { DeveloperKV(developerMmkv) }
    val protocol by lazy { ProtocolKV(metaAppMmkv) }
    val playGame by lazy { PlayGameKV(metaGameMmkv, this) }
    val userStatusKV by lazy { UserStatusKV(commonMmkv, account) }
    val web by lazy { WebKV(webMmkv) }
    val realName by lazy { RealNameKV(metaAppMmkv, this) }
    val friend by lazy { FriendKV(metaAppMmkv, account) }
    val groupChatKV by lazy { GroupChatKV(metaAppMmkv) }
    val mgsKV by lazy { MgsKV(metaAppMmkv) }
    val mw by lazy { MetaWordKV(metaAppMmkv) }
    val tTaiKV by lazy { TTaiKV(metaAppMmkv) }
    val gameReviewKV by lazy { GameReviewKV(metaAppMmkv, account) }
    val tsKV by lazy { TsKV(metaAppMmkv) }
    val screenRecordKV by lazy { ScreenRecordKV(metaAppMmkv) }
    val gameQuitKV by lazy { GameQuitKV(metaAppMmkv) }
    val miscKV by lazy { MiscKV(commonMmkv) }
    val reviewKv by lazy { ReviewKv(metaGameMmkv) }
    val communityKV by lazy { CommunityKV(metaAppMmkv) }
    val appraiseKV by lazy { AppraiseKV(commonMmkv, account) }

    val editorsChoiceKV by lazy { EditorsChoiceKV(metaAppMmkv, account) }
    val videoFeedKV by lazy { VideoFeedKV(commonMmkv, account) }
    val searchKV by lazy { SearchKV(commonMmkv) }
    val dailyTaskKV by lazy { DailyTaskKV(metaAppMmkv, time) }
    val avatarKv by lazy { AvatarKV(commonMmkv) }
    val unpublishedProjectKV by lazy { UnpublishedProjectKV(metaAppMmkv, account) }
}