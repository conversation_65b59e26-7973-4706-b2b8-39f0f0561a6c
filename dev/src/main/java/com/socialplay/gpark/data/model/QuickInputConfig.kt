package com.socialplay.gpark.data.model

import com.google.gson.annotations.SerializedName
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.util.GsonUtil

data class QuickInputConfig(
    /**
     * 游戏详情页的快捷回复配置
     */
    @SerializedName("partyDetail")
    val gameDetail: List<QuickInputTag>?,
    /**
     * 素材库的快捷回复配置
     */
    val libraryDetail: List<QuickInputTag>?,
    /**
     * 帖子详情页的快捷回复配置
     */
    val postDetail: List<QuickInputTag>?
) {
    companion object {
        private var defaultConfig: QuickInputConfig? = null
        suspend fun defaultConfig(): QuickInputConfig? {
            if (defaultConfig == null) {
                defaultConfig =
                    GsonUtil.gsonSafeParseCollection(BuildConfig.DEFAULT_QUICK_INPUT_JSON_CONFIG)
            }
            return defaultConfig
        }
    }
}

data class QuickInputTag(
    val content: String?,
    val tagId: String?,
)