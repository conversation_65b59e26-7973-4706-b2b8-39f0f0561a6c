package com.socialplay.gpark.data.model.choice

import androidx.annotation.DrawableRes
import com.meta.pandora.Pandora
import com.meta.pandora.data.entity.Event
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.ui.post.feed.tag.TagCommunityFeedFragmentArgs
import java.io.Serializable

/**
 * @des:
 * @author: lijunjia
 * @date: 2022/10/27 10:37
 */
data class ChoiceTabInfo(
    val id: Int = 0,
    val sort: Int = 0,
    var name: String = "",
    var imgUrl: String = "",
    var type: String = "H5",
    val target: String = "",
    val uncheckedColor: String = "#272829",
    val checkedColor: String = "#333333",
    val bgColor: String = "#FFFFFF",
    val indicatorColor: String = "#FF7210",
    val searchColor: String = "#333333",
    var translucentToolBar: Boolean = false,
    val resourceId: String = "",
    val showCategoryId: Int? = null,
    @Transient
    val event: Event? = null,
    @Transient
    val bgStartColor: String = "#FFFFFF",
    @Transient
    val bgEndColor: String = "#FFFFFF",
    @Transient
    val isBgGradient: Boolean = false,
    var bgGradientColor: BgGradient = BgGradient(),
    val tagInfos: List<GameLabel>? = null,
    val style: String = GROUP_TWO_EACH_ROW,
    val upgradeDesc: String? = null,
    val openType: Int? = null,
    @Transient
    @DrawableRes
    val iconResId: Int? = null,
    var pandoraKey: String? = null,
    val loginLimit: Int? = null,//1或null-无限制 2-需登录 3-仅正式账号
    val bgImg: String = "",
    val description: String = ""
) : Serializable {
    fun isChoiceNativeTabType() = type == TabType.NATIVE.name
    fun isHomeNativeTabType() = type == HomeTabType.NATIVE.name
    fun isH5() = type == HomeTabType.H5.name

    fun isTwoEachRow() = style == GROUP_TWO_EACH_ROW

    fun isRecommend() = type == TabType.NATIVE.name && target == TabTargetType.RECOMMEND.name

    //社区底栏发现tab
    fun isCommunityDiscoverTab() = type == CommunityTabType.NATIVE.name && target == CommunityTabTargetType.DISCOVER.name

    fun isOpenOutSize() = openType == 2

    fun needUpdateSchool(joinedSchool: Boolean): Boolean {
        val bgColor = if (joinedSchool) "#5EC2FD" else "#FFFFFF"
        return this.bgColor != bgColor
    }

    fun canShow(): Boolean {
        val localKey = pandoraKey
        return if (localKey.isNullOrEmpty()) {
            true
        } else {
            val booleanVal = Pandora.getAbConfig<Boolean?>(localKey, null)
            if (booleanVal != null) {
                return booleanVal == true
            } else {
                val booleanVal = Pandora.getAbConfig(localKey, 0)
                return booleanVal == 1
            }
        }
    }

    val isPostPublishVisible get() = !isCommunityDiscoverTab() && !isH5()

    fun canJumpTab(target: String?): Boolean{
        return this.target == target && target.isNotEmpty()
    }

    fun toTagCommunityFeedFragmentArgs(): TagCommunityFeedFragmentArgs {
        return TagCommunityFeedFragmentArgs(
            blockId = id.toLong(),
            blockName = name,
            type = target,
        )
    }

    companion object {
        const val COMMON_HOT_GAME_T_ID = "13212"
        const val GROUP_ONE_EACH_ROW = "col_1"
        const val GROUP_TWO_EACH_ROW = "col_2"
        const val NEW_BRAND_UI = "new_brand_ui"
    }
}

data class GameLabel(
    val tagId: Long = -1,
    val tagName: String = "",
    var isSelected: Boolean = false
) : Serializable

data class GameRankLabel(
    val gameId: String = "",
    val ranks: List<Rank>
) : Serializable

data class Rank(val rank: String = "", val name: String = "", val tagId: Long = -1L)


data class BgGradient(
    val start: String = "#DFE3FF",
    val end: String = "#F5F5F7",
    val solid: String = "#F5F5F7"
) : Serializable

enum class TabType {
    NATIVE, H5
}

enum class TabTarget {
    RECOMMEND,
    CATEGORY,
}


fun List<ChoiceTabInfo>.checkIdAndModifyIfNeeded(): List<ChoiceTabInfo> {
    if (this.find { it.id == 0 } != null || this.distinctBy { it.id }.size != this.size) {
        if (BuildConfig.DEBUG) {
            val message = this.joinToString(separator = ",") { "${it.id}=${it.name}" }
            throw kotlin.IllegalStateException("有id重复或为0 $message")
        } else {
            var pseudoId = Short.MIN_VALUE - 1
            val ids = kotlin.collections.HashSet<Int>()
            return this.map {
                val mapped = if (it.id == 0 || ids.contains(it.id)) {
                    while (ids.contains(pseudoId)) {
                        pseudoId--
                    }
                    it.copy(id = pseudoId)
                } else {
                    it
                }
                ids.add(mapped.id)
                return@map mapped
            }.toMutableList()
        }
    }
    return this
}

enum class MineTabType {
    SCHEME, H5
}

enum class NewMineTabType {
    SCHEME, H5, NATIVE
}


