package com.socialplay.gpark.data.model.community

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/26
 *     desc   :
 * </pre>
 */
data class PostCommentDraft(
    val text: String? = null,
    val images: List<PostCommentImageUpload>? = null
)

object CommentDraftPool {
    private val draftPool = mutableMapOf<String, PostCommentDraft>()
    fun saveDraft(key: String, data: PostCommentDraft) {
        draftPool[key] = data
    }

    fun retrieveDraft(key: String): PostCommentDraft? {
        return draftPool[key]
    }

    fun removeDraft(key: String) {
        draftPool.remove(key)
    }
}