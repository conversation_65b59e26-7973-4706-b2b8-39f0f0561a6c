package com.socialplay.gpark.data.model.creator

import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.data.model.creator.label.KolCreatorLabel
import com.socialplay.gpark.data.model.creator.label.UgcKolSpecificLabel
import com.socialplay.gpark.data.model.user.UserTagInfo

data class CreatorFrameResult(
    // 关注创作者列表
    val followUserList: List<KolCreatorInfo>?,
    // 关注作品
    val followGames: List<CreatorUgcGame>?,
    // 明星创作者
    val starUserInfo: StartCreatorWrapper?,
    // 明星创作者标签type列表，首页默认返回热门，单独查某类型时需要传入
    val starCreatorTypeList: List<KolCreatorLabel>?,
) {
    data class StartCreatorWrapper(
        val userInfoList: List<KolCreatorInfo>?,
        val configItem: KolCreatorLabel?
    )
}

data class RecommendCreatorRequest(
    val tagId: Int,
    val sinceId: String?
)

data class RecommendCreatorResult(
    val userList: List<KolCreatorInfo>?,
    val end: Boolean
)

data class KolCreatorInfo(
    val uuid: String,
    val nickname: String?,
    val avatar: String?,
    val online: Boolean,
    val followUser: Boolean,
    val sinceId: String?,
    // 用户发布游戏数量
    val releaseCount: Long,
    // TODO 服务器还没加这个字段
    val isOfficial: Boolean? = null,
    // TODO 服务器还没加这个字段
    val tags: List<UserTagInfo>? = null,
    // TODO 服务器还没加这个字段
    val labelInfo: LabelInfo? = null,
    // TODO 服务器还没加这个字段
    val signature: String? = null,
) {
    val tagIds get() = tags?.map { it.id }
}

/**
 * ugc游戏
 */
data class CreatorUgcGame(
    // UGC游戏id
    val id: String,
    val packageName: String,
    val ugcGameName: String,
    // pgc游戏code
    val gameCode: String?,
    val banner: String?,
    val userName: String?,
    val userIcon: String?,
    val pvCount: Long,
    // 1:新作品，2：hot，4：推荐
    val gameShowTag: Int,
    // 推荐没有gameShowTag，用isNewGame来判断gameShowTag=1的情况
    val isNewGame: Boolean,
    // 本地用，封装的标签UI属性
    val localLabel: UgcKolSpecificLabel? = null,
    val realGameCode: String? = null,
    val gameTagList: List<UgcGameTag>? = null,
    val loveQuantity: Long,
    val likeCount: Long? = null,
    val backgroundColor: String? = null,
) {
    companion object {
        const val GAME_SHOW_TAG_NEW = 1
        const val GAME_SHOW_TAG_HOT = 2
        const val GAME_SHOW_TAG_RECOMMEND = 4
    }

    val availableGameCode: String? get() = realGameCode ?: gameCode
}

data class UgcGameTag(
    val tagId: Int?,
    val name: String?,
)