package com.socialplay.gpark.data.model.creator

import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.creator.CreatorFrameResult.StartCreatorWrapper
import com.socialplay.gpark.data.model.creator.label.UgcKolSpecificLabel
import com.socialplay.gpark.data.model.creator.label.UgcPublishLabel
import com.socialplay.gpark.util.extension.ifEmptyNull
import com.socialplay.gpark.util.getStringByGlobal

/**
 * 本地定义，其中的大小用于列表排序了！
 */
object CreatorMultiType {
    // 运营位-飞轮位
    const val TYPE_OPERATION_FLY_WHEEL = 10

    // 运营位-banner
    const val TYPE_OPERATION_BANNER = 40

    // 推荐用户
    const val TYPE_RECOMMEND_CREATOR = 50

    // 推荐ugc作品-推荐接口
    const val TYPE_RECOMMEND_UGC_GAME = 60

    // 所有ugc作品选择器
    const val TYPE_ALL_UGC_GAME_SELECTOR = 70

    // 所有ugc作品，一行两个
    const val TYPE_LABEL_UGC_GAME_ITEM = 80
}

/**
 * 横滑创作者
 */
data class CreatorMultiInfo(
    /**
     * 类型 [com.socialplay.gpark.data.model.creator.CreatorMultiType]
     */
    val rvType: Int,
    val title: String? = null,
    val followCreatorList: List<KolCreatorInfo>? = null,
    val operationList: List<UniJumpConfig>? = null,
    val game: LabelCreatorUgcGame? = null,
    val gameList: List<CreatorUgcGame>? = null,
) {

    companion object {
        fun buildFlyWheelInfo(operation: List<UniJumpConfig>): CreatorMultiInfo {
            return CreatorMultiInfo(
                CreatorMultiType.TYPE_OPERATION_FLY_WHEEL,
                operationList = operation
            )
        }

        fun buildBannerInfo(operation: List<UniJumpConfig>): CreatorMultiInfo {
            return CreatorMultiInfo(
                CreatorMultiType.TYPE_OPERATION_BANNER,
                operationList = operation
            )
        }

        fun buildRecommendUserInfo(
            wrapper: StartCreatorWrapper,
        ): CreatorMultiInfo? {
            wrapper.userInfoList.ifEmptyNull() ?: return null
            return CreatorMultiInfo(
                CreatorMultiType.TYPE_RECOMMEND_CREATOR,
                title = getStringByGlobal(R.string.kol_start_creator)
            )
        }

        fun buildRecommendUgcInfo(gameList: List<CreatorUgcGame>): CreatorMultiInfo {
            return CreatorMultiInfo(
                CreatorMultiType.TYPE_RECOMMEND_UGC_GAME,
                gameList = gameList.map {
                    it.copy(
                        localLabel = UgcKolSpecificLabel.buildLabelById(
                            it.gameShowTag,
                            it.isNewGame
                        )
                    )
                },
                title = getStringByGlobal(R.string.recommend_for_you)
            )
        }

        fun buildUgcLabelSelectorInfo(labelList: List<UgcPublishLabel>?): CreatorMultiInfo? {
            if (labelList.isNullOrEmpty()) return null
            return CreatorMultiInfo(
                CreatorMultiType.TYPE_ALL_UGC_GAME_SELECTOR,
                title = getStringByGlobal(R.string.kol_more_map)
            )
        }

        fun buildLabelUgcInfo(game: LabelCreatorUgcGame): CreatorMultiInfo {
            return CreatorMultiInfo(
                CreatorMultiType.TYPE_LABEL_UGC_GAME_ITEM,
                game = game
            )
        }
    }

    fun toUgcListInfo(): CreatorUgcGameListInfo {
        return CreatorUgcGameListInfo(rvType, title.orEmpty(), gameList ?: emptyList())
    }

    fun toOperationListInfo(): CreatorOperationListInfo {
        return CreatorOperationListInfo(rvType, operationList ?: emptyList())
    }

    fun toUgcGameInfo(): AllCreatorUgcGameInfo? {
        return game?.let { AllCreatorUgcGameInfo(it) }
    }

    fun isBannerOperation() =
        rvType == CreatorMultiType.TYPE_OPERATION_BANNER

    fun isUgcLabelTab() =
        rvType == CreatorMultiType.TYPE_ALL_UGC_GAME_SELECTOR

    fun isLabelUgcGame() = rvType == CreatorMultiType.TYPE_LABEL_UGC_GAME_ITEM && game != null

    fun isRecommendCreatorList() =
        rvType == CreatorMultiType.TYPE_RECOMMEND_CREATOR

    val isFlyWheel get() = rvType == CreatorMultiType.TYPE_OPERATION_FLY_WHEEL
}

/**
 * 所有ugc游戏
 */
data class LabelCreatorUgcGame(
    // UGC游戏id
    val id: String,
    val packageName: String,
    val ugcGameName: String,
    // pgc游戏code
    val gameCode: String?,
    val banner: String?,
    val userName: String?,
    val userIcon: String?,
    val pvCount: Long,
    // 点赞人数
    val likeCount: Long? = null,
    val gameTagList: List<UgcPublishLabel>?,
    // 本地字段，布局左右
    val localColumnType: Int,
    val realGameCode: String? = null
) {
    companion object {
        const val COLUMN_TYPE_START = 1
        const val COLUMN_TYPE_END = 2

        fun newColumnType(columnType: Int) = if (columnType == COLUMN_TYPE_START) {
            COLUMN_TYPE_END
        } else {
            COLUMN_TYPE_START
        }
    }

    val availableGameCode: String? get() = realGameCode ?: gameCode
}
