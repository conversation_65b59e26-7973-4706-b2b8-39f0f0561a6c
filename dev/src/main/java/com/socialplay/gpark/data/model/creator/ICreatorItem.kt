package com.socialplay.gpark.data.model.creator

import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.function.analytics.resid.CategoryId

/**
 * Created by bo.li
 * Date: 2024/8/2
 * Desc:
 */
interface ICreatorItem {
    /**
     * 类型 [com.socialplay.gpark.data.model.creator.CreatorMultiType]
     */
    val viewType: Int
    val title: String?
    val spanCount: Int
    val categoryId: Int
}

/**
 * 横滑ugc作品
 */
data class CreatorUgcGameListInfo(
    /**
     * 类型
     * [com.socialplay.gpark.data.model.creator.CreatorMultiType.TYPE_RECOMMEND_UGC_GAME]
     */
    val rvType: Int,
    override val title: String,
    val gameList: List<CreatorUgcGame>
) : ICreatorItem {

    override val viewType: Int = rvType

    override val spanCount: Int = 2

    override val categoryId: Int =
        when (rvType) {
            CreatorMultiType.TYPE_RECOMMEND_UGC_GAME -> {
                CategoryId.CATEGORY_ID_KOL_RECOMMEND_UGC
            }

            else -> {
                0
            }
        }
}

/**
 * 运营位
 */
data class CreatorOperationListInfo(
    /**
     * 类型
     * [com.socialplay.gpark.data.model.creator.CreatorMultiType.TYPE_OPERATION_FLY_WHEEL]
     * [com.socialplay.gpark.data.model.creator.CreatorMultiType.TYPE_OPERATION_BANNER]
     */
    val rvType: Int,
    val operationList: List<UniJumpConfig>
) : ICreatorItem {

    override val viewType: Int = rvType

    override val title: String? = null

    override val spanCount: Int = 2

    override val categoryId: Int =
        when (rvType) {
            CreatorMultiType.TYPE_OPERATION_FLY_WHEEL -> {
                CategoryId.CATEGORY_ID_KOL_FLY_WHEEL
            }

            CreatorMultiType.TYPE_OPERATION_BANNER -> {
                CategoryId.CATEGORY_ID_KOL_BANNER
            }

            else -> {
                0
            }
        }
}

/**
 * 竖滑ugc作品
 */
data class AllCreatorUgcGameInfo(
    val game: LabelCreatorUgcGame
) : ICreatorItem {

    override val viewType: Int = CreatorMultiType.TYPE_LABEL_UGC_GAME_ITEM

    override val title: String? = null

    override val spanCount: Int = 1

    override val categoryId: Int = CategoryId.CATEGORY_ID_KOL_LABEL_UGC
}