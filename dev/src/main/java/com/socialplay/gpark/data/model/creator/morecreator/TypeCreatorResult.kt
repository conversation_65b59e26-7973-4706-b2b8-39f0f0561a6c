package com.socialplay.gpark.data.model.creator.morecreator

import com.socialplay.gpark.data.model.creator.KolCreatorInfo


data class TypeCreatorRequest(
    // 1-已关注，2-认识一下
    val listType: Int,
    val sinceId: String?,
    val relationType: String? = null,
) {
    companion object {
        const val TYPE_FOLLOWED = 1
        const val TYPE_RECOMMEND = 2

        /**
         *  block 拉黑关系(拉黑/被拉黑),
         *  follow 关注关系(关注/粉丝),
         *  friend 好友关系,
         *  match 配对关系,
         *  creator 用户关注国内创作者,
         *  AiBot 用户关注海外AIBot,
         *  kol 用户关注海外创作者
         */
        const val RELATION_TYPE_BLOCK = "block"
        const val RELATION_TYPE_FOLLOW = "follow"
        const val RELATION_TYPE_FRIEND = "friend"
        const val RELATION_TYPE_MATCH = "match"
        const val RELATION_TYPE_CREATOR = "creator"
        const val RELATION_TYPE_KOL = "kol"
    }
}

/**
 * 关注创作者的作品列表
 */
data class TypeCreatorResult(
    val userList: List<KolCreatorInfo>?,
    val end: <PERSON>olean,
)