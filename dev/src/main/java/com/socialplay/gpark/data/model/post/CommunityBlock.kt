package com.socialplay.gpark.data.model.post

import com.socialplay.gpark.data.model.choice.ChoiceTabInfo
import com.socialplay.gpark.data.model.choice.CommunityTabTargetType
import com.socialplay.gpark.data.model.choice.CommunityTabType

/**
 * Created by bo.li
 * Date: 2024/4/24
 * Desc: [mock] https://mock.metaapp.cn/project/595/interface/api/33487
 */
data class CommunityBlock(
    val id: Long,
    val name: String
) {
    fun toCommunityTab(): ChoiceTabInfo {
        return ChoiceTabInfo(
            id = id.toInt(),
            name = name ?: "",
            type = CommunityTabType.NATIVE.name,
            target = CommunityTabTargetType.CIRCLE_BLOCK.name
        )
    }
}