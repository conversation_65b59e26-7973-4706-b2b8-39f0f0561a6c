package com.socialplay.gpark.data.model.post

import android.os.Parcelable
import androidx.core.graphics.toColorInt
import com.socialplay.gpark.util.UnitUtil
import kotlinx.parcelize.Parcelize

data class IDevelopedPostCardRequest(
    val pageSize: Int,
    val offset: String?,
    val gameCardType: Int = TYPE_I_DEVELOPED_PGC,
) {
    companion object {
        // 仅请求时用
        const val TYPE_I_DEVELOPED_PGC = 4
    }
}

data class SearchPostCardRequest(
    val keyword: String,
    val pageNum: Int,
    val lastOrderNum: Long?
)

data class PostCardResult(
    val end: Boolean,
    val dataList: List<PostCardInfo>,
    // 本地用
    val lastOrderNum: Long? = null,
    val orderId: String? = null,
)

/**
 * Created by bo.li
 * Date: 2023/9/28
 * Desc: 社区卡片类型数据结构
 */
@Parcelize
data class PostCardInfo(
    /**
     * 资源类型
     * 1 普通PGC游戏卡片
     * 2 UGC游戏卡片
     */
    val resourceType: Int,
    val gameId: String,
    val packageName: String,
    // 点赞数(PGC)
    val likeCount: Long,
    // 游玩人数（UGC）
    val player: Long,
    val gameAuthor: String?,
    val gameIcon: String?,
    // 游戏标签
    val gameLabelList: List<String>?,
    val gameName: String?,
    val gameScore: Float,
    // 偏移量：获取个人发布的pgc游戏列表时会用到
    val offset: String?,
    val cardBgColor: String? = null
) : Parcelable {
    companion object {
        const val TYPE_PGC = 1
        const val TYPE_UGC = 2
    }

    val isSupported get() = resourceType == TYPE_PGC || resourceType == TYPE_UGC

    val isPgcType get() = resourceType == TYPE_PGC
    val isUgcType get() = resourceType == TYPE_UGC

    val likeCountStr get() = UnitUtil.formatKMCount(likeCount)
    val ratingStr get() = UnitUtil.formatScore(gameScore.toDouble())

    val cardBgColorInt
        get() = runCatching {
            cardBgColor?.toColorInt()
        }.getOrNull()
}
