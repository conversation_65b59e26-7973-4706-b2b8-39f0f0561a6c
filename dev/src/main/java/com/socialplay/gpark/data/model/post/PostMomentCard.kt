package com.socialplay.gpark.data.model.post

import android.os.Parcelable
import androidx.core.graphics.toColorInt
import com.google.gson.reflect.TypeToken
import com.socialplay.gpark.util.GsonUtil
import kotlinx.parcelize.Parcelize

@Parcelize
data class PostMomentCard(
    val resourceType: Int,
    val resourceValue: String? = null,
    val templateId: String? = null,
    val gameId: String? = null,
    val templateName: String? = null,
    val materialUrl: String? = null,
    val extendParams: String? = null,
    val cardBgColor: String? = null
) : Parcelable {
    companion object {
        const val KEY = "moment_post_card"
        const val RES_TYPE_MOMENT: Int = 8
    }
    val cardBgColorInt get() =  runCatching {
        cardBgColor?.toColorInt()
    }.getOrNull()
}

data class MomentLocalTSStartUp(
    val source: String,
    val listTemplateId: String,
) {
    companion object {
        fun fromJson(json: String): MomentLocalTSStartUp {
            if (json.isEmpty()) return MomentLocalTSStartUp("", "")
            val type = object : TypeToken<Map<String, String>>() {}.type
            val map = GsonUtil.gson.fromJson<Map<String, String>>(json, type)
            return fromMap(map)
        }

        private fun fromMap(map: Map<String, String>): MomentLocalTSStartUp {
            val source = map.getOrElse("source"){""}
            val listTemplateId = map.getOrElse("listTemplateId"){""}
            return MomentLocalTSStartUp(source, listTemplateId)
        }
    }

    fun toMap(): Map<String, String> {
        return mapOf(
            "source" to source, "listTemplateId" to listTemplateId
        )
    }
}

