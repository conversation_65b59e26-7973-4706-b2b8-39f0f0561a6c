package com.socialplay.gpark.data.model.post

import android.content.Context
import android.os.Parcelable
import com.bin.cpbus.CpEventBus
import com.luck.picture.lib.entity.LocalMedia
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.ui.post.v2.PublishPostExtra
import kotlinx.parcelize.Parcelize
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/09/14
 *     desc   :
 * </pre>
 */
@Parcelize
data class PostPublish(
    val content: String = "",
    val medias: List<PostMediaResource>? = null,
    val games: List<PostCardInfo>? = null,
    val communityTagList: List<TopicBean>? = null,
    val postId: String? = null,
    val source: Int?,
    val styleCardList: List<PostStyleCard>? = null,
    // 发帖时传null，发帖后接口会返回的已有的tagList
    val localExistTags: List<PostTag>? = null,
    val plotCardList: List<PostMomentCard>? = null,
    val syncVideoFeed: Boolean? = null,
    val clothesCardList: List<PostUgcDesignCard>? = null,
    val isEdit: Boolean = false
) : Parcelable {

    companion object {
        const val MIN_LETTER_COUNT = 10
        const val MAX_LETTER_COUNT = 1000
        const val MAX_IMG_COUNT = 9
        const val MAX_VIDEO_COUNT = 1
        const val MAX_GAME_COUNT = 3
        const val MAX_OUTFIT_COUNT = 1
        const val MAX_UGC_DESIGN_COUNT = 1

        const val SOURCE_COMMUNITY = 1
        const val SOURCE_VIDEO = 2
        const val SOURCE_AVATAR = 3

        /**
         * 检查前置条件
         */
        fun check(data: PostPublish): Pair<Boolean, String?> {
            return if (data.content.trim().isNotEmpty()
                || (data.medias?.count { it.isSupported } ?: 0) > 0
                || (data.games?.count { it.isSupported } ?: 0) > 0
                || (data.styleCardList?.count { it.isSupported } ?: 0) > 0
                || data.plotCardList?.isNotEmpty() == true
                || (data.clothesCardList?.count { it.isSupported } ?: 0) > 0
            ) {
                true to null
            } else {
                false to GlobalContext.get().get<Context>().getString(R.string.invalid_post_content)
            }
        }

        fun validateMedias(
            medias: List<PostMediaResource>?,
            ts: Long = System.currentTimeMillis(),
        ): List<PostMediaResource>? {
            return if (medias.isNullOrEmpty()) {
                null
            } else {
                val context: Context = GlobalContext.get().get()
                var video: PostMediaResource? = null
                val tempMedias = medias.map {
                    if (it.isUnknown && !it.localPath.isNullOrBlank()) {
                        kotlin.runCatching {
                            val media = LocalMedia.generateLocalMedia(context, it.localPath)
                            it.copy(
                                resourceType = PostMediaResource.mimeTypeToResourceType(media.mimeType),
                                resourceWidth = media.width,
                                resourceHeight = media.height
                            )
                        }.getOrDefault(it)
                    } else if (it.isSupported && (it.resourceWidth <= 0 || it.resourceHeight <= 0)) {
                        val (width, height) = it.getSize()
                        it.copy(
                            resourceWidth = width,
                            resourceHeight = height
                        )
                    } else {
                        it
                    }
                }.filter {
                    val valid = it.isSupported && (it.isNetResource || it.localExist)
                    if (valid && it.isVideo && video == null) {
                        video = it.copy(itemId = "$ts-$0")
                    }
                    valid
                }
                if (video != null) {
                    listOf(video!!)
                } else {
                    tempMedias.take(MAX_IMG_COUNT)
                        .mapIndexed { index, media -> media.copy(itemId = "$ts-$index") }
                }
            }
        }

        fun validateGames(games: List<PostCardInfo>?): List<PostCardInfo>? {
            return games?.filter { it.isSupported && !it.gameId.isNullOrBlank() && !it.packageName.isNullOrBlank() }
                ?.take(MAX_GAME_COUNT)
        }

        fun validateTags(tags: List<PostTag>?): List<PostTag>? {
            return tags?.filter { it.tagId != 0L && !it.tagName.isNullOrBlank() }
        }

        fun validateOutfits(outfits: List<PostStyleCard>?): List<PostStyleCard>? {
            return outfits
                ?.filter { it.isSupported && !it.roleId.isNullOrBlank() && !it.wholeBodyImage.isNullOrBlank() }
                ?.take(MAX_OUTFIT_COUNT)
        }

        fun validateUgcDesigns(ugcDesigns: List<PostUgcDesignCard>?): List<PostUgcDesignCard>? {
            return ugcDesigns
                ?.filter { it.isSupported && !it.itemId.isNullOrBlank() }
                ?.take(MAX_UGC_DESIGN_COUNT)
        }

        /**
         * 通过api补充缺失tag信息
         */
        suspend fun fillTagsByApi(tags: List<PostTag>?): List<PostTag>? {
            if (tags.isNullOrEmpty()) return tags
            val needFetchData = tags.any { it.tagId == 0L || it.tagName.isNullOrBlank() }
            return validateTags(
                if (needFetchData) {
                    kotlin.runCatching {
                        val allTags = GlobalContext.get().get<IMetaRepository>().getAllPostTagList()
                        val nameById = hashMapOf<Long, String>()
                        val idByName = hashMapOf<String, Long>()
                        for (tag in allTags) {
                            if (tag.tagId == 0L || tag.tagName.isNullOrBlank()) continue
                            nameById[tag.tagId] = tag.tagName
                            idByName[tag.tagName] = tag.tagId
                        }
                        tags.map { tag ->
                            if (tag.tagId == 0L && !tag.tagName.isNullOrBlank()) {
                                tag.copy(tagId = idByName[tag.tagName] ?: 0L)
                            } else if (tag.tagName.isNullOrBlank() && tag.tagId != 0L) {
                                tag.copy(tagName = nameById[tag.tagId])
                            } else {
                                tag
                            }
                        }
                    }.getOrDefault(tags)
                } else {
                    tags
                }
            )
        }

        /*fun validateContent(content: String?): String? {
            return if ((content?.length ?: 0) > MAX_LETTER_COUNT) {
                content?.substring(0, MAX_LETTER_COUNT)
            } else {
                content
            }
        }*/

        /**
         * 帖子来源: source 的值
         * 1 社区
         * 2 视频流
         * 3 avatar拍剧
         */
        private fun getSource(extraInfo: PublishPostExtra?): Int? {
            if (extraInfo?.templateId != null) {
                return 3
            }

            if(extraInfo?.isPublishVideo == true){
                return 2
            }
            return null
        }

        fun validate(
            postId: String?,
            content: String,
            medias: List<PostMediaResource>?,
            games: List<PostCardInfo>?,
            communityTagList: List<TopicBean>?,
            outfits: List<PostStyleCard>?,
            extraInfo: PublishPostExtra?,
            momentCard: List<PostMomentCard>?,
            syncVideoFeed: Boolean?,
            ugcDesigns: List<PostUgcDesignCard>?
        ): PostPublish {
            return PostPublish(
                content = content,
                medias = validateMedias(medias),
                games = validateGames(games),
                communityTagList = communityTagList,
                postId = postId,
                source = getSource(extraInfo),
                styleCardList = validateOutfits(outfits),
                plotCardList = momentCard,
                syncVideoFeed = syncVideoFeed,
                clothesCardList = ugcDesigns,
                isEdit = !postId.isNullOrEmpty()
            )
        }



        fun List<PostMediaResource>?.filterEmptyUrl(): List<PostMediaResource>? {
            return this?.filter { it.isNetResource }
        }
    }



    fun validateByApi(): PostPublish {
        return copy(
            content = content,
            medias = validateMedias(medias),
            games = validateGames(games),
            communityTagList = communityTagList,
            styleCardList = validateOutfits(styleCardList),
        )
    }

    fun toPublishPostRequest(circleId: String) = PublishPostRequest(
        postId,
        circleId,
        content,
        PublishPostRequest.VERSION_PLAIN_TEXT,
        medias?.map { it.copy(localPath = null, itemId = null) }.filterEmptyUrl(),
        games?.map {
            PostMediaResource(
                resourceType = it.resourceType,
                resourceValue = it.gameId,
                thumb = null,
                cover = null,
                gameId = it.gameId
            )
        },
        null,
        communityTagList,
        styleCardList,
        plotCardList,
        source,
        syncVideoFeed,
        clothesCardList
    )

    fun toPublishFeed(metaUserInfo: MetaUserInfo?, ts: Long): CommunityFeedInfo {
        return CommunityFeedInfo(
            postId = postId ?: "local_not_post_$ts",
            0,
            0,
            0,
            null,
            metaUserInfo?.uuid ?: "",
            metaUserInfo?.nickname,
            metaUserInfo?.portrait,
            System.currentTimeMillis(),
            System.currentTimeMillis(),
            content,
            0,
            createPublishTags(),
            medias,
            games,
            null,
            UserOfficialExtra(
                isOfficial = metaUserInfo?.isOfficial() == true,
                tags = metaUserInfo?.tags,
                ootdPrivateSwitch = metaUserInfo?.ootdPrivateSwitch ?: false,
                labelInfo = metaUserInfo?.labelInfo
            ),
            false,
            shareCount = 0,
            communityTagList = communityTagList,
            styleCardList = styleCardList,
            plotCardList = plotCardList,
            localId = ts,
            localPublishing = true,
            labelInfo = metaUserInfo?.labelInfo
        )
    }

    private fun createPublishTags(): List<PostTag>? {
        return communityTagList?.map { richTag ->
            localExistTags?.find { it.tagName == richTag.tagName } ?: PostTag(0, richTag.tagName)
        }
    }
}

data class PostPublishResult(
    val postId: String?,
    val mediaList: List<PostMediaResource>? = null,
    val gameCardList: List<PostCardInfo>? = null,
    val communityTagList: List<TopicBean>? = null,
    val tagList: List<PostTag>? = null,
    val styleCardList: List<PostStyleCard>? = null,
    val plotCardList: List<PostMomentCard>? = null,
    val clothesCardList: List<PostUgcDesignCard>? = null,
)

data class PublishPostRequest(
    val postId: String?,
    val circleId: String,
    val content: String,
    val contentParseVesrion: Int,
    val mediaList: List<PostMediaResource>?,
    val gameCardList: List<PostMediaResource>?,
    val tagList: List<Long>?,
    val communityTagList:List<TopicBean>?,
    val styleCardList: List<PostStyleCard>?,
    val plotCardList: List<PostMomentCard>?,
    val source: Int?,
    val syncVideoFeed: Boolean?,
    val clothesCardList: List<PostUgcDesignCard>?
) {
    companion object {
        const val VERSION_PLAIN_TEXT = 0
        const val VERSION_COMMUNITY_RICH_TEXT = 1
        const val VERSION_FORUM_CODE_BLOCK = 2
    }
}

@Parcelize
data class PostPublishStatus(
    val status: Int = STATUS_UNINITIALIZED,
    val errorMessage: String? = null,
    val uploadStatus: UploadStatus? = null,
    val post: PostPublish? = null,
    val ts: Long = System.currentTimeMillis()
) : Parcelable {
    companion object {
        const val STATUS_UNINITIALIZED = 0 // 未发帖
        const val STATUS_CHECKING = 10 // 检查中
        const val STATUS_UPLOADING = 11 // 上传文件中
        const val STATUS_PUBLISHING = 12 // 发布中
        const val STATUS_SUCCEEDED = 100 // 发布成功
        const val STATUS_FAILED = 1000 // 发布失败
    }

    fun hasPublishState(): Boolean {
        return status == STATUS_CHECKING || status == STATUS_UPLOADING || status == STATUS_PUBLISHING || status == STATUS_SUCCEEDED || status == STATUS_FAILED
    }

    fun publishOver(): Boolean {
        return status == STATUS_UNINITIALIZED || status == STATUS_SUCCEEDED || status == STATUS_FAILED
    }

    fun isVideoPublish(): Boolean {
        return post?.source == PostPublish.SOURCE_VIDEO
    }

    fun upload(
        doneCount: Int,
        totalCount: Int,
        currentPercent: Double,
    ): PostPublishStatus {
        return copy(
            status = STATUS_UPLOADING,
            uploadStatus = UploadStatus(doneCount, totalCount, currentPercent)
        )
    }

    fun publish(post: PostPublish): PostPublishStatus {
        return copy(status = STATUS_PUBLISHING, post = post)
    }

    fun succeed(post: PostPublish): PostPublishStatus {
        return copy(status = STATUS_SUCCEEDED, post = post)
    }

    fun check(post: PostPublish): PostPublishStatus {
        return PostPublishStatus(status = STATUS_CHECKING, post = post)
    }

    fun fail(errorMessage: String?, post: PostPublish? = null): PostPublishStatus {
        return copy(status = STATUS_FAILED, errorMessage = errorMessage, post = post ?: this.post)
    }

    fun printDebugLog() {
        if (BuildConfig.DEBUG) {
            val statusStr = when (status) {
                STATUS_UNINITIALIZED -> "STATUS_UNINITIALIZED"
                STATUS_CHECKING -> "STATUS_CHECKING"
                STATUS_UPLOADING -> "STATUS_UPLOADING"
                STATUS_PUBLISHING -> "STATUS_PUBLISHING"
                STATUS_SUCCEEDED -> "STATUS_SUCCEEDED"
                STATUS_FAILED -> "STATUS_FAILED"
                else -> "STATUS_UNKNOWN"
            }
            val uploadStatusStr = if (uploadStatus != null) {
                "${uploadStatus.curCount}/${uploadStatus.totalCount} - ${
                    String.format(
                        "%.1f",
                        uploadStatus.currentPercent * 100
                    )
                }%"
            } else {
                "0 0 0"
            }
            Timber.tag("PostPublishStatus").d("status: $statusStr\nuploadStatus: $uploadStatusStr")
        }
    }

    @Parcelize
    data class UploadStatus(
        val curCount: Int = 0, // 当前文件数
        val totalCount: Int = 0, // 总文件数
        val currentPercent: Double = 0.0, // [0, 1]，当前文件上传“百分比”，需要×100
    ) : Parcelable {

        val curTotalPercent: Double
            get() {
                return if (totalCount <= 0 || curCount <= 0) {
                    0.0
                } else {
                    ((curCount - 1) + currentPercent) / totalCount
                }
            }
    }
}

@Parcelize
open class CommonPostPublishEvent(
    val requestId: String,
    val method: String,
    val gameId: String,
    val processName: String,
) : Parcelable

class CommonPostPublishSendEvent(
    requestId: String,
    method: String,
    gameId: String,
    processName: String,
    val data: Any? = null,
) : CommonPostPublishEvent(
    requestId = requestId,
    method = method,
    gameId = gameId,
    processName = processName
) {

    fun sendBack(
        data: Any? = null,
        code: Int = 200,
        errMsg: String? = null,
    ) {
        CpEventBus.post(
            CommonPostPublishReceiveEvent(
                requestId = requestId,
                method = method,
                gameId = gameId,
                processName = processName,
                data = data,
                code = code,
                msg = errMsg
            )
        )
    }
}

class CommonPostPublishReceiveEvent(
    requestId: String,
    method: String,
    gameId: String,
    processName: String,
    val data: Any? = null,
    val code: Int = 200,
    val msg: String? = null,
) : CommonPostPublishEvent(
    requestId = requestId,
    method = method,
    gameId = gameId,
    processName = processName
)

/**
 * 标签
 */
@Parcelize
data class TopicBean(
    val tagId: Long? = null,
    val resourceType: String = "7",
    val resourceValue: String = "tagName",
    var tagName: String = "", // 文本
    var index: Int = 0,
    // 仅发帖页面本地用，其他地方为空
    var localEnd: Int = 0
) : Parcelable

class PublishTrackData(
    val startMills: Long,
    val uploadCount: Int,
    val uploadSize: Long
)