package com.socialplay.gpark.data.model.post

import android.os.Parcelable
import androidx.core.graphics.toColorInt
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.util.UnitUtil
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/03/25
 *     desc   :
 * </pre>
 */
@Parcelize
data class PostStyleCard(
    val roleId: String,
    val wholeBodyImage: String,
    val pv: Long? = null,
    val resourceType: Int? = null,
    val resourceValue: String? = null,
    val cardBgColor: String? = null
) : Parcelable {

    companion object {
        const val RES_TYPE_OUTFIT = 6

        fun createOutfitCard(roleId: String, wholeBodyImage: String) = PostStyleCard(
            roleId,
            wholeBodyImage,
            resourceType = RES_TYPE_OUTFIT,
            resourceValue = roleId
        )
    }

    val isSupported: Boolean get() = isOutfit
    val isOutfit: Boolean get() = resourceType == RES_TYPE_OUTFIT && PandoraToggle.enableTryOnShare

    val pvStr: String get() = UnitUtil.formatKMCount((pv ?: 0L).coerceAtLeast(1L))
    val cardBgColorInt get() =  runCatching {
        cardBgColor?.toColorInt()
    }.getOrNull()
}

data class PostOutfitVisitRequest(
    val resType: String,
    val resId: String,
) {

    companion object {
        const val RES_TYPE_OUTFIT = "7"

        fun createOutfitVisit(postId: String, roleId: String) = PostOutfitVisitRequest(
            RES_TYPE_OUTFIT,
            "${postId}_${roleId}"
        )
    }
}

