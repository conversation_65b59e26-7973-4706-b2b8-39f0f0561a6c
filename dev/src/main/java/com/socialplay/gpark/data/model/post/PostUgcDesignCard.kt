package com.socialplay.gpark.data.model.post

import android.os.Parcelable
import androidx.core.graphics.toColorInt
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/03/25
 *     desc   :
 * </pre>
 */
@Parcelize
data class PostUgcDesignCard(
    val itemId: String,
    val cover: String?,
    val title: String?,
    val resourceType: Int? = null,
    val resourceValue: String? = null,
    val tryOnCount: Long? = null,
    val cardBgColor: String? = null
) : Parcelable {

    companion object {
        const val RES_TYPE_UGC_DESIGN = 9

        fun createUgcDesignCard(itemId: String, cover: String?, title: String?) = PostUgcDesignCard(
            itemId,
            cover,
            title,
            resourceType = RES_TYPE_UGC_DESIGN,
            resourceValue = itemId
        )
    }

    val isSupported: Boolean get() = isUgcDesign
    val isUgcDesign: Boolean get() = resourceType == RES_TYPE_UGC_DESIGN
    val cardBgColorInt
        get() = runCatching {
            cardBgColor?.toColorInt()
        }.getOrNull()
}