package com.socialplay.gpark.data.model.share

import android.os.Parcelable
import android.os.SystemClock
import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.data.model.community.PostFeedCard
import com.socialplay.gpark.data.model.editor.PgcGameDetail
import com.socialplay.gpark.data.model.editor.UgcGameDetail
import com.socialplay.gpark.data.model.friend.CommonShareResult
import com.socialplay.gpark.data.model.outfit.UgcDesignDetail
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.PostDetail
import com.socialplay.gpark.data.model.post.PostShareDetail
import com.socialplay.gpark.data.model.post.request.AddPvRequest
import com.socialplay.gpark.data.model.profile.UserProfileInfo
import com.socialplay.gpark.data.model.profile.recent.RecentPlayListV2Response
import com.socialplay.gpark.data.model.videofeed.VideoFeedItem
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.util.extension.ifNullOrBlank
import kotlinx.parcelize.Parcelize
import kotlin.math.max

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/01
 *     desc   :
 * </pre>
 */
@Parcelize
data class ShareFeature(
    val featureId: Int,
    val iconRes: Int,
    val titleRes: Int = 0,
    val title: String? = null,
    val showRedDot: Boolean = false,
    /**
     * 点击的埋点值
     */
    val clickTrackParams: Map<String, String>? = null,
) : Parcelable{
    companion object{
        const val FEAT_DELETE_GAME = -1
    }
}

data class ShareRawData(
    val scene: String,
    val user: UserProfileInfo? = null,
    val userExtra: UserExtra? = null,
    val pgcGame: Game? = null,
    val ugcGame: UgcGameDetail? = null,
    val videoFeed: VideoFeedItem? = null,
    val images: List<String>? = null,
    val videos: List<String>? = null,
    val gameId: String? = null,
    val reqId: String? = null,
    val ugcDesignDetail: UgcDesignDetail? = null,
    val postDetail: PostShareDetail? = null,
) {

    companion object {

        fun user(
            user: UserProfileInfo,
            userExtra: UserExtra?
        ) = ShareRawData(
            ShareHelper.SCENE_PROFILE,
            user = user,
            userExtra = userExtra
        )

        fun pgc(
            pgcGame: PgcGameDetail,
            reqId: String?
        ): ShareRawData {
            val firstImage = pgcGame.images?.firstOrNull { !it.url.isNullOrBlank() }
            return ShareRawData(
                ShareHelper.SCENE_PGC_DETAIL,
                pgcGame = Game(
                    pgcGame.id,
                    pgcGame.displayName,
                    pgcGame.icon,
                    pgcGame.description,
                    pgcGame.packageName,
                    0,
                    max(pgcGame.updateTime, pgcGame.createTime),
                    pgcGame.author?.id,
                    pgcGame.author?.name,
                    pgcGame.author?.avatar,
                    pgcGame.author?.number,
                    pgcGame.onlineGameCount,
                    0.0f,
                    pgcGame.pvCount,
                    pgcGame.likeCount,
                    firstImage?.isHor() == true
                ),
                images = pgcGame.images?.mapNotNull { if (it.url.isNullOrBlank()) null else it.url },
                reqId = if (reqId == "null") null else reqId
            )
        }

        fun ugc(ugcGame: UgcGameDetail, reqId: String?) = ShareRawData(
            ShareHelper.SCENE_UGC_DETAIL,
            ugcGame = ugcGame,
            images = ugcGame.banner?.let { listOf(it) },
            reqId = if (reqId == "null") null else reqId
        )

        fun post(postDetail: PostDetail) = ShareRawData(
            ShareHelper.SCENE_POST_DETAIL,
            postDetail = PostShareDetail(
                postDetail.postId,
                postDetail.content.orEmpty(),
                postDetail.netThumbNail,
                postDetail.netVideo,
            ),
            images = postDetail.mediaList
                ?.filter { it.isImage && it.isNetResource }
                ?.map { it.resourceValue },
            videos = postDetail.mediaList
                ?.filter { it.isVideo && it.isNetResource }
                ?.map { it.resourceValue },
        )

        fun post(postFeed: CommunityFeedInfo) = ShareRawData(
            ShareHelper.SCENE_POST_DETAIL,
            postDetail = PostShareDetail(
                postFeed.postId,
                postFeed.content.orEmpty(),
                postFeed.netThumbNail,
                postFeed.netVideo,
            ),
            images = postFeed.mediaList
                ?.filter { it.isImage && it.isNetResource }
                ?.map { it.resourceValue },
            videos = postFeed.mediaList
                ?.filter { it.isVideo && it.isNetResource }
                ?.map { it.resourceValue },
        )

        fun post(postFeed: PostFeedCard) = ShareRawData(
            ShareHelper.SCENE_POST_DETAIL,
            postDetail = PostShareDetail(
                postFeed.postId ?: "",
                postFeed.content.orEmpty(),
                postFeed.netThumbNail,
                postFeed.netVideo,
            ),
            images = postFeed.mediaList
                ?.filter { it.isImage && it.isNetResource }
                ?.map { it.resourceValue },
            videos = postFeed.mediaList
                ?.filter { it.isVideo && it.isNetResource }
                ?.map { it.resourceValue },
        )

        fun video(video: VideoFeedItem, reqId: String?) = ShareRawData(
            ShareHelper.SCENE_VIDEO_FEED,
            videoFeed = video,
            reqId = if (reqId == "null") null else reqId
        )

        fun ocMoment(images: List<String>?, videos: List<String>?, gameId: String) = ShareRawData(
            ShareHelper.SCENE_OC_MOMENT,
            images = images,
            videos = videos,
            gameId = gameId
        )

        fun screenshot(image: String, gameId: String?) = ShareRawData(
            ShareHelper.SCENE_SCREENSHOT,
            images = listOf(image),
            gameId = gameId
        )

        fun ugcDesignDetail(ugcDesignDetail: UgcDesignDetail) = ShareRawData(
            ShareHelper.SCENE_UGC_DESIGN_DETAIL,
            ugcDesignDetail = ugcDesignDetail
        )
    }

    val hasMedia: Boolean
        get() = hasVideo || hasImage

    val hasVideo: Boolean
        get() = when (scene) {
            ShareHelper.SCENE_VIDEO_FEED -> true
            ShareHelper.SCENE_POST_DETAIL,
            ShareHelper.SCENE_OC_MOMENT -> !videos.isNullOrEmpty()

            else -> false
        }

    val hasImage
        get() = when (scene) {
            ShareHelper.SCENE_PGC_DETAIL,
            ShareHelper.SCENE_UGC_DETAIL,
            ShareHelper.SCENE_POST_DETAIL,
            ShareHelper.SCENE_OC_MOMENT,
            ShareHelper.SCENE_SCREENSHOT -> !images.isNullOrEmpty()

            else -> false
        }

    val video: String?
        get() = when (scene) {
            ShareHelper.SCENE_VIDEO_FEED -> videoFeed?.videoUrl
            ShareHelper.SCENE_POST_DETAIL,
            ShareHelper.SCENE_OC_MOMENT -> videos?.firstOrNull()

            else -> null
        }

    val image: String?
        get() = when (scene) {
            ShareHelper.SCENE_PGC_DETAIL,
            ShareHelper.SCENE_UGC_DETAIL,
            ShareHelper.SCENE_POST_DETAIL,
            ShareHelper.SCENE_OC_MOMENT,
            ShareHelper.SCENE_SCREENSHOT -> images?.firstOrNull()

            else -> null
        }

    val trackSource
        get() = when (scene) {
            ShareHelper.SCENE_PROFILE -> 1
            ShareHelper.SCENE_PGC_DETAIL -> 2
            ShareHelper.SCENE_UGC_DETAIL -> 3
            ShareHelper.SCENE_POST_DETAIL -> 4
            ShareHelper.SCENE_VIDEO_FEED -> 5
            ShareHelper.SCENE_OC_MOMENT -> 6
            ShareHelper.SCENE_SCREENSHOT -> 7
            ShareHelper.SCENE_UGC_DESIGN_DETAIL -> 8
            else -> 0
        }

    fun getResIdAndType(): Pair<String, String>? {
        return when (scene) {
            ShareHelper.SCENE_PROFILE -> {
                null
            }

            ShareHelper.SCENE_PGC_DETAIL -> {
                (pgcGame?.id ?: return null) to AddPvRequest.RES_TYPE_PGC_SHARE
            }

            ShareHelper.SCENE_UGC_DETAIL -> {
                (ugcGame?.id ?: return null) to AddPvRequest.RES_TYPE_UGC_SHARE
            }

            ShareHelper.SCENE_VIDEO_FEED -> {
                (videoFeed?.videoId ?: return null) to AddPvRequest.RES_TYPE_POST_SHARE
            }

            ShareHelper.SCENE_OC_MOMENT -> {
                null
            }

            ShareHelper.SCENE_SCREENSHOT -> {
                null
            }

            else -> {
                null
            }
        }
    }

    data class Game(
        val id: String? = null,
        val name: String? = null,
        val icon: String? = null,
        val description: String? = null,
        val pkg: String? = null,
        val shareCount: Long = 0,
        val updateTime: Long = 0,
        val authorId: String? = null,
        val authorName: String? = null,
        val authorAvatar: String? = null,
        val authorNumber: String? = null,
        val authorPortfolio: Long? = null,
        // GameScoreResult
        val avgScore: Float? = null,
        // SnsInfo
        val playerCount: Long? = null,
        val likeCount: Long? = null,
        // IGameCover
        val isBannerHor: Boolean
    )

    data class UserExtra(
        val recentGames: List<RecentPlayListV2Response.Game>,
        val qrCode: String
    )
}

data class SharePendingData(
    val platform: String,
    val subPlatform: String? = null,
    val info: CommonShareResult? = null,
    val ts: Long = SystemClock.elapsedRealtime()
) {

    val url get() = info?.jumpUrl.ifNullOrBlank { ShareHelper.downloadPage }
}

typealias ShareConfigCallback = (config: ShareConfig) -> ShareData

data class ShareResult(
    val requestId: String,
    val platform: String,
    val status: Int,
    val code: Int = ShareHelper.CODE_OK,
    val msg: String? = null,
    val sdkCode1: Int? = null,
    val sdkCode2: Int? = null,
    val extra: String? = null,
    val extraData: Map<String, String>? = null
) {

    companion object {
        const val STATUS_SUCCESS = 1
        const val STATUS_CANCEL = 0
        const val STATUS_FAIL = -1

        private fun success(
            requestId: String,
            platform: String,
            extra: String? = null,
            extraData: Map<String, String>? = null
        ) =
            ShareResult(
                requestId,
                platform,
                STATUS_SUCCESS,
                extra = extra,
                extraData = extraData
            )

        private fun cancel(
            requestId: String,
            platform: String,
            extra: String? = null,
            extraData: Map<String, String>? = null
        ) =
            ShareResult(
                requestId,
                platform,
                STATUS_CANCEL,
                ShareHelper.CODE_CANCELLED,
                null,
                extra = extra,
                extraData = extraData
            )

        private fun fail(
            requestId: String,
            platform: String,
            code: Int,
            msg: String?,
            sdkCode1: Int? = null,
            sdkCode2: Int? = null,
            extra: String? = null,
            extraData: Map<String, String>? = null
        ) = ShareResult(
            requestId,
            platform,
            STATUS_FAIL,
            code,
            msg,
            sdkCode1 = sdkCode1,
            sdkCode2 = sdkCode2,
            extra = extra,
            extraData = extraData
        )

        fun notifySuccess(
            requestId: String,
            platform: String,
            extra: String? = null,
            extraData: Map<String, String>? = null
        ) {
            CpEventBus.post(success(requestId, platform, extra = extra, extraData = extraData))
        }

        fun notifyCancel(
            requestId: String,
            platform: String,
            extra: String? = null,
            extraData: Map<String, String>? = null
        ) {
            CpEventBus.post(cancel(requestId, platform, extra = extra, extraData = extraData))
        }

        fun notifyFail(
            requestId: String,
            platform: String,
            code: Int,
            msg: String?,
            sdkCode1: Int? = null,
            sdkCode2: Int? = null,
            extra: String? = null,
            extraData: Map<String, String>? = null
        ) {
            CpEventBus.post(
                fail(
                    requestId,
                    platform,
                    code,
                    msg,
                    sdkCode1 = sdkCode1,
                    sdkCode2 = sdkCode2,
                    extra = extra,
                    extraData = extraData
                )
            )
        }
    }

    val isSuccess get() = status == STATUS_SUCCESS
    val isCancel get() = status == STATUS_CANCEL
    val isFail get() = status == STATUS_FAIL
    val fullCode: String
        get() {
            val sb = StringBuilder(code)
            if (sdkCode1 != null) {
                sb.append("_${sdkCode1}")
            }
            if (sdkCode2 != null) {
                sb.append("_${sdkCode2}")
            }
            return sb.toString()
        }

    fun match(requestId: String, platform: String): Boolean {
        return this.requestId == requestId && this.platform == platform
    }

    fun match(shareData: ShareData): Boolean {
        return match(shareData.requestId, shareData.platform)
    }

    fun match(directShareData: DirectShareData): Boolean {
        return match(directShareData.requestId, directShareData.platform)
    }
}