package com.socialplay.gpark.data.model.startup

import android.os.Parcelable
import com.socialplay.gpark.EnvConfig
import kotlinx.parcelize.Parcelize

@Parcelize
data class GuideInfo(
    val title: String?,
    val desc: String?,
    val imgUrl: String?,
    val type: String?
) : Parcelable {
    companion object {
        const val TYPE_MAPS = "maps"
        const val TYPE_DESIGNS = "designs"
        const val TYPE_MOMENTS = "moments"
        const val TYPE_POSTS = "posts"
        const val TYPE_MODULES = "modules"
        const val TYPE_DRAWS = "draws"
        const val TYPE_SHOT = "shot"
        const val TYPE_CAR = "car"
        const val TYPE_OBBY = "obby"
        const val TYPE_GOODS = "goods"

        const val TYPE_MODULE_RECYCLE = "moduleRecycle"
    }

    val supported get() = isMap || isModule || isPosts || isCar || isModuleRecycle
    val blockBack get() = isMap || isModule || isShot
    val isMap get() = type == TYPE_MAPS
    val isDesign get() = type == TYPE_DESIGNS
    val isModule get() = type == TYPE_MODULES
    val isDraw get() = type == TYPE_DRAWS
    val isShot get() = type == TYPE_SHOT
    val isPosts get() = type == TYPE_POSTS
    val isCar get() = type == TYPE_CAR
    val isObby get() = type == TYPE_OBBY

    val isModuleRecycle get() = type == TYPE_MODULE_RECYCLE

    val trackType
        get() = when {
            isMap -> 1
            isDesign -> 2
            isModule -> 3
            isDraw -> 4
            isCar -> 5
            isShot -> 6
            isObby -> 7
            else -> 0
        }
}

@Parcelize
data class AddTemplate(
    val type: String?,
    val templateId: String?
) : Parcelable