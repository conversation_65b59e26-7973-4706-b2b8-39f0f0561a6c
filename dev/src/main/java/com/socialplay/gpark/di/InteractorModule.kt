package com.socialplay.gpark.di

import android.app.Application
import android.content.Context
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.interactor.*
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.function.exoplayer.SharedVideoPlayerControllerInteractor
import com.socialplay.gpark.function.exoplayer.VideoPlayerCacheInteractor
import com.socialplay.gpark.function.network.NetworkChangedInteractor
import com.socialplay.gpark.data.interactor.EditorGameLoadInteractor
import com.meta.ipc.IPC
import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.function.exoplayer.VideoFeedPreloadInteractor
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.pay.PayProvider
import org.koin.android.ext.koin.androidContext
import org.koin.dsl.module
import retrofit2.Retrofit

/**
 * Created by yaqi.liu on 2021/5/12
 */
val interactorModule = module {
    fun provideUpdateAppInteractor(
        metaRepository: IMetaRepositoryWrapper,
    ): UpdateAppInteractor {
        return UpdateAppInteractor(metaRepository)
    }

    fun provideAccountInteractor(
        metaRepository: IMetaRepositoryWrapper,
        metaKV: MetaKV
    ): AccountInteractor {
        return AccountInteractor(metaRepository, metaKV)
    }

    fun provideDeviceInteractor(metaKV: MetaKV, context: Context): DeviceInteractor {
        return DeviceInteractor(metaKV, context)
    }

    fun provideFriendInteractor(
        context: Context,
        retrofit: Retrofit,
        repository: IMetaRepository,
        accountInteractor: AccountInteractor,
        networkChangedInteractor: NetworkChangedInteractor,
    ): FriendInteractor {
        return FriendInteractor(
            context,
            retrofit,
            repository,
            accountInteractor,
            networkChangedInteractor
        )
    }

    fun providerNetworkInteractor(context: Context) = NetworkChangedInteractor(context)

    fun provideQrCodeInteractor(metaRepository: IMetaRepository, context: Context, metaKV: MetaKV) = QrCodeInteractor(metaRepository, context, metaKV)

    fun provideImInteractor(metaRepository: IMetaRepository, metaKV: MetaKV) = ImInteractor(metaRepository, metaKV)

    fun provideMgsInteractor(
        metaRepository: IMetaRepository,
        accountInteractor: AccountInteractor,
        banBlockInteractor: BanBlockInteractor,
        uploadFileInteractor: UploadFileInteractor,
        metaKV: MetaKV
    ) = MgsInteractor(
        metaRepository,
        accountInteractor,
        banBlockInteractor,
        uploadFileInteractor,
        metaKV
    )

    fun provideFloatNoticeInteractor(accountInteractor: AccountInteractor, friendInteractor: FriendInteractor, metaRepository: IMetaRepository) = FloatNoticeInteractor(accountInteractor, friendInteractor, metaRepository)

    fun providePayInteractor(
        metaRepository: IMetaRepositoryWrapper,
        metaApp: Application,
        accountInteractor: AccountInteractor,
        metaKV: MetaKV
    ): IPayInteractor {
        return PayProvider.getPayInteractor(metaRepository,
            metaApp,
            accountInteractor,
            metaKV
        )
    }

    fun provideIPC() = IPC.getInstance()

    fun provideBanBlockInteractor(metaRepository: IMetaRepository) = BanBlockInteractor(metaRepository)

    single { provideAccountInteractor(get(), get()) }
    single { provideDeviceInteractor(get(), androidContext()) }
    single { NetworkInteractor(get()) }


    single { providerNetworkInteractor(androidContext()) }
    single { provideFriendInteractor(get(), get(), get(), get(), get()) }
    single { provideQrCodeInteractor(get(), get(), get()) }
    single { provideImInteractor(get(), get()) }
    single { provideFloatNoticeInteractor(get(), get(), get()) }
    single { provideMgsInteractor(get(), get(), get(), get(), get()) }
    single { provideIPC() }
    single { SystemNoticeInteractor(get(), get()) }
    single { providePayInteractor(get(), get(), get(), get()) }
    single { EditorInteractor(get(), get(), get()) }
    single { SdkInteractor() }
    single { provideBanBlockInteractor(get()) }
    single { MVCoreProxyInteractor() }
    single { EditorGameLoadInteractor(get(), get(), get(), get(), get(), get()) }
    single { VideoPlayerCacheInteractor(get()) }
    single { SharedVideoPlayerControllerInteractor(get()) }
    single { UserMemberInteractor(get(), get(), get()) }
    single { H5PageConfigInteractor(get(), get()) }
    single { UploadFileInteractor(get()) }
    single { PingInteractor(get()) }
    single { TabConfigInteractor(get(), get(), get()) }
    single { TTaiInteractor(get(), get()) }
    single { PublishPostInteractor(androidContext(), get(), get(), get()) }
    single { EditorGameLifecycleInteractor(get(),get(),get()) }
    single { VideoFeedPreloadInteractor(get(), get(), get()) }
    single { provideUpdateAppInteractor(get()) }
}

