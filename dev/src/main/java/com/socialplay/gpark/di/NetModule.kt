package com.socialplay.gpark.di

import android.app.Application
import android.content.Context
import android.os.Build
import android.os.SystemClock
import android.telephony.TelephonyManager
import com.bin.stetho.WrapStetho
import com.google.gson.Gson
import com.meta.lib.mwbiz.MWBiz
import com.meta.pandora.Pandora
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.api.NAMED_DTOKEN_OKHTTP_CLIENT
import com.socialplay.gpark.data.api.NAMED_DTOKEN_RETROFIT
import com.socialplay.gpark.data.interactor.DeviceInteractor
import com.socialplay.gpark.data.interactor.HeaderHelper
import com.socialplay.gpark.data.interactor.PingInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.local.SimpleDiskLruCache
import com.socialplay.gpark.data.local.VideoThumbnailCache
import com.socialplay.gpark.data.model.API_HEADER_TOKEN
import com.socialplay.gpark.data.model.QrResult
import com.socialplay.gpark.data.model.QrResultDeserializer
import com.socialplay.gpark.data.model.community.RecommendFeedApiDeserializer
import com.socialplay.gpark.data.model.community.RecommendFeedCard
import com.socialplay.gpark.data.model.mw.MWPartInfoResponseDeserializer
import com.socialplay.gpark.data.model.mw.PartInfoResponse
import com.socialplay.gpark.data.model.qrcode.QrCodeDataDeserializer
import com.socialplay.gpark.data.model.qrcode.QrCodeResolveApiDeserializer
import com.socialplay.gpark.data.model.qrcode.QrCodeResolveApiResponse
import com.socialplay.gpark.data.model.qrcode.QrCodeResolveResponse
import com.socialplay.gpark.function.http.CheckTokenInterceptor
import com.socialplay.gpark.function.http.HUnpackApiCodeInterceptor
import com.socialplay.gpark.function.http.HashCodeHttpLoggingInterceptor
import com.socialplay.gpark.function.http.MUnpackApiCodeInterceptor
import com.socialplay.gpark.function.http.OtherUnpackApiCodeInterceptor
import com.socialplay.gpark.function.locale.MetaLanguages
import com.socialplay.gpark.function.startup.StartupProcessType
import com.socialplay.gpark.function.startup.core.StartupContext
import com.socialplay.gpark.util.DeviceUtil
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.RequestEncryptionUtil
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import okio.Buffer
import org.koin.android.ext.koin.androidApplication
import org.koin.core.context.GlobalContext
import org.koin.core.qualifier.named
import org.koin.dsl.module
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import timber.log.Timber
import java.io.IOException
import java.nio.charset.Charset
import java.nio.charset.StandardCharsets
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit


class ApiInterceptorIOException(message: String? = null, override val cause: Throwable) :
    IOException(message, cause)


/**
 * Created by yaqi.liu on 2021/5/7
 */
val networkModule = module {
    single { CommonParamsProvider(get(), get(), get()) }

    Timber.d("initConfig 初始化 BuildConfig.BASE_URL:${BuildConfig.BASE_URL}")
    single { provideRetrofit(okHttpClient = get(), url = BuildConfig.BASE_URL) }

    // 双Token retrofit（防止刷新token阻塞）
    single(named(NAMED_DTOKEN_RETROFIT)) { provideDTokenRetrofit(okHttpClient = get(named(NAMED_DTOKEN_OKHTTP_CLIENT)), url = BuildConfig.BASE_URL) }

    single { provideOkHttpClient() }

    // 双Token okhttpClient
    single(named(NAMED_DTOKEN_OKHTTP_CLIENT)) { provideDTokenOkHttpClient() }

    single { provideDiskLruCache(androidApplication()) }

    single { provideVideoThumbnailCache() }
}

fun provideDiskLruCache(app: Application): SimpleDiskLruCache {
    return SimpleDiskLruCache(app)
}

fun provideVideoThumbnailCache(): VideoThumbnailCache {
    return VideoThumbnailCache()
}

internal fun provideOkHttpClient(
    connectionTimeout: Long = 10L,
    readTimeout: Long = 60L,
    writeTimeout: Long = 10L,
    builderBlock: ((OkHttpClient.Builder) -> Unit)? = null
): OkHttpClient {
    val httpLoggingInterceptor = HashCodeHttpLoggingInterceptor().apply {
        level = if (BuildConfig.DEBUG) HashCodeHttpLoggingInterceptor.Level.CURL else HashCodeHttpLoggingInterceptor.Level.BASIC
    }
    val builder = OkHttpClient.Builder()
    if (BuildConfig.DEBUG) {
        builder.addNetworkInterceptor(WrapStetho.getNetworkInterceptor() as Interceptor)
    }

    builder
        .connectTimeout(connectionTimeout, TimeUnit.SECONDS)
        .readTimeout(readTimeout, TimeUnit.SECONDS)
        .writeTimeout(writeTimeout, TimeUnit.SECONDS)
        .addInterceptor(provideSEApiReqTimeInterceptor())
        .addInterceptor(provideHttpMonitorInterceptor())
        .addInterceptor(provideHeaderInterceptor())
        .addInterceptor(httpLoggingInterceptor)
        .addInterceptor(provideUnpackApiCodeInterceptor())
        .addInterceptor(RequestEncryptInterceptor()) // 部分接口加密
        .addInterceptor(ResponseDecryptInterceptor()) // 部分接口解密
    builderBlock?.invoke(builder)
    return builder.build()
}

/**
 * glide 图片加载端到端监控
 */
fun provideGlideOkHttpClient(): OkHttpClient {
    val builder = OkHttpClient.Builder()
    if (BuildConfig.DEBUG) {
        builder.addNetworkInterceptor(WrapStetho.getNetworkInterceptor() as Interceptor)
    }
    return builder
        .connectTimeout(10L, TimeUnit.SECONDS)
        .readTimeout(10L, TimeUnit.SECONDS)
        .addInterceptor(provideHttpMonitorInterceptor())
        .build()
}

internal fun provideDTokenOkHttpClient(): OkHttpClient {
    val httpLoggingInterceptor = HashCodeHttpLoggingInterceptor().apply {
        level = if (BuildConfig.DEBUG) HashCodeHttpLoggingInterceptor.Level.CURL else HashCodeHttpLoggingInterceptor.Level.BASIC
    }
    return OkHttpClient.Builder()
        .connectTimeout(10L, TimeUnit.SECONDS)
        .readTimeout(10L, TimeUnit.SECONDS)
        .addInterceptor(provideHttpMonitorInterceptor())
        .addInterceptor(provideHeaderInterceptor())
        .addInterceptor(httpLoggingInterceptor)
        .addInterceptor(provideUnpackApiCodeInterceptor()) // 不会走token检查刷新逻辑，会走潘多拉接口监控
        .build()
}

internal fun provideUnpackApiCodeInterceptor(): CheckTokenInterceptor {
    return when (StartupContext.get().processType) {
        StartupProcessType.H -> {
            HUnpackApiCodeInterceptor()
        }
        StartupProcessType.M -> {
            MUnpackApiCodeInterceptor()
        }
        else -> {
            OtherUnpackApiCodeInterceptor()
        }
    }
}

internal fun provideRetrofit(okHttpClient: OkHttpClient, url: String): Retrofit {
    Timber.d("baseUrl: $url")
    val gson = Gson()
        .newBuilder()
        .disableHtmlEscaping()
        .registerTypeAdapter(QrResult::class.java, QrResultDeserializer())
        .registerTypeAdapter(QrCodeResolveResponse::class.java, QrCodeDataDeserializer())
        .registerTypeAdapter(QrCodeResolveApiResponse::class.java, QrCodeResolveApiDeserializer())
        .registerTypeAdapter(PartInfoResponse::class.java, MWPartInfoResponseDeserializer)
        .registerTypeAdapter(RecommendFeedCard::class.java, RecommendFeedApiDeserializer)
        .create()
    return Retrofit.Builder()
        .baseUrl(url)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create(gson))
        .build()
}

internal fun provideDTokenRetrofit(okHttpClient: OkHttpClient, url: String): Retrofit {
    Timber.d("baseUrl: $url")
    val gson = Gson()
        .newBuilder()
        .disableHtmlEscaping()
        .registerTypeAdapter(QrResult::class.java, QrResultDeserializer())
        .registerTypeAdapter(QrCodeResolveResponse::class.java, QrCodeDataDeserializer())
        .registerTypeAdapter(QrCodeResolveApiResponse::class.java, QrCodeResolveApiDeserializer())
        .registerTypeAdapter(PartInfoResponse::class.java, MWPartInfoResponseDeserializer)
        .create()
    return Retrofit.Builder()
        .baseUrl(url)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create(gson))
        .build()
}

private fun provideHttpMonitorInterceptor(): Interceptor {

    return Interceptor { chain ->
        val request = chain.request()
        val requestUrl = request.url.toString().substringBefore("?")
        val monitor = Pandora.monitor(requestUrl)
        monitor.start()
        val response = try {
            chain.proceed(request)
        } catch (e: Exception) {
            monitor.error(e::class.java.name)
            throw ApiInterceptorIOException(requestUrl, e)
        }
        monitor.finish(response.code)

        response
    }
}

private fun provideHeaderInterceptor(): Interceptor {
    return object : Interceptor {
        private val commonHeaderBuilder by lazy { CommonHeaderBuilder() }
        override fun intercept(chain: Interceptor.Chain): Response {
            try {
                return processWithException(chain)
            } catch (e: Throwable) {
                if (e !is IOException) {
                    throw ApiInterceptorIOException(cause = e)
                } else {
                    throw e
                }
            }
        }

        @Throws(Throwable::class)
        private fun processWithException(chain: Interceptor.Chain): Response {
            val request = chain.request()
            val shouldAddAuthHeaders = request.headers["isAuthorizable"] != "false"
            val newRequest = request.newBuilder()
                .apply {
                    commonHeaderBuilder.buildCommonParams(shouldAddAuthHeaders).apply(this)
                }
                .build()
            return chain.proceed(newRequest)
        }
    }
}
class CommonHeaderBuilder {

    private val commonParams by lazy { GlobalContext.get().get<CommonParamsProvider>() }

    fun buildCommonParams(shouldAddAuthHeaders: Boolean = true): CommonHeader {
        return CommonHeader().apply {
            addHeader("device_id", commonParams.deviceId)
            if (commonParams.oaId.isNotEmpty()) {
                addHeader("oaid", commonParams.oaId)
            }
            addHeader("app_version_code", commonParams.appVersionCode.toString())
            addHeader("platform", "a")
            addHeader("self_package_name", commonParams.selfPackageName)
            addHeader("language_code", "${commonParams.language}_${commonParams.region}")
            addHeader("metaverse_version", commonParams.metaverseVersion)
            addHeader("metaverse_engine_version", commonParams.metaverseEngineVersion)
            addHeader("iosAndroid", "a")
            if (shouldAddAuthHeaders) {
                commonParams.accessToken?.let { addHeader(API_HEADER_TOKEN, it) }
            }
            addHeader("lang", commonParams.languageCode)

            HeaderHelper.addExtraCommonHeaders(this, commonParams, shouldAddAuthHeaders)
        }
    }
}

class CommonHeader {

    private val header = mutableMapOf<String, String>()
    fun addHeader(key: String, value: String) {
        header[key] = value
    }

    fun getParams(): Map<String, String> = header

    fun apply(builder: Request.Builder) {
        header.onEach {
            builder.addHeader(it.key, it.value)
        }
    }
}

private fun provideSEApiReqTimeInterceptor(): Interceptor {
    return object : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val start = SystemClock.elapsedRealtime()
            val response = chain.proceed(chain.request())
            val duration = SystemClock.elapsedRealtime() - start

            val pingInteractor = GlobalContext.get().get<PingInteractor>()
            pingInteractor.addApiReqTime(duration)

            return response
        }
    }
}

@Retention
@Target(allowedTargets = [AnnotationTarget.FUNCTION])
annotation class SpecificTimeout(val timeout: Int, val timeUnit: TimeUnit)
class RequestEncryptInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request: Request = chain.request()
        val method = request.method.lowercase().trim()
        val edVersion = request.header("Ed-Version")
        if (edVersion == RequestEncryptionUtil.TYPE_DD|| edVersion == RequestEncryptionUtil.TYPE_AD) {
            if (method == "post" || method == "get") {
                // post与get都只加密body
                val requestBody = request.body
                if (requestBody != null) {
                    val contentType = requestBody.contentType()
                    val buffer = Buffer()
                    requestBody.writeTo(buffer)
                    val charset: Charset =
                        contentType?.charset(StandardCharsets.UTF_8) ?: StandardCharsets.UTF_8
                    val requestData = buffer.readString(charset)
                    val encryptData = try {
                        RequestEncryptionUtil.encryptBody(requestData, edVersion)
                    } catch (e: Exception) {
                        throw ApiInterceptorIOException("before-process failed due to ${e.message}", e)
                    }
                    val newRequestBody = encryptData.toRequestBody(contentType)
                    val newRequest = request.newBuilder().post(newRequestBody).build()
                    return chain.proceed(newRequest)
                }
            }
        }
        if (edVersion == RequestEncryptionUtil.TYPE_AO || edVersion == RequestEncryptionUtil.TYPE_DO) {
            if (method == "post" || method == "get") {
                // post与get都只加密body
                val requestBody = request.body
                if (requestBody != null) {
                    val contentType = requestBody.contentType()
                    val buffer = Buffer()
                    requestBody.writeTo(buffer)
                    val charset: Charset =
                        contentType?.charset(StandardCharsets.UTF_8) ?: StandardCharsets.UTF_8
                    val requestData = buffer.readString(charset)
                    val encryptData = try {
                        RequestEncryptionUtil.encryptBody(requestData, edVersion)
                    } catch (e: Exception) {
                        throw ApiInterceptorIOException("before-process failed due to ${e.message}", e)
                    }
                    val newRequestBody = encryptData.toRequestBody(contentType)
                    val newRequest = request.newBuilder().post(newRequestBody).build()
                    return chain.proceed(newRequest)
                }
            }
        }
        return chain.proceed(request)
    }
}
class ResponseDecryptInterceptor : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val request: Request = chain.request()
        var response = chain.proceed(request)
        val method = request.method.lowercase().trim()
        val edVersion = request.header("Ed-Version")
        if (response.header("Content-Type") == "text/plain" && (edVersion == RequestEncryptionUtil.TYPE_AD || edVersion == RequestEncryptionUtil.TYPE_DD)) {
            if (method == "post" || method == "get") {
                // 先只处理post
                val responseBody = response.body
                if (responseBody != null) {
                    val source = responseBody.source()
                    source.request(Long.MAX_VALUE) // Buffer the entire body.
                    val buffer = source.buffer
                    val charset: Charset =
                        responseBody.contentType()?.charset(StandardCharsets.UTF_8)
                            ?: StandardCharsets.UTF_8
                    val bodyString = buffer.clone().readString(charset)
                    // 解密后的body
                    val decryptData = try {
                        RequestEncryptionUtil.decryptBody(bodyString, edVersion)
                    } catch (e: Exception) {
                        throw ApiInterceptorIOException("after-process failed due to ${e.message}", e)
                    }
                    val newResponseBody =
                        decryptData.toResponseBody("application/json".toMediaType())
                    response = response.newBuilder().body(newResponseBody).build()
                }
            }
        }
        if (response.header("Content-Type") == "text/plain" && (edVersion == RequestEncryptionUtil.TYPE_AO || edVersion == RequestEncryptionUtil.TYPE_DO)) {
            if (method == "post" || method == "get") {
                // 先只处理post
                val responseBody = response.body
                if (responseBody != null) {
                    val source = responseBody.source()
                    source.request(Long.MAX_VALUE) // Buffer the entire body.
                    val buffer = source.buffer
                    val charset: Charset =
                        responseBody.contentType()?.charset(StandardCharsets.UTF_8)
                            ?: StandardCharsets.UTF_8
                    val bodyString = buffer.clone().readString(charset)
                    // 解密后的body
                    val decryptData = try {
                        RequestEncryptionUtil.decryptBody(bodyString, edVersion)
                    } catch (e: Exception) {
                        throw ApiInterceptorIOException("after-process failed due to ${e.message}", e)
                    }
                    val newResponseBody =
                        decryptData.toResponseBody("application/json".toMediaType())
                    response = response.newBuilder().body(newResponseBody).build()
                }
            }
        }
        return response
    }
}

class CommonParamsProvider(
    val metaKV: MetaKV,
    val device: DeviceInteractor,
    val context: Context
) {
    val accessToken: String?                  // 业务accessToken
        get() = metaKV.account.accessToken

    val refreshToken: String?                  // 业务refreshToken
        get() = metaKV.account.refreshToken

    val uid: String
        get() = metaKV.account.uuid
    val deviceId: String                  // 设备唯一标识
        get() = device.deviceId
    val androidId: String                  // AndroidId
        get() = device.androidId

    val oaId: String //安卓10开始的设备唯一标识，用户自己可变
        get() = device.oaid

    val channelId: String
        get() {
            return device.channelId
        } // 用户渠道
    val apkChannelId: String
        get() {
            return device.apkChannelId
        } //apk的渠道

    val metaverseVersion: String
        get() = MWBiz.version()
    val metaverseEngineVersion: String
        get() = MWBiz.engineVersion()
    val selfPackageName = BuildConfig.APPLICATION_ID  //自己Apk包名
    val appVersionCode = BuildConfig.VERSION_CODE
    val appVersionName = BuildConfig.VERSION_NAME
    val systemVersion = Build.VERSION.RELEASE ?: "unknown" //安卓系统版本
    val systemVersionCode = Build.VERSION.SDK_INT //安卓系统版本API
    val deviceBrand = Build.BRAND ?: "unknown"//系统品牌
    val deviceName = "${deviceBrand}_${Build.MODEL}" //设备名，品牌+机型
    val deviceTime = Build.TIME
    val iosAndroid = "a"    // 标记IOS还是安卓 i：IOS、a：安卓、w：web
    val platform = "a"    // 标记IOS还是安卓 i：IOS、a：安卓、w：web
    val language = Locale.getDefault().language ?: "unknown"
    val region = Locale.getDefault().country ?: "unknown"

    val isTablet get() = device.isTablet


    val timezone by lazy {
        kotlin.runCatching {
            val calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT"), Locale.getDefault())
            val currentLocalTime = calendar.time
            val date: DateFormat = SimpleDateFormat("z", Locale.getDefault())
            date.format(currentLocalTime)
        }.getOrDefault("unknown")
    }

    val networkType: String //网络类型 wifi 4g 3g 等等，待更新
        get() = NetUtil.getNetTypeSafe()
    val pandora_ab_group: Set<String> //pandora首页进组实验ID
        get() = Pandora.getAllAbGroupVidSet()
    val pandora_switch_ab_group: Set<String>  //pandora功能进组实验ID
        get() = Pandora.getFeatureInGroupVidSet()
    val pandora_switch_new_ab_group: Set<String>  //pandora功能新进组实验ID
        get() = Pandora.getFeatureTodayIntoGroupVidSet()
    val pandora_new_ab_group: Set<String>  //pandora首页新进组实验ID
        get() = Pandora.getAllTodayIntoGroupVidSet()

    val deviceModel: String
        get() = DeviceUtil.getPhoneModel() ?: ""

    val installationId: String                  // installationId
        get() = device.installationId

    // 客户端当前使用的语言code
    val languageCode: String
        get() = MetaLanguages.getAppLanguageCode(context).toString()
    //国家代码
    val simCountryCode :String
        get() {
            return kotlin.runCatching { (context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager).networkCountryIso }
                .getOrDefault("")
        }
}

