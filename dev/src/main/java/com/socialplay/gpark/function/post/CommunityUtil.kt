package com.socialplay.gpark.function.post

import android.content.Context
import androidx.fragment.app.Fragment
import com.meta.box.biz.friend.internal.model.FriendStatus
import com.meta.box.biz.friend.internal.model.toLocalStatus
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.FriendInteractor
import com.socialplay.gpark.data.model.choice.ChoiceFriendInfo
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.TopicBean
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.editorschoice.header.friends.HomeFriendsActionDialog
import com.socialplay.gpark.ui.post.feed.CommunityFeedFragment
import com.socialplay.gpark.ui.post.feed.tag.CommunityTagSpannable
import com.socialplay.gpark.util.SpannableHelper
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/10/11
 *     desc   :
 * </pre>
 */
object CommunityUtil {

    // 用户达到300人，关注报错
    const val FOLLOW_REACH_LIMIT_CODE = 3307027

    /**
     * 解析帖子正文
     * @param content 正文
     * @param tagList 完整的话题信息列表
     * @param topicIndexList 话题富文本信息
     * @param fromLocal 来自本地发布，此时tagList为空
     * @param tagClickCallback 话题点击回调
     */
    fun parsePostContent(
        context: Context,
        content: String?,
        tagList: List<PostTag>?,
        topicIndexList: List<TopicBean>?,
        tagClickCallback: (PostTag) -> Unit
    ): CharSequence? {
        if (content.isNullOrEmpty()) return content
        val builder = SpannableHelper.Builder().text(content)
        if (!tagList.isNullOrEmpty()) {
            // 处理话题
            val tag = context.getString(R.string.community_hashtag)
            topicIndexList?.forEachIndexed { index, topicBean ->
                kotlin.runCatching {
                    topicBean.tagName.takeIf { !it.isNullOrEmpty() }?.let { tagName ->
                        tagList.find {
                            if (topicBean.tagId != null) {
                                it.tagId == topicBean.tagId
                            } else {
                                it.tagName == tagName
                            }
                        }?.let {
                            builder.click(
                                CommunityTagSpannable(context, it, tagClickCallback),
                                topicBean.index,
                                topicBean.index + topicBean.tagName.length + tag.length
                            )
                        }
                    }
                }.getOrElse {
                    // 防止可能数组越界崩溃
                    Timber.tag(CommunityFeedFragment.POST_TAG).e("parsePostContent failed! content:${content} topic:${topicBean} throwable:${it}")
                }
            }
        }
        return builder.build()
    }

    fun goGameDetail(fragment: Fragment, card: PostCardInfo, categoryId: Int) {
        when (card.resourceType) {
            PostCardInfo.TYPE_PGC -> {
                MetaRouter.GameDetail.navigate(
                    fragment,
                    card.gameId,
                    ResIdBean().setCategoryID(categoryId).setGameId(card.gameId),
                    card.packageName,
                    card.gameName
                )
            }

            PostCardInfo.TYPE_UGC -> {
                MetaRouter.MobileEditor.ugcDetail(
                    fragment,
                    card.gameId,
                    null,
                    ResIdBean().setCategoryID(categoryId).setGameId(card.gameId)
                )
            }
        }
    }

    fun showRoomDialog(
        fragment: Fragment,
        userStatus: FriendStatus,
        uid: String,
        username: String?,
        categoryId: Int
    ) {
        val localStatus = userStatus.toLocalStatus()
        HomeFriendsActionDialog.show(
            fragment,
            ChoiceFriendInfo(
                type = ChoiceFriendInfo.TYPE_USER,
                userId = uid,
                userName = username.orEmpty(),
                userOnline = localStatus == FriendStatus.ONLINE,
                userInGame = localStatus == FriendStatus.PLAYING_GAME,
                roomId = userStatus.gameStatus?.room?.roomIdFromCp ?: "",
                isUgc = userStatus.gameStatus?.isUgc == true,
                gameName = userStatus.gameStatus?.gameName ?: "",
                gameId = userStatus.gameStatus?.gameId ?: "",
                gamePackageName = userStatus.gameStatus?.packageName ?: "",
                gameAvatar = userStatus.gameStatus?.gameIcon ?: "",
                showHint = false,
                showChat = GlobalContext.get().get<FriendInteractor>().checkIsFriend(uid)
            ),
            ResIdBean.newInstance().setCategoryID(categoryId)
        )
    }
}