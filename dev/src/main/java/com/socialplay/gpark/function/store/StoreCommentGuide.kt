package com.socialplay.gpark.function.store

import android.content.Context
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.AppCommonKV
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.local.AppDatabase
import com.socialplay.gpark.data.model.guide.StoreCommentGuideConfig
import com.socialplay.gpark.data.model.guide.StoreCommentGuideConfigData
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.dialog.DialogScene
import com.socialplay.gpark.ui.dialog.IDialogManager
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.ui.store.StoreCommentGuideInnerDialog
import com.socialplay.gpark.util.ProcessUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * 引导用户商店评分弹框的逻辑
 * 一个是内部弹框，让用户选择喜欢还是不喜欢
 * 如果用户选择喜欢，则调起商店的弹框
 * 如果用户选择不喜欢，则过一段时间再弹框内部的弹框
 */
object StoreCommentGuide : IDialogManager {
    private const val SCENE_MAIN = "main"
    private const val SCENE_GAME_SUCCESS = "game"
    private const val SCENE_LIKE = "like"

    private var configInitialized: Boolean = false
    private var config: StoreCommentGuideConfig? = null

    private const val INNER_DIALOG: Int = 1
    private const val STORE_DIALOG: Int = 2
    private var showDialogType: Int = INNER_DIALOG
    private var dialogConfigId: String = ""

    private var currentGameDuration: Long = 0

    private var metaKv: MetaKV? = null
    private var appCommonKv: AppCommonKV? = null

    /**
     * 检查是否满足条件
     */
    suspend fun checkDialogNeedShow(scene: String, needShowCallback: (Boolean) -> Unit) {
        if (config == null) {
            needShowCallback(false)
            return
        }
        if (metaKv?.appKV?.isShowedStoreCommentDialog == true) {
            // 已经展示过商店弹框了，就判断距离上一次商店弹框过去了多久，是否可以再次展示商店弹框了
            if (!checkIntervalTimeHours(metaKv?.appKV?.lastTimeShowStoreCommentDialogTime, config?.innerIntervalTime)) {
                // 还在间隔时间内，不用往下走了，直接返回false
                needShowCallback(false)
                return
            }

            // 如果过了间隔时间，则清空该值，以便于二次展示判断
            metaKv?.appKV?.isShowedStoreCommentDialog = false
            metaKv?.appKV?.lastTimeShowStoreCommentDialogTime = 0
        }

        // 先确定，是否是首次内部弹框已经结束了，要展示真正的商店弹框了
        if (metaKv?.appKV?.isShowedCommentGuideInnerDialog == true && config!!.keepInnerDialog == false) {
            // 展示过内部弹框了（展示过商店弹框后会清空该值，以便于二次展示判断）
            if (metaKv?.appKV?.isDislikeCommentGuideInnerDialog == true) {
                // 如果是用户点击了拒绝的，即使展示过了，也不能展示商店弹框，还需要在拒绝间隔时间后再弹(点击喜欢后，会清空该值，以便于二次展示判断)
                if (!checkIntervalTimeHours(metaKv?.appKV?.lastTimeShowCommentGuideInnerDialogTime, config?.disLikeIntervalTime)) {
                    // 还没过间隔时间，不继续展示
                    needShowCallback(false)
                    return
                }
                // 过时后，拒绝状态恢复
                metaKv?.appKV?.isShowedCommentGuideInnerDialog = false
            } else {
                // 用户点击了内部弹框的同意，要判断是否达到展示商店弹框的时间间隔
                if (!checkIntervalTimeHours(metaKv?.appKV?.lastTimeShowCommentGuideInnerDialogTime, config!!.storeDialogIntervalTime)) {
                    needShowCallback(false)
                    return
                }
            }
        }

        var isShowInnerDialog = false
        var dialogConfigId: String? = null
        var isShowStoreDialog = false
        if (config!!.keepInnerDialog == false && metaKv?.appKV?.isShowedCommentGuideInnerDialog == false) {
            // 不跳过内部弹框，做条件判断
            config!!.innerDialogConfig?.forEach {
                if (it == null) {
                    return@forEach
                }
                val configId = it.configId
                val config = it.configData
                if (checkOtherConditions(config, scene)) {
                    isShowInnerDialog = true
                    dialogConfigId = configId
                }
            }
        }

        if (config!!.keepInnerDialog == true || metaKv?.appKV?.isShowedCommentGuideInnerDialog == true) {
            // 跳过内部弹框，直接上商店弹框 或者是内部弹框已经展示过了
            config!!.storeDialogConfig?.forEach {
                if (it == null) {
                    return@forEach
                }
                val configId = it.configId
                val config = it.configData

                if (checkOtherConditions(config, scene)) {
                    isShowStoreDialog = true
                    dialogConfigId = configId
                }
            }
        }

        showDialogType = if (isShowInnerDialog) {
            INNER_DIALOG
        } else if (isShowStoreDialog) {
            STORE_DIALOG
        } else {
            0
        }
        this.dialogConfigId = dialogConfigId ?: ""
        if (showDialogType == 0) {
            // 都不满足条件，不展示
            needShowCallback(false)
        } else {
            needShowCallback(true)
        }
    }

    private fun showStoreDialog(fragment: Fragment, configId: String, onDismissCallback: (Boolean) -> Unit) {
        StoreInAppReview().showInAppReview(fragment) { isSuccess ->
            if (isSuccess) {
                Analytics.track(EventConstants.EVENT_STORE_EVALUATE_SHOW, mapOf("source" to configId, "result" to "1"))
                // 如果展示成功，才记录展示过了，否则不记录
                metaKv?.appKV?.isShowedStoreCommentDialog = true
                metaKv?.appKV?.lastTimeShowStoreCommentDialogTime = System.currentTimeMillis()
                metaKv?.appKV?.isShowedCommentGuideInnerDialog = false
                metaKv?.appKV?.lastTimeShowCommentGuideInnerDialogTime = 0
            } else {
                Analytics.track(EventConstants.EVENT_STORE_EVALUATE_SHOW, mapOf("source" to configId, "result" to "0"))
            }
            onDismissCallback.invoke(true)
        }
    }

    private fun showInnerCommentGuideDialog(fragment: Fragment, configId: String, onDismissCallback: (Boolean) -> Unit) {
        metaKv?.appKV?.isShowedCommentGuideInnerDialog = true
        metaKv?.appKV?.lastTimeShowCommentGuideInnerDialogTime = System.currentTimeMillis()

        StoreCommentGuideInnerDialog().apply {
            strategyId = configId
            this.onDismissCallback = onDismissCallback
        }.show(fragment.childFragmentManager, "StoreCommentGuideInnerDialog")
    }

    suspend fun checkOtherConditions(commonConfig: StoreCommentGuideConfigData?, scene: String? = null): Boolean {
        if (commonConfig == null) {
            return false
        }

        if (commonConfig.triggerScene != scene) {
            // 展示场景不对，不继续进行
            return false
        }

        if (!checkGameTotalTime(commonConfig.totalGameTime)) {
            // 游戏总时间未达标，不继续进行
            return false
        }

        if (!checkGameOnceTime(commonConfig.onceGameTime)) {
            // 游戏单次时间未达标，不继续进行
            return false
        }

        if (!checkGameAppStartCount(commonConfig.appStartCount)) {
            // App启动次数未达标，不继续进行
            return false
        }

        if (!checkGameAppStartDayCount(commonConfig.appStartDayCount)) {
            // App启动天数未达标，不继续进行
            return false
        }

        if (!checkGameLikeCount(commonConfig.likeCount)) {
            // 点赞次数未达标，不继续进行
            return false
        }

        return true
    }

    /**
     * 检查游戏总时间是否达标
     * @param configTotalGameTime 远程配置的 达标时间 单位是分钟
     * @return true: 达标，false: 不达标
     */
    suspend fun checkGameTotalTime(configTotalGameTime: Int?): Boolean {
        if (configTotalGameTime == null) {
            return true
        }
        // 本地存储的 allDuration 是毫秒，需要转换成分钟再进行比较
        val allDuration = GlobalContext.get().get<AppDatabase>().playGameTimeDao.queryLast30DaysDuration()
        Timber.d("StoreCommentGuide checkGameTotalTime: allDuration=$allDuration, configTotalGameTime=$configTotalGameTime")
        return allDuration >= (configTotalGameTime * 60 * 1000L)
    }

    /**
     * 检查游戏单次游玩时间是否达标
     * @param configOnceGameTime 远程配置的 达标时间 单位是分钟
     * @return true: 达标，false: 不达标
     */
    suspend fun checkGameOnceTime(configOnceGameTime: Int?): Boolean {
        if (configOnceGameTime == null) {
            return true
        }
        val gameOnceTime = GlobalContext.get().get<AppDatabase>().playGameTimeDao.getLatestByRecordTime()?.duration
        Timber.d("StoreCommentGuide checkGameOnceTime: gameOnceTime=$gameOnceTime, configOnceGameTime=$configOnceGameTime")
        return (gameOnceTime ?: 0) >= (configOnceGameTime * 60 * 1000L)
    }

    /**
     * 检查App启动次数是否达标
     * @param configAppStartCount 远程配置的 达标次数
     * @return true: 达标，false: 不达标
     */
    fun checkGameAppStartCount(configAppStartCount: Int?): Boolean {
        if (configAppStartCount == null) {
            return true
        }
        val appStartCount = metaKv?.appKV?.appOpenTimes ?: 0
        return appStartCount >= configAppStartCount
    }

    /**
     * 检查App启动天数是否达标
     * @param configAppStartDayCount 远程配置的 达标天数
     * @return true: 达标，false: 不达标
     */
    fun checkGameAppStartDayCount(configAppStartDayCount: Int?): Boolean {
        if (configAppStartDayCount == null) {
            return true
        }
        val appStartDayCount = metaKv?.appKV?.appUseDays ?: 0
        return appStartDayCount >= configAppStartDayCount
    }

    /**
     * 检查点赞次数是否达标
     * @param configLikeCount 远程配置的 达标次数
     * @return true: 达标，false: 不达标
     */
    fun checkGameLikeCount(configLikeCount: Int?): Boolean {
        if (configLikeCount == null) {
            return true
        }
        val likeCount = metaKv?.appKV?.commentGuideLikeCount ?: 0
        return likeCount >= configLikeCount
    }

    /**
     * 检查当前时间是否已经过了指定的小时数
     */
    fun checkIntervalTimeHours(startTime: Long?, intervalTime: Int?): Boolean {
        val firstIntervalDuration = System.currentTimeMillis() - (startTime ?: 0)
        val formatIntervalConfigTime = (intervalTime ?: 0) * (60 * 60 * 1000L)
        return firstIntervalDuration > formatIntervalConfigTime
    }

    fun triggerMain(fragment: Fragment, needShowCallback: (Boolean) -> Unit = {}) {
        fragment.lifecycleScope.launch(Dispatchers.IO) {
            checkDialogNeedShow(SCENE_MAIN, needShowCallback)
        }
    }

    fun triggerGameSuccess(needShowCallback: (Boolean) -> Unit = {}) {
        Timber.d("StoreCommentGuide triggerGameSuccess: currentGameDuration: $currentGameDuration process: ${ProcessUtil.isMainProcess(GlobalContext.get().get<Context>())} config: ${config}")
        val curActivity = LifecycleInteractor.activityRef?.get() ?: return
        val navHostFragment = if (curActivity is MainActivity) {
            curActivity.findNavHostFragment()
        } else {
            null
        }
        navHostFragment?.lifecycleScope?.launch(Dispatchers.IO) {
            checkDialogNeedShow(SCENE_GAME_SUCCESS, needShowCallback)
        }

    }

    fun triggerLike(fragment: Fragment, needShowCallback: (Boolean) -> Unit = {}) {
        metaKv?.appKV?.commentGuideLikeCount++
        fragment.lifecycleScope.launch(Dispatchers.IO) {
            checkDialogNeedShow(SCENE_LIKE, needShowCallback)
        }
    }

    override suspend fun initData(finishCallback: (Boolean) -> Unit) {
        if (configInitialized) {
            // 已经初始化过了，就不重复初始化了
            finishCallback.invoke(true)
            return
        }
        metaKv = GlobalContext.get().get<MetaKV>()
        appCommonKv = metaKv?.appKV
        GlobalContext.get().get<TTaiInteractor>().getTTaiWithTypeV3<StoreCommentGuideConfig>(TTaiKV.ID_GUIDE_COMMENT_DIALOG_ID).collect {
            if (it == null) {
                finishCallback.invoke(true)
                return@collect
            }
            configInitialized = true
            val guideStoreCommentConfigId = PandoraToggle.guideStoreCommentDialog
            if (guideStoreCommentConfigId == false) {
                // Pandora没开开关
                Timber.d("guideStoreCommentDialog false")
                finishCallback.invoke(true)
                return@collect
            }
            config = it
            finishCallback.invoke(true)
        }
    }

    override fun needShow(fragment: Fragment, scene: DialogScene, args: Bundle?, needShowCallback: (Boolean) -> Unit) {
        if (scene == DialogScene.MAIN_PAGE) {
            triggerMain(fragment, needShowCallback)
        }
        if (scene == DialogScene.GAME_SUCCESS) {
            triggerGameSuccess(needShowCallback)
        }
        if (scene == DialogScene.LIKE_OPT) {
            triggerLike(fragment, needShowCallback)
        }
    }

    override fun showByDialogManager(fragment: Fragment, onDismissCallback: (Boolean) -> Unit) {
        if (showDialogType == INNER_DIALOG) {
            showInnerCommentGuideDialog(fragment, dialogConfigId, onDismissCallback)
        } else if (showDialogType == STORE_DIALOG) {
            showStoreDialog(fragment, dialogConfigId, onDismissCallback)
        }
    }

    override fun exeDismiss() {

    }
}