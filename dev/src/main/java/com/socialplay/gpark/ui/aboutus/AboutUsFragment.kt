package com.socialplay.gpark.ui.aboutus

import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.app.initialize.LibBuildConfigInit
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.databinding.FragmentAboutUsBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject

/**
 * author : wei.zhu
 * e-mail : <EMAIL>
 * time   : 2021/06/06
 * desc   :
 */
class AboutUsFragment : BaseFragment<FragmentAboutUsBinding>() {

    private val metaKV: MetaKV by inject()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAboutUsBinding? {
        return FragmentAboutUsBinding.inflate(inflater, container, false)
    }

    override fun init() {
        binding.tvAboutUs.text = String.format(getString(R.string.about_desc), getString(R.string.app_name))

        binding.tvVersion.text = String.format(getString(R.string.about_version), BuildConfig.VERSION_NAME)

        binding.tvVersion.setOnLongClickListener {
            MetaRouter.Developer.appParams(this)
            true
        }

        binding.toolbar.setOnBackAntiViolenceClickedListener {
            findNavController().popBackStack()
        }
        /*进入开发者模式*/
        registerDeveloperToggle()
    }

    override fun loadFirstData() {}

    override fun getFragmentName(): String {
        return PageNameConstants.FRAGMENT_NAME_ABOUT_US
    }

    /**
     * 开发者模式入口,分为两步
     * 1.点击关于我们icon 8次
     * 2.如果是Release包，需要输入密码：869233
     * 注意：点击超过8次，需要等5秒之后重试，或退出页面重来
     */
    private fun registerDeveloperToggle() {
        if (LibBuildConfigInit.buildCfg.isAlreadyOpenDev()) {
            binding.tvDevModel.visible(true)
            binding.tvDevModel.setOnClickListener {
                MetaRouter.Developer.developer(this@AboutUsFragment)
            }
        } else {
            binding.tvDevModel.visible(false)
        }
        binding.logo.setOnClickListener {
            longClickCount++
            when {
                longClickCount == 8 -> {
                    handler.postDelayed(openAnimation, 500)
                }

                longClickCount > 8 -> {
                    handler.removeCallbacksAndMessages(null)
                    handler.postDelayed({ longClickCount = 0 }, 5_000)
                }
            }
        }
    }

    private val handler by lazy { Handler() }
    private var longClickCount = 0

    private val openAnimation = {
        binding.developerOpen.animate().rotation(135F).setDuration(800).start()
        binding.logo.animate().alpha(0.3F).setDuration(500).withEndAction {
            longClickCount = 0
            if (isBindingAvailable()) {
                viewLifecycleOwner.lifecycleScope.launch {
                    delay(1000)
                    openDeveloper()
                }
            }
        }.start()
    }

    private val openDeveloper = {
        if (isBindingAvailable()) {
            LibBuildConfigInit.buildCfg.setDeveloperOpen(true)
            MetaRouter.Developer.developer(this@AboutUsFragment)
            binding.logo.alpha = 1F
        }
    }

    override fun onDestroyView() {
        handler.removeCallbacksAndMessages(null)
        super.onDestroyView()
    }
}