package com.socialplay.gpark.ui.core

import androidx.annotation.LayoutRes
import androidx.viewbinding.ViewBinding

/**
 * <pre>
 * author : zhiwei.xu
 * e-mail : <EMAIL>
 * time   : 2022/11/04
 * desc   :
 * </pre>
 */


abstract class BaseLazyFragment<VB : ViewBinding>(@LayoutRes contentLayoutId: Int) : BaseFragment<VB>(contentLayoutId) {

    protected var isFirstVisible = true // 是否第一次可见

    override fun onResume() {
        super.onResume()
        if (isFirstVisible) {
            loadLazyData()
            isFirstVisible = false
        }
    }

    abstract fun loadLazyData()

    override fun onDestroy() {
        super.onDestroy()
        isFirstVisible = true
    }
}


abstract class BaseCoreLazyFragment<VB : ViewBinding>(@LayoutRes contentLayoutId: Int) : BaseFragment<VB>(contentLayoutId) {

    private var isFirstVisible = true // 是否第一次可见

    override fun onResume() {
        super.onResume()
        if (isFirstVisible) {
            loadLazyData()
            isFirstVisible = false
        }
    }

    abstract fun loadLazyData()

    override fun onDestroy() {
        super.onDestroy()
        isFirstVisible = true
    }
}

abstract class BaseCoreLazyListFragment<VB : ViewBinding>(@LayoutRes contentLayoutId: Int) : BaseRecyclerViewFragment<VB>(contentLayoutId) {

    protected var isFirstVisible = true // 是否第一次可见

    override fun onResume() {
        super.onResume()
        if (isFirstVisible) {
            loadLazyData()
            isFirstVisible = false
        }
    }

    abstract fun loadLazyData()

    override fun onDestroy() {
        super.onDestroy()
        isFirstVisible = true
    }
}