package com.socialplay.gpark.ui.core.maverick

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized


fun <T> Success<List<T>>.append(data: Success<List<T>>): Success<List<T>> {
    return Success(ArrayList(invoke()).also { it.addAll(data.invoke()) })
}

operator fun <T> Success<List<T>>.plus(data: Success<List<T>>): Success<List<T>> {
    return append(data)
}

operator fun <T> Async<List<T>>.plus(data: Async<List<T>>): Async<List<T>> {
    return when (this) {
        is Fail -> copy(value = invoke()?.let { origin ->
            ArrayList(origin).also { data.invoke()?.let { append -> it.addAll(append) } }
        })

        is Loading -> copy(value = invoke()?.let { origin ->
            ArrayList(origin).also { data.invoke()?.let { append -> it.addAll(append) } }
        })

        is Success -> copy(
            ArrayList(invoke()).also { data.invoke()?.let { append -> it.addAll(append) } }
        )

        Uninitialized -> this
    }
}

operator fun <T> List<T>.plus(data: List<T>): List<T> {
    return ArrayList(this).also { data.let { append -> it.addAll(append) } }
}

inline fun <T, R> Async<T>.map(crossinline transform: (T) -> R): Async<R> {
    return when (this) {
        is Fail -> invoke().let {
            it ?: return Fail(error)
            Fail(error, transform.invoke(it))
        }

        is Loading -> invoke().let {
            it ?: return Loading()
            Loading(transform.invoke(it))
        }

        is Success -> Success(transform(invoke()))
        Uninitialized -> Uninitialized
    }
}

fun <T, R> Async<T>.mapRetained(prop: Async<R>): Async<R> {
    return when (this) {
        is Fail -> Fail(error, prop())

        is Loading -> Loading(prop())

        Uninitialized -> Uninitialized

        is Success -> throw IllegalStateException("You should not retain when succeeded")
    }
}

fun <T> Async<T>.copyEx(value: T?): Async<T> {
    return when (this) {
        is Fail -> copy(value = value)
        is Loading -> copy(value)
        is Success -> if (value == null) this else copy(value)
        Uninitialized -> this
    }
}

fun <T> Async<T>.default(value: T?): Async<T> {
    return when (this) {
        is Fail -> if (invoke() == null) copy(value = value) else this
        is Loading -> if (invoke() == null) copy(value) else this
        else -> this
    }
}

inline fun <T, R> Async<List<T>>.mapItem(crossinline transform: (T) -> R): Async<List<R>> {
    return when (this) {
        is Fail -> invoke().let { list->
            list ?: return Fail(error)
            Fail(error, list.map { transform.invoke(it) })
        }

        is Loading -> invoke().let { list->
            list ?: return Loading()
            Loading(list.map { transform.invoke(it) })
        }

        is Success -> Success(invoke().map { transform.invoke(it) })
        Uninitialized -> Uninitialized
    }
}

inline fun <T> Async<List<T>>.whenSuccessEmpty(crossinline transform: () -> Async<List<T>>): Async<List<T>> {
    return when (this) {
        is Fail -> invoke().let { list ->
            list ?: return Fail(error)
            Fail(error, list)
        }

        is Loading -> invoke().let { list ->
            list ?: return Loading()
            Loading(list)
        }

        is Success -> invoke().let { list ->
            if (list.isEmpty()) {
                transform()
            } else {
                Success(list)
            }
        }

        Uninitialized -> Uninitialized
    }
}