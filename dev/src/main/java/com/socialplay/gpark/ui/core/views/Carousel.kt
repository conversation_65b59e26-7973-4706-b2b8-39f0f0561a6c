package com.socialplay.gpark.ui.core.views

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.CarouselModelBuilder
import com.airbnb.epoxy.CarouselModel_
import com.airbnb.epoxy.EpoxyModel
import kotlin.math.abs


fun MetaModelCollector.carouselBuilder(
    builder: EpoxyCarouselBuilder,
    build: EpoxyCarouselBuilder.() -> Unit
): CarouselModel_ {
    val carouselModelBuilder = builder.apply { build() }
    add(carouselModelBuilder.carouselModel)
    return carouselModelBuilder.carouselModel
}

fun MetaModelCollector.carouselBuilder(builder: EpoxyCarouselBuilder.() -> Unit): CarouselModel_ {
    val carouselModelBuilder = EpoxyCarouselBuilder().apply { builder() }
    add(carouselModelBuilder.carouselModel)
    return carouselModelBuilder.carouselModel
}

class EpoxyCarouselBuilder(
    internal val carouselModel: CarouselModel_ = CarouselModel_()
) : MetaModelCollector, CarouselModelBuilder by carouselModel {
    private val models = mutableListOf<EpoxyModel<*>>()
    override var buildItemIndex: Int = 0

    override fun add(model: EpoxyModel<*>) {
        models.add(model)
        buildItemIndex++
        // Set models list every time a model is added so that it can run debug validations to
        // ensure it is still valid to mutate the carousel model.
        carouselModel.models(models)
    }


}

fun MetaModelCollector.carouselNoSnapBuilder(
    builder: EpoxyCarouselNoSnapBuilder,
    build: EpoxyCarouselNoSnapBuilder.() -> Unit
): CarouselModel_ {
    val carouselModelBuilder = builder.apply { build() }
    add(carouselModelBuilder.carouselModel)
    return carouselModelBuilder.carouselModel
}

fun MetaModelCollector.carouselNoSnapBuilder(builder: EpoxyCarouselNoSnapBuilder.() -> Unit): CarouselNoSnapModel {
    val carouselModelBuilder = EpoxyCarouselNoSnapBuilder().apply { builder() }
    add(carouselModelBuilder.carouselModel)
    return carouselModelBuilder.carouselModel
}


class EpoxyCarouselNoSnapBuilder(
    internal val carouselModel: CarouselNoSnapModel = CarouselNoSnapModel()
) : MetaModelCollector, CarouselModelBuilder by carouselModel {
    private val models = mutableListOf<EpoxyModel<*>>()
    override var buildItemIndex: Int = 0

    override fun add(model: EpoxyModel<*>) {
        models.add(model)
        buildItemIndex++
        // Set models list every time a model is added so that it can run debug validations to
        // ensure it is still valid to mutate the carousel model.
        carouselModel.models(models)
    }

}

class CarouselNoSnapModel : CarouselModel_() {
    override fun buildView(parent: ViewGroup): CarouselNoSnap {
        val v = CarouselNoSnap(parent.context)
        v.layoutParams =
            MarginLayoutParams(MarginLayoutParams.MATCH_PARENT, MarginLayoutParams.WRAP_CONTENT)
        return v
    }
}

class CarouselNoSnap @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : Carousel(context, attrs, defStyle) {

    override fun getSnapHelperFactory(): SnapHelperFactory? {
        return null
    }
}

//region no snap wrap
fun MetaModelCollector.carouselNoSnapWrapBuilder(
    builder: EpoxyCarouselNoSnapWrapBuilder,
    build: EpoxyCarouselNoSnapWrapBuilder.() -> Unit
): CarouselModel_ {
    val carouselModelBuilder = builder.apply { build() }
    add(carouselModelBuilder.carouselModel)
    return carouselModelBuilder.carouselModel
}

fun MetaModelCollector.carouselNoSnapWrapBuilder(builder: EpoxyCarouselNoSnapWrapBuilder.() -> Unit): CarouselNoSnapWrapModel {
    val carouselModelBuilder = EpoxyCarouselNoSnapWrapBuilder().apply { builder() }
    add(carouselModelBuilder.carouselModel)
    return carouselModelBuilder.carouselModel
}


class EpoxyCarouselNoSnapWrapBuilder(
    internal val carouselModel: CarouselNoSnapWrapModel = CarouselNoSnapWrapModel()
) : MetaModelCollector, CarouselModelBuilder by carouselModel {
    private val models = mutableListOf<EpoxyModel<*>>()
    override var buildItemIndex: Int = 0

    override fun add(model: EpoxyModel<*>) {
        models.add(model)
        buildItemIndex++
        // Set models list every time a model is added so that it can run debug validations to
        // ensure it is still valid to mutate the carousel model.
        carouselModel.models(models)
    }

}

class CarouselNoSnapWrapModel(val skipIntercept: Boolean = false) : CarouselModel_() {
    override fun buildView(parent: ViewGroup): CarouselNoSnapWrap {
        val v = CarouselNoSnapWrap(parent.context)
        v.skipIntercept = skipIntercept
        v.layoutParams =
            MarginLayoutParams(MarginLayoutParams.MATCH_PARENT, MarginLayoutParams.WRAP_CONTENT)
        return v
    }
}

class CarouselNoSnapWrap @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : Carousel(context, attrs, defStyle) {

    private var initialX = 0f
    private var initialY = 0f
    private var handled = false
    private val scaledTouchSlop = ViewConfiguration.get(context).scaledTouchSlop
    var skipIntercept = false

    override fun getSnapHelperFactory(): SnapHelperFactory? {
        return null
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        (layoutManager as? LinearLayoutManager)?.let {
            it.isItemPrefetchEnabled = false
        }
        isNestedScrollingEnabled = false
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        if (skipIntercept) {
            return return super.dispatchTouchEvent(ev)
        }
        when (ev.action) {
            MotionEvent.ACTION_DOWN -> {
                handled = false
                initialX = ev.x
                initialY = ev.y
                parent?.requestDisallowInterceptTouchEvent(true)
            }

            MotionEvent.ACTION_MOVE -> {
                if (!handled) {
                    val endX = ev.x
                    val endY = ev.y

                    val dx = abs(endX - initialX)
                    val dy = abs(endY - initialY)

                    if (dx >= scaledTouchSlop || dy >= scaledTouchSlop) {
                        if (dx > dy) {
                            parent?.requestDisallowInterceptTouchEvent(true)
                        } else {
                            parent?.requestDisallowInterceptTouchEvent(false)
                        }
                        handled = true
                    }
                }
            }

            MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_UP -> {
                parent?.requestDisallowInterceptTouchEvent(false)
            }
        }

        return super.dispatchTouchEvent(ev)
    }
}
//endregion