package com.socialplay.gpark.ui.core.views

import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import androidx.annotation.GravityInt
import androidx.annotation.StringRes
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ViewControlEndLoadMoreVerticalBinding
import com.socialplay.gpark.databinding.ViewEmptyBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.extension.getString
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.unsetOnClick

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/10/18
 *     desc   :
 * </pre>
 */
fun MetaModelCollector.empty(
    @DrawableRes iconRes: Int = R.drawable.icon_no_recent_activity,
    @StringRes descRes: Int = R.string.footer_load_end,
    desc: String? = null,
    top: Int = 0,
    height: Int = ViewGroup.LayoutParams.WRAP_CONTENT,
    @GravityInt gravity: Int = Gravity.CENTER_HORIZONTAL,
    id: Long? = null,
    idStr: String? = null,
    spanSize: Int = 1,
    onClick: (() -> Unit)? = null
) {
    add {
        Empty(iconRes, descRes, desc, top, height, gravity, spanSize, onClick).apply {
            id?.let { id(id) } ?: idStr?.let { id(idStr) } ?: id("Empty-$it")
            spanSizeOverride { totalSpanCount, _, _ ->
                spanSize.coerceAtMost(totalSpanCount)
            }
        }
    }
}

data class Empty(
    val iconRes: Int,
    val descRes: Int,
    val desc: String?,
    val top: Int,
    val height: Int,
    val gravity: Int,
    val spanSize: Int,
    val onClick: (() -> Unit)?
) : ViewBindingItemModel<ViewEmptyBinding>(
    R.layout.view_empty,
    ViewEmptyBinding::bind
) {

    override fun ViewEmptyBinding.onBind() {
        root.setHeight(height)
        root.gravity = gravity
        loadingView.setMargin(top = top)
        if (desc.isNullOrBlank()) {
            loadingView.showEmpty(getString(descRes))
        } else {
            loadingView.showEmpty(desc)
        }
        if (onClick != null) {
            root.setOnAntiViolenceClickListener {
                onClick.invoke()
            }
        }
    }

    override fun ViewEmptyBinding.onUnbind() {
        if (onClick != null) {
            root.unsetOnClick()
        }
    }
}