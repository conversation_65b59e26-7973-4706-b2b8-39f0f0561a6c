package com.socialplay.gpark.ui.editor.create

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.meta.biz.ugc.model.UgcDraftInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.LoadStatus
import com.socialplay.gpark.data.model.LoadType
import com.socialplay.gpark.data.model.editor.EditorCreationShowInfo
import com.socialplay.gpark.databinding.AdapterEditorCreateV2MineEmptyBinding
import com.socialplay.gpark.databinding.FragmentEditorCreateV2MineBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.apm.apiStart
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.editor.BaseEditorFragment
import com.socialplay.gpark.ui.editor.legecy.RenameLocalDialog
import com.socialplay.gpark.ui.view.EmptyLoadMoreView
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.extension.addOnLayoutChangeListener
import com.socialplay.gpark.util.extension.apiMonitor
import com.socialplay.gpark.util.extension.clearFragmentResultAndListenerByActivity
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setFragmentResultListenerByActivity
import com.socialplay.gpark.util.extension.setOnAntiViolenceChildItemClickListener
import com.socialplay.gpark.util.extension.setOnAntiViolenceItemClickListener
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.sharedViewModelFromParentFragment
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/04/06
 *     desc   :
 * </pre>
 */
@Deprecated("replaced by EditorCreateMineParentFragment and EditorCreateV3MineFragment")
class EditorCreateV2MineFragment : BaseEditorFragment<FragmentEditorCreateV2MineBinding>() {

    private val viewModel by sharedViewModelFromParentFragment<EditorCreateViewModel>()

    private val adapter = EditorCreateV2MineAdapter(::glide)
    private var hasLoaded = false
    var job: Job? = null

    companion object {
        const val REQUEST_KEY_EDITOR_CREATION = "request_key_editor_creation"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.creationLiveData.apiMonitor(this) {
            it.first.status != LoadType.Fail
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentEditorCreateV2MineBinding? {
        return FragmentEditorCreateV2MineBinding.inflate(inflater, container, false)
    }

    override fun init() {
        super.init()
        initView()
        initData()
    }

    private fun initView() {
        binding.lv.showLoading(true, backgroundColor = Color.WHITE)
        binding.lv.setRetry {
            refresh()
        }
        binding.srl.setOnRefreshListener {
            refresh()
        }
        initAdapter()
        binding.ivTips.visible((PandoraToggle.isUgcBackup))
        binding.ivTips.setOnClickListener {
            if (binding.ivTipsArrow.isVisible) {
                visibleList(
                    binding.ivTipsArrow,
                    binding.tvTips,
                    visible = false
                )
                job?.cancel()
                job = null
            } else {
                visibleList(
                    binding.ivTipsArrow,
                    binding.tvTips,
                    visible = true
                )
                job?.cancel()
                job = viewLifecycleOwner.lifecycleScope.launch {
                    delay(3000)
                    visibleList(
                        binding.ivTipsArrow,
                        binding.tvTips,
                        visible = false
                    )
                    job = null
                }
            }
        }
        binding.tvTitle.setTextWithArgs(R.string.ugc_my_works, "")
        binding.ivTips.addOnLayoutChangeListener(viewLifecycleOwner) { v, l, t, r, b, ol, ot, or, ob ->
            binding.tvTips.updateLayoutParams<ConstraintLayout.LayoutParams> {
                matchConstraintMinWidth = r - dp(12)
            }
        }
    }

    private fun initData() {
        viewModel.errorToastLiveData.observe(viewLifecycleOwner) {
            // 报错
            toast(it ?: getString(R.string.common_failed))
        }
        viewModel.creationLiveData.observe(viewLifecycleOwner) {
            binding.srl.isRefreshing = false
            updateCreationView(it)
        }
        viewModel.copyCallback.observe(viewLifecycleOwner) { file, resId ->
            if (file != null) {
                refresh(true)
                toast(R.string.duplicated_successfully)
            } else if (resId != null) {
                binding.lv.hide()
                toast(resId)
            } else {
                binding.lv.hide()
                toast(R.string.duplicated_failed)
            }
        }
        viewModel.maxCreationCountLiveData.observe(viewLifecycleOwner) {
            // 创作数量
            updateCreationCountView(it.first, it.second)
        }
        setFragmentResultListenerByActivity(
            REQUEST_KEY_EDITOR_CREATION, viewLifecycleOwner
        ) { key, bundle ->
            if (key == REQUEST_KEY_EDITOR_CREATION && bundle.getString(
                    RenameLocalDialog.KEY_RESULT
                ) == RenameLocalDialog.RESULT_REFRESH_LOCAL
            ) {
                val path = bundle.getString(RenameLocalDialog.KEY_PATH) ?: return@setFragmentResultListenerByActivity
                val newName = bundle.getString(RenameLocalDialog.KEY_NEW_NAME) ?: return@setFragmentResultListenerByActivity
                viewModel.renameLocal(newName, path)
            }
        }
    }

    private fun updateCreationView(result: Pair<LoadStatus, MutableList<EditorCreationShowInfo>?>) {
        val status = result.first
        val list = result.second
        when (status.status) {
            LoadType.Refresh, LoadType.RefreshEnd -> {
                // 可能：Refresh、RefreshEmpty、RefreshFailed、RefreshEnd
                adapter.submitDataV2(viewLifecycleOwner.lifecycle, list, true)
                when {
                    list.isNullOrEmpty() && !status.message.isNullOrEmpty() -> {
                        // RefreshFailed
                        binding.lv.showError()
                    }
                    list.isNullOrEmpty()                                    -> {
                        // RefreshEmpty
                        binding.lv.hide()
                    }
                    else                                                    -> {
                        // Refresh、RefreshEnd
                        binding.lv.hide()
                        if (status.status == LoadType.RefreshEnd) {
                            adapter.loadMoreModule.loadMoreEnd()
                        } else {
                            adapter.resetLoadMore()
                        }
                    }
                }
            }
            LoadType.LoadMore                     -> {
                adapter.submitDataV2(viewLifecycleOwner.lifecycle, list)
                adapter.loadMoreModule.loadMoreComplete()
                binding.lv.hide()
            }
            LoadType.End                          -> {
                // LoadMoreEnd
                adapter.submitDataV2(viewLifecycleOwner.lifecycle, list)
                adapter.loadMoreModule.loadMoreEnd()
                binding.lv.hide()
            }
            LoadType.Fail                         -> {
                // LoadMoreFailed、不能判断RefreshFailed（可能有数据但是RefreshFailed）
                adapter.loadMoreModule.loadMoreFail()
                if (binding.lv.isVisible && list.isNullOrEmpty() && !NetUtil.isNetworkAvailable()) {
                    binding.lv.showError(true)
                } else {
                    binding.lv.hide()
                }
            }
            LoadType.Update                       -> {
                adapter.submitDataV2(viewLifecycleOwner.lifecycle, list, list.isNullOrEmpty())
                binding.lv.hide()
            }
            else                                  -> {
                binding.lv.hide()
            }
        }
    }

    private fun updateCreationCountView(count: Int, max: Long) {
        binding.tvTitle.setTextWithArgs(
            R.string.ugc_my_works, if (count > 0) {
                if (PandoraToggle.isUgcBackup) {
                    " ($count/$max)"
                } else {
                    " ($count)"
                }
            } else {
                ""
            }
        )
    }

    private fun initAdapter() {
        if (!adapter.hasEmptyView()) {
              val emptyView = AdapterEditorCreateV2MineEmptyBinding.inflate(layoutInflater)
            emptyView.root.layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
            )
            adapter.setEmptyView(emptyView.root)
        }
        adapter.loadMoreModule.apply {
            if (!PandoraToggle.isUgcBackupNotDeletePublish) {
                isEnableLoadMore = true
                loadMoreView = EmptyLoadMoreView()
                setOnLoadMoreListener {
                    if (!binding.srl.isRefreshing) {
                        viewModel.loadMoreCreationList()
                    }
                }
            }
        }
        adapter.setOnAntiViolenceItemClickListener { adapter, _, position ->
            val item = adapter.getItemOrNull(position) ?: return@setOnAntiViolenceItemClickListener
            // 更多
            showMoreDialog(item)
        }
        adapter.addChildClickViewIds(R.id.tv_edit)
        adapter.setOnAntiViolenceChildItemClickListener { _, view, position ->
            val item = adapter.getItem(position) as? EditorCreationShowInfo ?: return@setOnAntiViolenceChildItemClickListener
            when (view.id) {
                R.id.tv_edit -> {
                    if (item.isOnlyCloud()) {
                        goUgcBackup(item)
                    } else {
                        // 进编辑游戏
                        val draftInfo =
                            item.draftInfo ?: return@setOnAntiViolenceChildItemClickListener
                        Analytics.track(
                            EventConstants.EVENT_UGC_CREATE_EDIT_CLICK, getCommonParams(item)
                        )
                        viewModel.needRefreshMine = true
                        editorGameLaunchHelper?.startLocalGame(
                            this,
                            draftInfo.jsonConfig.gid,
                            draftInfo.path,
                            draftInfo.jsonConfig.parentPackageName.orEmpty(),
                            draftInfo.jsonConfig.fileId ?: "",
                            ResIdBean().setClickGameTime(System.currentTimeMillis())
                                .setGameCode(draftInfo.jsonConfig.gid)
                                .setGameId(draftInfo.jsonConfig.gid)
                                .setCategoryID(CategoryId.UGC_BUILD_LOCAL)
                                .setTsType(ResIdBean.TS_TYPE_LOCAL)
                        )
                    }
                }
            }
        }
        adapter.setOnItemShowListener { item, _ ->
            item.trackShow()
        }
        binding.rv.layoutManager = LinearLayoutManager(requireContext())
        binding.rv.adapter = adapter
    }

    override fun showDeleteSaveDialogHelper(item: EditorCreationShowInfo, context: Context) {
        binding.lv.showLoading(true, backgroundColor = Color.TRANSPARENT)
        viewModel.deleteCreation(item, context)
    }

    override fun onClickCopyGameHelper(draft: UgcDraftInfo) {
        binding.lv.showLoading(true, backgroundColor = Color.TRANSPARENT)
        viewModel.copyProject(draft.path.orEmpty())
    }

    private var firstLoading = true

    fun refresh(needLoading: Boolean = false) {
        if (firstLoading) {
            firstLoading = false
            binding.lv.showLoading(true, backgroundColor = Color.WHITE)
        } else if (needLoading) {
            binding.lv.showLoading(true, backgroundColor = Color.TRANSPARENT)
        }
        apiStart()
        viewModel.refreshV2Mine()
    }

    override fun loadFirstData() {}

    override fun onResume() {
        super.onResume()
        if (!hasLoaded || viewModel.needRefreshMine) {
            hasLoaded = true
            viewModel.needRefreshMine = false
            refresh(true)
        } else {
            adapter.invokeItemShow()
        }
    }

    override fun onPause() {
        visibleList(
            binding.ivTipsArrow,
            binding.tvTips,
            visible = false
        )
        job?.cancel()
        job = null
        super.onPause()
    }

    override fun onDestroyView() {
        binding.rv.adapter = null
        adapter.loadMoreModule.setOnLoadMoreListener(null)
        adapter.loadMoreModule.loadMoreComplete()

        clearFragmentResultAndListenerByActivity(REQUEST_KEY_EDITOR_CREATION)

        super.onDestroyView()
    }

    override fun getFragmentName() = PageNameConstants.FRAGMENT_NEW_CREATE_CENTER

}