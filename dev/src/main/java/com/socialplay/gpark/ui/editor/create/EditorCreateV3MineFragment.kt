package com.socialplay.gpark.ui.editor.create

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.net.toUri
import androidx.core.view.isVisible
import androidx.core.view.marginStart
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.meta.biz.ugc.model.UgcDraftInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.LoadStatus
import com.socialplay.gpark.data.model.LoadType
import com.socialplay.gpark.data.model.editor.EditorCreationShowInfo
import com.socialplay.gpark.data.model.game.UgcMaxLimitTipsConfig
import com.socialplay.gpark.databinding.AdapterEditorCreateV2MineEmptyBinding
import com.socialplay.gpark.databinding.FragmentEditorCreateV3MineBinding
import com.socialplay.gpark.databinding.ViewControlEndLoadMoreBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.apm.apiStart
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.editor.BaseEditorFragment
import com.socialplay.gpark.ui.editor.legecy.RenameLocalDialog
import com.socialplay.gpark.ui.gamedetail.unify.GameDetailEditViewModel
import com.socialplay.gpark.ui.view.EmptyLoadMoreView
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.apiMonitor
import com.socialplay.gpark.util.extension.clearFragmentResultAndListenerByActivity
import com.socialplay.gpark.util.extension.getLocationOnScreenEx
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setFragmentResultListenerByActivity
import com.socialplay.gpark.util.extension.setOnAntiViolenceChildItemClickListener
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setOnAntiViolenceItemClickListener
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.sharedViewModelFromParentFragment
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.getValue

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/04/06
 *     desc   :
 * </pre>
 */
class EditorCreateV3MineFragment : BaseEditorFragment<FragmentEditorCreateV3MineBinding>() {

    private val viewModel by sharedViewModelFromParentFragment<EditorCreateViewModel>(
        owner = { requireParentFragment().requireParentFragment() }
    )

    private val sharedViewModel: GameDetailEditViewModel by activityViewModels()

    private val adapter by lazy {
        EditorCreateV3MineAdapter(::glide).apply {
            val endView = ViewControlEndLoadMoreBinding.inflate(layoutInflater).apply {
                loadMoreLoadingView.gone()
                loadMoreLoadFailView.gone()
                loadMoreLoadCompleteView.gone()
                loadMoreLoadEndView.visible()
            }.root
            addFooterView(endView)
        }
    }

    private var hasLoaded = false
    var job: Job? = null

    private var mType: Int = EditorCreateMineParentFragment.TYPE_ALL

    companion object {
        const val REQUEST_KEY_EDITOR_CREATION = "request_key_editor_creation"
        fun newInstance(type: Int): EditorCreateV3MineFragment {
            return EditorCreateV3MineFragment().apply {
                mType = type
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.creationLiveData.apiMonitor(this) {
            it.first.status != LoadType.Fail
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentEditorCreateV3MineBinding? {
        return FragmentEditorCreateV3MineBinding.inflate(inflater, container, false)
    }

    override fun init() {
        super.init()
        initView()
        initData()
    }

    private fun initView() {
        binding.lv.showLoading(true, backgroundColor = Color.WHITE)
        binding.lv.setRetry {
            refresh(false, true)
        }
        binding.srl.setOnRefreshListener {
            refresh(false, true)
        }
        initAdapter()
        binding.ivTips.visible((PandoraToggle.isUgcBackup))
        binding.ivTips.setOnClickListener {
            if (binding.ivTipsArrow.isVisible) {
                visibleList(
                    binding.ivTipsArrow,
                    binding.layoutTips,
                    visible = false
                )
                job?.cancel()
                job = null
            } else {
                visibleList(
                    binding.ivTipsArrow,
                    binding.layoutTips,
                    visible = true
                )
                // 弹窗尖角左右两侧的宽度应该相等
                val loc = binding.ivTips.getLocationOnScreenEx()
                binding.layoutTips.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    width = (loc.x + binding.ivTips.width / 2 - binding.layoutTips.marginStart) * 2
                }
                val tipsConfig = viewModel.ugcWorksMaxLimitTipsLiveData.value
                    ?: UgcMaxLimitTipsConfig.DEFAULT_CONFIG
                binding.tvTips.text = tipsConfig.context ?: UgcMaxLimitTipsConfig.DEFAULT_TIPS
                binding.tvTipsBtn.visible(!tipsConfig.jump.isNullOrEmpty() && tipsConfig.jump != "-1")
                binding.tvTipsBtn.setOnAntiViolenceClickListener {
                    val schemaLink = tipsConfig.jump ?: return@setOnAntiViolenceClickListener
                    val schemaUri = runCatching { schemaLink.toUri() }.getOrNull()
                        ?: return@setOnAntiViolenceClickListener
                    MetaRouter.Scheme.jumpScheme(
                        this@EditorCreateV3MineFragment,
                        schemaUri,
                        getFragmentName()
                    )
                }
                job?.cancel()
                job = viewLifecycleOwner.lifecycleScope.launch {
                    delay(5000)
                    visibleList(
                        binding.ivTipsArrow,
                        binding.layoutTips,
                        visible = false
                    )
                    job = null
                }
            }
        }
        binding.tvTitle.setTextWithArgs(
            when (mType) {
                EditorCreateMineParentFragment.TYPE_PUBLISHED -> {
                    R.string.work_manage_mime_published_title
                }

                EditorCreateMineParentFragment.TYPE_DRAFT -> {
                    R.string.work_manage_mime_draft_title
                }

                else -> {
                    R.string.work_manage_mime_all_title
                }
            },
            ""
        )
        if (mType == EditorCreateMineParentFragment.TYPE_PUBLISHED) {
            binding.tvDesc.visible()
            binding.tvDesc.text = getString(R.string.ugc_center_header_publish_tip)
        } else if (mType == EditorCreateMineParentFragment.TYPE_DRAFT) {
            binding.tvDesc.visible()
            val descArray = getString(R.string.ugc_center_header_draf_tip).split("%s")
            if (descArray.size > 1) {
                val builder = SpannableHelper.Builder()
                if (descArray[0].isNotEmpty()) {
                    builder.text(descArray[0])
                        .colorRes(R.color.color_666666)
                }
                builder.text(getString(R.string.ugc_center_header_draf_tip_red))
                    .colorRes(R.color.color_FF5F42)
                if (descArray[1].isNotEmpty()) {
                    builder.text(descArray[1])
                        .colorRes(R.color.color_666666)
                }
                binding.tvDesc.text = builder.build()
            } else {
                binding.tvDesc.gone()
            }
        } else {
            binding.tvDesc.gone()
        }
    }

    private fun initData() {
        lifecycleScope.launch {
            // 游戏详情页删除游戏后, 要将远端的删除
            sharedViewModel.deleteResult.collect { deleteResult->
                viewModel.deleteRemoteCreation(deleteResult.gameId)
            }
        }
        viewModel.errorToastLiveData.observe(viewLifecycleOwner) {
            // 报错
            toast(it ?: getString(R.string.common_failed))
        }
        viewModel.creationLiveData.observe(viewLifecycleOwner) {
            binding.srl.isRefreshing = false
            updateCreationView(it)
        }
        viewModel.copyCallback.observe(viewLifecycleOwner) { file, resId ->
            if (file != null) {
                refresh(true, true)
                toast(R.string.duplicated_successfully)
            } else if (resId != null) {
                binding.lv.hide()
                toast(resId)
            } else {
                binding.lv.hide()
                toast(R.string.duplicated_failed)
            }
        }
        viewModel.creationCountData.observe(viewLifecycleOwner) {
            // 创作数量
            updateCreationCountView(it)
        }
        setFragmentResultListenerByActivity(
            REQUEST_KEY_EDITOR_CREATION, viewLifecycleOwner
        ) { key, bundle ->
            if (key == REQUEST_KEY_EDITOR_CREATION && bundle.getString(
                    RenameLocalDialog.KEY_RESULT
                ) == RenameLocalDialog.RESULT_REFRESH_LOCAL
            ) {
                val path = bundle.getString(RenameLocalDialog.KEY_PATH)
                    ?: return@setFragmentResultListenerByActivity
                val newName = bundle.getString(RenameLocalDialog.KEY_NEW_NAME)
                    ?: return@setFragmentResultListenerByActivity
                viewModel.renameLocal(newName, path)
            }
        }
    }

    private fun updateCreationView(result: Pair<LoadStatus, MutableList<EditorCreationShowInfo>?>) {
        val status = result.first
        val listTemp = result.second
        // 从ViewModel中拿到的数据, 前面一截来源于本地草稿+云备份, 后面一截来源于已发布游戏列表
        // 现在要求按照时间严格倒序排序
        listTemp?.sortBy { -it.getShowTime() }
        val list = if (listTemp == null) {
            listTemp
        } else {
            when (mType) {
                EditorCreateMineParentFragment.TYPE_PUBLISHED -> {
                    if (status.status == LoadType.RefreshEnd || status.status == LoadType.End) {
                        listTemp.filter { !it.isDraftStatus() }
                    } else {
                        // 有的 item 是已发布状态, 但是没有 ugcInfo, 说明已发布列表分页加载还没加载到这里来, 暂时不展示, 等加载到这里来之后再展示
                        listTemp.filter { it.ugcInfo != null }
                    }
                }

                EditorCreateMineParentFragment.TYPE_DRAFT -> {
                    listTemp.filter { it.isDraftStatus() }
                }

                else -> {
                    if (status.status == LoadType.RefreshEnd || status.status == LoadType.End) {
                        listTemp
                    } else {
                        // 查找最后一个有ugcInfo的下标
                        val index =
                            listTemp.withIndex().lastOrNull { it.value.ugcInfo != null }?.index
                        if (index == null || index >= listTemp.size - 1) {
                            listTemp
                        } else {
                            listTemp.subList(0, index + 1)
                        }
                    }
                }
            }.toMutableList()
        }
        when (status.status) {
            LoadType.Refresh, LoadType.RefreshEnd -> {
                // 可能：Refresh、RefreshEmpty、RefreshFailed、RefreshEnd
                adapter.submitDataV2(viewLifecycleOwner.lifecycle, list, true)
                when {
                    list.isNullOrEmpty() && !status.message.isNullOrEmpty() -> {
                        // RefreshFailed
                        binding.lv.showError()
                    }

                    list.isNullOrEmpty() -> {
                        // RefreshEmpty
                        binding.lv.hide()
                    }

                    else -> {
                        // Refresh、RefreshEnd
                        binding.lv.hide()
                        if (status.status == LoadType.RefreshEnd) {
                            adapter.loadMoreModule.loadMoreEnd()
                        } else {
                            adapter.resetLoadMore()
                        }
                    }
                }
            }

            LoadType.LoadMore -> {
                adapter.submitDataV2(viewLifecycleOwner.lifecycle, list)
                adapter.loadMoreModule.loadMoreComplete()
                binding.lv.hide()
            }

            LoadType.End -> {
                // LoadMoreEnd
                adapter.submitDataV2(viewLifecycleOwner.lifecycle, list)
                adapter.loadMoreModule.loadMoreEnd()
                binding.lv.hide()
            }

            LoadType.Fail -> {
                // LoadMoreFailed、不能判断RefreshFailed（可能有数据但是RefreshFailed）
                adapter.loadMoreModule.loadMoreFail()
                if (binding.lv.isVisible && list.isNullOrEmpty() && !NetUtil.isNetworkAvailable()) {
                    binding.lv.showError(true)
                } else {
                    binding.lv.hide()
                }
            }

            LoadType.Update -> {
                adapter.submitDataV2(viewLifecycleOwner.lifecycle, list, list.isNullOrEmpty())
                binding.lv.hide()
            }

            else -> {
                binding.lv.hide()
            }
        }
    }

    private fun updateCreationCountView(creationCountData: CreationCountData) {
        when (mType) {
            EditorCreateMineParentFragment.TYPE_PUBLISHED -> {
                val publishedCount = creationCountData.creationCount - creationCountData.draftCount
                binding.tvTitle.setTextWithArgs(
                    R.string.work_manage_mime_published_title,
                    publishedCount
                )
            }

            EditorCreateMineParentFragment.TYPE_DRAFT -> {
                binding.tvTitle.setTextWithArgs(
                    R.string.work_manage_mime_draft_title,
                    creationCountData.draftCount
                )
            }

            else -> {
                binding.tvTitle.setTextWithArgs(
                    R.string.work_manage_mime_draft_title,
                    "${creationCountData.creationCount}/${creationCountData.maxLimitCount}"
                )
            }
        }
    }

    private fun initAdapter() {
        if (!adapter.hasEmptyView()) {
            val emptyView = AdapterEditorCreateV2MineEmptyBinding.inflate(layoutInflater)
            emptyView.root.layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
            )
            adapter.setEmptyView(emptyView.root)
        }
        adapter.loadMoreModule.apply {
            // TODO 如果是选择的草稿, 还需要加载更多么?
            if (!PandoraToggle.isUgcBackupNotDeletePublish) {
                isEnableLoadMore = true
                loadMoreView = EmptyLoadMoreView()
                setOnLoadMoreListener {
                    if (!binding.srl.isRefreshing) {
                        viewModel.loadMoreCreationList()
                    }
                }
            }
        }
        adapter.setOnAntiViolenceItemClickListener { adapter, _, position ->
            val item = adapter.getItemOrNull(position) ?: return@setOnAntiViolenceItemClickListener
            // 更多
            showMoreDialog(item, getPageType())
        }
        adapter.addChildClickViewIds(R.id.tvBtn)
        adapter.setOnAntiViolenceChildItemClickListener { _, view, position ->
            val item = adapter.getItem(position) as? EditorCreationShowInfo
                ?: return@setOnAntiViolenceChildItemClickListener
            when (view.id) {
                R.id.tvBtn -> {
                    if (item.isOnlyCloud()) {
                        goUgcBackup(item)
                    } else {
                        // 进编辑游戏
                        val draftInfo =
                            item.draftInfo ?: return@setOnAntiViolenceChildItemClickListener
                        Analytics.track(
                            EventConstants.EVENT_UGC_CREATE_EDIT_CLICK, getCommonParams(item)
                        )
                        viewModel.needRefreshMine = true
                        editorGameLaunchHelper?.startLocalGame(
                            this,
                            draftInfo.jsonConfig.gid,
                            draftInfo.path,
                            draftInfo.jsonConfig.parentPackageName.orEmpty(),
                            draftInfo.jsonConfig.fileId ?: "",
                            ResIdBean().setClickGameTime(System.currentTimeMillis())
                                .setGameCode(draftInfo.jsonConfig.gid)
                                .setGameId(draftInfo.jsonConfig.gid)
                                .setCategoryID(CategoryId.UGC_BUILD_LOCAL)
                                .setTsType(ResIdBean.TS_TYPE_LOCAL)
                        )
                    }
                }
            }
        }
        adapter.setOnItemShowListener { item, _ ->
            item.trackShow(getPageType())
        }
        binding.rv.layoutManager = LinearLayoutManager(requireContext())
        binding.rv.adapter = adapter
    }

    private fun getPageType(): String{
        return if (mType == EditorCreateMineParentFragment.TYPE_PUBLISHED) {
            "online"
        } else if (mType == EditorCreateMineParentFragment.TYPE_DRAFT) {
            "draft"
        } else {
            "all"
        }
    }

    override fun showDeleteSaveDialogHelper(item: EditorCreationShowInfo, context: Context) {
        binding.lv.showLoading(true, backgroundColor = Color.TRANSPARENT)
        viewModel.deleteCreation(item, context)
    }

    override fun onClickCopyGameHelper(draft: UgcDraftInfo) {
        binding.lv.showLoading(true, backgroundColor = Color.TRANSPARENT)
        viewModel.copyProject(draft.path.orEmpty())
    }

    private var firstLoading = true

    fun refresh(needLoading: Boolean = false, force: Boolean = true) {
        if (firstLoading) {
            firstLoading = false
            // 由于 viewModel 是共用的, 有可能有数据了, 此时没必要显示 loading, 否则会闪一下
            if (viewModel.creationLiveData.value == null) {
                binding.lv.showLoading(true, backgroundColor = Color.WHITE)
            }
        } else if (needLoading) {
            binding.lv.showLoading(true, backgroundColor = Color.TRANSPARENT)
        }
        apiStart()
        viewModel.refreshV2Mine(force)
    }

    override fun loadFirstData() {}

    override fun onResume() {
        super.onResume()
        if (!hasLoaded || viewModel.needRefreshMine) {
            hasLoaded = true
            viewModel.needRefreshMine = false
            refresh(true, true)
        } else {
            adapter.invokeItemShow()
        }
    }

    override fun onPause() {
        visibleList(
            binding.ivTipsArrow,
            binding.layoutTips,
            visible = false
        )
        job?.cancel()
        job = null
        super.onPause()
    }

    override fun onDestroyView() {
        binding.rv.adapter = null
        adapter.loadMoreModule.setOnLoadMoreListener(null)
        adapter.loadMoreModule.loadMoreComplete()

        clearFragmentResultAndListenerByActivity(REQUEST_KEY_EDITOR_CREATION)

        super.onDestroyView()
    }

    override fun getFragmentName() = PageNameConstants.FRAGMENT_NEW_CREATE_CENTER

}