package com.socialplay.gpark.ui.editor.detail.comment

import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.databinding.PopUpGameDetailCommonBinding
import com.socialplay.gpark.databinding.PopUpGameDetailCommonCommentBinding
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.view.PopupWindowCompat
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.getStringByGlobal
import kotlinx.coroutines.launch

class CommentMorePopup(layoutInflater: LayoutInflater) {

    private lateinit var sortPopupWindow: PopupWindowCompat
    val sortPopupBinding by lazy {
        PopUpGameDetailCommonBinding.inflate(layoutInflater)
    }

    private lateinit var commentPopupWindow: PopupWindowCompat
    val commentPopupBinding by lazy {
        PopUpGameDetailCommonCommentBinding.inflate(layoutInflater)
    }


    fun initPopup(
        onDefaultClick: () -> Unit,
        onNewestClick: () -> Unit,
        onHotClick: () -> Unit,
        onAuthorOnlyClick: () -> Unit,
        onSelfOnlyClick: () -> Unit,
    ) {
        sortPopupWindow = PopupWindowCompat(
            sortPopupBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = R.style.PopupAnimationGameDetailCommon
        }
        sortPopupBinding.root.setOnClickListener {
            sortPopupWindow.dismiss()
        }
        sortPopupBinding.vDefaultClick.setOnAntiViolenceClickListener {
            onDefaultClick()
            sortPopupWindow.dismiss()
        }
        sortPopupBinding.vNewestClick.setOnAntiViolenceClickListener {
            onNewestClick()
            sortPopupWindow.dismiss()
        }
        sortPopupBinding.vHotClick.setOnAntiViolenceClickListener {
            onHotClick()
            sortPopupWindow.dismiss()
        }
        sortPopupBinding.vAuthorOnlyClick.setOnAntiViolenceClickListener {
            onAuthorOnlyClick()
            sortPopupWindow.dismiss()
        }
        sortPopupBinding.vSelfOnlyClick.setOnAntiViolenceClickListener {
            onSelfOnlyClick()
            sortPopupWindow.dismiss()
        }

        commentPopupWindow = PopupWindowCompat(
            commentPopupBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = 0
        }
        commentPopupBinding.root.setOnClickListener {
            commentPopupWindow.dismiss()
        }
        commentPopupWindow.setOnDismissListener {
            commentPopupBinding.vCopyClick.unsetOnClick()
            commentPopupBinding.vPinClick.unsetOnClick()
            commentPopupBinding.vUnpinClick.unsetOnClick()
            commentPopupBinding.vReportClick.unsetOnClick()
            commentPopupBinding.vDeleteClick.unsetOnClick()
        }
    }

    fun showSortPopup(targetView: View, rootLayout: View) {
        sortPopupBinding.cv.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        val x = -sortPopupBinding.cv.measuredWidth + targetView.measuredWidth - 32.dp
        val rawY = (-26).dp
        val rect = Rect()
        targetView.getGlobalVisibleRect(rect)
        val rawBottom = rect.bottom + sortPopupBinding.cv.measuredHeight + rawY
        rootLayout.getGlobalVisibleRect(rect)
        val y = if (rawBottom > rect.bottom) {
            -sortPopupBinding.cv.measuredHeight - targetView.measuredHeight - 6.dp
        } else {
            rawY
        }
        sortPopupWindow.showAsDropDownByLocation(targetView, x, y, autoHeight = false)
    }

    fun operateComment(
        fragment: Fragment,
        rootLayout: View,
        view: View,
        comment: PostComment? = null,
        reply: PostReply? = null,
        showRedDot: Boolean,
        /**
         * 当前评论是否是当前用户发的
         */
        isMe: Boolean,
        /**
         * 当前用户是否是作者
         */
        isCreator: Boolean,
        onCopyClick: () -> Unit,
        onPinClick: () -> Unit,
        onUnpinClick: () -> Unit,
        onReportClick: () -> Unit,
        onDeleteCancel: () -> Unit,
        onDeleteConfirm: () -> Unit,
    ) {
        if (comment != null && isCreator) {
            if (comment.top == true) {
                visibleList(
                    commentPopupBinding.mtvPin,
                    commentPopupBinding.vPinClick,
                    commentPopupBinding.vPinRedDot,
                    commentPopupBinding.vDivider1,
                    visible = false
                )
                visibleList(
                    commentPopupBinding.mtvUnpin,
                    commentPopupBinding.vUnpinClick,
                    commentPopupBinding.vDivider2,
                    visible = true
                )
            } else {
                visibleList(
                    commentPopupBinding.mtvPin,
                    commentPopupBinding.vPinClick,
                    commentPopupBinding.vDivider1,
                    visible = true
                )
                commentPopupBinding.vPinRedDot.visible(showRedDot)
                visibleList(
                    commentPopupBinding.mtvUnpin,
                    commentPopupBinding.vUnpinClick,
                    commentPopupBinding.vDivider2,
                    visible = false
                )
            }
        } else {
            visibleList(
                commentPopupBinding.mtvPin,
                commentPopupBinding.vPinClick,
                commentPopupBinding.vPinRedDot,
                commentPopupBinding.vDivider1,
                commentPopupBinding.mtvUnpin,
                commentPopupBinding.vUnpinClick,
                commentPopupBinding.vDivider2,
                visible = false
            )
        }
        visibleList(
            commentPopupBinding.mtvReport,
            commentPopupBinding.vReportClick,
            commentPopupBinding.vDivider3,
            visible = !isMe
        )
        visibleList(
            commentPopupBinding.mtvDelete,
            commentPopupBinding.vDeleteClick,
            commentPopupBinding.vDivider4,
            visible = isMe || isCreator
        )
        val showCopy = if (comment != null && comment.content.isNullOrEmpty()) {
            false
        } else if (reply != null && reply.content.isNullOrEmpty()) {
            false
        } else {
            true
        }
        visibleList(
            commentPopupBinding.mtvCopy,
            commentPopupBinding.vCopyClick,
            visible = showCopy
        )
        if (!showCopy) {
            visibleList(
                commentPopupBinding.vDivider1,
                commentPopupBinding.vDivider2,
                visible = false
            )
        }

        commentPopupBinding.vCopyClick.setOnAntiViolenceClickListener {
            onCopyClick()
            fragment.viewLifecycleOwner.lifecycleScope.launch {
                ClipBoardUtil.setClipBoardContent(
                    comment?.content ?: reply?.content,
                    fragment.requireContext(),
                    if (comment != null) "Comment" else "Reply"
                )
            }

            fragment.toast(R.string.copied_to_clipboard)
            commentPopupWindow.dismiss()
        }
        commentPopupBinding.vPinClick.setOnAntiViolenceClickListener {
            onPinClick()
            commentPopupWindow.dismiss()
        }
        commentPopupBinding.vUnpinClick.setOnAntiViolenceClickListener {
            onUnpinClick()
            commentPopupWindow.dismiss()
        }
        commentPopupBinding.vReportClick.setOnAntiViolenceClickListener {
            onReportClick()
            commentPopupWindow.dismiss()
        }
        commentPopupBinding.vDeleteClick.setOnAntiViolenceClickListener {
            ConfirmDialog.Builder(fragment)
                .image(R.drawable.dialog_icon_cry)
                .content(
                    getStringByGlobal(
                        R.string.delete_confirm,
                        getStringByGlobal(
                            if (comment != null) {
                                R.string.comment
                            } else {
                                R.string.reply
                            }
                        )
                    )
                )
                .cancelBtnTxt(getStringByGlobal(R.string.dialog_cancel))
                .confirmBtnTxt(getStringByGlobal(R.string.delete_cap))
                .isRed(true)
                .cancelCallback {
                    onDeleteCancel()
                }
                .confirmCallback {
                    onDeleteConfirm()
                }
                .show()
            commentPopupWindow.dismiss()
        }

        commentPopupBinding.cv.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        val rect = Rect()
        view.getGlobalVisibleRect(rect)
        val dp5 = 5.dp
        val dp16 = 16.dp
        val x = -commentPopupBinding.cv.measuredWidth + 8.dp
        val rawBottom = rect.top - dp5 + commentPopupBinding.cv.measuredHeight
        rootLayout.getGlobalVisibleRect(rect)
        val y = if (rawBottom > rect.bottom) {
            -dp16 - commentPopupBinding.cv.measuredHeight + dp5
        } else {
            -dp16 - view.height.coerceAtLeast(view.measuredHeight) - dp5
        }
        commentPopupWindow.showAsDropDownByLocation(view, x, y, autoHeight = false)
    }

    fun release() {
        if (::sortPopupWindow.isInitialized) {
            sortPopupWindow.dismiss()
        }
        if (::commentPopupWindow.isInitialized) {
            commentPopupWindow.dismiss()
        }
    }
}