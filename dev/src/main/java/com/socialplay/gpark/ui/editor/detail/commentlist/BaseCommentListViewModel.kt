package com.socialplay.gpark.ui.editor.detail.commentlist

import android.app.Application
import androidx.annotation.CallSuper
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.meta.pandora.utils.ConcurrentSet
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.PagingApiResult
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.account.ClearRedDotEvent
import com.socialplay.gpark.data.model.post.AddPostCommentReplyTarget
import com.socialplay.gpark.data.model.post.OpinionRequestBody
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.post.PostReplyListRequestBody
import com.socialplay.gpark.data.model.post.event.CommentMetaData
import com.socialplay.gpark.data.model.post.event.CommentMetaDataUpdateEvent
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.gamedetail.unify.BaseGameDetailCommonFragment
import com.socialplay.gpark.util.extension.dropAtWithResult
import com.socialplay.gpark.util.extension.getWithIndex
import com.socialplay.gpark.util.extension.insertAt
import com.socialplay.gpark.util.extension.moveNewTo
import com.socialplay.gpark.util.extension.replaceAt
import org.greenrobot.eventbus.EventBus

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/12/13
 *     desc   :
 * </pre>
 */
abstract class BaseCommentListViewModel<S : MavericksState>(
    initialState: S,
    protected val context: Application,
    protected val repo: IMetaRepository,
    val accountInteractor: AccountInteractor
) : BaseViewModel<S>(initialState) {

    companion object {
        const val REPLY_SIZE_NORMAL = 10
        const val CONTENT_TYPE_UGC_GAME_COMMENT = 1
        const val CONTENT_TYPE_UGC_DESIGN_COMMENT = 2
        const val CONTENT_TYPE_GAME_DETAIL_COMMENT = 3

        const val MODULE_COMMUNITY = 1
        const val MODULE_PGC = 2
        const val MODULE_UGC = 3
        const val MODULE_MATERIAL = 6
    }

    private val commentSet = ConcurrentSet<String>()
    private val replySet = ConcurrentSet<String>()

    private var replyTarget: AddPostCommentReplyTarget? = null

    protected abstract val oldListResult: PagingApiResult<PostComment>?

    abstract val authorId: String?

    protected abstract val moduleContentId: String
    protected abstract val pageType: Long

    protected abstract val moduleContentType: Int

    protected abstract val S.oldListResult: PagingApiResult<PostComment>?

    protected abstract fun S.updateCommentList(
        result: PagingApiResult<PostComment>?,
        msg: Any? = null
    ): S

    protected open fun updateCommentList(result: PagingApiResult<PostComment>?, msg: Any? = null) {
        if (result == null && msg == null) return
        setState { updateCommentList(result, msg) }
    }

    protected open fun S.handleExecute(result: Async<Pair<PagingApiResult<PostComment>?, Any?>?>): S {
        if (result is Loading) return this
        val resultPair = result.invoke()
        val data = resultPair?.first
        val msg = resultPair?.second ?: if (result is Fail) result else null
        return updateCommentList(data, msg)
    }

    protected abstract fun invokeToast(resId: Int)

    //region 评论
    fun getCommentList(
        moduleContentId: String,
        sortType: Int,
        page: Int,
        pageSize: Int,
        replySize: Int,
        replySortType: Int,
        refresh: Boolean,
        targetCommentId: String? = null,
        targetReplyId: String? = null,
        commentCollapse: Boolean = false,
        commentReplyStatus: Int = PostComment.REPLY_STATUS_INIT,
        withAuthorReply: Boolean = false,
        authorReplySize: Int = 0,
        filterType: Int? = null,
        reducer: S.(Async<PagingApiResult<PostComment>>, Int) -> S
    ) {
        val targetPage = if (refresh) 1 else page + 1
        repo.getPostCommentListV2(
            PostCommentListRequestBody(
                moduleContentType,
                moduleContentId,
                sortType,
                pageSize,
                targetPage,
                replySize,
                replySortType,
                targetCommentId,
                targetReplyId,
                withAuthorReply = withAuthorReply,
                filterType = filterType
            )
        ).map {
            var hasTargetComment = false
            var hasTargetReply = false
            // 刷新清空set
            if (refresh) {
                commentSet.clear()
                replySet.clear()
            }
            val tempCommentSet = HashSet<String>()
            // 过滤set
            val newCommentList = it.dataList?.filter { comment ->
                if (commentSet.contains(comment.commentId) || tempCommentSet.contains(comment.commentId)) {
                    false
                } else {
                    tempCommentSet.add(comment.commentId)
                    true
                }
            }?.take(
                pageSize
            )?.map { comment ->
                commentSet.add(comment.commentId)
                val topReply = comment.showReply?.firstOrNull()?.apply {
                    highlight = true
                    forceShow = true
                    needLocate = true

                    hasTargetReply = true
                }
                val tempReplySet = HashSet<String>()
                // 作者回复一级评论时, 回复内容中不会下发 replyUid 字段
                // 作者回复二级评论时, 回复内容中会下发 replyUid 字段
                // 判断一级评论是否被作者回复过, 就看 replyUid 字段是否为空
                val authorReplied =
                    comment.authorReply?.firstOrNull { reply -> reply.replyUid.isNullOrEmpty() } != null
                val newAuthorReplyList = if (topReply != null) {
                    comment.authorReply.insertAt(0, topReply)
                } else {
                    comment.authorReply
                }?.filter { reply ->
                    if (replySet.contains(reply.replyId) || tempReplySet.contains(reply.replyId)) {
                        false
                    } else {
                        tempReplySet.add(reply.replyId)
                        true
                    }
                }?.take(authorReplySize)
                newAuthorReplyList?.forEach { reply ->
                    replySet.add(reply.replyId)
                }
                val newAuthorReplyCount = (newAuthorReplyList?.size ?: 0).toLong()
                val replyTotal = (comment.replyCommonPage?.total ?: 0)
                    .coerceAtLeast(newAuthorReplyCount)
                val isEnd = newAuthorReplyCount == replyTotal
                val newReplyListWrapper = PostComment.ReplyData(
                    replyTotal,
                    newAuthorReplyList,
                    isEnd
                )
                comment.copy(
                    replyCommonPage = newReplyListWrapper,
                    authorReply = null,
                    authorReplied = authorReplied,
                    collapse = commentCollapse && newAuthorReplyCount > 0,
                    replyStatus = if (isEnd) PostComment.REPLY_STATUS_MORE else commentReplyStatus,
                    initAuthorReplyCount = newAuthorReplyCount.toInt(),
                    showReply = null
                ).apply {
                    if (commentId == targetCommentId) {
                        if (targetReplyId.isNullOrEmpty()) {
                            needLocate = true
                            highlight = true
                        }

                        hasTargetComment = true
                    }
                }
            }
            if ((!targetReplyId.isNullOrEmpty() && !hasTargetReply) || (!targetCommentId.isNullOrEmpty() && !hasTargetComment)) {
                invokeToast(R.string.target_comment_deleted)
            }
            if (refresh) {
                it.copy(dataList = newCommentList)
            } else {
                // 加载更多加到列表最后
                val commentList = oldListResult?.dataList.insertAt(-1, newCommentList)
                it.copy(dataList = commentList)
            }
        }.execute {
            reducer(it, targetPage)
        }
    }

    fun addComment(comment: PostComment?) = withState { s ->
        comment ?: return@withState
        // 评论set不能有新评论
        if (commentSet.contains(comment.commentId)) return@withState
        val oldListResult = s.oldListResult ?: return@withState
        comment.highlight = true
        comment.isNewAdd = true
        comment.underReview = true
        commentSet.add(comment.commentId)
        val oldTotal = oldListResult.total
        val newTotal = oldTotal + 1
        val oldList = oldListResult.dataList
        val newList = oldList.insertAt(0, comment)
        val newListResult = oldListResult.copy(dataList = newList, total = newTotal)
        updateCommentList(newListResult)
    }

    fun pinComment(
        comment: PostComment,
        commentPosition: Int,
        showRedDot: Boolean
    ) {
        if (showRedDot) {
            clearCommentPinRedDot()
        }
        val pin = !(comment.top ?: false)
        repo.topGameAppraiseV2(comment.commentId, pin).map {
            check(commentSet.contains(comment.commentId))
            val oldListResult = oldListResult ?: return@map null
            val oldList = oldListResult.dataList ?: return@map null
            val newComment = comment.pin(pin)
            val newList = if (pin) {
                oldList.moveNewTo(commentPosition, 0, comment, newComment)
            } else {
                val lastTopPosition = oldList.indexOfLast {
                    it.top == true
                }
                if (lastTopPosition == -1) {
                    return@map null
                }
                oldList.moveNewTo(commentPosition, lastTopPosition, comment, newComment)
            }
            val msgRes = if (pin) R.string.comment_pinned else R.string.comment_unpinned
            oldListResult.copy(dataList = newList) to context.getString(msgRes)
        }.execute {
            handleExecute(it)
        }
    }

    fun deleteComment(comment: PostComment, commentPosition: Int) {
        val isMe = isMe(comment.uid)
        when (moduleContentType) {
            MODULE_PGC -> {
                Analytics.track(
                    EventConstants.GAME_REVIEW_DELETE_CLICK,
                    "gameid" to moduleContentId,
                    "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_PGC,
                    "type" to (if (isMe) 1 else 2)
                )

            }
            MODULE_UGC -> {
                Analytics.track(
                    EventConstants.GAME_REVIEW_DELETE_CLICK,
                    "gameid" to moduleContentId,
                    "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_UGC,
                    "type" to (if (isMe) 1 else 2)
                )
            }
        }
        repo.deletePostComment(comment.commentId).map {
            trackDelete(comment.commentId, isMe, 1L)
            assert(commentSet.contains(comment.commentId))
            val oldListResult = oldListResult ?: return@map null
            val oldList = oldListResult.dataList
            val (ok, _, newList) = oldList.dropAtWithResult(commentPosition, comment)
            if (!ok) return@map null
            commentSet.remove(comment.commentId)
            val oldTotal = oldListResult.total
            val newTotal = (oldTotal - 1).coerceAtLeast(0)
            oldListResult.copy(
                dataList = newList,
                total = newTotal
            ) to context.getString(R.string.comment_deleted)
        }.execute {
            handleExecute(it)
        }
    }

    fun likeComment(comment: PostComment, commentPosition: Int) = withState { s ->
        val oldListResult = s.oldListResult ?: return@withState
        val oldList = oldListResult.dataList
        val newComment = comment.switchLike()
        val newList = oldList.replaceAt(commentPosition, newComment)
        val newListResult = oldListResult.copy(dataList = newList)
        updateCommentList(newListResult)
        EventBus.getDefault().post(
            CommentMetaDataUpdateEvent(
                commentMetaData = CommentMetaData(
                    commentId = comment.commentId,
                    commentLiked = !OpinionRequestBody.isLike(comment.opinion),
                    commentLikeCount = newComment.likeCount
                )
            )
        )
        repo.saveOpinion(OpinionRequestBody.commentLike(comment.commentId, newComment.isLike))
            .execute {
                this
            }
    }
    //endregion

    //region 回复
    fun addReply(replyPair: Pair<PostReply, AddPostCommentReplyTarget>) = withState { s ->
        val oldListResult = s.oldListResult ?: return@withState
        val (reply, replyTarget) = replyPair
        val newListResult = if (replySet.contains(reply.replyId)) {
            return@withState
        } else {
            replySet.add(reply.replyId)
            val oldList = oldListResult.dataList
            val (oldPosition, oldComment) = oldList.getWithIndex(replyTarget.commonPosition) { e ->
                e.commentId == reply.commentId
            } ?: return@withState
            reply.highlight = true
            reply.underReview = true
            val newComment = oldComment.insertReply(reply, 0)
            val newList = oldList.replaceAt(oldPosition, newComment)
            oldListResult.copy(dataList = newList)
        }
        updateCommentList(newListResult)
    }

    fun deleteReply(
        reply: PostReply,
        replyPosition: Int,
        commentPosition: Int,
        isAuthorReply: Boolean
    ) {
        repo.deletePostReply(reply.replyId).map {
            trackDelete(reply.replyId, isMe(reply.uid), 2L)
            assert(replySet.contains(reply.replyId))
            val oldListResult = oldListResult ?: return@map null
            val oldList = oldListResult.dataList
            val (oldPosition, oldComment) = oldList.getWithIndex(commentPosition) { e ->
                e.commentId == reply.commentId
            } ?: return@map null
            replySet.remove(reply.replyId)
            val newComment = oldComment.deleteReply(reply, replyPosition, isAuthorReply)
            val newList = oldList.replaceAt(oldPosition, newComment)
            oldListResult.copy(dataList = newList) to context.getString(R.string.reply_deleted)
        }.execute {
            handleExecute(it)
        }
    }

    fun likeReply(
        reply: PostReply,
        replyPosition: Int,
        commentPosition: Int,
        isAuthorReply: Boolean
    ) = withState { s ->
        val oldListResult = s.oldListResult ?: return@withState
        val oldList = oldListResult.dataList
        val (oldPosition, oldComment) = oldList.getWithIndex(commentPosition) { e ->
            e.commentId == reply.commentId
        } ?: return@withState
        val newReply = reply.switchLike()
        val newComment = oldComment.replaceReply(newReply, replyPosition, isAuthorReply)
        val newList = oldList.replaceAt(oldPosition, newComment)
        val newListResult = oldListResult.copy(dataList = newList)
        updateCommentList(newListResult)
        repo.saveOpinion(OpinionRequestBody.replyLike(reply.replyId, newReply.isLike))
            .execute {
                this
            }
    }

    fun loadMoreReplies(
        comment: PostComment,
        commentPosition: Int
    ) {
        if (comment.isCollapse) {
            collapseReply(comment, commentPosition, false)
        } else {
            if (comment.loading) return
            val page: Int
            val pageSize: Int
            when (comment.replyStatus) {
                PostComment.REPLY_STATUS_INIT -> {
                    page = 1
                    pageSize = comment.initAuthorReplyCount + 3
                }

                PostComment.REPLY_STATUS_FIRST -> {
                    page = 1
                    pageSize = comment.initAuthorReplyCount + 13
                }

                else -> {
                    page = comment.page + 1
                    pageSize = REPLY_SIZE_NORMAL
                }
            }
            repo.getPostReplyListV2(
                PostReplyListRequestBody(
                    pageSize,
                    comment.commentId,
                    PostReplyListRequestBody.QUERY_DEPTH,
                    pageNum = page
                )
            ).map {
                assert(commentSet.contains(comment.commentId))
                val oldListResult = oldListResult ?: return@map null
                val oldList = oldListResult.dataList
                val (oldPosition, oldComment) = oldList.getWithIndex(commentPosition) { e ->
                    e.commentId == comment.commentId
                } ?: return@map null
                val takeReplySize = if (pageSize == REPLY_SIZE_NORMAL) {
                    REPLY_SIZE_NORMAL
                } else {
                    pageSize - comment.initAuthorReplyCount
                }
                val tempReplySet = HashSet<String>()
                val newReplyList = it.dataList!!.filter { reply ->
                    if (replySet.contains(reply.replyId) || tempReplySet.contains(reply.replyId)) {
                        false
                    } else {
                        tempReplySet.add(reply.replyId)
                        true
                    }
                }.take(takeReplySize)
                newReplyList.forEach { reply ->
                    replySet.add(reply.replyId)
                }
                val newComment = oldComment.insertReplies(newReplyList, -1, it)
                val newList = oldList.replaceAt(oldPosition, newComment)
                oldListResult.copy(dataList = newList)
            }.execute { result ->
                when (result) {
                    is Success -> {
                        val data = result.invoke()
                        if (data != null) {
                            updateCommentList(data)
                        } else {
                            this
                        }
                    }

                    is Fail -> {
                        updateCommentLoadingStatus(comment, commentPosition, false)
                    }

                    else -> {
                        updateCommentLoadingStatus(comment, commentPosition, true)
                    }
                }
            }
        }
    }

    fun collapseReply(
        comment: PostComment,
        commentPosition: Int,
        toCollapse: Boolean
    ) = withState { s ->
        if (comment.collapse == toCollapse) return@withState
        val newComment = comment.collapse(toCollapse)
        val oldListResult = s.oldListResult ?: return@withState
        val oldList = oldListResult.dataList
        val newList = oldList.replaceAt(commentPosition, newComment)
        val newListResult = oldListResult.copy(dataList = newList)
        updateCommentList(newListResult)
    }
    //endregion

    //region helper
    private fun S.updateCommentLoadingStatus(
        comment: PostComment,
        commentPosition: Int,
        loading: Boolean
    ): S {
        if (!commentSet.contains(comment.commentId)) return this
        val oldListResult = oldListResult ?: return this
        val oldList = oldListResult.dataList
        val (oldPosition, oldComment) = oldList.getWithIndex(commentPosition) { e ->
            e.commentId == comment.commentId
        } ?: return this
        val newComment = oldComment.updateLoadingStatus(loading)
        val newList = oldList.replaceAt(oldPosition, newComment)
        val newListResult = oldListResult.copy(dataList = newList)
        return updateCommentList(newListResult)
    }

    fun isMe(uid: String?) = accountInteractor.isMe(uid)

    fun isCreator(uid: String?) =
        !uid.isNullOrBlank() && authorId == uid

    val myUuid: String?
        get() = accountInteractor.curUuidOrNull

    val iAmCreator: Boolean
        get() = isCreator(myUuid)

    protected open fun trackDelete(id: String, isMe: Boolean, type: Long) {

    }

    fun getReplyTarget() = this.replyTarget

    fun setReplyTarget(replyTarget: AddPostCommentReplyTarget) {
        this.replyTarget = replyTarget
    }

    fun clearReplyTarget() {
        this.replyTarget = null
    }

    @CallSuper
    open fun clearCommentPinRedDot() {
        accountInteractor.showCommentPinRedDot = false
        ClearRedDotEvent.postCommentPin()
    }

    override fun onCleared() {
        this.replyTarget = null
        commentSet.clear()
        replySet.clear()
        super.onCleared()
    }
}