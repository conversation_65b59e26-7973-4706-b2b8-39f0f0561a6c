package com.socialplay.gpark.ui.gamedetail.cache

import android.content.Context
import android.graphics.Color
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.socialplay.gpark.databinding.FragmentGameDetailCommonBinding
import timber.log.Timber
import java.lang.ref.WeakReference

/**
 * GameDetail页面ViewBinding缓存管理器
 * 专门管理FragmentGameDetailCommonBinding的缓存和复用
 */
class GameDetailBindingCache private constructor() :
    ViewBindingCacheManager<FragmentGameDetailCommonBinding>(
        maxCacheSize = 3,  // 最多缓存3个实例
        minCacheSize = 1   // 始终保持1个实例
    ), DefaultLifecycleObserver {

    companion object {
        private const val TAG = "ViewBindingCache-Detail"

        @Volatile
        private var INSTANCE: GameDetailBindingCache? = null

        fun getInstance(): GameDetailBindingCache {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: GameDetailBindingCache().also {
                    INSTANCE = it
                    // 注册应用生命周期监听
                    ProcessLifecycleOwner.get().lifecycle.addObserver(it)
                }
            }
        }
    }

    private val mainHandler = Handler(Looper.getMainLooper())
    private var contextRef: WeakReference<Context>? = null
    private var isPreloaded = false

    // 定期清理任务
    private val cleanupRunnable = object : Runnable {
        override fun run() {
            cleanExpiredCache()
            // 每5分钟执行一次清理
            mainHandler.postDelayed(this, 5 * 60 * 1000L)
        }
    }

    override fun createBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentGameDetailCommonBinding {
        // 获取调用栈信息，区分是直接使用还是预加载
        val stackTrace = Thread.currentThread().stackTrace
        val isFromPreload = stackTrace.any { it.methodName.contains("preload") }
        val caller = if (isFromPreload) "preload" else "direct use"

        Timber.tag(TAG).d("Creating new FragmentGameDetailCommonBinding (caller: $caller)")
        return FragmentGameDetailCommonBinding.inflate(inflater, container, false)
    }

    override fun resetBinding(binding: FragmentGameDetailCommonBinding) {
        try {
            // 采用保守的重置策略，只清理必要的内容
            // 避免过度重置导致UI效果丢失

            // 清理可能导致内存泄漏的监听器
            clearListeners(binding)

            // 清理图片资源（防止内存泄漏）
            clearImageResources(binding)

            // 重置Banner状态（确保Banner正常工作）
            resetBannerState(binding)

            // 重置Glide状态（确保图片能正常加载）
            resetGlideState(binding)

            // 最小化的UI重置（只重置必要的状态）
            minimalUIReset(binding)

            Timber.tag(TAG).d("Binding reset completed")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to reset binding")
        }
    }

    override fun isBindingReusable(binding: FragmentGameDetailCommonBinding): Boolean {
        return try {
            // 检查root view是否还有效
            binding.root.parent == null && !binding.root.isAttachedToWindow
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "Binding reusability check failed")
            false
        }
    }

    /**
     * 初始化预加载
     * 在应用启动时调用，但延迟到有Activity Context时再预创建缓存实例
     */
    fun initPreload(context: Context) {
        if (isPreloaded) return

        contextRef = WeakReference(context)

        // 在主线程中预加载
        mainHandler.post {
            try {
                // 检查是否有Activity Context可用
                val activityContext = getActivityContext(context)
                if (activityContext != null) {
                    val inflater = LayoutInflater.from(activityContext)
                    preloadCache(inflater, null, minCacheSize)
                    isPreloaded = true

                    // 启动定期清理任务
                    mainHandler.postDelayed(cleanupRunnable, 5 * 60 * 1000L)

                    Timber.tag(TAG).i("GameDetail binding cache preload completed")
                } else {
                    Timber.tag(TAG).w("No Activity context available for preload, will preload on first use")
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Failed to preload cache")
            }
        }
    }

    /**
     * 获取Activity Context
     * 确保有正确的主题环境用于ViewBinding创建
     */
    private fun getActivityContext(context: Context): Context? {
        return when (context) {
            is android.app.Activity -> context
            is androidx.appcompat.view.ContextThemeWrapper -> context
            is android.view.ContextThemeWrapper -> context
            else -> {
                // 尝试从Application Context获取当前Activity
                try {
                    val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
                    // 如果没有Activity Context，返回null，稍后在Fragment中再预加载
                    null
                } catch (e: Exception) {
                    null
                }
            }
        }
    }

    /**
     * 确保缓存中至少有一个实例
     * 优化：异步预加载，避免阻塞UI
     */
    fun ensureMinCache(inflater: LayoutInflater, container: ViewGroup?) {
        val stats = getCacheStats()
        val needCreate = minCacheSize - stats.cacheSize

        if (needCreate > 0) {
            Timber.tag(TAG).d("ensureMinCache: need to create $needCreate instances, current cache size: ${stats.cacheSize}")

            // 异步预加载，避免阻塞UI
            mainHandler.post {
                try {
                    preloadCache(inflater, container, needCreate)
                    if (!isPreloaded) {
                        isPreloaded = true
                        // 启动定期清理任务
                        mainHandler.postDelayed(cleanupRunnable, 5 * 60 * 1000L)
                    }
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "Failed to ensure min cache")
                }
            }
        } else {
            Timber.tag(TAG).d("ensureMinCache: cache is sufficient, current size: ${stats.cacheSize}")
        }
    }

    /**
     * 最小化UI重置
     * 只重置绝对必要的状态，保留所有UI效果和动画
     */
    private fun minimalUIReset(binding: FragmentGameDetailCommonBinding) {
        with(binding) {
            try {
                // 只重置滚动位置，保持UI展开状态
                abl.setExpanded(true, false)
                rvComment.scrollToPosition(0)
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "Failed to reset scroll position")
            }

            // 重置文本内容（但保留样式）
            tvGameName.text = ""
            tvDescription.text = ""
            tvUpdateTime.text = ""
            tvUsername.text = ""
            tvAuthorName.text = ""
            tvUserId.text = ""
            tvPortfolio.text = ""

            // 不重置背景色，保留原有的背景设置
            bgViewTopLayout.setBackgroundColor(Color.WHITE) // 注释掉，避免覆盖banner背景

            // 数值展示重置（时长、游玩、鲜花、点赞、评论、送花）
            tvHonorLikeCount.text = ""
            tvHonorCreateTime.text = ""
            tvHonorPlayCount.text = ""
            tvHonorFlowersCount.text = ""
//            tvLike.text = ""
//            tvLightUp.text = ""
//            tvComment.text = ""
//            tvSendFlowers.text = ""
            // Fragment重新使用时会重新设置这些内容
        }
    }

    /**
     * 清理图片资源
     * 防止内存泄漏，但保留Banner相关的状态
     */
    private fun clearImageResources(binding: FragmentGameDetailCommonBinding) {
        with(binding) {
            try {
                // 清理头像图片（防止内存泄漏）
                ivAvatar.setImageDrawable(null)
                ivUserAvatar1.setImageDrawable(null)
                ivUserAvatar2.setImageDrawable(null)
                ivUserAvatar3.setImageDrawable(null)
                ivAssetCover.setImageDrawable(null)

                // 不清理Banner的数据和状态，避免白图问题
                // gameBanner.setDatas(null)  // 注释掉，避免清理Banner数据
                // gameBanner.destroy()       // 注释掉，避免销毁Banner

                // Banner会在Fragment重新使用时被重新设置数据
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "Failed to clear image resources")
            }
        }
    }

    /**
     * 重置Banner状态
     * 确保Banner在缓存复用时能正常工作
     */
    private fun resetBannerState(binding: FragmentGameDetailCommonBinding) {
        with(binding) {
            try {
                // 重置Banner的可见性状态为GONE，让Fragment重新设置
                gameBanner.visibility = android.view.View.GONE
                gameBannerIndicator.visibility = android.view.View.GONE

                // 重置Banner的位置到第一页
                if (gameBanner.adapter != null && gameBanner.adapter!!.itemCount > 0) {
                    gameBanner.currentItem = 0
                }

                // 不销毁adapter，保持Banner的基本结构
                // Fragment重新使用时会调用updateBanner重新设置数据

            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "Failed to reset banner state")
            }
        }
    }


    /**
     * 清理监听器
     * 只清理可能导致内存泄漏的关键监听器
     */
    private fun clearListeners(binding: FragmentGameDetailCommonBinding) {
        with(binding) {
            try {
                // 清理可能导致内存泄漏的监听器
                mrl.setOnRefreshListener(null)
                rvToolkit.onOverScrollTriggered = null
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "Failed to clear listeners")
            }

            // 其他监听器会在Fragment重新使用时被重新设置
            // 不需要在这里清理，避免影响UI效果
        }
    }

    /**
     * 重置Glide状态
     * 确保Glide在缓存复用时能正常工作
     */
    private fun resetGlideState(binding: FragmentGameDetailCommonBinding) {
        try {
            val context = binding.root.context
            if (context != null) {
                // 确保Glide请求处于恢复状态，避免白图问题
                com.bumptech.glide.Glide.with(context).resumeRequests()
                Timber.tag(TAG).d("Glide requests resumed for cached binding")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "Failed to reset Glide state")
        }
    }

    // 应用生命周期回调
    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        // 应用进入前台时，确保有足够的缓存
        contextRef?.get()?.let { context ->
            mainHandler.post {
                val inflater = LayoutInflater.from(context)
                ensureMinCache(inflater, null)
            }
        }
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        // 应用进入后台时，清理过期缓存
        mainHandler.post {
            cleanExpiredCache(2 * 60 * 1000L) // 2分钟过期
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        // 应用销毁时，清理所有资源
        mainHandler.removeCallbacks(cleanupRunnable)
        clearCache()
        contextRef?.clear()
        contextRef = null
    }

    /**
     * 获取缓存状态信息（用于调试）
     */
    fun logCacheStats() {
        val stats = getCacheStats()
        Timber.tag(TAG).i(
            "Cache Stats - Size: ${stats.cacheSize}, " +
                    "Hit: ${stats.hitCount}, Miss: ${stats.missCount}, " +
                    "Created: ${stats.createCount}, Hit Rate: ${"%.2f".format(stats.hitRate * 100)}%"
        )
    }
}
