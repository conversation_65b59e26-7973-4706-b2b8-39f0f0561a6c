package com.socialplay.gpark.ui.gamedetail.unify

import android.animation.ValueAnimator
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.CallSuper
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.graphics.ColorUtils
import androidx.core.text.set
import androidx.core.view.ViewCompat
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.PagingApiResult
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.ImageInfo
import com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest
import com.socialplay.gpark.data.model.gamedetail.LikeAndPlayerItemData
import com.socialplay.gpark.data.model.gift.SendGiftConditionsInfo
import com.socialplay.gpark.data.model.outfit.UgcDesignToolkit
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.post.event.GameMetaData
import com.socialplay.gpark.data.model.post.event.GameMetaDataUpdateEvent
import com.socialplay.gpark.data.model.share.ShareFeature
import com.socialplay.gpark.databinding.FragmentGameDetailCommonBinding
import com.socialplay.gpark.databinding.PopUpGameDetailCommonBinding
import com.socialplay.gpark.databinding.PopUpGameDetailCommonCommentBinding
import com.socialplay.gpark.ui.gamedetail.cache.GameDetailBindingCache
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.IGlobalShareCallback
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.core.views.empty
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.core.views.spacer
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.dialog.FlowerGiftingGuidelineDialog
import com.socialplay.gpark.ui.dialog.LoadingDialogFragment
import com.socialplay.gpark.ui.editor.detail.comment.CommentMorePopup
import com.socialplay.gpark.ui.editor.detail.comment.IUgcCommentListener
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentExpandItem
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentItem
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentLoading
import com.socialplay.gpark.ui.editor.detail.comment.ugcReplyItem
import com.socialplay.gpark.ui.feedback.FeedbackTypeWrapper
import com.socialplay.gpark.ui.gamedetail.ClickType
import com.socialplay.gpark.ui.gamedetail.GameDetailAnalyticsObserver
import com.socialplay.gpark.ui.gamedetail.dialog.FlowerLeaderboardDialog
import com.socialplay.gpark.ui.gamedetail.dialog.LikeAndPlayerListFragmentDialog
import com.socialplay.gpark.ui.gamedetail.lightup.SparkDialog
import com.socialplay.gpark.ui.gamedetail.sendflower.SendFlowerConditionDialog
import com.socialplay.gpark.ui.gamepay.PayDialogStyle
import com.socialplay.gpark.ui.gamepay.PayResult
import com.socialplay.gpark.ui.gamepay.PayScene
import com.socialplay.gpark.ui.outfit.BuyUgcDesignState
import com.socialplay.gpark.ui.outfit.BuyUgcDesignViewModel
import com.socialplay.gpark.ui.toolkit.IUgcItemClickedListener
import com.socialplay.gpark.ui.toolkit.ToolkitRecommendState
import com.socialplay.gpark.ui.toolkit.ToolkitUsedItem
import com.socialplay.gpark.ui.toolkit.UgcDesignToolkitViewModel
import com.socialplay.gpark.ui.view.center.CenterImageSpan
import com.socialplay.gpark.util.DateUtil.formatUpdateDate
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.addListener
import com.socialplay.gpark.util.extension.addModelBuildListener
import com.socialplay.gpark.util.extension.addOnOffsetChangedListener
import com.socialplay.gpark.util.extension.addUpdateListener
import com.socialplay.gpark.util.extension.alphaColor
import com.socialplay.gpark.util.extension.attach
import com.socialplay.gpark.util.extension.attachV2
import com.socialplay.gpark.util.extension.clearCompoundDrawables
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.getDrawableByRes
import com.socialplay.gpark.util.extension.getLocationInWindow
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setDraggableForever
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.youth.banner.listener.OnPageChangeListener
import com.zhpan.indicator.enums.IndicatorSlideMode
import com.zhpan.indicator.enums.IndicatorStyle
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.koin.core.context.GlobalContext
import timber.log.Timber
import kotlin.math.abs

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/22
 *     desc   :
 * </pre>
 */
abstract class BaseGameDetailCommonFragment :
    BaseRecyclerViewFragment<FragmentGameDetailCommonBinding>(R.layout.fragment_game_detail_common),
    IGlobalShareCallback {

    companion object {
        const val GAME_TYPE_PGC = 1
        const val GAME_TYPE_UGC = 2
        const val GAME_TYPE_UGC_DESIGN = 3

        const val FEAT_REPORT = 1
        const val FEAT_SEND_FLOWERS = 2

        /**
         * 作品访问权限设置
         */
        const val FEAT_ACCESS_SETTINGS = 3

        /**
         * 作品置顶
         */
        const val FEAT_PIN = 4

        /**
         * 作品取消置顶
         */
        const val FEAT_UN_PIN = 5

        /**
         * 最多允许显示几张banner图片
         */
        const val MAX_BANNER_IMAGE_COUNT = 5
    }

    val sharedViewModel: GameDetailEditViewModel by activityViewModels()

    val fragmentResult = Bundle()

    private var bgMainColor: Int? = null
    private var isBgMainColorWhite: Boolean? = null

    private var scrollOffset = 0

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvComment

    protected val commonVm: GameDetailCommonViewModel by fragmentViewModel()
    protected val ugcDesignToolkitVm: UgcDesignToolkitViewModel by fragmentViewModel()
    protected val buyViewModel: BuyUgcDesignViewModel by fragmentViewModel()
    private val metaKV: MetaKV = GlobalContext.get().get<MetaKV>()

    protected var scrollToTop = false
    protected var needLocate = false
    protected var locatePosition = 0
    protected var shouldPlayLikeAnim = false

    /**
     * 点赞数
     */
    private var likeCount = 0L

    /**
     * 新增点赞数
     */
    private var incrementLikeCount = 0L

    /**
     * 游玩数
     */
    private var playerCount = 0L

    /**
     * 送花数量
     */
    private var flowerCount = 0L

    /**
     * 新增送花数
     */
    private var incrementFlowerCount = 0L

    val analyticsObserve: GameDetailAnalyticsObserver by lazy { GameDetailAnalyticsObserver() }

    protected val commentMorePopup: CommentMorePopup by lazy { CommentMorePopup(layoutInflater) }

    protected var pendingRefreshAnim: ValueAnimator? = null

    private var loadingDialog: LoadingDialogFragment? = null

    protected var trackGameReviewShow = true
    protected val tagsEpoxyController by lazy { tagsEpoxyController() }
    protected val toolkitEpoxyController by lazy { toolkitEpoxyController() }
    private var toolkitVisibilityTracker: EpoxyVisibilityTracker? = null

    private val ugcClickedListener = object : IUgcItemClickedListener {
        private fun trackGetToolkitClick(item: UgcDesignToolkit, isSuccess: Boolean) {
            Analytics.track(
                EventConstants.LIBRARY_METARIAL_GET_CLICK,
                "metrialidid" to item.guid.orEmpty(),
                "show_categoryid" to CategoryId.GAME_DETAIL_ASSETS_TOOLKIT,
                "authorid" to item.uuid.orEmpty(),
                "result" to (if (isSuccess) "0" else "1"),
                "type" to (if (item.isCloth) {
                    "0"
                } else if (item.isMod) {
                    "1"
                } else {
                    ""
                }),
                "is_price" to (if (item.isPriced == true) "yes" else "no"),
            )
        }

        override fun onBuyClicked(item: UgcDesignToolkit) {
            val needPurchase = item.needPurchase
            if (needPurchase) {
                loadingDialog?.dismissAllowingStateLoss()
                loadingDialog = MetaRouter.Dialog.loading(childFragmentManager)
                buyViewModel.buy(
                    lifecycleOwner = viewLifecycleOwner,
                    commodityId = item.commodityId,
                    onStartPay = { payInfo ->
                        loadingDialog?.dismissAllowingStateLoss()
                        loadingDialog = null
                        payInfo.copy(
                            productCode = item.commodityId ?: "",
                            productName = item.title ?: "",
                            productCount = 1,
                            sceneCode = PayScene.MODULE.value,
                            source = "detail",
                            metrialidid = item.guid,
                            payDialogStyle = PayDialogStyle.BOTTOM,
                            productImageUrl = item.cover ?: "",
                            productSoldCount = item.purchaseCount ?: 0L,
                            creatorName = item.userName ?: "",
                            creatorAvatarUrl = item.userIcon ?: "",
                        )
                    },
                    onPayResult = { payResult ->
                        loadingDialog?.dismissAllowingStateLoss()
                        loadingDialog = null
                        trackGetToolkitClick(item, payResult.isSuccess)
                        if (payResult.isSuccess) {
                            toast(R.string.assets_obtain_success)
                            // 支付成功,处理支付成功逻辑,更新Item展示状态
                            ugcDesignToolkitVm.buyUgcDesignSuccess(gameId, item)
                        } else if (payResult.resultCode == PayResult.RESULT_CODE_FAILED) {
                            // 支付失败,处理支付失败逻辑
                            toast(payResult.failedReason ?: getString(R.string.common_failed))
                        } else {
                            // 用户取消
                        }
                    }
                )
            } else {
                // 无需购买, 直接获取
                val feedId = item.feedId ?: return
                buyViewModel.get(
                    viewLifecycleOwner,
                    feedId,
                    onGetResult = { getResult ->
                        if (getResult is Success) {
                            trackGetToolkitClick(item, true)
                            // 获取成功,处理支付成功逻辑,更新Item展示状态
                            ugcDesignToolkitVm.buyUgcDesignSuccess(gameId, item)
                            toast(R.string.assets_obtain_success)
                        } else if (getResult is Fail) {
                            trackGetToolkitClick(item, false)
                            toast(getResult.invoke())
                        }
                    })
            }
        }

        override fun onClicked(item: UgcDesignToolkit) {
            item.feedId ?: return
            MetaRouter.UgcDesign.detail(
                this@BaseGameDetailCommonFragment,
                item.feedId,
                // 这个参数用于埋点
                CategoryId.GAME_DETAIL_ASSETS_TOOLKIT.toInt(),
            )
        }

        override fun onItemVisible(item: UgcDesignToolkit) {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_SHOW,
                "metrialidid" to item.guid.orEmpty(),
                "show_categoryid" to CategoryId.GAME_DETAIL_ASSETS_TOOLKIT,
                "authorid" to item.uuid.orEmpty(),
                "type" to (if (item.isCloth) {
                    "0"
                } else if (item.isMod) {
                    "1"
                } else {
                    ""
                }),
                "is_recreate" to (if (item.editable == true) "yes" else "no"),
                "is_price" to (if (item.isPriced == true) "yes" else "no")
            )
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentGameDetailCommonBinding? {
        val cache = GameDetailBindingCache.getInstance()
        return cache.getBinding(inflater, container)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        tagsEpoxyController.onRestoreInstanceState(savedInstanceState)
        toolkitEpoxyController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onResume() {
        super.onResume()
        toolkitVisibilityTracker?.clearVisibilityStates()
        toolkitVisibilityTracker?.requestVisibilityCheck()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        tagsEpoxyController.onSaveInstanceState(outState)
        toolkitEpoxyController.onSaveInstanceState(outState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.rvTags.setController(tagsEpoxyController)
        binding.rvToolkit.setController(toolkitEpoxyController)
        toolkitVisibilityTracker = EpoxyVisibilityTracker().attach(viewLifecycleOwner, binding.rvToolkit)
        // Item 有 50% 的部分显示出来时
        toolkitVisibilityTracker?.partialImpressionThresholdPercentage = 50
        childFragmentManager.setFragmentResultListener(
            GameDetailMoreDialog.TAG,
            viewLifecycleOwner
        ) { _, bundle ->
            val function = bundle.getInt(GameDetailMoreDialog.KEY_FUNCTION)
            when (function) {
                GameDetailMoreDialog.FUNCTION_FEEDBACK -> {
                    MetaRouter.Feedback.feedback(
                        this,
                        gameId,
                        SubmitNewFeedbackRequest.SOURCE_GAME_NUMBER,
                        FeedbackTypeWrapper.experience.id,
                        false,
                        needBackGame = false,
                        fromGameId = null
                    )
                }

                GameDetailMoreDialog.FUNCTION_SEND_FLOWERS -> {
                    showSendFlowerConditionDialog()
                }
            }
        }

        trackGameReviewShow = true

        binding.bottomCommentInput.lavLikeAnim.setMinAndMaxProgress(0.0f, 1.0f)

        binding.tbl.setOnBackAntiViolenceClickedListener {
            navigateUp()
        }
        binding.buildBtnLayout.setOnAntiViolenceClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.Build
            )
            MetaRouter.MobileEditor.creation(this, source = "build")
        }
        binding.ivMoreBtn.setOnAntiViolenceClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.More
            )
            Analytics.track(EventConstants.EVENT_FEEDBACK_SHOW_PAGE) {
                put("gameid", gameId)
                put("source", SubmitNewFeedbackRequest.SOURCE_GAME_NUMBER)
            }
            if (enableShare) {
                onMoreClick()
            } else {
                GameDetailMoreDialog.show(this, false, isMe() && PandoraToggle.enableGameGiftOption)
            }
        }
        binding.bottomCommentInput.setOnLightUpClickListener {
            Analytics.track(EventConstants.UGC_LIGHT_UP_CLICK)
            SparkDialog().show(childFragmentManager, "SparkDialog")
        }
        binding.bottomCommentInput.setOnSendFlowerClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.Flower
            )
            if (isMe()) {
                toast(R.string.toast_game_detail_send_flower_to_me)
                return@setOnSendFlowerClickListener
            }
            // 客态第一次点击需要先显示送花须知弹窗
            if (metaKV.account.showSendFlowerGuidelineDialog) {
                FlowerGiftingGuidelineDialog.show(childFragmentManager) {
                    // 只有用户点了我知道了, 才不弹弹窗
                    metaKV.account.showSendFlowerGuidelineDialog = false
                    showSendFlowerDialog("gamedetail")
                }
            } else {
                showSendFlowerDialog("gamedetail")
            }
        }

        binding.honorFlowersClick.setOnAntiViolenceClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.FlowerRank
            )
            Analytics.track(
                EventConstants.C_GAMEDETAIL_FLOWER_RANK_SHOW,
                "gameid" to gameId,
                "user_type" to if (isMe()) "owner" else "visitor",
                "creatortype" to gameType.toString(),
            )
            FlowerLeaderboardDialog.newInstance(
                gameId,
                gameType,
                isOwner = isMe(),
                onSendFlowerClicked = {
                    if (isMe()) {
                        toast(R.string.toast_game_detail_send_flower_to_me)
                        return@newInstance
                    }
                    showSendFlowerDialog("rank")
                }
            ).show(childFragmentManager, "FlowerLeaderboardDialog")
        }
        binding.honorPlayerAndLikesClick.setOnAntiViolenceClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.LikeList
            )
            LikeAndPlayerListFragmentDialog.newInstance(
                gameId,
                likeCount = likeCount.toInt(),
                playerCount = playerCount.toInt(),
                LikeAndPlayerListFragmentDialog.TYPE_LIKE,
                isOwner = isMe(),
                gameType
            ).show(childFragmentManager, "LikeAndPlayerListFragmentDialog")
        }

        binding.vToolkitAllClick.setOnAntiViolenceClickListener {
            MetaRouter.UgcDesign.recommendToolkitPage(
                this,
                isMe(),
                gameId
            )
        }

        // 先屏蔽左划逻辑
        binding.rvToolkit.enableBounce = false
        binding.rvToolkit.onOverScrollTriggered = {
            MetaRouter.UgcDesign.recommendToolkitPage(
                this@BaseGameDetailCommonFragment,
                isMe(),
                gameId
            )
        }

        binding.abl.addOnOffsetChangedListener(viewLifecycleOwner) { _, offset ->
            binding.bgViewTopLayout.translationY = offset.toFloat()
            scrollOffset = offset
            updateUiByOffset(offset, updateAuthorNameColor = true, force = false)
        }
        binding.abl.setDraggableForever(viewLifecycleOwner)
        epoxyController.addModelBuildListener(viewLifecycleOwner) {
            if (needLocate) {
                needLocate = false
                scrollToTop = false
                binding.abl.setExpanded(false, true)
                binding.rvComment.smoothScrollToPosition(
                    locatePosition.coerceIn(
                        0,
                        epoxyController.adapter.itemCount - 1
                    )
                )
            } else if (scrollToTop) {
                scrollToTop = false
                binding.rvComment.scrollToPosition(0)
            }
        }
        binding.rvNotice.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
        EpoxyVisibilityTracker().attachV2(viewLifecycleOwner, binding.rvComment)
        initPopup()

        commonVm.onEach(
            GameDetailCommonState::pinTopResult,
            deliveryMode = uniqueOnly(),
        ) { pinTopResultAsync ->
            if (pinTopResultAsync is Loading) {
                return@onEach
            }
            val pinTopResult = pinTopResultAsync.invoke()
            val isPinTop = pinTopResult?.first
            val result = pinTopResult?.second
            if (isPinTop != null && result != null) {
                if (result is DataResult.Success) {
                    toast(
                        if (isPinTop) {
                            R.string.toast_pin_game_success
                        } else {
                            R.string.toast_cancel_pin_game_success
                        }
                    )
                    updatePin(isPinTop)
                    sharedViewModel.pinTop(gameId, isPinTop)
                } else {
                    toast(
                        result.message ?: getString(
                            if (isPinTop) {
                                R.string.toast_pin_game_failed
                            } else {
                                R.string.toast_cancel_pin_game_failed
                            }
                        )
                    )
                }
            }
        }

        if (bgMainColor == null || isBgMainColorWhite == null) {
            // 先将控件颜色改为默认颜色
            updateByNoneBgMainColor()

            commonVm.onEach(
                GameDetailCommonState::bgMainColor,
            ) { bgMainColorPair ->
                if (bgMainColorPair == null) {
                    return@onEach
                }
                val bgMainColor = bgMainColorPair.first
                val fromCache = bgMainColorPair.second
                this.bgMainColor = bgMainColor
                val whiteColor = getColorByRes(R.color.white)
                isBgMainColorWhite = bgMainColor == whiteColor

                updateBgMainColor(bgMainColor, false)
                if (!isBgMainColorWhite!!) {
                    updateExtraColor(bgMainColor)
                    updateUiByOffset(scrollOffset, updateAuthorNameColor = false, force = true)
                }
            }
        } else {
            updateExtraColor(bgMainColor!!)
            // 已经存在缓存颜色了
            updateBgMainColor(bgMainColor!!, false)
        }

        commonVm.onEach(
            GameDetailCommonState::enableSendFlower,
        ) { enableSendFlower ->
            updateUIBySendFlowerEnable(enableSendFlower)
        }
        commonVm.loadSendFlowerConditions(gameId)

        binding.gameBanner.addOnPageChangeListener(object : OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
            }

            override fun onPageScrollStateChanged(state: Int) {
                val glide = Glide.with(this@BaseGameDetailCommonFragment)
                if (state == ViewPager2.SCROLL_STATE_IDLE) {
                    glide.resumeRequests()
                } else {
                    glide.pauseRequests()
                }
            }
        })

        ugcDesignToolkitVm.onAsync(
            ToolkitRecommendState::usedToolkitList,
            deliveryMode = uniqueOnly(),
            onFail = { _, _ ->
                visibleList(
                    binding.tvToolkitTitle,
                    binding.tvToolkitAll,
                    binding.ivToolkitAll,
                    binding.vToolkitAllClick,
                    binding.rvToolkit,
                    visible = false
                )
            },
            onLoading = { usedToolkitList ->
                visibleList(
                    binding.tvToolkitTitle,
                    binding.tvToolkitAll,
                    binding.ivToolkitAll,
                    binding.vToolkitAllClick,
                    binding.rvToolkit,
                    visible = !usedToolkitList?.second.isNullOrEmpty()
                )
            }
        ) { usedToolkitList ->
            visibleList(
                binding.tvToolkitTitle,
                binding.tvToolkitAll,
                binding.ivToolkitAll,
                binding.vToolkitAllClick,
                binding.rvToolkit,
                visible = usedToolkitList.second.isNotEmpty()
            )
        }

        buyViewModel.registerAsyncErrorToast(BuyUgcDesignState::getResult)

        // ensureMinCache已移至onCreateViewBinding中，避免重复调用
    }

    /**
     * 一些View无需参与颜色渐变
     */
    private fun updateExtraColor(targetColor: Int) {
        // 下拉刷新的转圈颜色
        binding.mrl.setCircleProgressColor(getColorByRes(R.color.white))
        // 图片指示器颜色
        binding.gameBannerIndicator.setSliderColor(
            getColorByRes(R.color.white_40),
            getColorByRes(R.color.white)
        )
        binding.gameBannerIndicator.notifyDataChanged()

        val panelBgColor = targetColor.alphaColor(0.05f)
        // 荣耀面板
        binding.honorPanel.background = GradientDrawable().apply {
            shape = GradientDrawable.RECTANGLE
            setColor(panelBgColor)
            cornerRadius = dp(8).toFloat()
        }
        val honorDividerColor = targetColor.alphaColor(0.1f)
        binding.honorDivider0.setBackgroundColor(honorDividerColor)
        binding.honorDivider1.setBackgroundColor(honorDividerColor)
        binding.honorDivider2.setBackgroundColor(honorDividerColor)
        binding.honorDivider3.setBackgroundColor(honorDividerColor)

        binding.rvNotice.background = GradientDrawable().apply {
            shape = GradientDrawable.RECTANGLE
            setColor(panelBgColor)
            cornerRadius = dp(8).toFloat()
        }
    }

    /**
     * 还没吸取到颜色时, 当前页面各个View显示的颜色
     */
    private fun updateByNoneBgMainColor() {
        if (!isBindingAvailable()) {
            return
        }
        // 还没吸取到颜色的时候
        val whiteColor = getColorByRes(R.color.white)
        val defaultColor = getColorByRes(R.color.color_F0F0F0)
        binding.tbl.setBackIcon(getDrawableByRes(R.drawable.ic_feat_24_1a1a1a_back))
        binding.tvAuthorName.setTextColor(defaultColor)
        binding.tvGameName.setTextColor(defaultColor)

        binding.updateBtn.setTextColor(defaultColor)
        binding.updateBtn.background = GradientDrawable().apply {
            shape = GradientDrawable.RECTANGLE
            setStroke(
                dp(1),
                defaultColor
            )
            cornerRadius = dp(100).toFloat()
        }

        binding.buildBtn.setTextColor(defaultColor)
        binding.buildBtnLayout.background = GradientDrawable().apply {
            shape = GradientDrawable.RECTANGLE
            setStroke(
                dp(1),
                defaultColor
            )
            cornerRadius = dp(100).toFloat()
        }
        binding.buildBtn.compoundDrawables(left = getDrawableByRes(R.drawable.icon_game_detail_build)?.apply {
            mutate().setTint(defaultColor)
        })

        binding.dpbEnter2.setTextColor(whiteColor)
        binding.dpbEnter2.setTextCoverColor(whiteColor)
        binding.dpbEnter2.mBackgroundStartColor = defaultColor
        binding.dpbEnter2.mBackgroundEndColor = defaultColor
        binding.dpbEnter2.invalidate()
    }

    private fun updateBgMainColor(targetColor: Int, withAnimation: Boolean = false) {
        val defaultColor = getColorByRes(R.color.color_F0F0F0)
        val whiteColor = getColorByRes(R.color.white)
        val isWhiteColor = whiteColor == targetColor

        val targetAuthorNameColorWhiteBg = getColorByRes(R.color.colorPrimaryDark)
        val targetAuthorNameColorColorBg = whiteColor
        val targetAuthorNameColor = if (isWhiteColor) {
            targetAuthorNameColorWhiteBg
        } else {
            targetAuthorNameColorColorBg
        }

        val targetGameNameColor = targetAuthorNameColor

        val targetUpdateBtnColor = if (isWhiteColor) {
            getColorByRes(R.color.color_4AB4FF)
        } else {
            targetColor
        }

        val targetUpdateBtnContentColor = if (isWhiteColor) {
            getColorByRes(R.color.color_EFF7FF)
        } else {
            whiteColor
        }

        val targetBuildBtnColor = if (isWhiteColor) {
            getColorByRes(R.color.color_B57EFF)
        } else {
            targetColor
        }

        val targetBuildBtnContentColor = if (isWhiteColor) {
            getColorByRes(R.color.color_F9F5FF)
        } else {
            whiteColor
        }

        val targetPlayBtnColor = if (isWhiteColor) {
            getColorByRes(R.color.colorAccentPrimary)
        } else {
            targetColor
        }

        val targetPlayBtnTextColor = if (isWhiteColor) {
            getColorByRes(R.color.textColorPrimary)
        } else {
            whiteColor
        }

        if (withAnimation) {
            val animator = ValueAnimator.ofFloat(0f, 1f)
            animator.duration = 350
            animator.addUpdateListener(this) {
                if (!isBindingAvailable()) {
                    return@addUpdateListener
                }
                val percent = it.animatedValue as Float
                // 背景颜色
                binding.bgViewTopLayout.setBackgroundColor(
                    ColorUtils.blendARGB(
                        whiteColor,
                        targetColor,
                        percent
                    )
                )

                // 顶部游戏创作者的颜色
                if (isWhiteColor) {
                    binding.tvAuthorName.setTextColor(
                        ColorUtils.blendARGB(
                            defaultColor,
                            targetAuthorNameColor,
                            percent
                        )
                    )
                } else {
                    // 用户有可能已经滑动了一段距离, 所以需要根据滚动距离计算目标颜色
                    val targetAuthorNameColorByScrollOffset = ColorUtils.blendARGB(
                        targetAuthorNameColor,
                        targetAuthorNameColorWhiteBg,
                        getScrollPercent(scrollOffset)
                    )
                    binding.tvAuthorName.setTextColor(
                        ColorUtils.blendARGB(
                            defaultColor,
                            targetAuthorNameColorByScrollOffset,
                            percent
                        )
                    )
                }
                // 游戏标题颜色
                binding.tvGameName.setTextColor(
                    ColorUtils.blendARGB(
                        defaultColor,
                        targetGameNameColor,
                        percent
                    )
                )
                // 更新按钮
                val updatePercentColor = ColorUtils.blendARGB(
                    defaultColor,
                    targetUpdateBtnColor,
                    percent
                )
                binding.updateBtn.setBackgroundDrawable(GradientDrawable().apply {
                    shape = GradientDrawable.RECTANGLE
                    setStroke(
                        dp(1),
                        updatePercentColor
                    )
                    setColor(
                        ColorUtils.blendARGB(
                            whiteColor,
                            targetUpdateBtnContentColor,
                            percent
                        )
                    )
                    cornerRadius = dp(100).toFloat()
                })
                binding.updateBtn.setTextColor(updatePercentColor)

                // 建造按钮
                val buildPercentColor = ColorUtils.blendARGB(
                    defaultColor,
                    targetBuildBtnColor,
                    percent
                )
                binding.buildBtnLayout.background = GradientDrawable().apply {
                    shape = GradientDrawable.RECTANGLE
                    setStroke(
                        dp(1),
                        buildPercentColor
                    )
                    setColor(
                        ColorUtils.blendARGB(
                            whiteColor,
                            targetBuildBtnContentColor,
                            percent
                        )
                    )
                    cornerRadius = dp(100).toFloat()
                }
                binding.buildBtn.setTextColor(buildPercentColor)
                binding.buildBtn.compoundDrawables(left = getDrawableByRes(R.drawable.icon_game_detail_build)?.apply {
                    mutate().setTint(ColorUtils.blendARGB(buildPercentColor, whiteColor, 0.1f))
                })

                // 玩游戏按钮
                val playPercentColor = ColorUtils.blendARGB(
                    defaultColor,
                    targetPlayBtnColor,
                    percent
                )
                val actualPlayColor = ColorUtils.blendARGB(playPercentColor, whiteColor, 0.1f)
                val playTextColor =
                    ColorUtils.blendARGB(whiteColor, targetPlayBtnTextColor, percent)
                binding.dpbEnter2.setTextColor(playTextColor)
                binding.dpbEnter2.setTextCoverColor(playTextColor)
                binding.dpbEnter2.mBackgroundStartColor = actualPlayColor
                binding.dpbEnter2.mBackgroundEndColor = actualPlayColor
                binding.dpbEnter2.invalidate()
            }
            animator.start()
        } else {
            // 背景颜色
            binding.bgViewTopLayout.setBackgroundColor(targetColor)
            // 顶部游戏创作者的颜色
            binding.tvAuthorName.setTextColor(
                if (isWhiteColor) {
                    targetAuthorNameColor
                } else {
                    // 用户有可能已经滑动了一段距离, 所以需要根据滚动距离计算目标颜色
                    ColorUtils.blendARGB(
                        targetAuthorNameColor,
                        targetAuthorNameColorWhiteBg,
                        getScrollPercent(scrollOffset)
                    )
                }
            )
            // 游戏标题颜色
            binding.tvGameName.setTextColor(targetGameNameColor)
            // 更新按钮
            binding.updateBtn.setBackgroundDrawable(GradientDrawable().apply {
                shape = GradientDrawable.RECTANGLE
                setStroke(
                    dp(1),
                    targetUpdateBtnColor
                )
                setColor(targetUpdateBtnContentColor)
                cornerRadius = dp(100).toFloat()
            })
            binding.updateBtn.setTextColor(targetUpdateBtnColor)

            // 建造按钮
            binding.buildBtnLayout.background = GradientDrawable().apply {
                shape = GradientDrawable.RECTANGLE
                setStroke(
                    dp(1),
                    targetBuildBtnColor
                )
                setColor(targetBuildBtnContentColor)
                cornerRadius = dp(100).toFloat()
            }
            binding.buildBtn.setTextColor(targetBuildBtnColor)
            binding.buildBtn.compoundDrawables(left = getDrawableByRes(R.drawable.icon_game_detail_build)?.apply {
                mutate().setTint(ColorUtils.blendARGB(targetBuildBtnColor, whiteColor, 0.1f))
            })

            // 玩游戏按钮
            val actualPlayColor = ColorUtils.blendARGB(targetPlayBtnColor, whiteColor, 0.1f)
            binding.dpbEnter2.setTextColor(targetPlayBtnTextColor)
            binding.dpbEnter2.setTextCoverColor(targetPlayBtnTextColor)
            binding.dpbEnter2.mBackgroundStartColor = actualPlayColor
            binding.dpbEnter2.mBackgroundEndColor = actualPlayColor
            binding.dpbEnter2.invalidate()
        }
    }

    private fun getScrollPercent(scrollOffset: Int): Float {
        if (!isBindingAvailable()) {
            return 0f
        }
        // 从二级页面回到游戏详情页时, bgViewTopLayout.height 为 0, 这里直接用设计好的高度值
        // val bgHeight = binding.bgViewTopLayout.height
        val bgHeight = dp(439)
        var offset = if (scrollOffset >= 0) {
            0
        } else {
            abs(scrollOffset).coerceAtLeast(0).coerceAtMost(bgHeight)
        }
        return offset.toFloat() / bgHeight
    }

    private var lastScrollOffset = 0

    /**
     * 从默认颜色渐变为主题色时, updateAuthorNameColor 的颜色也在渐变, 这种情况下需要排除 authorNameColor 的颜色设置
     */
    private fun updateUiByOffset(
        scrollOffset: Int,
        updateAuthorNameColor: Boolean,
        force: Boolean = false
    ) {
        val bgHeight = binding.bgViewTopLayout.height
        var offset = if (isBgMainColorWhite ?: true) {
            // 如果背景颜色是白色, 最终的颜色和滑动超过 bgHeight 之后的效果差不多
            bgHeight
        } else if (scrollOffset >= 0) {
            0
        } else {
            abs(scrollOffset).coerceAtLeast(0).coerceAtMost(bgHeight)
        }
        if (!force && offset == lastScrollOffset) {
            return
        }
        lastScrollOffset = offset

        val percent = offset.toFloat() / bgHeight
        binding.tbl.setDividerColor(getColorByRes(R.color.color_account_line).alphaColor(percent))
        val percentColor = ColorUtils.blendARGB(
            getColorByRes(R.color.white),
            getColorByRes(R.color.color_1A1A1A),
            percent
        )
        binding.tbl.setBackIcon(getDrawableByRes(R.drawable.ic_feat_24_1a1a1a_back)?.apply {
            mutate().setTint(percentColor)
        })
        if (updateAuthorNameColor) {
            binding.tvAuthorName.setTextColor(percentColor)
        }
        if (binding.tvTopFollowBtn.isVisible) {
            if (isFollow) {
                val followPercentColor = ColorUtils.blendARGB(
                    getColorByRes(R.color.white_60),
                    getColorByRes(R.color.color_CCCCCC),
                    percent
                )
                binding.tvTopFollowBtn.setTextColor(followPercentColor)
                binding.tvTopFollowBtn.setBackgroundDrawable(GradientDrawable().apply {
                    shape = GradientDrawable.RECTANGLE
                    setStroke(
                        dp(0.5),
                        followPercentColor
                    )
                    cornerRadius = dp(128).toFloat()
                })
                binding.tvTopFollowBtn.clearCompoundDrawables(left = true)
            } else {
                val followPercentColor = ColorUtils.blendARGB(
                    getColorByRes(R.color.white),
                    getColorByRes(R.color.color_1A1A1A),
                    percent
                )
                binding.tvTopFollowBtn.setTextColor(followPercentColor)
                binding.tvTopFollowBtn.setBackgroundDrawable(GradientDrawable().apply {
                    shape = GradientDrawable.RECTANGLE
                    setStroke(
                        dp(0.5),
                        followPercentColor
                    )
                    cornerRadius = dp(128).toFloat()
                })
                binding.tvTopFollowBtn.compoundDrawables(left = getDrawableByRes(R.drawable.ic_add_1a1a1a_s12)?.apply {
                    mutate().setTint(followPercentColor)
                })
            }
        }
        val moreBgPercentColor = ColorUtils.blendARGB(
            getColorByRes(R.color.transparent),
            getColorByRes(R.color.color_F6F6F6),
            percent
        )
        val moreStrokePercentColor = ColorUtils.blendARGB(
            getColorByRes(R.color.white),
            getColorByRes(R.color.transparent),
            percent
        )
        val moreIconPercentColor = ColorUtils.blendARGB(
            getColorByRes(R.color.white),
            getColorByRes(R.color.color_1A1A1A),
            percent
        )
        binding.ivMoreBtn.setImageDrawable(
            getDrawableByRes(
                if (isMe()) {
                    R.drawable.ic_feat_24_1a1a1a_more_dot
                } else {
                    R.drawable.ic_feat_24_1a1a1a_share
                }
            )?.apply {
                mutate().setTint(moreIconPercentColor)
            })
    }

    private fun initPopup() {
        commentMorePopup.initPopup(
            onDefaultClick = {
                updateSortType(PostCommentListRequestBody.QUERY_TYPE_DEFAULT)
            },
            onNewestClick = {
                updateSortType(PostCommentListRequestBody.QUERY_TYPE_TOP)
            },
            onHotClick = {
                updateSortType(PostCommentListRequestBody.QUERY_TYPE_LIKE)
            },
            onAuthorOnlyClick = {
                updateFilterType(PostCommentListRequestBody.FILTER_AUTHOR)
            },
            onSelfOnlyClick = {
                updateFilterType(PostCommentListRequestBody.FILTER_SELF)
            },
        )

        binding.tvSortBtn.setOnAntiViolenceClickListener {
            commentMorePopup.showSortPopup(it, binding.root)
        }
    }

    fun updateBanner(imageInfoList: List<ImageInfo>?, bannerColor: String? = null) {
        if (imageInfoList.isNullOrEmpty()) {
            binding.gameBanner.gone()
            binding.gameBannerIndicator.gone()
        } else {
            // 最多显示5张图片
            val imageInfos = imageInfoList.take(MAX_BANNER_IMAGE_COUNT)
            commonVm.updateFirstImage(glide, imageInfos.first().url, bannerColor)
            val size = imageInfos.size
            binding.gameBanner.visible()
            if (size > 1) {
                val maxIndicatorWidth = screenWidth - dp(16) * 2
                val sliderGap = dp(8)
                val totalSliderGap = sliderGap * (MAX_BANNER_IMAGE_COUNT - 1)
                val sliderWidth = (maxIndicatorWidth - totalSliderGap) / MAX_BANNER_IMAGE_COUNT
                binding.gameBannerIndicator.visible()
                binding.gameBannerIndicator.apply {
                    val dp4 = dp(4).toFloat()
                    setIndicatorStyle(IndicatorStyle.ROUND_RECT)
                    setSliderWidth(sliderWidth.toFloat(), sliderWidth.toFloat())
                    setSliderHeight(dp4)
                    setSlideMode(IndicatorSlideMode.NORMAL)
                    setSliderGap(sliderGap.toFloat())
                    setPageSize(size)
                    notifyDataChanged()
                }
                binding.gameBannerIndicator.setCurrentPosition(0)
            }
            binding.gameBanner.startPosition = 1
            binding.gameBanner.registerOnPageCallback()
                .addBannerResumeLifecycleObserver(viewLifecycleOwner)
                .setAdapter(
                    BannerImageInfoAdapter(
                        imageInfos,
                        marginLeft = dp(16),
                        marginRight = dp(16)
                    )
                )
                .isAutoLoop(false)
                //.setLoopTime(5_000L)
                .setOnBannerListener { _, position ->
                    // ignore
                }.addOnPageChangeListener(object : OnPageChangeListener {
                    override fun onPageScrolled(
                        position: Int,
                        positionOffset: Float,
                        positionOffsetPixels: Int
                    ) {
                        binding.gameBannerIndicator.onPageScrolled(
                            position,
                            positionOffset,
                            positionOffsetPixels
                        )
                    }

                    override fun onPageSelected(position: Int) {
                        binding.gameBannerIndicator.onPageSelected(position)
                    }

                    override fun onPageScrollStateChanged(state: Int) {
                        binding.gameBannerIndicator.onPageScrollStateChanged(state)
                    }
                })
        }
    }

    protected fun updateSortTypeText(sortType: Int, filterType: Int?) {
        val resId = when (filterType) {
            PostCommentListRequestBody.FILTER_AUTHOR -> {
                R.string.sort_author_only
            }

            PostCommentListRequestBody.FILTER_SELF -> {
                R.string.sort_self_only
            }

            else -> {
                when (sortType) {
                    PostCommentListRequestBody.QUERY_TYPE_DEFAULT -> {
                        R.string.sort_hot
                    }

                    PostCommentListRequestBody.QUERY_TYPE_TOP -> {
                        R.string.sort_newest
                    }

                    PostCommentListRequestBody.QUERY_TYPE_LIKE -> {
                        R.string.sort_hottest
                    }

                    else -> {
                        return
                    }
                }
            }
        }
        binding.tvSortBtn.setText(resId)
    }

    protected fun animateCommentRefresh(height: Int) {
        pendingRefreshAnim?.cancel()
        pendingRefreshAnim = ValueAnimator.ofInt(binding.llCommentRefresh.height, height).apply {
            duration = 100L
            addUpdateListener(viewLifecycleOwner) {
                if (isBindingAvailable()) {
                    binding.llCommentRefresh.setHeight(it.animatedValue as Int)
                }
            }
            addListener(viewLifecycleOwner, onEnd = {
                pendingRefreshAnim = null
            })
            start()
        }
    }

    protected fun updatePv(pv: Long) {
        playerCount = pv
        binding.tvHonorPlayCount.text = UnitUtil.formatNumberWithUnit(
            num = pv,
            decimal = 1,
            kCaps = true,
            mCaps = true,
            bCaps = true,
            showRawNumberBelow10W = false
        )
    }

    protected fun updateTime(ts: Long) {
        val updateTime = getString(
            R.string.detail_update_time,
            ts.formatUpdateDate(requireContext())
        )
        binding.tvUpdateTime.text = updateTime
    }

    /**
     * 当前方法由子类调用
     */
    protected fun initUgcDesignToolkit(needsFillUsed: Boolean) {
        ugcDesignToolkitVm.initData(
            isRefresh = true,
            gameId = gameId,
            needsFillUsed = needsFillUsed,
            loadRecommend = false,
            loadBalance = false,
        )
    }

    protected fun updateMwCompatibilityTips(mwTips: String?) {
        //MW引擎不兼容提示
        binding.tvMwNotCompatible.isVisible = !mwTips.isNullOrEmpty()
        if (!mwTips.isNullOrEmpty()) {
            binding.tvMwNotCompatible.text =
                binding.getDrawableByRes(R.drawable.ic_warn_engine_not_compatible)?.let {
                    it.setBounds(0, 0, dp(14), dp(14))
                    val tips = SpannableString("_$mwTips")
                    tips[0, 1] = CenterImageSpan(it, right = dp(5))
                    tips
                } ?: mwTips
        }
    }

    private var isFollow = false
    protected fun updateFollow(isFollow: Boolean) {
        this.isFollow = isFollow
        if (isFollow) {
            binding.tvFollowBtn.setBackgroundResource(R.drawable.bg_corner_s100_stroke_c999999_s05)
            binding.tvFollowBtn.clearCompoundDrawables(left = true)
            binding.tvFollowBtn.setTextColorByRes(R.color.color_999999)
            binding.tvFollowBtn.setText(R.string.following_cap)
        } else {
            binding.tvFollowBtn.setBackgroundResource(R.drawable.bg_corner_s100_stroke_c1a1a1a_s05)
            binding.tvFollowBtn.compoundDrawables(left = R.drawable.ic_add_1a1a1a_s12)
            binding.tvFollowBtn.setTextColorByRes(R.color.color_1A1A1A)
            binding.tvFollowBtn.setText(R.string.follow)
        }
        if (isFollow) {
            binding.tvTopFollowBtn.setText(R.string.following_cap)
        } else {
            binding.tvTopFollowBtn.setText(R.string.follow)
        }
        updateUiByOffset(scrollOffset, updateAuthorNameColor = false, force = true)
    }

    protected fun updateIsMe(isMe: Boolean) {
        updateUiByOffset(scrollOffset, updateAuthorNameColor = false, force = true)
        updateIncrementData(incrementLikeCount, incrementFlowerCount)
    }

    protected fun updateMyAvatar(avatar: String?) {
        glide?.run {
            load(avatar).placeholder(R.drawable.icon_default_avatar)
                .circleCrop()
                .into(binding.ivMyAvatar)
        }
    }

    protected fun updateLike(isLike: Boolean, likeCount: Long) {
        val commentInput = binding.bottomCommentInput
        commentInput.updateLike(
            isLike = isLike,
            likeCount = likeCount,
            anim = shouldPlayLikeAnim
        )
        shouldPlayLikeAnim = false
        this.likeCount = likeCount
        val likeCountStr = UnitUtil.formatNumberWithUnit(
            num = likeCount,
            decimal = 1,
            kCaps = true,
            mCaps = true,
            bCaps = true,
            showRawNumberBelow10W = false
        )
        commentInput.tvLike.text = likeCountStr
        binding.tvHonorLikeCount.text = likeCountStr
    }

    protected fun updateFlowerCount(flowerCount: Long) {
        this.flowerCount = flowerCount
        val flowerCountStr = UnitUtil.formatNumberWithUnit(
            num = flowerCount,
            decimal = 1,
            kCaps = true,
            mCaps = true,
            bCaps = true,
            showRawNumberBelow10W = false
        )
        binding.tvHonorFlowersCount.text = flowerCountStr
        binding.bottomCommentInput.tvSendFlowers.text = flowerCountStr
    }

    protected fun updateIncrementData(likeCount: Long, flowerCount: Long) {
        incrementLikeCount = likeCount
        incrementFlowerCount = flowerCount
        // 仅主态才显示新增的点赞
        if (binding.honorLikeLayout.isVisible && likeCount > 0 && isMe()) {
            binding.tvHonorNewLikesHotTips.visible()
            binding.tvHonorNewLikesHotTips.setTextWithArgs(
                R.string.game_detail_page_new_likes_hot_tips,
                UnitUtil.formatNumberWithUnit(
                    num = likeCount,
                    decimal = 1,
                    kCaps = true,
                    mCaps = true,
                    bCaps = true,
                    showRawNumberBelow10W = false
                )
            )
        }
        // 仅主态才显示新增的送花
        if (flowerCount > 0 && commonVm.isSendFlowerEnable() && isMe()) {
            binding.tvHonorNewFlowersHotTips.visible(true)
            binding.tvHonorNewFlowersHotTips.setTextWithArgs(
                R.string.game_detail_page_new_flowers_hot_tips,
                UnitUtil.formatNumberWithUnit(
                    num = flowerCount,
                    decimal = 1,
                    kCaps = true,
                    mCaps = true,
                    bCaps = true,
                    showRawNumberBelow10W = false
                )
            )
        }
    }

    protected fun updatePlayers(players: List<LikeAndPlayerItemData>?) {
        glide?.run {
            players?.getOrNull(0)?.let {
                load(it.portrait).placeholder(R.drawable.default_user_avatar_1)
                    .circleCrop()
                    .into(binding.ivUserAvatar1)
            } ?: run {
                binding.ivUserAvatar1.setImageResource(R.drawable.default_user_avatar_1)
            }
            players?.getOrNull(1)?.let {
                load(it.portrait).placeholder(R.drawable.default_user_avatar_2)
                    .circleCrop()
                    .into(binding.ivUserAvatar2)
            } ?: run {
                binding.ivUserAvatar2.setImageResource(R.drawable.default_user_avatar_2)
            }
            players?.getOrNull(2)?.let {
                load(it.portrait).placeholder(R.drawable.default_user_avatar_3)
                    .circleCrop()
                    .into(binding.ivUserAvatar3)
            } ?: run {
                binding.ivUserAvatar3.setImageResource(R.drawable.default_user_avatar_3)
            }
        }
    }

    protected fun updateCreationTime(time: Long) {
        binding.tvHonorCreateTime.text = if (time > 0) {
            UnitUtil.formatBuildTime(requireContext(), time)
        } else {
            getString(R.string.update_next_day)
        }
    }

    protected fun updateCommentStatus(
        commentList: Async<PagingApiResult<PostComment>>,
        loadMore: Async<LoadMoreState>
    ) {
        if (commentList is Success && loadMore is Success) {
            visibleList(
                binding.ivMyAvatar,
                binding.tvReplyHint,
                binding.ivEmojiBtn,
                binding.ivImageBtn,
                binding.tvSortBtn,
                visible = true
            )
            val commentCount = UnitUtil.formatNumberWithUnit(
                num = commentList.invoke().total,
                decimal = 1,
                kCaps = true,
                mCaps = true,
                bCaps = true,
                showRawNumberBelow10W = false
            )
            binding.bottomCommentInput.tvComment.text = commentCount
            binding.tvCommentCount.text = commentCount
        }
    }

    @CallSuper
    override fun invokeShareFeature(feature: ShareFeature) {
        when (feature.featureId) {
            FEAT_REPORT -> {
                MetaRouter.Feedback.feedback(
                    this,
                    gameId,
                    SubmitNewFeedbackRequest.SOURCE_GAME_NUMBER,
                    FeedbackTypeWrapper.experience.id,
                    false,
                    needBackGame = false,
                    fromGameId = null
                )
            }

            FEAT_SEND_FLOWERS -> {
                showSendFlowerConditionDialog()
            }

            FEAT_PIN -> {
                commonVm.pinTop(gameId, true)
            }

            FEAT_UN_PIN -> {
                commonVm.pinTop(gameId, false)
            }
        }
    }

    fun showAuthorThank(sendFlowerCount: Int) {
        if (sendFlowerCount > 0 && isBindingAvailable()) {
            val accountInteractor = GlobalContext.get().get<AccountInteractor>()
            val nickName = accountInteractor.accountLiveData.value?.nickname
            binding.tvThanks.setTextWithArgs(
                R.string.send_flowers_thank_text,
                nickName,
                sendFlowerCount
            )
            // 感谢语显示3秒, 然后隐藏
            binding.clSendFlowerThanks.visible()
            lifecycleScope.launch {
                delay(3000)
                with(Dispatchers.Main) {
                    if (isBindingAvailable()) {
                        binding.clSendFlowerThanks.visible(false, transition = true)
                    }
                }
            }
        }
    }

    fun updateUIBySendFlowerEnable(isSendFlowerEnable: Boolean) {
        if (isSendFlowerEnable) {
            Analytics.track(
                EventConstants.C_GAMEDETAIL_FLOWER_ICON_SHOW,
                "gameid" to gameId,
                "creatortype" to gameType.toString(),
                "user_type" to if (isMe()) "owner" else "visitor",
            )
        }
        visibleList(
            binding.honorFlowersLayout,
            binding.honorFlowersClick,
            binding.honorDivider3,
            visible = isSendFlowerEnable
        )
        binding.bottomCommentInput.setSendFlowerEnabled(isSendFlowerEnable)
        binding.tvHonorNewFlowersHotTips.visible(isSendFlowerEnable && !binding.tvHonorNewFlowersHotTips.text.isNullOrEmpty())
    }

    private fun showSendFlowerConditionDialog() {
        MetaRouter.GameDetail.showSendFlowerConditionDialog(
            this,
            gameId,
            gameType,
            commonVm.getSendGiftConditionsInfo()
        ) { conditionsInfo ->
            if (conditionsInfo != null) {
                commonVm.updateSendGiftConditionsInfo(conditionsInfo)
            }
        }
    }

    fun triggerGameDetailScene() {
        if (isMe()) {
            // 自动弹出SendFlowerConditionDialog时, 用户可能会关闭送花功能
            this.childFragmentManager.setFragmentResultListener(
                SendFlowerConditionDialog.REQUEST_KEY_SEND_FLOWER_CONDITIONS_INFO,
                this
            ) { _, bundle ->
                bundle.apply {
                    val key = SendFlowerConditionDialog.KEY_SEND_FLOWER_CONDITIONS_INFO
                    if (containsKey(key)) {
                        val conditionsInfo = getParcelable<SendGiftConditionsInfo>(key)
                        if (conditionsInfo != null) {
                            commonVm.updateSendGiftConditionsInfo(conditionsInfo)
                        }
                    }
                }
            }
        }
        DialogShowManager.triggerGameDetailScene(
            this,
            gameId,
            gameType,
            isMe(),
            commonVm.getSendGiftConditionsInfo()
        )
    }

    private fun showSendFlowerDialog(source: String) {
        MetaRouter.GameDetail.showSendFlowerDialog(
            this,
            gameId,
            gameType,
            source,
            flowerCount > 0
        ) { flowerCount ->
            if (flowerCount > 0 && isBindingAvailable()) {
                updateFlowerCount(this.flowerCount + flowerCount)
                showAuthorThank(flowerCount)
            }
        }
    }

    fun tryScrollToComment(): Boolean {
        val commentDividerY = binding.vDividerComment.getLocationInWindow().y
        val bottomCommentY = binding.bottomCommentInput.getLocationInWindow().y
        if (commentDividerY > bottomCommentY) {
            val titleDividerY = binding.tbl.getLocationInWindow().y + binding.tbl.height
            val distance = commentDividerY - titleDividerY
            val layoutParams = binding.abl.layoutParams as CoordinatorLayout.LayoutParams
            val animator = ValueAnimator.ofInt(0, distance)
            animator.duration = 200
            animator.addUpdateListener(
                this,
                true,
                object : ValueAnimator.AnimatorUpdateListener {
                    var lastValue = 0
                    override fun onAnimationUpdate(valueAnimator: ValueAnimator) {
                        if (!isBindingAvailable()) {
                            return
                        }
                        val value = valueAnimator.animatedValue as Int
                        layoutParams.behavior?.onNestedPreScroll(
                            binding.vcl,
                            binding.abl,
                            binding.flCommentContainer,
                            0,
                            value - lastValue,
                            intArrayOf(0, 0),
                            ViewCompat.TYPE_TOUCH
                        )
                        lastValue = value
                    }
                })
            animator.start()
            return true
        }
        return false
    }

    protected fun MetaEpoxyController.buildCommentController(
        comments: Async<PagingApiResult<PostComment>>,
        loadMore: Async<LoadMoreState>,
        uniqueTag: Int,
        firstPin: Boolean
    ) {
        when (comments) {
            is Success -> {
                val list = comments().dataList
                if (list.isNullOrEmpty()) {
                    empty(
                        iconRes = R.drawable.icon_no_recent_activity,
                        descRes = R.string.let_comm_begin_with_your_comment,
                        top = dp(32)
                    ) {
                        getCommentList(true)
                    }
                } else {
                    val dp05 = dp(0.5)
                    val dp8 = dp(8)
                    val dp62 = dp(62)
                    val dp16 = dp(16)
                    val commentContentWidth = screenWidth - 78.dp
                    val replyContentWidth = screenWidth - 112.dp
                    val atColor = getColorByRes(R.color.color_0083FA)
                    list.forEachIndexed { commentPosition, comment ->
                        if (comment.isNewAdd) {
                            comment.isNewAdd = false
                            scrollToTop = true
                        }
                        ugcCommentItem(
                            uniqueTag,
                            comment,
                            commentPosition,
                            commentContentWidth,
                            true,
                            firstPin,
                            itemListener
                        )
                        if (comment.needLocate) {
                            comment.needLocate = false
                            needLocate = true
                            locatePosition = buildItemIndex
                        }
                        var showReplyItem = false
                        if (!comment.collapse) {
                            showReplyItem = (comment.authorReply?.size
                                ?: 0) + (comment.replyCommonPage?.dataList?.size ?: 0) > 0
                            comment.authorReply?.forEachIndexed { replyPosition, reply ->
                                ugcReplyItem(
                                    uniqueTag,
                                    reply,
                                    replyPosition,
                                    commentPosition,
                                    true,
                                    atColor,
                                    replyContentWidth,
                                    true,
                                    itemListener
                                )
                                if (reply.needLocate) {
                                    reply.needLocate = false
                                    needLocate = true
                                    locatePosition = buildItemIndex
                                }
                            }
                            comment.replyCommonPage?.dataList?.forEachIndexed { replyPosition, reply ->
                                ugcReplyItem(
                                    uniqueTag,
                                    reply,
                                    replyPosition,
                                    commentPosition,
                                    false,
                                    atColor,
                                    replyContentWidth,
                                    true,
                                    itemListener
                                )
                                if (reply.needLocate) {
                                    reply.needLocate = false
                                    needLocate = true
                                    locatePosition = buildItemIndex
                                }
                            }
                        } else {
                            comment.replyCommonPage?.dataList?.forEachIndexed { replyPosition, reply ->
                                if (reply.forceShow) {
                                    showReplyItem = true
                                    ugcReplyItem(
                                        uniqueTag,
                                        reply,
                                        replyPosition,
                                        commentPosition,
                                        false,
                                        atColor,
                                        replyContentWidth,
                                        true,
                                        itemListener
                                    )
                                    if (reply.needLocate) {
                                        reply.needLocate = false
                                        needLocate = true
                                        locatePosition = buildItemIndex
                                    }
                                }
                            }
                        }
                        val showReplyButtons = comment.showReplyButtons
                        if (comment.loading) {
                            ugcCommentLoading(
                                uniqueTag,
                                comment
                            )
                        } else if (showReplyButtons) {
                            ugcCommentExpandItem(
                                uniqueTag,
                                comment,
                                commentPosition,
                                showReplyItem,
                                itemListener
                            )
                        }
                    }
                    loadMoreFooter(
                        loadMore,
                        idStr = "GameCommentFooter-${uniqueTag}",
                        endText = getString(R.string.community_article_comment_empty),
                        endTextColorRes = R.color.textColorSecondary
                    ) {
                        getCommentList(false)
                    }
                }
            }

            is Loading -> {
                loadMoreFooter(idStr = "GameCommentFooterLoading") {}
            }

            is Fail -> {
                empty(
                    iconRes = R.drawable.icon_no_recent_activity,
                    descRes = R.string.footer_load_failed,
                    top = dp(32)
                ) {
                    initCommentList()
                }
            }

            else -> {}
        }
    }

    protected fun MetaEpoxyController.buildTagsController(
        tags: List<String>
    ) {
        tags.forEachIndexed { index, tag ->
            gameTagItem(tag, index.toLong())
        }
    }

    private fun toolkitEpoxyController() = simpleController(
        ugcDesignToolkitVm,
        ToolkitRecommendState::uniqueUsedTag,
        ToolkitRecommendState::usedToolkitList,
    ) { uniqueTag, usedToolkitListAsync ->
        val usedToolkitsPair = usedToolkitListAsync.invoke() ?: return@simpleController
        if (gameId == usedToolkitsPair.first) {
            val usedToolkits = usedToolkitsPair.second
            val currentUuid = ugcDesignToolkitVm.currentAccountUuid()
            if (usedToolkits.isNotEmpty()) {
                spacer(width = dp(12))
                // 游戏详情页的工具包最多显示8个
                usedToolkits.take(8).forEach { toolkit ->
                    add {
                        ToolkitUsedItem(
                            glide = glide,
                            isMainState = isMe(),
                            currentUuid = currentUuid,
                            item = toolkit,
                            // 减去距离屏幕边框的距离, 然后分成4等分
                            viewWidth = (screenWidth - dp(16) - dp(16)) / 4,
                            listener = ugcClickedListener
                        ).id(
                            "ToolkitUsedItem-${toolkit.isObtained(currentUuid)}-${toolkit.inMyModLibrary}-${toolkit.isPurchased}-${toolkit.guid}-${toolkit.feedId}"
                        )
                    }
                }
                spacer(width = dp(12))
            }
        }
    }

    fun commonMoreTrackParams(featureType: String): MutableMap<String, String> {
        return mutableMapOf(
            "gameid" to gameId,
            "creatortype" to gameType.toString(),
            "user_type" to if (isMe()) "owner" else "visitor",
            "user_type" to if (isMe()) "owner" else "visitor",
            "click" to featureType,
        )
    }

    override fun onDestroyView() {
        commentMorePopup.release()
        loadingDialog = null
        binding.rvToolkit.onOverScrollTriggered = null
        toolkitVisibilityTracker = null

        // 回收binding到缓存池（在super.onDestroyView()之前）
        try {
            if (isBindingAvailable()) {
                GameDetailBindingCache.getInstance().recycleBinding(binding)
            }
        } catch (e: Exception) {
            // 如果回收失败，记录日志但不影响正常的销毁流程
            Timber.e(e, "Failed to recycle GameDetail binding")
        }

        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().post(
            GameMetaDataUpdateEvent(
                metaData = GameMetaData(
                    gameId = gameId,
                    likeCount = likeCount,
                    playCount = playerCount,
                )
            )
        )
    }

    protected abstract val gameId: String
    protected abstract val gameType: Int
    protected abstract val enableShare: Boolean
    protected abstract val itemListener: IGameDetailCommonListener

    protected abstract fun getCommentList(isRefresh: Boolean)
    protected abstract fun initCommentList()
    protected abstract fun onMoreClick()
    protected abstract fun isMe(): Boolean
    protected abstract fun isPin(): Boolean?
    protected abstract fun updatePin(isPinTop: Boolean)
    abstract fun tagsEpoxyController(): EpoxyController

    @CallSuper
    protected open fun updateSortType(sortType: Int) {
        Analytics.track(
            EventConstants.GAME_REVIEW_TYPE_CHOOSE_CLICK,
            "type" to when (sortType) {
                PostCommentListRequestBody.QUERY_TYPE_DEFAULT -> {
                    1
                }

                PostCommentListRequestBody.QUERY_TYPE_LIKE -> {
                    2
                }

                PostCommentListRequestBody.QUERY_TYPE_TOP -> {
                    3
                }

                else -> {
                    return
                }
            },
            "creatortype" to gameType,
            "gameid" to gameId
        )
    }

    @CallSuper
    protected open fun updateFilterType(filterType: Int) {
        Analytics.track(
            EventConstants.GAME_REVIEW_TYPE_CHOOSE_CLICK,
            "type" to when (filterType) {
                PostCommentListRequestBody.FILTER_AUTHOR -> {
                    4
                }

                PostCommentListRequestBody.FILTER_SELF -> {
                    5
                }

                else -> {
                    return
                }
            },
            "creatortype" to gameType,
            "gameid" to gameId
        )
    }

    protected interface IGameDetailCommonListener : IUgcCommentListener, IGameOperationListener
}