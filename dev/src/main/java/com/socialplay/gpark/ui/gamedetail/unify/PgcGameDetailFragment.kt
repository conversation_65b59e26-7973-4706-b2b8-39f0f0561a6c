package com.socialplay.gpark.ui.gamedetail.unify

import android.os.Bundle
import android.os.Parcelable
import android.view.View
import android.widget.ImageView
import androidx.core.view.doOnNextLayout
import androidx.core.view.postDelayed
import androidx.lifecycle.lifecycleScope
import com.airbnb.epoxy.EpoxyController
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.meta.box.biz.friend.model.LabelInfo
import com.meta.lib.mwbiz.MWBiz
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.editor.PgcGameDetail
import com.socialplay.gpark.data.model.game.OperationInfo
import com.socialplay.gpark.data.model.post.AddPostCommentReplyTarget
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.data.model.share.ShareFeature
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.mw.MWGameStartScenes
import com.socialplay.gpark.function.mw.launch.OnDownloadListener
import com.socialplay.gpark.function.mw.launch.TSLaunch
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.mw.launch.ui.TSLaunchFailedWrapper
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.account.AccPwdV7Dialog
import com.socialplay.gpark.ui.account.AccPwdV7DialogArgs
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.editor.create.EditorCreateV2Fragment
import com.socialplay.gpark.ui.gamedetail.ClickType
import com.socialplay.gpark.ui.gamedetail.GameDetailAnalyticsObserver
import com.socialplay.gpark.ui.gamedetail.cover.DetailVideoAnalytics
import com.socialplay.gpark.ui.gamereview.ReviewListFragment
import com.socialplay.gpark.ui.gamereview.dialog.GameAppraiseAdapter
import com.socialplay.gpark.ui.imgpre.v2.OpenPreviewBuilder
import com.socialplay.gpark.ui.imgpre.ImgPreDialogFragment
import com.socialplay.gpark.ui.mgs.danmu.advanced.PAGE_ANIMATION_DURATION
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialog
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialogParams
import com.socialplay.gpark.ui.profile.BaseProfilePage
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialog
import com.socialplay.gpark.ui.reportBlock.ReportSuccessDialogAnalyticsParams
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.view.DownloadProgressButton
import com.socialplay.gpark.ui.view.ExpandableTextView
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.util.DownloadProgressUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setFragmentResultListenerByHostFragment
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.simMsg
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/20
 *     desc   :
 * </pre>
 */
@Parcelize
data class PgcGameDetailFragmentArgs(
    val gId: String,
    val resIdBean: ResIdBean,
    val packageName: String,
    val type: String,
    val isComment: Boolean,
    val targetCommentId: String?,
    val expandPage: Boolean,
    val targetReplyId: String?,
    val fromGameId: String?,
    val fromPkgName: String?,
    val autoDownloadGame: Boolean
) : Parcelable

class PgcGameDetailFragment : BaseGameDetailCommonFragment() {

    companion object {
        const val TRACK_TAG = "game_review"
    }

    override val gameId: String
        get() = args.gId

    override val gameType = GAME_TYPE_PGC

    override val enableShare: Boolean
        get() = PandoraToggle.enableSharePgc

    private val vmV2: PgcGameDetailViewModel by fragmentViewModel()

    val args by args<PgcGameDetailFragmentArgs>()

    private val gameStartScenes by lazy { MWGameStartScenes(this) }
    private val tsLaunch: TSLaunch by lazy { TSLaunch() }

    private var contentHeight = 0
    private var descExpandState = ExpandableTextView.STATE_SHRINK

    override val itemListener = ItemListener()

    private val operationController by lazy { buildOperationController() }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        // 立即调用super.onViewCreated以确保动画正常工作
        super.onViewCreated(view, savedInstanceState)

        // 使用BaseFragment的动画结束回调，确保在动画完成后再初始化
        runAfterEnterAnimationEnd {
            initView()
            initData()
            initTSGame()
        }
    }

    private fun initTSGame() {
        //监听MW引擎下载
        tsLaunch.onDownloadListener(this, object : OnDownloadListener {
            override fun invoke(info: GameDetailInfo, percent: Float) {
                updateDownloadProgress(percent * 100, info.id)
            }
        })
        //监听TS游戏拉起状态
        tsLaunch.onLaunchListener(viewLifecycleOwner) {
            onLaunchPrepare {
                showLaunchingGameUI()
            }
            onLaunchGame {
                gameStartScenes.show()
            }
            onPauseDownload {
                showPauseDownloadUI()
            }
            onLaunchGameEnd { params, e ->
                showStartGameUI()
                if (e != null) gameStartScenes.hide()
                TSLaunchFailedWrapper.show(this@PgcGameDetailFragment, params, e)
                Analytics.track(EventConstants.EVENT_MW_CLICK_STATUS) {
                    put("status", if (e == null) "success" else "failed")
                    if (e != null) put("reason", e.simMsg() ?: "")
                    put("game_version_name", params.getGameVersionName())
                }
            }
        }

//        UpdateDialog.showFromGameDetail(this, viewModel)
    }

    private fun initView() {
        if (contentHeight != 0) {
            binding.clTop.minHeight = contentHeight
        }

        getBtnDownload().state = DownloadProgressButton.STATE_NORMAL
        getBtnDownload().setDownloadingText(getString(R.string.game_detail_page_play_btn))
        initEvent()

        binding.ivMyAvatar.setOnAntiViolenceClickListener {
            showReplyDialog(AddPostCommentReplyTarget(args.gId, vmV2.replyTargetName))
        }
        binding.tvReplyHint.setOnAntiViolenceClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.CommentMiddle
            )
            showReplyDialog(AddPostCommentReplyTarget(args.gId, vmV2.replyTargetName))
        }
        binding.bottomCommentInput.setOnReplyClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.CommentBar
            )
            showReplyDialog(AddPostCommentReplyTarget(args.gId, vmV2.replyTargetName), source = 3)
        }
        binding.ivEmojiBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.gId, vmV2.replyTargetName),
                showEmoji = true
            )
        }
        binding.bottomCommentInput.ivEmojiBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.gId, vmV2.replyTargetName),
                showEmoji = true
            )
        }
        binding.ivImageBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.gId, vmV2.replyTargetName),
                showImage = true
            )
        }
        binding.bottomCommentInput.ivImageBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.gId, vmV2.replyTargetName),
                showImage = true
            )
        }
        binding.rvNotice.setController(operationController)
        binding.bottomCommentInput.setOnCommentClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.CommentIcon
            )
            if (!tryScrollToComment()) {
                showReplyDialog(
                    AddPostCommentReplyTarget(args.gId, vmV2.replyTargetName),
                    source = 2
                )
            }
        }
        binding.updateBtn.setOnAntiViolenceClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.Update
            )
            MetaRouter.MobileEditor.creation(this, initTab = EditorCreateV2Fragment.TAB_MINE)
        }
        visibleList(
            binding.honorCreateTimeLayout,
            binding.honorDivider1,
            visible = false
        )
        // PGC 游戏没有模板
        visibleList(
            binding.tvCreativeAssetsTitle,
            binding.ivAssetCover,
            binding.viewAssetTemplateBg,
            binding.viewAssetTemplateBg2,
            binding.ivAssetsRankIcon,
            binding.tvAssetsTemplate,
            binding.ivAssetsStar1,
            binding.ivAssetsStar2,
            binding.ivAssetsStar3,
            binding.spaceAssetsStarEnd,
            binding.tvAssetsName,
            binding.tagsAssets,
            binding.btnAssetsCreate,
            visible = false
        )
        // PGC 游戏不展示资源包
        visibleList(
            binding.tvToolkitTitle,
            binding.tvToolkitAll,
            binding.ivToolkitAll,
            binding.vToolkitAllClick,
            binding.rvToolkit,
            visible = false
        )
    }

    private var isFirstLoadGameDetail = true

    private fun initData() {
        setFragmentResultListenerByHostFragment(
            BaseProfilePage.RESULT_FOLLOW,
            viewLifecycleOwner
        ) { _, bundle ->
            val uuid = bundle.getString(BaseProfilePage.KEY_UUID)
            if (vmV2.authorId == uuid) {
                vmV2.follow(bundle.getBoolean(BaseProfilePage.KEY_IS_FOLLOW))
            }
        }
        vmV2.onAsync(PgcGameDetailState::detail, onFail = { _, data ->
            if (data == null) {
                showCdnErrorUI()
                binding.ivMoreBtn.gone()
            }
        }) {
            args.resIdBean.setGameVersionName(it.gameVersion)
            updateView(it)
            //预获取游戏启动数据
            tsLaunch.preLoadLaunchParams(it.toGameDetailInfo())
            vmV2.initCommentList()
            if (isFirstLoadGameDetail) {
                isFirstLoadGameDetail = false
                analyticsObserve.sendEnterGameDetailAnalytic(
                    args.gId,
                    args.packageName,
                    vmV2.getGameDetailEnteredTimes(args.gId),
                    args.type,
                    args.resIdBean,
                    creatorType = gameType,
                    isMyGame = isMe()
                )
                triggerGameDetailScene()
            }
        }

        vmV2.onAsync(
            PgcGameDetailState::likeStatus,
            onFail = { _, pair ->
                if (pair != null) {
                    updateLike(pair.first, pair.second)
                }
            }
        ) { (isLike, likeCount) ->
            updateLike(isLike, likeCount)
        }
        vmV2.onEach(PgcGameDetailState::flowerCount) {
            updateFlowerCount(it)
        }
        vmV2.onEach(PgcGameDetailState::follow) {
            updateFollow(it)
        }

        vmV2.accountInteractor.accountLiveData.observe(viewLifecycleOwner) {
            updateMyAvatar(it?.portrait)
        }

        vmV2.onEach(PgcGameDetailState::operationList) {
            if (it().isNullOrEmpty()) {
                binding.rvNotice.gone()
            } else {
                binding.rvNotice.visible()
            }
        }
        vmV2.onEach(
            PgcGameDetailState::commentList,
            PgcGameDetailState::commentListLoadMore
        ) { commentList, loadMore ->
            updateCommentStatus(commentList, loadMore)
        }
        vmV2.onEach(
            PgcGameDetailState::sortType,
            PgcGameDetailState::filterType
        ) { sortType, filterType ->
            updateSortTypeText(sortType, filterType)
        }
        vmV2.onEach(PgcGameDetailState::isCommentListRefresh, deliveryMode = uniqueOnly()) {
            if (it.invoke() == true) {
                when (it) {
                    is Loading -> {
                        animateCommentRefresh(dp(52))
                    }

                    is Success -> {
                        animateCommentRefresh(0)
                    }

                    else -> {
                        animateCommentRefresh(0)
                    }
                }
            }
        }
        vmV2.setupRefreshLoading(
            PgcGameDetailState::detail,
            binding.lv,
            binding.mrl
        ) {
            vmV2.getPgcDetail(true)
            vmV2.getOperationList()
            commonVm.loadSendFlowerConditions(gameId)
            getCommentList(true)
        }
        vmV2.registerToast(PgcGameDetailState::toast)
        vmV2.registerAsyncErrorToast(PgcGameDetailState::detail)
        vmV2.registerAsyncErrorToast(PgcGameDetailState::commentListLoadMore)
        vmV2.registerAsyncErrorToast(PgcGameDetailState::addCommentResult)
        vmV2.registerAsyncErrorToast(PgcGameDetailState::addReplyResult)
        vmV2.registerAsyncErrorToast(PgcGameDetailState::likeStatus)
    }

    private fun initEvent() {
        getBtnDownload().setOnAntiViolenceClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.PlayGame
            )
            clickStartGame()
//            MetaRouter.Pay.startPay(PayInfo(
//                gameId = EditorGameInteractHelper.getRoleGameId(),
//                payAmount = 100
//            ))
        }
        binding.tvDescription.setExpandListener(object : ExpandableTextView.OnExpandListener {
            override fun onExpand(view: ExpandableTextView) {
                Analytics.track(
                    EventConstants.DETAIL_DESCRIPTION_MORE_CLICK,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                descExpandState = ExpandableTextView.STATE_EXPAND
            }

            override fun onShrink(view: ExpandableTextView) {
                descExpandState = ExpandableTextView.STATE_SHRINK
            }
        })
        binding.bottomCommentInput.setOnLikeClickListener {
            shouldPlayLikeAnim = true
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.Like
            )
            if (vmV2.isLike) {
                Analytics.track(
                    EventConstants.GAME_DETAIL_PAGE_LIKE_CANCEL,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
            } else {
                Analytics.track(
                    EventConstants.GAME_DETAIL_PAGE_LIKE,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                DialogShowManager.triggerLike(this)
            }
            vmV2.like()
        }
        binding.ulv.setListener(viewLifecycleOwner) {
            UserLabelView.showDescDialog(this, it)
        }
    }

    private fun clickStartGame() {
        if (getBtnDownload().state == DownloadProgressButton.STATE_UNAVAILABLE) {
            return
        }
        val info = vmV2.detail
        if (info != null && info.isTs && !info.id.isNullOrEmpty() && !info.packageName.isNullOrEmpty()) {
            val resIdBean = args.resIdBean.setClickGameTime(System.currentTimeMillis())
            if (tsLaunch.isLaunching(info.id)) {
                //启动中
                Analytics.track(EventConstants.EVENT_TS_DETAIL_LAUNCHING) {
                    putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                    put("game_type", "ts")
                    put("gameid", info.id)
                    put("packagename", info.packageName)
                }
                toast(R.string.launching)
            } else {
                val clickType = if (MWBiz.isAvailable()) {
                    GameDetailAnalyticsObserver.CLICK_TYPE_START_GAME
                } else {
                    GameDetailAnalyticsObserver.CLICK_TYPE_START_DOWNLOAD
                }
                val gameDetailInfo = info.toGameDetailInfo()
                analyticsObserve.sendClickDownloadAnalytic(gameDetailInfo, clickType, resIdBean)
                vmV2.metaKV.analytic.saveClickLaunchTime(
                    info.packageName,
                    System.currentTimeMillis()
                )
                val params = TSLaunchParams(gameDetailInfo, resIdBean)
                tsLaunch.launch(requireContext(), params)
            }
        } else {
            toast(R.string.loading_failed_click_to_retry)
        }
    }

    private var isMe = false
    override fun isMe(): Boolean {
        return isMe
    }

    override fun isPin(): Boolean? {
        return vmV2.isPin
    }

    override fun updatePin(isPinTop: Boolean) {
        vmV2.updatePin(isPinTop)
    }

    private fun updateView(gameDetailInfo: PgcGameDetail) {
        binding.lv.hide()
        binding.ivMoreBtn.visible()
        binding.tvGameName.text = gameDetailInfo.displayName
        binding.tvTitleBarGameName.text = gameDetailInfo.displayName
        if (gameDetailInfo.description.isNullOrEmpty()) {
            visibleList(
                binding.tvDescriptionLabel,
                binding.tvDescription,
                visible = false
            )
        } else {
            visibleList(
                binding.tvDescriptionLabel,
                binding.tvDescription,
                visible = true
            )
            binding.tvDescription.updateForRecyclerView(
                gameDetailInfo.description,
                screenWidth - dp(32),
                descExpandState
            )
        }
        updatePv(gameDetailInfo.pvCount)
        updateTime(gameDetailInfo.updateTime.coerceAtLeast(gameDetailInfo.createTime))
        updateDownloadBtn()

        updateBanner(gameDetailInfo.images, gameDetailInfo.bannerColor)

        // pgc 客态和主态都显示建造
        binding.updateBtn.visible(false)
        binding.buildBtnLayout.visible(true)
        if (gameDetailInfo.author != null && !gameDetailInfo.author.id.isNullOrEmpty()
            && !gameDetailInfo.author.name.isNullOrEmpty()
        ) {
            visibleList(
                binding.ivAvatar,
                binding.vAuthorClick,
                binding.tvUsername,
                binding.tvPortfolio,
                binding.tvUserId,
                binding.ulv,
                visible = true
            )
            binding.ulv.show(
                gameDetailInfo.author.tagIds,
                gameDetailInfo.userLabel,
                isCreator = true,
                glide = glide
            )
            val isMe = vmV2.isMe(gameDetailInfo.author.id)
            this.isMe = isMe
            super.updateIsMe(isMe)

            visibleList(
                binding.tvFollowBtn,
                binding.tvTopFollowBtn,
                commentMorePopup.sortPopupBinding.mtvAuthorOnly,
                commentMorePopup.sortPopupBinding.vAuthorOnlyClick,
                commentMorePopup.sortPopupBinding.vDivider4,
                visible = !isMe
            )
            glide?.run {
                load(gameDetailInfo.author.avatar).placeholder(R.drawable.placeholder_circle)
                    .error(R.drawable.placeholder_circle)
                    .circleCrop()
                    .into(binding.ivAvatar)
                load(gameDetailInfo.author.avatar).placeholder(R.drawable.placeholder_circle)
                    .error(R.drawable.placeholder_circle)
                    .circleCrop()
                    .into(binding.ivAuthorAvatar)
            }
            binding.tvUsername.text = gameDetailInfo.author.name
            binding.tvAuthorName.text = gameDetailInfo.author.name
            binding.tvPortfolio.text = SpannableHelper.Builder()
                .text(getString(R.string.game_detail_page_maps_count))
                .textAppearance(context, R.style.MetaTextView_S12_PoppinsMedium500)
                .colorRes(R.color.color_999999)
                .text("${gameDetailInfo.onlineGameCount}")
                .textAppearance(context, R.style.MetaTextView_S12_PoppinsRegular400)
                .colorRes(R.color.color_999999)
                .build()
            binding.tvUserId.text = SpannableHelper.Builder()
                .text(getString(R.string.game_detail_page_user_id))
                .textAppearance(context, R.style.MetaTextView_S12_PoppinsMedium500)
                .colorRes(R.color.color_999999)
                .text(gameDetailInfo.author.number ?: "unknown")
                .textAppearance(context, R.style.MetaTextView_S12_PoppinsRegular400)
                .colorRes(R.color.color_999999)
                .build()

            binding.vAuthorClick.setOnAntiViolenceClickListener {
                MetaRouter.Profile.other(
                    this@PgcGameDetailFragment,
                    gameDetailInfo.author.id,
                    "8",
                    checkFollow = true
                )
            }
            binding.tvFollowBtn.setOnAntiViolenceClickListener {
                vmV2.follow()
            }
            binding.tvTopFollowBtn.setOnAntiViolenceClickListener {
                vmV2.follow()
            }
        } else {
            visibleList(
                binding.ivAvatar,
                binding.vAuthorClick,
                binding.tvUsername,
                binding.tvPortfolio,
                binding.ulv,
                binding.tvFollowBtn,
                binding.tvTopFollowBtn,
                commentMorePopup.sortPopupBinding.mtvAuthorOnly,
                commentMorePopup.sortPopupBinding.vAuthorOnlyClick,
                commentMorePopup.sortPopupBinding.vDivider4,
                visible = false
            )
        }
        updateMwCompatibilityTips(gameDetailInfo.mwTip)
        updateIncrementData(gameDetailInfo.newAddLikeCount, gameDetailInfo.newAddFlowerCount)
        updatePlayers(gameDetailInfo.gameLikePlayerList)
        updateCreationTime(0)
        binding.clTop.doOnNextLayout {
            binding.clTop.minHeight = 0
        }
    }

    /**
     * 获取到数据后更新下载按钮
     */
    private fun updateDownloadBtn() {
        // 重新拉取信息后，将错误状态重置回正常状态
        getBtnDownload().state = when {
            getBtnDownload().state == DownloadProgressButton.STATE_CDN_ERROR -> {
                DownloadProgressButton.STATE_NORMAL
            }

            else -> {
                DownloadProgressButton.STATE_NORMAL
            }
        }
        when (getBtnDownload().state) {
            DownloadProgressButton.STATE_NORMAL -> {
                showStartGameUI()
            }

            DownloadProgressButton.STATE_DOWNLOAD_ERROR -> {
                showDownloadErrorUI()
            }

            DownloadProgressButton.STATE_CDN_ERROR -> {
                showCdnErrorUI()
            }

            DownloadProgressButton.STATE_DOWNLOADING -> {
                showStartDownloadUI()
            }

            DownloadProgressButton.STATE_PAUSE -> {
                showPauseDownloadUI()
            }

            DownloadProgressButton.STATE_UNAVAILABLE -> {
                showGameUnAvailableUI()
            }
        }
    }

    /**
     * 显示下载游戏的UI
     */
    private fun showStartDownloadUI() {
        setDownloadState(DownloadProgressButton.STATE_DOWNLOADING)
    }

    /**
     * 显示暂停下载的UI
     */
    private fun showPauseDownloadUI() {
        setDownloadState(DownloadProgressButton.STATE_PAUSE)
        getBtnDownload().setDownloadingText(getString(R.string.resume_download_game))
    }

    /**
     * 显示获取信息错误的UI
     */
    private fun showCdnErrorUI() {
        setDownloadState(DownloadProgressButton.STATE_CDN_ERROR)
        getBtnDownload().setCurrentText(getString(R.string.retry_download_game))
    }

    /**
     * 显示下载信息错误的UI
     */
    private fun showDownloadErrorUI() {
        setDownloadState(DownloadProgressButton.STATE_DOWNLOAD_ERROR)
        getBtnDownload().setCurrentText(getString(R.string.retry_download_game))
    }

    /**
     * 显示可以开始游戏的UI
     */
    private fun showStartGameUI() {
        setDownloadState(DownloadProgressButton.STATE_NORMAL)
        getBtnDownload().setCurrentText(getString(R.string.game_detail_page_play_btn))
    }

    /**
     * 显示启动游戏中的UI
     */
    private fun showLaunchingGameUI() {
        setDownloadState(DownloadProgressButton.STATE_NORMAL)
        getBtnDownload().setCurrentText(getString(R.string.launching))
    }

    /**
     * 显示游戏不可玩UI
     */
    private fun showGameUnAvailableUI() {
        setDownloadState(DownloadProgressButton.STATE_UNAVAILABLE)
        getBtnDownload().setCurrentText(getString(R.string.unavailable))
    }

    /**
     * 更新下载进度UI
     */
    private fun updateDownloadProgress(progress: Float, gameId: String) {
        var nowProgress = progress
        if (nowProgress > 99) {
            getBtnDownload().initProgress(nowProgress)
            showLaunchingGameUI()
        } else {
            setDownloadState(DownloadProgressButton.STATE_DOWNLOADING)
            nowProgress = DownloadProgressUtil.getShowProgress(nowProgress)
            getBtnDownload().setProgress(progress = nowProgress)
        }
    }

    /**
     * 设置下载状态
     */
    private fun setDownloadState(newState: Int) {
        getBtnDownload().state = newState
    }

    private fun getCommonAnalyticParams(): Map<String, String> {
        val info = vmV2.detail
        return hashMapOf("gameid" to (info?.id ?: args.gId))
    }

    private fun getBtnDownload() = binding.dpbEnter2

    override fun onDestroyView() {
        contentHeight = binding.clTop.height
        DetailVideoAnalytics.gameId = null
        super.onDestroyView()
    }

    override fun onShareCountIncrease(data: ShareRawData) {
    }

    override fun epoxyController() = simpleController(
        vmV2,
        PgcGameDetailState::commentList,
        PgcGameDetailState::commentListLoadMore,
        PgcGameDetailState::uniqueTag,
        PgcGameDetailState::showCommentPinRedDot,
    ) { comments, loadMore, uniqueTag, showCommentPinRedDot ->
        buildCommentController(comments, loadMore, uniqueTag, showCommentPinRedDot)
    }

    override fun tagsEpoxyController() = simpleController(
        vmV2,
        PgcGameDetailState::detail
    ) {
        it()?.gameShowTags?.let {
            buildTagsController(it.mapNotNull { it.tagName })
        }
    }

    private fun buildOperationController() = simpleController(
        vmV2,
        PgcGameDetailState::operationList
    ) {
        it()?.forEachIndexed { index, item ->
            gameOperationItem(item, index, itemListener)
        }
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_NAME_GAME_DETAIL

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        args.resIdBean.setTsType(ResIdBean.TS_TYPE_NORMAL).setIconType(ResIdBean.ICON_TYPE_PGC)
        vmV2.apiMonitor(
            this,
            PgcGameDetailState::detail
        )

        DetailVideoAnalytics.gameId = args.gId
        operationController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        operationController.onSaveInstanceState(outState)
    }

    /**
     * @param source 来源: 1: 默认; 2: 点击底部评论计数图标; 3: 点击底部评论输入栏
     */
    private fun showReplyDialog(
        target: AddPostCommentReplyTarget,
        showEmoji: Boolean = false,
        showImage: Boolean = false,
        source: Int = 1
    ) {
        AccPwdV7Dialog.show(this, AccPwdV7DialogArgs.SOURCE_PUBLISH_PGC_COMMENT) {
            if (it && isBindingAvailable()) {
                val replyType: Long
                val reviewId: String
                val type: Int
                if (target.isTargetComment) {
                    Analytics.track(
                        EventConstants.GAME_MORE_REVIEW_WRITE_CLICK,
                        "gameid" to gameId,
                        "creatortype" to gameType
                    )
                    replyType = 0L
                    reviewId = target.asComment.commentId
                    type = ArticleCommentInputDialog.TYPE_COMMENT_COMMENT
                } else if (target.isTargetReply) {
                    Analytics.track(
                        EventConstants.GAME_MORE_REVIEW_WRITE_CLICK,
                        "gameid" to gameId,
                        "creatortype" to gameType
                    )
                    replyType = 1L
                    reviewId = target.asReply.replyId
                    type = ArticleCommentInputDialog.TYPE_COMMENT_REPLAY
                } else {
                    Analytics.track(
                        EventConstants.GAME_FIRST_REVIEW_WRITE_CLICK,
                        "source" to source,
                        "gameid" to gameId,
                        "creatortype" to gameType
                    )
                    replyType = -1L
                    reviewId = args.gId
                    type = ArticleCommentInputDialog.TYPE_COMMENT_ARTICLE
                }
                vmV2.setReplyTarget(target)
                ArticleCommentInputDialog.show(
                    this,
                    replyUniqueKey = "$gameId-$reviewId",
                    target.toNickname,
                    gameId,
                    null,
                    type,
                    0.7f,
                    showEmoji,
                    showImage,
                    getPageName(),
                    ArticleCommentInputDialogParams.CONTENT_TYPE_PGC_DETAIL,
                    1,
                    true,
                    pageName = getPageName()
                ) {
                    if (it == null || !it.valid) return@show
                    if (target.isTargetPost) {
                        vmV2.addCommentViaNet(it)
                    } else {
                        vmV2.addReplyViaNet(it)
                    }
                }
            }
        }
    }

    private fun handleOperateComment(
        view: View,
        comment: PostComment,
        commentPosition: Int,
        showRedDot: Boolean
    ) {
        handleOperationHelper(
            view,
            comment = comment,
            showRedDot = showRedDot,
            commentPosition = commentPosition
        )
    }

    private fun handleOperateReply(
        view: View,
        reply: PostReply,
        replyPosition: Int,
        commentPosition: Int,
        isAuthorReply: Boolean
    ) {
        handleOperationHelper(
            view,
            reply = reply,
            commentPosition = commentPosition,
            replyPosition = replyPosition,
            isAuthorReply = isAuthorReply
        )
    }

    private fun handleOperationHelper(
        view: View,
        comment: PostComment? = null,
        showRedDot: Boolean = false,
        reply: PostReply? = null,
        commentPosition: Int = 0,
        replyPosition: Int = 0,
        isAuthorReply: Boolean = false
    ) {
        val isMe = vmV2.isMe(comment?.uid ?: reply?.uid)
        val isCreator = vmV2.isCreator(vmV2.myUuid)
        commentMorePopup.operateComment(
            fragment = this@PgcGameDetailFragment,
            rootLayout = binding.root,
            view = view,
            comment = comment,
            reply = reply,
            showRedDot = showRedDot,
            isMe = isMe,
            isCreator = isCreator,
            onCopyClick = {},
            onPinClick = {
                if (comment != null) {
                    Analytics.track(
                        EventConstants.GAME_REVIEW_PIN_CLICK,
                        "gameid" to gameId,
                        "creatortype" to gameType
                    )
                    vmV2.pinComment(comment, commentPosition, showRedDot)
                }
            },
            onUnpinClick = {
                if (comment != null) {
                    Analytics.track(
                        EventConstants.GAME_CANCEL_REVIEW_PIN_CLICK,
                        "gameid" to gameId,
                        "creatortype" to gameType
                    )
                    vmV2.pinComment(comment, commentPosition, showRedDot)
                }
            },
            onReportClick = {
                if (comment != null) {
                    goReport(comment.commentId, ReportType.PgcReview)
                } else if (reply != null) {
                    goReport(reply.replyId, ReportType.PgcReply)
                }
            },
            onDeleteCancel = {
                if (comment != null) {
                    Analytics.track(EventConstants.YOUR_REVIEW_DELETE_CANCEL) {
                        put("gameid", gameId)
                        put("reviewid", comment.commentId)
                        put(
                            "type",
                            GameAppraiseAdapter.AppraiseItemListener.TYPE_COMMENT
                        )
                    }
                } else if (reply != null) {
                    Analytics.track(EventConstants.YOUR_REVIEW_DELETE_CANCEL) {
                        put("gameid", gameId)
                        put("reviewid", reply.replyId)
                        put(
                            "type",
                            GameAppraiseAdapter.AppraiseItemListener.TYPE_REPLAY
                        )
                    }
                }
            },
            onDeleteConfirm = {
                if (comment != null) {
                    vmV2.deleteComment(comment, commentPosition)
                    Analytics.track(EventConstants.MY_GAME_REVIEW_DELETE_SUCCESS) {
                        put("gameid", gameId)
                        put("reviewid", comment.commentId)
                        put(
                            "type",
                            GameAppraiseAdapter.AppraiseItemListener.TYPE_COMMENT
                        )
                    }
                    Analytics.track(EventConstants.YOUR_REVIEW_DELETE) {
                        put("gameid", gameId)
                        put("reviewid", comment.commentId)
                        put(
                            "type",
                            GameAppraiseAdapter.AppraiseItemListener.TYPE_COMMENT
                        )
                    }
                } else if (reply != null) {
                    vmV2.deleteReply(
                        reply,
                        replyPosition,
                        commentPosition,
                        isAuthorReply
                    )
                    Analytics.track(EventConstants.MY_GAME_REVIEW_DELETE_SUCCESS) {
                        put("gameid", gameId)
                        put("reviewid", reply.replyId)
                        put(
                            "type",
                            GameAppraiseAdapter.AppraiseItemListener.TYPE_REPLAY
                        )
                    }
                    Analytics.track(EventConstants.YOUR_REVIEW_DELETE) {
                        put("gameid", gameId)
                        put("reviewid", reply.replyId)
                        put(
                            "type",
                            GameAppraiseAdapter.AppraiseItemListener.TYPE_REPLAY
                        )
                    }
                }
            }
        )
    }

    private fun goReport(reportId: String, reportType: ReportType) {
        Analytics.track(
            EventConstants.EVENT_REVIEW_REPORT_CLICK,
            "gameid" to gameId,
            "type" to (if (reportType == ReportType.PgcReview) {
                GameAppraiseAdapter.AppraiseItemListener.TYPE_COMMENT
            } else {
                GameAppraiseAdapter.AppraiseItemListener.TYPE_REPLAY
            })
        )
        MetaRouter.Report.postReport(this, reportId, reportType, gameId = gameId) {
            if (it) {
                val analyticsParams = when (reportType) {
                    ReportType.PgcReview -> {
                        ReportSuccessDialogAnalyticsParams.GameComment(
                            gameId = gameId,
                            commentId = reportId,
                        )
                    }

                    ReportType.PgcReply -> {
                        ReportSuccessDialogAnalyticsParams.GameCommentReply(
                            gameId = gameId,
                            replyId = reportId,
                        )
                    }

                    else -> {
                        null
                    }
                }
                ReportReasonDialog.showReportSuccessDialog(this, analyticsParams)
            }
        }
    }

    override fun getCommentList(isRefresh: Boolean) {
        vmV2.getCommentList(isRefresh)
    }

    override fun initCommentList() {
        vmV2.initCommentList()
    }

    override fun onMoreClick() {
        vmV2.detail?.let {
            val features = mutableListOf<ShareFeature>()
            features.add(
                ShareFeature(
                    FEAT_REPORT,
                    R.drawable.ic_share_feat_feedback,
                    titleRes = R.string.feedback,
                    clickTrackParams = commonMoreTrackParams("feedback")
                )
            )
            // 仅游戏作者, 在更多里面才显示送花按钮
            if (PandoraToggle.enableGameGiftOption && isMe()) {
                features.add(
                    ShareFeature(
                        FEAT_SEND_FLOWERS,
                        R.drawable.ic_share_feat_send_flowers,
                        titleRes = R.string.game_detail_page_more_dialog_send_flowers,
                        clickTrackParams = commonMoreTrackParams("flower")
                    )
                )
            }
            if (isMe() && isPin() == false) {
                features.add(
                    ShareFeature(
                        FEAT_PIN,
                        R.drawable.ic_share_feat_pin,
                        titleRes = R.string.game_detail_page_more_dialog_pin,
                        clickTrackParams = commonMoreTrackParams("pin")
                    )
                )
            }
            if (isMe() && isPin() == true) {
                features.add(
                    ShareFeature(
                        FEAT_UN_PIN,
                        R.drawable.ic_share_feat_unpin,
                        titleRes = R.string.game_detail_page_more_dialog_cancel_pin,
                        clickTrackParams = commonMoreTrackParams("pin")
                    )
                )
            }
            GlobalShareDialog.show(
                childFragmentManager,
                ShareRawData.pgc(
                    it,
                    args.resIdBean.getReqId()
                ),
                requestKey = vmV2.requestKey,
                features = features
            )
        }
    }

    override fun updateSortType(sortType: Int) {
        super.updateSortType(sortType)
        vmV2.updateSortType(sortType)
    }

    override fun updateFilterType(filterType: Int) {
        super.updateFilterType(filterType)
        vmV2.updateFilterType(filterType)
    }

    inner class ItemListener : IGameDetailCommonListener {
        override fun isMe(uid: String?): Boolean {
            return vmV2.isMe(uid)
        }

        override fun isCreator(uid: String?): Boolean {
            return vmV2.isCreator(uid)
        }

        override fun iAmCreator(): Boolean {
            return vmV2.iAmCreator
        }

        override fun goUserPage(uid: String?) {
            if (!uid.isNullOrBlank()) {
                MetaRouter.Profile.other(this@PgcGameDetailFragment, uid, TRACK_TAG)
            }
        }

        override fun operateComment(
            view: View,
            comment: PostComment,
            commentPosition: Int,
            showRedDot: Boolean
        ) {
            handleOperateComment(view, comment, commentPosition, showRedDot)
        }

        override fun likeComment(comment: PostComment, commentPosition: Int) {
            if (!comment.isLike) {
                Analytics.track(EventConstants.GAME_REVIEW_LIKE_CLICK) {
                    put("gameid", args.gId)
                    put("reviewid", comment.commentId)
                    put("type", ReviewListFragment.REVIEW_AUTHOR)
                }
            }
            if (!comment.isLike) {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CLICK,
                    "type" to 1,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
                DialogShowManager.triggerLike(this@PgcGameDetailFragment)
            } else {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CANCEL_CLICK,
                    "type" to 1,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
            }
            vmV2.likeComment(comment, commentPosition)
        }

        override fun reply2Comment(comment: PostComment, commentPosition: Int) {
            showReplyDialog(AddPostCommentReplyTarget(comment, commentPosition))
        }

        override fun operateReply(
            view: View,
            reply: PostReply,
            replyPosition: Int,
            commentPosition: Int,
            isAuthorReply: Boolean
        ) {
            handleOperateReply(view, reply, replyPosition, commentPosition, isAuthorReply)
        }

        override fun likeReply(
            reply: PostReply,
            replyPosition: Int,
            commentPosition: Int,
            isAuthorReply: Boolean
        ) {
            if (!reply.isLike) {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CLICK,
                    "type" to 2,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
                DialogShowManager.triggerLike(this@PgcGameDetailFragment)
            } else {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CANCEL_CLICK,
                    "type" to 2,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
            }
            vmV2.likeReply(reply, replyPosition, commentPosition, isAuthorReply)
        }

        override fun reply2Reply(reply: PostReply, commentPosition: Int) {
            Analytics.track(EventConstants.EVENT_GAME_REVIEW_REPLIES_CLICK)
            showReplyDialog(AddPostCommentReplyTarget(reply, reply.commentId, commentPosition))
        }

        override fun loadMoreReply(comment: PostComment, commentPosition: Int) {
            Analytics.track(
                EventConstants.EVENT_GAME_REVIEW_EXPAND,
                "creatortype" to gameType
            )
            vmV2.loadMoreReplies(comment, commentPosition)
        }

        override fun collapseReply(comment: PostComment, commentPosition: Int) {
            Analytics.track(
                EventConstants.GAME_REVIEW_COLLAPSE_CLICK,
                "creatortype" to gameType
            )
            vmV2.collapseReply(comment, commentPosition, true)
        }

        override fun goCommentListPage() {}
        override fun previewImage(
            mediaList: List<String>?,
            imageViews: List<ImageView>,
            imagePosition: Int
        ) {
            OpenPreviewBuilder(this@PgcGameDetailFragment)
                .setImageUrls(mediaList ?: emptyList())
                .setClickViews(imageViews)
                .setClickPosition(imagePosition)
                .show()
        }

        override fun clickOperation(item: OperationInfo, position: Int) {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.Notice
            )
            item.content ?: return
            if (item.isWebType()) {
                MetaRouter.Web.navigate(this@PgcGameDetailFragment, item.title, item.content)
            } else if (item.isArticleType()) {
                MetaRouter.Post.goPostDetail(
                    this@PgcGameDetailFragment,
                    item.content,
                    "pgc_detail_operation"
                )
            }
        }

        override fun showComment(comment: PostComment, commentPosition: Int) {
            Analytics.track(EventConstants.GAME_REVIEW_ITEM_SHOW) {
                put("gameid", gameId)
                put("reviewid", comment.commentId)
                put("from", "2") //2 游戏详情页
            }
            if (trackGameReviewShow) {
                trackGameReviewShow = false
                Analytics.track(
                    EventConstants.GAME_REVIEW_SHOW,
                    "creatortype" to gameType
                )
            }
        }

        override fun clickLabel(data: Pair<Int, LabelInfo?>) {
            UserLabelView.showDescDialog(this@PgcGameDetailFragment, data)
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }
}