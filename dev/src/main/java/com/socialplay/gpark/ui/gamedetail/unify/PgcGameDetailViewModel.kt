package com.socialplay.gpark.ui.gamedetail.unify

import android.app.Application
import android.content.ComponentCallbacks
import android.os.SystemClock
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.ApiDataException
import com.socialplay.gpark.data.base.PagingApiResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.account.ClearRedDotEvent
import com.socialplay.gpark.data.model.account.UserFollowEvent
import com.socialplay.gpark.data.model.community.PostCommentContent
import com.socialplay.gpark.data.model.editor.PgcGameDetail
import com.socialplay.gpark.data.model.game.OperationInfo
import com.socialplay.gpark.data.model.game.UpdateGameLikeEvent
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.post.PostCommentRequestBody
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.post.PostReplyListRequestBody
import com.socialplay.gpark.data.model.post.PostReplyRequestBody
import com.socialplay.gpark.data.model.post.ReviewOpinion
import com.socialplay.gpark.data.model.user.RelationType
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.editor.detail.commentlist.BaseCommentListViewModel
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.extension.collectIn
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.unregisterHermes
import kotlinx.coroutines.flow.map
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/21
 *     desc   :
 * </pre>
 */
data class PgcGameDetailState(
    val gameId: String,
    val targetCommentId: String?,
    val targetReplyId: String?,
    val showCommentPinRedDot: Boolean,
    val detail: Async<PgcGameDetail> = Uninitialized,
    val likeStatus: Async<Pair<Boolean, Long>> = Uninitialized,
    val authorId: String? = null,
    val commentList: Async<PagingApiResult<PostComment>> = Uninitialized,
    val commentListLoadMore: Async<LoadMoreState> = Uninitialized,
    val page: Int = 1,
    val addCommentResult: Async<PostComment> = Uninitialized,
    val addReplyResult: Async<PostReply> = Uninitialized,
    val toast: ToastData = ToastData.EMPTY,
    val follow: Boolean = false,
    val operationList: Async<List<OperationInfo>> = Uninitialized,
    val sortType: Int = PostCommentListRequestBody.QUERY_TYPE_DEFAULT,
    val filterType: Int? = null,
    val isCommentListRefresh: Async<Boolean> = Uninitialized,
    val uniqueTag: Int = 0,
    val flowerCount: Long = 0,
    val isPin: Boolean? = null, // 当前作品是否被置顶
) : MavericksState

class PgcGameDetailViewModel(
    initialState: PgcGameDetailState,
    context: Application,
    repo: IMetaRepository,
    accountInteractor: AccountInteractor,
    val metaKV: MetaKV
) : BaseCommentListViewModel<PgcGameDetailState>(initialState, context, repo, accountInteractor) {

    val requestKey = "GameDetailViewModel_${SystemClock.elapsedRealtime()}"

    override val oldListResult: PagingApiResult<PostComment>?
        get() = oldState.commentList()
    override val authorId: String?
        get() = detail?.author?.id
    override val moduleContentId: String
        get() = oldState.gameId
    override val pageType = 1L

    override val moduleContentType: Int = MODULE_PGC

    val detail get() = oldState.detail()
    val isLike: Boolean
        get() = oldState.likeStatus()?.first == true
    val replyTargetName get() = detail?.author?.name
    val enableGiveaway get() = detail?.giveaway == true

    val isPin: Boolean? get() = oldState.isPin

    override val PgcGameDetailState.oldListResult: PagingApiResult<PostComment>?
        get() = commentList()

    init {
        registerHermes()
        getPgcDetail(true)
        getOperationList()
    }

    fun getPgcDetail(isRefresh: Boolean) = withState { s ->
        if (!isRefresh && !s.detail.shouldLoad) return@withState
        repo.getPgcGameDetail(s.gameId)
            .execute(retainValue = PgcGameDetailState::detail) { result ->
                if (result is Success) {
                    val newDetail = result.invoke()
                    copy(
                        detail = result,
                        likeStatus = Success(newDetail.likeIt to newDetail.likeCount),
                        follow = newDetail.author?.follow ?: false,
                        flowerCount = newDetail.flowerCount,
                        isPin = newDetail.topOn
                    )
                } else {
                    copy(detail = result)
                }
            }
    }

    fun initCommentList() = withState { s ->
        if (s.commentListLoadMore is Uninitialized) {
            getCommentList(true, s.targetCommentId, s.targetReplyId)
        }
    }

    fun getCommentList(
        refresh: Boolean,
        targetCommentId: String? = null,
        targetReplyId: String? = null
    ) = withState { s ->
        if (s.commentListLoadMore is Loading) return@withState
        getCommentList(
            moduleContentId = s.gameId,
            sortType = s.sortType,
            page = s.page,
            pageSize = 10,
            replySize = 0,
            replySortType = PostReplyListRequestBody.QUERY_LATEST,
            refresh = refresh,
            targetCommentId = targetCommentId,
            targetReplyId = targetReplyId,
            commentCollapse = false,
            commentReplyStatus = PostComment.REPLY_STATUS_INIT,
            withAuthorReply = true,
            authorReplySize = 1,
            filterType = s.filterType
        ) { result, targetPage ->
            when (result) {
                is Success -> {
                    copy(
                        commentList = result,
                        commentListLoadMore = Success(LoadMoreState(result().end)),
                        page = targetPage,
                        isCommentListRefresh = if (commentList is Success) {
                            Success(refresh)
                        } else {
                            isCommentListRefresh
                        },
                        uniqueTag = if (refresh) uniqueTag.xor(1) else uniqueTag
                    )
                }

                is Fail -> {
                    copy(
                        commentList = if (commentList is Success) {
                            commentList
                        } else {
                            Fail(result.error)
                        },
                        commentListLoadMore = Fail(result.error),
                        isCommentListRefresh = if (commentList is Success) {
                            Fail(result.error, refresh)
                        } else {
                            isCommentListRefresh
                        }
                    )
                }

                else -> {
                    copy(
                        commentList = if (commentList is Success) {
                            commentList
                        } else {
                            Loading()
                        },
                        commentListLoadMore = Loading(),
                        isCommentListRefresh = if (commentList is Success) {
                            Loading(refresh)
                        } else {
                            isCommentListRefresh
                        }
                    )
                }
            }
        }
    }

    fun addCommentViaNet(commentContent: PostCommentContent) = withState { s ->
        if (!commentContent.valid || s.addCommentResult is Loading) return@withState
        clearReplyTarget()
        val ts = System.currentTimeMillis()
        val requestBody = PostCommentRequestBody(
            commentContent.text,
            MODULE_PGC,
            s.gameId,
            mediaList = commentContent.mediaList
        )
        val tempComment = requestBody.toPostComment(
            "",
            accountInteractor.accountLiveData.value,
            ts
        )
        repo.addPostComment(requestBody).map {
            Analytics.track(EventConstants.GAME_REVIEW_PUBLISH_SUCCESS) {
                put("gameid", s.gameId)
                put("from", 1)
                put("reviewid", it.data.orEmpty())
                put("content", commentContent.text)
                put("time", DateUtil.getTodayHM())
            }
            Analytics.track(
                EventConstants.GAME_REVIEW_REPLIES_SUCCESS,
                "type" to 1,
                "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_PGC,
                "gameid" to moduleContentId
            )
            val result = tempComment.copy(commentId = it.data.orEmpty())
            if (result.commentId.isNotEmpty()) {
                addComment(result)
            }
            result to it.toastMsg
        }.execute { result ->
            when (result) {
                is Success -> {
                    val (data, toastMsg) = result()
                    copy(
                        addCommentResult = if (data.commentId.isNotEmpty()) {
                            Success(data)
                        } else {
                            Fail(ApiDataException(String::class))
                        },
                        toast = if (toastMsg.isNullOrEmpty()) {
                            toast
                        } else {
                            toast.toMsg(toastMsg)
                        }
                    )
                }

                is Fail -> {
                    copy(addCommentResult = Fail(result.error, tempComment))
                }

                else -> {
                    copy(addCommentResult = Loading())
                }
            }
        }
    }

    fun addReplyViaNet(replyContent: PostCommentContent) = withState { s ->
        if (!replyContent.valid || s.addCommentResult is Loading) return@withState
        val replyTarget = getReplyTarget() ?: return@withState
        clearReplyTarget()
        val ts = System.currentTimeMillis()
        val userInfo = accountInteractor.accountLiveData.value
        val requestBody = if (replyTarget.isTargetComment) {
            PostReplyRequestBody(
                replyContent.text,
                userInfo?.uuid.orEmpty(),
                replyTarget.asComment.commentId,
                mediaList = replyContent.mediaList
            )
        } else {
            val targetReply = replyTarget.asReply
            PostReplyRequestBody(
                content = replyContent.text,
                uid = userInfo?.uuid.orEmpty(),
                commentId = replyTarget.commentId.orEmpty(),
                replyUid = targetReply.uid,
                replyNickname = targetReply.nickname,
                replyContentId = targetReply.replyId,
                mediaList = replyContent.mediaList
            )
        }
        repo.addPostReply(requestBody).map {
            Analytics.track(
                EventConstants.GAME_REVIEW_REPLIES_SUCCESS,
                "type" to 2,
                "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_PGC,
                "gameid" to moduleContentId
            )
            val result = requestBody.toPostReply(it, userInfo, ts)
            addReply(result to replyTarget)
            result
        }.execute {
            copy(addReplyResult = it)
        }
    }

    fun getOperationList() = withState { s ->
        if (s.operationList is Loading) return@withState
        repo.getGameDetailOperationInfo("game_detail", s.gameId, 1, 3).map {
            it.dataList.take(3)
        }.execute {
            copy(operationList = it)
        }
    }

    fun updateSortType(sortType: Int) = withState { s ->
        if (s.commentListLoadMore is Loading) return@withState
        if (s.sortType != sortType || s.filterType != null) {
            setState { copy(sortType = sortType, filterType = null) }
        }
        getCommentList(true)
    }

    fun updateFilterType(filterType: Int) = withState { s ->
        if (s.commentListLoadMore is Loading) return@withState
        if (s.filterType != filterType) {
            setState {
                copy(
                    sortType = PostCommentListRequestBody.QUERY_TYPE_DEFAULT,
                    filterType = filterType
                )
            }
        }
        getCommentList(true)
    }

    fun follow() = withState { s ->
        val authorId = authorId ?: return@withState
        val oldFollow = s.follow
        val toFollow = !oldFollow
        Analytics.track(
            EventConstants.EVENT_FOLLOW_CLICK,
            "userid" to authorId,
            "location" to "3",
            "type" to (if (toFollow) "1" else "2"),
            "creation_type" to "1"
        )
        if (toFollow) {
            repo.relationAdd(authorId, RelationType.Follow.value)
        } else {
            repo.relationDel(authorId, RelationType.Follow.value)
        }.map {
            check(it.succeeded) { it.message.orEmpty() }
            EventBus.getDefault().post(UserFollowEvent(authorId, toFollow, UserFollowEvent.FROM_GAME_DETAIL))
            true
        }.execute { result ->
            when (result) {
                is Success -> {
                    copy(follow = toFollow)
                }

                else -> this
            }
        }
    }

    fun follow(isFollow: Boolean) = withState { s ->
        if (s.follow == isFollow) return@withState
        setState { copy(follow = isFollow) }
    }

    fun like() = withState { s ->
        val (isLike, likeCount) = s.likeStatus() ?: (true to -1L)
        val newIsLike = !isLike
        val newLikeCount: Long
        val newOpinion = if (newIsLike) {
            newLikeCount = likeCount + 1
            ReviewOpinion.IS_LIKE
        } else {
            newLikeCount = (likeCount - 1).coerceAtLeast(0)
            ReviewOpinion.IS_NONE
        }
        repo.saveCommunityLike(s.gameId, newOpinion, ReviewOpinion.RES_TYPE_GAME).map {
            check(it.succeeded && it.data == true) { it.message.orEmpty() }
            CpEventBus.post(UpdateGameLikeEvent(s.gameId, newLikeCount))
        }.execute { result ->
            when (result) {
                is Fail -> {
                    copy(likeStatus = result.map { isLike to likeCount })
                }

                else -> {
                    copy(likeStatus = Success(newIsLike to newLikeCount))
                }
            }
        }
    }

    /**
     * 获取进入详情页次数
     */
    fun getGameDetailEnteredTimes(gameId: String): Long {
        return repo.getGameDetailEnteredTimes(gameId)
    }

    fun updatePin(isPinTop: Boolean) {
        setState { copy(isPin = isPinTop) }
    }

    override fun PgcGameDetailState.updateCommentList(
        result: PagingApiResult<PostComment>?,
        msg: Any?
    ): PgcGameDetailState {
        return if (result == null && msg == null) {
            this
        } else {
            copy(
                commentList = if (result != null) commentList.copyEx(result) else commentList,
                toast = if (msg != null) toast.tryToMsg(msg) else toast
            )
        }
    }

    override fun clearCommentPinRedDot() = withState { s ->
        super.clearCommentPinRedDot()
        if (!s.showCommentPinRedDot) return@withState
        setState { copy(showCommentPinRedDot = false) }
    }

    override fun invokeToast(resId: Int) = withState {
        setState { copy(toast = toast.toResMsg(resId)) }
    }

    @Subscribe
    fun onClearRedDotEvent(event: ClearRedDotEvent) = withState { s ->
        if (!event.isCommentPin || !s.showCommentPinRedDot) return@withState
        setState { copy(showCommentPinRedDot = false) }
    }

    override fun onCleared() {
        unregisterHermes()
        super.onCleared()
    }

    companion object : KoinViewModelFactory<PgcGameDetailViewModel, PgcGameDetailState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: PgcGameDetailState
        ): PgcGameDetailViewModel {
            return PgcGameDetailViewModel(state, get(), get(), get(), get())
        }

        override fun ComponentCallbacks.initialState(viewModelContext: ViewModelContext): PgcGameDetailState? {
            val args = viewModelContext.args as PgcGameDetailFragmentArgs
            val accountInteractor: AccountInteractor = get()

            return PgcGameDetailState(
                args.gId,
                args.targetCommentId,
                args.targetReplyId,
                accountInteractor.showCommentPinRedDot
            )
        }
    }
}