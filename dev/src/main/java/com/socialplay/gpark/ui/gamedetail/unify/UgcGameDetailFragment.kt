package com.socialplay.gpark.ui.gamedetail.unify

import android.os.Bundle
import android.os.Parcelable
import android.view.View
import android.widget.ImageView
import androidx.core.view.doOnNextLayout
import androidx.core.view.postDelayed
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.meta.box.biz.friend.model.LabelInfo
import com.meta.lib.mwbiz.MWLifeCallback
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.ImageInfo
import com.socialplay.gpark.data.model.editor.UgcGameDetail
import com.socialplay.gpark.data.model.game.OperationInfo
import com.socialplay.gpark.data.model.post.AddPostCommentReplyTarget
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.data.model.share.ShareFeature
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.editor.EditorGameLaunchHelper
import com.socialplay.gpark.function.editor.IEditorLaunchCallback
import com.socialplay.gpark.function.editor.LaunchOverResult
import com.socialplay.gpark.function.mw.MWGameStartScenes
import com.socialplay.gpark.function.mw.launch.exception.TSEngineVersionNotMatchException
import com.socialplay.gpark.function.mw.launch.exception.TSUserCancelledException
import com.socialplay.gpark.function.mw.launch.ui.TSEngineNotMatchDialog
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.account.AccPwdV7Dialog
import com.socialplay.gpark.ui.account.AccPwdV7DialogArgs
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.editor.create.EditorCreateV2Fragment
import com.socialplay.gpark.ui.editor.create.EditorCreateViewModel
import com.socialplay.gpark.ui.gamedetail.ClickType
import com.socialplay.gpark.ui.gamereview.dialog.GameAppraiseAdapter
import com.socialplay.gpark.ui.imgpre.v2.OpenPreviewBuilder
import com.socialplay.gpark.ui.imgpre.ImgPreDialogFragment
import com.socialplay.gpark.ui.mgs.danmu.advanced.PAGE_ANIMATION_DURATION
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialog
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialogParams
import com.socialplay.gpark.ui.profile.BaseProfilePage
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialog
import com.socialplay.gpark.ui.reportBlock.ReportSuccessDialogAnalyticsParams
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.view.ExpandableTextView
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setFragmentResultListenerByHostFragment
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.ifNullOrEmpty
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/22
 *     desc   :
 * </pre>
 */
@Parcelize
data class UgcGameDetailFragmentArgs(
    val ugcId: String,
    val parentId: String,
    val resIdBean: ResIdBean,
    val targetCommentId: String?,
    val targetReplyId: String?,
    val source: String
) : Parcelable {
    companion object {
        const val DEFAULT_SOURCE = "other"
    }
}

class UgcGameDetailFragment : BaseGameDetailCommonFragment() {

    companion object {
        const val TRACK_TAG = "game_review"
    }

    override val gameId: String
        get() = args.ugcId

    override val gameType = GAME_TYPE_UGC

    override val enableShare: Boolean
        get() = PandoraToggle.enableShareUgc

    private val vm: UgcGameDetailViewModel by fragmentViewModel()
    private val editorViewModel by viewModel<EditorCreateViewModel>()
    private val args by args<UgcGameDetailFragmentArgs>()

    private val pageType = 1L

    override val itemListener = ItemListener()

    private val operationController by lazy { buildOperationController() }

    private var editorGameLaunchHelper: EditorGameLaunchHelper? = null

    private var contentHeight = 0
    private var descExpandState = ExpandableTextView.STATE_SHRINK

    private val gameStartScenes by lazy { MWGameStartScenes(this) }

    private val editorDownloadCallback: IEditorLaunchCallback = object : IEditorLaunchCallback {
        override fun onLaunchOver(result: LaunchOverResult) {
            Timber.d("checkcheck onLaunchOver, ${result.launchSuccess}, ${if (!result.launchSuccess) result.msg else null}")
            if (isBindingAvailable()) {
                if (result.e is TSUserCancelledException) {
                    hideLoadingUI(result.launchSuccess, result.msg, result.needGoMineLocal)
                } else if (result.e is TSEngineVersionNotMatchException) {
                    TSEngineNotMatchDialog.show(this@UgcGameDetailFragment, result.gameInfo?.icon)
                    hideLoadingUI(result.launchSuccess, "", result.needGoMineLocal)
                } else {
                    hideLoadingUI(result.launchSuccess, result.msg, result.needGoMineLocal)
                }
            }
        }

        override fun onChecking(
            gameInfo: GameDetailInfo?,
            id: String?,
            path: String?,
            type: String?
        ) {
            Timber.d("checkcheck onLaunchingGame type:$type")
            if (isBindingAvailable() && type == EditorGameLaunchHelper.TYPE_TEMPLATE) {
                showLoadingUI()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        // 立即调用super.onViewCreated以确保动画正常工作
        super.onViewCreated(view, savedInstanceState)

        // 使用BaseFragment的动画结束回调，确保在动画完成后再初始化
        runAfterEnterAnimationEnd {
            initView()
            initData()
            initTs()
        }
    }

    private fun initView() {
        if (contentHeight != 0) {
            binding.clTop.minHeight = contentHeight
        }

        binding.lv.showLoading()
        binding.dpbEnter2.setCurrentText(getString(R.string.game_detail_page_play_btn))
        binding.dpbEnter2.setOnAntiViolenceClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.PlayGame
            )
            clickStartGame()
        }
        binding.vAuthorClick.setOnAntiViolenceClickListener {
            handleUserClick()
        }
        binding.tvFollowBtn.setOnAntiViolenceClickListener {
            vm.follow()
        }
        binding.tvTopFollowBtn.setOnAntiViolenceClickListener {
            vm.follow()
        }
        binding.bottomCommentInput.setOnLikeClickListener {
            shouldPlayLikeAnim = true
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.Like
            )
            val type = if (vm.isLike) {
                Analytics.track(
                    EventConstants.GAME_DETAIL_PAGE_LIKE_CANCEL,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                "1"
            } else {
                Analytics.track(
                    EventConstants.GAME_DETAIL_PAGE_LIKE,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                DialogShowManager.triggerLike(this)
                "2"
            }
            Analytics.track(
                EventConstants.UGC_DETAIL_PAGE_LIKE,
                "ugcid" to args.ugcId,
                "parentid" to vm.parentId,
                "type" to type
            )
            vm.like()
        }
        binding.ivMyAvatar.setOnAntiViolenceClickListener {
            showReplyDialog(AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName))
        }
        binding.tvReplyHint.setOnAntiViolenceClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.CommentMiddle
            )
            showReplyDialog(AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName))
        }
        binding.bottomCommentInput.setOnReplyClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.CommentBar
            )
            showReplyDialog(AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName), source = 3)
        }
        binding.ivEmojiBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName),
                showEmoji = true
            )
        }
        binding.bottomCommentInput.ivEmojiBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName),
                showEmoji = true
            )
        }
        binding.ivImageBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName),
                showImage = true
            )
        }
        binding.bottomCommentInput.ivImageBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName),
                showImage = true
            )
        }
        binding.rvNotice.setController(operationController)
        binding.tvDescription.setExpandListener(object : ExpandableTextView.OnExpandListener {
            override fun onExpand(view: ExpandableTextView) {
                Analytics.track(
                    EventConstants.DETAIL_DESCRIPTION_MORE_CLICK,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                descExpandState = ExpandableTextView.STATE_EXPAND
            }

            override fun onShrink(view: ExpandableTextView) {
                descExpandState = ExpandableTextView.STATE_SHRINK
            }
        })
        binding.bottomCommentInput.setOnCommentClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.CommentIcon
            )
            if (!tryScrollToComment()) {
                showReplyDialog(
                    AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName),
                    source = 2
                )
            }
        }
        binding.ulv.setListener(viewLifecycleOwner) {
            UserLabelView.showDescDialog(this, it)
        }
        binding.updateBtn.setOnAntiViolenceClickListener {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.Update
            )
            vm.searchLocalProjectByUgid()
        }
        visibleList(
            binding.honorLikeLayout,
            binding.honorDivider0,
            binding.midHonorLikeLayout,
            binding.tvHonorNewLikesHotTips,
            visible = false
        )

        if (PandoraToggle.gameDetailShowTemplate) {
            // 通过游戏模板创建游戏游戏
            binding.btnAssetsCreate.setOnAntiViolenceClickListener {
                Analytics.track(
                    EventConstants.EVENT_ITEM_CLICK,
                    "show_categoryid" to CategoryId.GAME_DETAIL_TEMPLATE,
                )
                Analytics.track(
                    EventConstants.EVENT_UGC_CREATE_TEMPLATE_CLICK,
                    "gameid" to args.ugcId,
                    "show_categoryid" to CategoryId.GAME_DETAIL_TEMPLATE,
                )
                val template = vm.oldState.gameUsedTemplate ?: return@setOnAntiViolenceClickListener
                val isUpperMaxLimit = editorViewModel.maxCreationCountLiveData.value?.let {
                    it.first >= it.second
                } == true
                if (isUpperMaxLimit) {
                    ToastUtil.showShort(R.string.ugc_work_amount_reach_limit)
                    return@setOnAntiViolenceClickListener
                }
                val gameCode = template.crGameId ?: return@setOnAntiViolenceClickListener
                editorViewModel.getTemplateByGameCode(gameCode, 2L)
            }
        }
    }

    private var isFirstLoadGameDetail = true

    private fun initData() {
        setFragmentResultListenerByHostFragment(
            BaseProfilePage.RESULT_FOLLOW,
            viewLifecycleOwner
        ) { _, bundle ->
            val uuid = bundle.getString(BaseProfilePage.KEY_UUID)
            if (vm.authorId == uuid) {
                vm.follow(bundle.getBoolean(BaseProfilePage.KEY_IS_FOLLOW))
            }
        }
        vm.accountInteractor.accountLiveData.observe(viewLifecycleOwner) {
            updateMyAvatar(it?.portrait)
        }
        vm.onAsync(UgcGameDetailState::detail, onFail = { _, data ->
            if (data == null) {
                binding.ivMoreBtn.gone()
            }
        }) {
            vm.initCommentList()
            initDetail(it)
            if (isFirstLoadGameDetail) {
                isFirstLoadGameDetail = false
                analyticsObserve.sendEnterGameDetailAnalytic(
                    args.ugcId,
                    "",
                    -1,
                    "UGC",
                    args.resIdBean,
                    creatorType = gameType,
                    isMyGame = isMe()
                )
                triggerGameDetailScene()
            }
        }
        vm.onAsync(
            UgcGameDetailState::likeStatus,
            onFail = { _, pair ->
                if (pair != null) {
                    updateLike(pair.first, pair.second)
                }
            }
        ) { (isLike, likeCount) ->
            updateLike(isLike, likeCount)
        }
        vm.onEach(
            UgcGameDetailState::commentList,
            UgcGameDetailState::commentListLoadMore
        ) { commentList, loadMore ->
            updateCommentStatus(commentList, loadMore)
            if (commentList is Success && loadMore is Success) {
                Analytics.track(
                    EventConstants.UGC_DETAIL_PAGE_REVIEW_SHOW,
                    "gameid" to args.ugcId
                )
            }
        }
        vm.onEach(UgcGameDetailState::flowerCount) {
            updateFlowerCount(it)
        }
        vm.onEach(UgcGameDetailState::follow) {
            updateFollow(it)
        }
        vm.onEach(UgcGameDetailState::operationList) {
            if (it().isNullOrEmpty()) {
                binding.rvNotice.gone()
            } else {
                binding.rvNotice.visible()
            }
        }
        vm.onEach(
            UgcGameDetailState::sortType,
            UgcGameDetailState::filterType
        ) { sortType, filterType ->
            updateSortTypeText(sortType, filterType)
        }
        vm.onEach(UgcGameDetailState::isCommentListRefresh, deliveryMode = uniqueOnly()) {
            if (it.invoke() == true) {
                when (it) {
                    is Loading -> {
                        animateCommentRefresh(dp(52))
                    }

                    is Success -> {
                        animateCommentRefresh(0)
                    }

                    else -> {
                        animateCommentRefresh(0)
                    }
                }
            }
        }

        vm.onEach(
            UgcGameDetailState::deleteUgcGameResult,
            deliveryMode = uniqueOnly(),
        ) { deleteUgcGameAsync ->
            val deleteResult = deleteUgcGameAsync.invoke()
            if (deleteUgcGameAsync is Success) {
                toast(
                    if (deleteResult == true) {
                        R.string.map_delete_successful
                    } else {
                        R.string.toast_delete_game_failed
                    }
                )
                if (deleteResult == true) {
                    sharedViewModel.delete(gameId)
                }
                navigateUp()
            } else if (deleteUgcGameAsync is Fail) {
                toast(R.string.toast_delete_game_failed)
            }
        }

        vm.onAsync(
            UgcGameDetailState::localProjectCallback,
            deliveryMode = uniqueOnly(),
            onFail = { }
        ) {
            if (it != null) {
                editorGameLaunchHelper?.startLocalGame(
                    this,
                    it.jsonConfig.gid,
                    it.path,
                    it.jsonConfig.parentPackageName.orEmpty(),
                    it.jsonConfig.fileId.orEmpty(),
                    ResIdBean().setClickGameTime(System.currentTimeMillis())
                        .setGameCode(it.jsonConfig.gid)
                        .setGameId(it.jsonConfig.gid)
                        .setCategoryID(CategoryId.UGC_GAME_DETAIL)
                        .setTsType(ResIdBean.TS_TYPE_LOCAL)
                )
            } else {
                MetaRouter.MobileEditor.creation(this, initTab = EditorCreateV2Fragment.TAB_MINE)
            }
        }

        if (PandoraToggle.gameDetailShowTemplate) {
            visibleList(
                binding.tvCreativeAssetsTitle,
                binding.ivAssetCover,
                binding.viewAssetTemplateBg,
                binding.viewAssetTemplateBg2,
                binding.ivAssetsRankIcon,
                binding.tvAssetsTemplate,
                binding.ivAssetsStar1,
                binding.ivAssetsStar2,
                binding.ivAssetsStar3,
                binding.spaceAssetsStarEnd,
                binding.tvAssetsName,
                binding.tagsAssets,
                binding.btnAssetsCreate,
                visible = true
            )
            visibleList(
                binding.tvToolkitTitle,
                binding.tvToolkitAll,
                binding.ivToolkitAll,
                binding.vToolkitAllClick,
                binding.rvToolkit,
                visible = true
            )
            vm.onEach(
                UgcGameDetailState::gameUsedTemplate
            ) { template ->
                visibleList(
                    binding.tvCreativeAssetsTitle,
                    binding.ivAssetCover,
                    binding.viewAssetTemplateBg,
                    binding.viewAssetTemplateBg2,
                    binding.ivAssetsRankIcon,
                    binding.tvAssetsTemplate,
                    binding.ivAssetsStar1,
                    binding.ivAssetsStar2,
                    binding.ivAssetsStar3,
                    binding.spaceAssetsStarEnd,
                    binding.tvAssetsName,
                    binding.tagsAssets,
                    binding.btnAssetsCreate,
                    visible = template != null
                )
                if (template != null) {
                    Analytics.track(
                        EventConstants.EVENT_ITEM_SHOW,
                        "show_categoryid" to CategoryId.GAME_DETAIL_TEMPLATE,
                    )
                    Analytics.track(
                        EventConstants.UGC_TEMPLATE_PAGE_LIST_SHOW,
                        "gameid" to gameId,
                        "show_categoryid" to CategoryId.GAME_DETAIL_TEMPLATE,
                    )
                    glide?.apply {
                        load(template.banner).placeholder(R.drawable.placeholder_corner_12)
                            .into(binding.ivAssetCover)
                    }
                    if (template.level == null || template.level <= 0) {
                        binding.ivAssetsStar1.setImageResource(R.drawable.ic_assets_rank_start_unselected)
                        binding.ivAssetsStar2.setImageResource(R.drawable.ic_assets_rank_start_unselected)
                        binding.ivAssetsStar3.setImageResource(R.drawable.ic_assets_rank_start_unselected)
                    } else if (template.level == 1) {
                        binding.ivAssetsStar1.setImageResource(R.drawable.ic_assets_rank_start_selected)
                        binding.ivAssetsStar2.setImageResource(R.drawable.ic_assets_rank_start_unselected)
                        binding.ivAssetsStar3.setImageResource(R.drawable.ic_assets_rank_start_unselected)
                    } else if (template.level == 2) {
                        binding.ivAssetsStar1.setImageResource(R.drawable.ic_assets_rank_start_selected)
                        binding.ivAssetsStar2.setImageResource(R.drawable.ic_assets_rank_start_selected)
                        binding.ivAssetsStar3.setImageResource(R.drawable.ic_assets_rank_start_unselected)
                    } else {
                        binding.ivAssetsStar1.setImageResource(R.drawable.ic_assets_rank_start_selected)
                        binding.ivAssetsStar2.setImageResource(R.drawable.ic_assets_rank_start_selected)
                        binding.ivAssetsStar3.setImageResource(R.drawable.ic_assets_rank_start_selected)
                    }
                    binding.tvAssetsName.text = template.name ?: ""
                    if (!template.tag.isNullOrEmpty()) {
                        binding.tagsAssets.setTags(listOf(template.tag))
                    } else {
                        binding.tagsAssets.gone()
                    }
                }
            }
            vm.getGameTemplate(args.ugcId)
        }
        vm.setupRefreshLoading(
            UgcGameDetailState::detail,
            binding.lv,
            binding.mrl
        ) {
            vm.getUgcDetailInfo(true)
            vm.getOperationList()
            commonVm.loadSendFlowerConditions(gameId)
            getCommentList(true)
        }
        vm.registerToast(UgcGameDetailState::toast)
        vm.registerAsyncErrorToast(UgcGameDetailState::detail)
        vm.registerAsyncErrorToast(UgcGameDetailState::commentListLoadMore)
        vm.registerAsyncErrorToast(UgcGameDetailState::addCommentResult)
        vm.registerAsyncErrorToast(UgcGameDetailState::addReplyResult)
        vm.registerAsyncErrorToast(UgcGameDetailState::likeStatus)
        editorViewModel.errorToastLiveData.observe(viewLifecycleOwner) {
            // 报错
            toast(it ?: getString(R.string.common_failed))
        }

        if (PandoraToggle.gameDetailShowTemplate) {
            editorViewModel.templateCallback.observe(viewLifecycleOwner) {
                editorGameLaunchHelper?.startTemplateGame(
                    this,
                    it,
                    ResIdBean.newInstance().setCategoryID(CategoryId.GAME_DETAIL_TEMPLATE.toInt())
                )
            }
        }
    }

    private fun initTs() {
        editorGameLaunchHelper = EditorGameLaunchHelper(editorDownloadCallback)
        editorGameLaunchHelper?.init(this)
        val invoke = { data: Pair<String, String> ->
            Timber.d("checkcheck onReceivedStartGame errorMsg:${data.first}")
            if (isBindingAvailable()) {
                viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                    if (data.first.isNullOrEmpty()) {
                        gameStartScenes.show()
                    } else {
                        hideLoadingUI(false, null, false)
                    }
                }
            }
        }
        MWLifeCallback.startGame.observe(viewLifecycleOwner, false, observer = invoke)
        MWLifeCallback.startLocalGame.observe(viewLifecycleOwner, false, observer = invoke)
    }

    private var isMe = false
    override fun isMe(): Boolean {
        return isMe
    }

    override fun isPin(): Boolean? {
        return vm.isPin
    }

    override fun updatePin(isPinTop: Boolean) {
        vm.updatePin(isPinTop)
    }

    private fun initDetail(detail: UgcGameDetail) {
        binding.lv.hide()
        binding.ivMoreBtn.visible()
        binding.tvGameName.text = detail.ugcGameName
        binding.tvTitleBarGameName.text = detail.ugcGameName
        binding.tvDescription.updateForRecyclerView(
            detail.ugcGameDesc.ifNullOrEmpty { getString(R.string.ugc_detail_default_desc) },
            screenWidth - dp(32),
            descExpandState
        )
        updatePv(detail.pvCount)
        updateTime(detail.updateTime.coerceAtLeast(detail.releaseTime))
        // UGC 游戏的 banner 图片就是宽图
        updateBanner(listOf(ImageInfo(url = detail.banner, isHor = true)), detail.bannerColor)
        // 作者信息
        binding.ulv.show(
            detail.tagIds,
            detail.labelInfo,
            isCreator = true,
            glide = glide
        )
        val isMe = vm.isMe(detail.userUuid)
        this.isMe = isMe
        super.updateIsMe(isMe)
        if (PandoraToggle.gameDetailShowTemplate) {
            super.initUgcDesignToolkit(!isMe)
        }

        visibleList(
            binding.tvFollowBtn,
            binding.tvTopFollowBtn,
            binding.buildBtnLayout,
            commentMorePopup.sortPopupBinding.mtvAuthorOnly,
            commentMorePopup.sortPopupBinding.vAuthorOnlyClick,
            commentMorePopup.sortPopupBinding.vDivider4,
            visible = !isMe
        )
        binding.updateBtn.visible(isMe)
        glide?.run {
            load(detail.author?.avatar).placeholder(R.drawable.placeholder_circle)
                .error(R.drawable.placeholder_circle)
                .circleCrop()
                .into(binding.ivAvatar)
            load(detail.author?.avatar).placeholder(R.drawable.placeholder_circle)
                .error(R.drawable.placeholder_circle)
                .circleCrop()
                .into(binding.ivAuthorAvatar)
        }
        binding.tvUsername.text = detail.author?.name
        binding.tvAuthorName.text = detail.author?.name
        binding.tvPortfolio.text = SpannableHelper.Builder()
            .text(getString(R.string.game_detail_page_maps_count))
            .textAppearance(context, R.style.MetaTextView_S12_PoppinsMedium500)
            .colorRes(R.color.color_999999)
            .text("${detail.userReleaseCount}")
            .textAppearance(context, R.style.MetaTextView_S12_PoppinsRegular400)
            .colorRes(R.color.color_999999)
            .build()
        binding.tvUserId.text = SpannableHelper.Builder()
            .text(getString(R.string.game_detail_page_user_id))
            .textAppearance(context, R.style.MetaTextView_S12_PoppinsMedium500)
            .colorRes(R.color.color_999999)
            .text(detail.author?.number ?: "unknown")
            .textAppearance(context, R.style.MetaTextView_S12_PoppinsRegular400)
            .colorRes(R.color.color_999999)
            .build()
        //MW引擎不兼容提示

        updateMwCompatibilityTips(detail.mwTip)
        updateIncrementData(detail.newAddLoveQuantity, detail.newAddFlowerCount)
        updatePlayers(detail.gameLikePlayerList)
        updateCreationTime(detail.totalEditTime)
        binding.clTop.doOnNextLayout {
            binding.clTop.minHeight = 0
        }
    }

    private fun handleUserClick() {
        Analytics.track(
            EventConstants.UGC_DETAIL_PAGE_PROFILE,
            "ugcid" to args.ugcId,
            "parentid" to vm.parentId,
        )
        val uuid = vm.detail?.userUuid ?: return
        MetaRouter.Profile.other(requireParentFragment(), uuid, "9", checkFollow = true)
    }

    private fun clickStartGame() {
        Analytics.track(
            EventConstants.UGC_DETAIL_PAGE_CLICK,
            "ugcid" to args.ugcId,
            "parentid" to vm.parentId,
        )
        vm.detail?.let { detail ->
            editorGameLaunchHelper?.startUgcGame(
                this,
                detail,
                args.resIdBean
            )
        }
    }

    override fun epoxyController() = simpleController(
        vm,
        UgcGameDetailState::commentList,
        UgcGameDetailState::commentListLoadMore,
        UgcGameDetailState::uniqueTag,
        UgcGameDetailState::showCommentPinRedDot,
    ) { comments, loadMore, uniqueTag, showCommentPinRedDot ->
        buildCommentController(comments, loadMore, uniqueTag, showCommentPinRedDot)
    }

    override fun tagsEpoxyController() = simpleController(
        vm,
        UgcGameDetailState::detail,
    ) {
        it()?.gameTags?.let {
            buildTagsController(it.mapNotNull { it.name })
        }
    }

    private fun buildOperationController() = simpleController(
        vm,
        UgcGameDetailState::operationList
    ) {
        it()?.forEachIndexed { index, item ->
            gameOperationItem(item, index, itemListener)
        }
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_UGC_DETAIL

    override fun onShareCountIncrease(data: ShareRawData) {
    }

    private fun handleOperateComment(
        view: View,
        comment: PostComment,
        commentPosition: Int,
        showRedDot: Boolean
    ) {
        handleOperationHelper(
            view,
            comment = comment,
            showRedDot = showRedDot,
            commentPosition = commentPosition
        )
    }

    private fun handleOperateReply(
        view: View,
        reply: PostReply,
        replyPosition: Int,
        commentPosition: Int,
        isAuthorReply: Boolean
    ) {
        handleOperationHelper(
            view,
            reply = reply,
            commentPosition = commentPosition,
            replyPosition = replyPosition,
            isAuthorReply = isAuthorReply
        )
    }

    private fun handleOperationHelper(
        view: View,
        comment: PostComment? = null,
        showRedDot: Boolean = false,
        reply: PostReply? = null,
        commentPosition: Int = 0,
        replyPosition: Int = 0,
        isAuthorReply: Boolean = false
    ) {
        Analytics.track(
            EventConstants.UGC_GAME_REVIEW_SET_SHOW,
            "gameid" to args.ugcId,
            "reviewid" to (comment?.commentId ?: reply?.replyId.orEmpty()),
            "reviewtype" to if (comment != null) 1L else 2L,
            "pagetype" to pageType
        )
        val isMe = vm.isMe(comment?.uid ?: reply?.uid)
        val isCreator = vm.isCreator(vm.myUuid)

        commentMorePopup.operateComment(
            fragment = this@UgcGameDetailFragment,
            rootLayout = binding.root,
            view = view,
            comment = comment,
            reply = reply,
            showRedDot = showRedDot,
            isMe = isMe,
            isCreator = isCreator,
            onCopyClick = {
                val reviewId: String = comment?.commentId ?: reply?.replyId.orEmpty()
                val reviewType: Long = if (comment != null) 1L else 2L
                Analytics.track(
                    EventConstants.UGC_GAME_REVIEW_COPY_CLICK,
                    "gameid" to args.ugcId,
                    "reviewid" to reviewId,
                    "reviewtype" to reviewType,
                    "pagetype" to pageType
                )
            },
            onPinClick = {
                if (comment != null) {
                    Analytics.track(
                        EventConstants.UGC_GAME_REVIEW_TOP_CLICK,
                        "gameid" to args.ugcId,
                        "reviewid" to comment.commentId,
                        "toptype" to 0L,
                        "pagetype" to pageType
                    )
                    Analytics.track(
                        EventConstants.GAME_REVIEW_PIN_CLICK,
                        "gameid" to gameId,
                        "creatortype" to gameType
                    )
                    vm.pinComment(comment, commentPosition, showRedDot)
                }
            },
            onUnpinClick = {
                if (comment != null) {
                    Analytics.track(
                        EventConstants.UGC_GAME_REVIEW_TOP_CLICK,
                        "gameid" to args.ugcId,
                        "reviewid" to comment.commentId,
                        "toptype" to 1L,
                        "pagetype" to pageType
                    )
                    Analytics.track(
                        EventConstants.GAME_CANCEL_REVIEW_PIN_CLICK,
                        "gameid" to gameId,
                        "creatortype" to gameType
                    )
                    vm.pinComment(comment, commentPosition, showRedDot)
                }
            },
            onReportClick = {
                if (comment != null) {
                    goReport(comment.commentId, ReportType.UgcReview)
                } else if (reply != null) {
                    goReport(reply.replyId, ReportType.UgcReply)
                }
            },
            onDeleteCancel = {},
            onDeleteConfirm = {
                if (comment != null) {
                    vm.deleteComment(comment, commentPosition)
                } else if (reply != null) {
                    vm.deleteReply(
                        reply,
                        replyPosition,
                        commentPosition,
                        isAuthorReply
                    )
                }
            }
        )
    }

    private fun goReport(reportId: String, reportType: ReportType) {
        Analytics.track(
            EventConstants.EVENT_REVIEW_REPORT_CLICK,
            "gameid" to gameId,
            "type" to (if (reportType == ReportType.UgcReview) {
                GameAppraiseAdapter.AppraiseItemListener.TYPE_COMMENT
            } else {
                GameAppraiseAdapter.AppraiseItemListener.TYPE_REPLAY
            })
        )
        MetaRouter.Report.postReport(this, reportId, reportType, gameId = args.ugcId) {
            if (it) {
                val analyticsParams = when (reportType) {
                    ReportType.UgcReview -> {
                        ReportSuccessDialogAnalyticsParams.GameComment(
                            gameId = gameId,
                            commentId = reportId,
                        )
                    }

                    ReportType.UgcReply -> {
                        ReportSuccessDialogAnalyticsParams.GameCommentReply(
                            gameId = gameId,
                            replyId = reportId,
                        )
                    }

                    else -> {
                        null
                    }
                }
                ReportReasonDialog.showReportSuccessDialog(this, analyticsParams)
                Analytics.track(
                    EventConstants.UGC_GAME_REVIEW_REPORT_SUCCESS,
                    "gameid" to args.ugcId,
                    "reviewid" to reportId,
                    "reviewtype" to if (reportType == ReportType.UgcReview) 1L else 2L
                )
            }
        }
    }

    /**
     * @param source 来源: 1: 默认; 2: 点击底部评论计数图标; 3: 点击底部评论输入栏
     */
    private fun showReplyDialog(
        target: AddPostCommentReplyTarget,
        showEmoji: Boolean = false,
        showImage: Boolean = false,
        source: Int = 1
    ) {
        AccPwdV7Dialog.show(this, AccPwdV7DialogArgs.SOURCE_PUBLISH_UGC_COMMENT) {
            if (it && isBindingAvailable()) {
                val replyType: Long
                val reviewId: String
                val type: Int
                if (target.isTargetComment) {
                    Analytics.track(
                        EventConstants.GAME_MORE_REVIEW_WRITE_CLICK,
                        "gameid" to gameId,
                        "creatortype" to gameType
                    )
                    replyType = 0L
                    reviewId = target.asComment.commentId
                    type = ArticleCommentInputDialog.TYPE_COMMENT_COMMENT
                } else if (target.isTargetReply) {
                    Analytics.track(
                        EventConstants.GAME_MORE_REVIEW_WRITE_CLICK,
                        "gameid" to gameId,
                        "creatortype" to gameType
                    )
                    replyType = 1L
                    reviewId = target.asReply.replyId
                    type = ArticleCommentInputDialog.TYPE_COMMENT_REPLAY
                } else {
                    Analytics.track(
                        EventConstants.GAME_FIRST_REVIEW_WRITE_CLICK,
                        "source" to source,
                        "gameid" to gameId,
                        "creatortype" to gameType
                    )
                    replyType = -1L
                    reviewId = args.ugcId
                    type = ArticleCommentInputDialog.TYPE_COMMENT_ARTICLE
                }
                if (replyType != -1L) {
                    Analytics.track(
                        EventConstants.UGC_GAME_REVIEW_REPLAY_CLICK,
                        "reviewid" to reviewId,
                        "gameid" to args.ugcId,
                        "replaytype" to replyType,
                        "pagetype" to pageType
                    )
                }
                vm.setReplyTarget(target)
                ArticleCommentInputDialog.show(
                    this,
                    replyUniqueKey = "$gameId-$reviewId",
                    target.toNickname,
                    gameId,
                    null,
                    type,
                    0.7f,
                    showEmoji,
                    showImage,
                    getPageName(),
                    ArticleCommentInputDialogParams.CONTENT_TYPE_UGC_DETAIL,
                    1,
                    true,
                    pageName = getPageName()
                ) {
                    if (it == null || !it.valid) return@show
                    if (target.isTargetPost) {
                        vm.addCommentViaNet(it)
                    } else {
                        vm.addReplyViaNet(it)
                    }
                }
            }
        }
    }

    private fun trackLike(id: String, type: Long) {
        Analytics.track(
            EventConstants.UGC_GAME_REVIEW_LIKE_CLICK,
            "reviewid" to id,
            "gameid" to args.ugcId,
            "reviewtype" to type,
            "pagetype" to pageType
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        args.resIdBean.setTsType(ResIdBean.TS_TYPE_UCG).setIconType(ResIdBean.ICON_TYPE_UGC)
        vm.apiMonitor(
            this,
            UgcGameDetailState::detail
        )
        operationController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        operationController.onSaveInstanceState(outState)
    }

    override fun getCommentList(isRefresh: Boolean) {
        vm.getCommentList(isRefresh)
    }

    override fun initCommentList() {
        vm.initCommentList()
    }

    override fun onMoreClick() {
        val state = vm.oldState
        val detail = state.detail() ?: return
        val features = mutableListOf<ShareFeature>()
        features.add(
            ShareFeature(
                FEAT_REPORT,
                R.drawable.ic_share_feat_feedback,
                titleRes = R.string.feedback,
                clickTrackParams = commonMoreTrackParams("feedback")
            )
        )
        if (PandoraToggle.enableGameGiftOption && isMe()) {
            features.add(
                ShareFeature(
                    FEAT_SEND_FLOWERS,
                    R.drawable.ic_share_feat_send_flowers,
                    titleRes = R.string.game_detail_page_more_dialog_send_flowers,
                    clickTrackParams = commonMoreTrackParams("flower")
                )
            )
        }
        if (isMe()) {
            // 作品权限设置的需求暂时不做
//                features.add(
//                    ShareFeature(
//                        FEAT_ACCESS_SETTINGS,
//                        R.drawable.ic_share_feat_access_settings,
//                        titleRes = R.string.game_detail_page_more_dialog_access_settings
//                    )
//                )
        }
        if (isMe() && isPin() == false) {
            features.add(
                ShareFeature(
                    FEAT_PIN,
                    R.drawable.ic_share_feat_pin,
                    titleRes = R.string.game_detail_page_more_dialog_pin,
                    clickTrackParams = commonMoreTrackParams("pin")
                )
            )
        }
        if (isMe() && isPin() == true) {
            features.add(
                ShareFeature(
                    FEAT_UN_PIN,
                    R.drawable.ic_share_feat_unpin,
                    titleRes = R.string.game_detail_page_more_dialog_cancel_pin,
                    clickTrackParams = commonMoreTrackParams("pin")
                )
            )
        }
        if (isMe()) {
            features.add(
                ShareFeature(
                    ShareFeature.FEAT_DELETE_GAME,
                    R.drawable.ic_share_feat_delete,
                    titleRes = R.string.game_detail_page_more_dialog_delete,
                    clickTrackParams = commonMoreTrackParams("delete")
                )
            )
        }
        GlobalShareDialog.show(
            childFragmentManager,
            ShareRawData.ugc(
                detail.copy(loveQuantity = state.likeStatus()?.second ?: 0),
                args.resIdBean.getReqId()
            ),
            requestKey = vm.requestKey,
            features = features
        )
    }

    /**
     * 隐藏加载界面
     */
    private fun hideLoadingUI(launchSuccess: Boolean, msg: String?, needGoMine: Boolean) {
        viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            if (!launchSuccess) {
                toast(msg ?: getString(R.string.verse_download_failed))
                gameStartScenes.hide()
            }
            if (needGoMine && PandoraToggle.isUgcBackup) {
                MetaRouter.MobileEditor.creation(this@UgcGameDetailFragment, initTab = 1)
            }
        }
    }

    override fun invokeShareFeature(feature: ShareFeature) {
        super.invokeShareFeature(feature)
        when (feature.featureId) {
            FEAT_ACCESS_SETTINGS -> {
                // 作品权限设置的需求暂时不做
            }

            ShareFeature.FEAT_DELETE_GAME -> {
                vm.deleteUgcGame(gameId)
            }
        }
    }

    /**
     * 展示加载界面
     */
    private fun showLoadingUI() {
        gameStartScenes.show()
    }

    override fun onDestroyView() {
        contentHeight = binding.clTop.height
        editorGameLaunchHelper?.onDestroyHelper()
        editorGameLaunchHelper = null
        super.onDestroyView()
    }

    override fun updateSortType(sortType: Int) {
        super.updateSortType(sortType)
        vm.updateSortType(sortType)
    }

    override fun updateFilterType(filterType: Int) {
        super.updateFilterType(filterType)
        vm.updateFilterType(filterType)
    }

    inner class ItemListener : IGameDetailCommonListener {
        override fun isMe(uid: String?): Boolean {
            return vm.isMe(uid)
        }

        override fun isCreator(uid: String?): Boolean {
            return vm.isCreator(uid)
        }

        override fun iAmCreator(): Boolean {
            return vm.iAmCreator
        }

        override fun goUserPage(uid: String?) {
            if (!uid.isNullOrBlank()) {
                MetaRouter.Profile.other(this@UgcGameDetailFragment, uid, TRACK_TAG)
            }
        }

        override fun operateComment(
            view: View,
            comment: PostComment,
            commentPosition: Int,
            showRedDot: Boolean
        ) {
            handleOperateComment(view, comment, commentPosition, showRedDot)
        }

        override fun likeComment(comment: PostComment, commentPosition: Int) {
            if (!comment.isLike) {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CLICK,
                    "type" to 1,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
                DialogShowManager.triggerLike(this@UgcGameDetailFragment)
            } else {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CANCEL_CLICK,
                    "type" to 1,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
            }
            trackLike(comment.commentId, 1L)
            vm.likeComment(comment, commentPosition)
        }

        override fun reply2Comment(comment: PostComment, commentPosition: Int) {
            showReplyDialog(AddPostCommentReplyTarget(comment, commentPosition))
        }

        override fun operateReply(
            view: View,
            reply: PostReply,
            replyPosition: Int,
            commentPosition: Int,
            isAuthorReply: Boolean
        ) {
            handleOperateReply(view, reply, replyPosition, commentPosition, isAuthorReply)
        }

        override fun likeReply(
            reply: PostReply,
            replyPosition: Int,
            commentPosition: Int,
            isAuthorReply: Boolean
        ) {
            if (!reply.isLike) {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CLICK,
                    "type" to 2,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
                DialogShowManager.triggerLike(this@UgcGameDetailFragment)
            } else {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CANCEL_CLICK,
                    "type" to 2,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
            }
            trackLike(reply.replyId, 2L)
            vm.likeReply(reply, replyPosition, commentPosition, isAuthorReply)
        }

        override fun reply2Reply(reply: PostReply, commentPosition: Int) {
            showReplyDialog(AddPostCommentReplyTarget(reply, reply.commentId, commentPosition))
        }

        override fun loadMoreReply(comment: PostComment, commentPosition: Int) {
            Analytics.track(
                EventConstants.UGC_DETAIL_PAGE_REVIEW_COLLAPSE_CLICK,
                "pagetype" to pageType,
                "reviewid" to comment.commentId,
                "gameid" to args.ugcId
            )
            Analytics.track(
                EventConstants.EVENT_GAME_REVIEW_EXPAND,
                "creatortype" to gameType
            )
            vm.loadMoreReplies(comment, commentPosition)
        }

        override fun collapseReply(comment: PostComment, commentPosition: Int) {
            Analytics.track(
                EventConstants.UGC_DETAIL_PAGE_REVIEW_REPLISE_CLICK,
                "pagetype" to pageType,
                "reviewid" to comment.commentId,
                "gameid" to args.ugcId
            )
            Analytics.track(
                EventConstants.GAME_REVIEW_COLLAPSE_CLICK,
                "creatortype" to gameType
            )
            vm.collapseReply(comment, commentPosition, true)
        }

        override fun goCommentListPage() {}
        override fun previewImage(
            mediaList: List<String>?,
            imageViews: List<ImageView>,
            imagePosition: Int
        ) {
            OpenPreviewBuilder(this@UgcGameDetailFragment)
                .setImageUrls(mediaList ?: emptyList())
                .setClickViews(imageViews)
                .setClickPosition(imagePosition)
                .show()
        }

        override fun clickOperation(item: OperationInfo, position: Int) {
            analyticsObserve.sendClickBtnAnalytic(
                gameId,
                gameType,
                isMe(),
                ClickType.Notice
            )
            item.content ?: return
            if (item.isWebType()) {
                MetaRouter.Web.navigate(this@UgcGameDetailFragment, item.title, item.content)
            } else if (item.isArticleType()) {
                MetaRouter.Post.goPostDetail(
                    this@UgcGameDetailFragment,
                    item.content,
                    "ugc_detail_operation"
                )
            }
        }

        override fun showComment(comment: PostComment, commentPosition: Int) {
            if (trackGameReviewShow) {
                trackGameReviewShow = false
                Analytics.track(
                    EventConstants.GAME_REVIEW_SHOW,
                    "creatortype" to gameType
                )
            }
        }

        override fun clickLabel(data: Pair<Int, LabelInfo?>) {
            UserLabelView.showDescDialog(this@UgcGameDetailFragment, data)
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }
}