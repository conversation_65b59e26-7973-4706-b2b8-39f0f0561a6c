package com.socialplay.gpark.ui.im

import android.graphics.Color
import android.graphics.Rect
import android.graphics.Typeface
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.forEachIndexed
import androidx.core.view.isVisible
import androidx.core.view.postDelayed
import androidx.lifecycle.asLiveData
import androidx.lifecycle.lifecycleScope
import androidx.navigation.Navigator
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.ly123.tes.mgs.metacloud.message.MetaConversation
import com.ly123.tes.mgs.metacloud.model.Conversation.ConversationType
import com.meta.box.biz.friend.FriendBiz
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.ImInteractor
import com.socialplay.gpark.data.model.ListStatus
import com.socialplay.gpark.data.model.SysHeaderInfo
import com.socialplay.gpark.data.model.editor.EditorNotice
import com.socialplay.gpark.data.model.im.AbsConversationMessage
import com.socialplay.gpark.data.model.im.ConversationMessage
import com.socialplay.gpark.data.model.im.GroupConversationMessage
import com.socialplay.gpark.data.repository.GroupChatRepository
import com.socialplay.gpark.databinding.FragmentConversationListBinding
import com.socialplay.gpark.databinding.PopUpConversationListAddBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventConstants.EVENT_FIRST_PUSH_POST_CLOSE
import com.socialplay.gpark.function.analytics.EventConstants.EVENT_FIRST_PUSH_POST_SHOW
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.aibot.AiBotConversation
import com.socialplay.gpark.ui.aibot.AiBotConversationItem
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.home.adapter.HomeLoadMoreFooterAdapter
import com.socialplay.gpark.ui.im.conversation.GroupChatViewModel
import com.socialplay.gpark.ui.im.groupchat.GroupChatConfig
import com.socialplay.gpark.ui.mgs.danmu.advanced.PAGE_ANIMATION_DURATION
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.ui.view.PopupWindowCompat
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.util.CustomEpoxyTouchHelper
import com.socialplay.gpark.util.DeleteDragView
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.PopWindowUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.SimpleVibratorUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getDimensionPx
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.registerAdapterDataObserver
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.tagIds
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.toJSON
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.android.ext.android.inject
import timber.log.Timber

/**
 * @author: ning.wang
 * @date: 2021-06-21 4:46 下午
 * @desc:
 */
class ConversationListFragment : BaseFragment<FragmentConversationListBinding>(R.layout.fragment_conversation_list) {
    private val viewModel: ConversationListViewModel by fragmentViewModel()
    private val groupChatViewModel: GroupChatViewModel by fragmentViewModel()
    private lateinit var conversationListAdapter: ConversationListAdapter
    override val destId: Int = R.id.chat_tab
    private lateinit var friendRequestsHeader: FriendRequestsHeader
    private lateinit var sysHeaderAdapter: SysHeaderAdapter
    private val sysListAdapter: SysListAdapter = SysListAdapter(::glide, onClickTopHeader = { item ->
        if (item.isRequestFriend) {
            MetaRouter.IM.goFriendGroupsRequestList(this@ConversationListFragment)
        } else {
            MetaRouter.IM.goSys(
                this@ConversationListFragment,
                item.groupId,
                item.groupContentType,
                item.title,
                (item.unread).toLong(),
                0,
                item.gameId,
                item.tabList.toJSON(),
                -1,
                item.icon
            )
        }
        Analytics.track(
            EventConstants.EVENT_NOTICE_CLICK_SEND_MESSAGE,
            "group_id" to item.groupId
        )
    }).apply {
        onClickFilter = {
            PopWindowUtil.PopupWindowBuilder(requireActivity())
                .setView(R.layout.pop_conversation_filter_list)
                .setElevation(3.0f)
                .create().apply {
                    this.showAsDropDown(it)
                    val linearLayout = this.mContentView as LinearLayout
                    val tv = linearLayout.getChildAt(viewModel.curFilter.ordinal) as TextView
                    tv.setTextColor(Color.parseColor("#1a1a1a"))
                    tv.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
                    linearLayout.forEachIndexed { index, view ->
                        view.setOnAntiViolenceClickListener {
                            viewModel.setCurrentFilter(index)
                            setFilter(SysListAdapter.ConversationFilter.entries[index])
                            dissmiss()
                        }
                    }
                }
        }
    }

    private lateinit var listStatusAdapter: ListStatusAdapter

    private val imInteractor: ImInteractor by inject()

    private var aiConversationController: MetaEpoxyController? = null
    var removeItem: AiBotConversationItem? = null
    var imgCleared: Boolean = true
    var touchHelper: ItemTouchHelper? = null
    var isGotSet: Boolean = false

    private val args by navArgs<ConversationListFragmentArgs>()

    override fun getPageName(): String = PageNameConstants.FRAGMENT_NAME_CONVERSATION_LIST
    private lateinit var popupWindowAdd: PopupWindowCompat
    private val popupAddBinding by lazy { PopUpConversationListAddBinding.inflate(layoutInflater) }
    private var popupWindowMeasuredWidth = -1

    private val itemSpacingDecoration = object : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            val holder = parent.getChildViewHolder(view) ?: return
            if (holder.absoluteAdapterPosition == 0) {
                outRect.top = 10.dp
            }

            //有数据且是最后一条数据，给他加Spacing，防止被Tab挡住
//            if (holder.bindingAdapter == conversationListAdapter && holder.bindingAdapterPosition == conversationListAdapter.itemCount-1) {
//                outRect.bottom = 82.dp
//            }
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentConversationListBinding? {
        return FragmentConversationListBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Timber.d("DX-ConversationListFragment-onViewCreated")
        view.postDelayed(PAGE_ANIMATION_DURATION) {
            init()
        }
    }

    fun init() {
        val requestManager = Glide.with(this)
        conversationListAdapter = ConversationListAdapter(requestManager) {
            UserLabelView.showDescDialog(this, it)
        }
        friendRequestsHeader = FriendRequestsHeader()
        sysHeaderAdapter = SysHeaderAdapter(::glide)
        listStatusAdapter = ListStatusAdapter()
        initView()
        initData()
        initPopup()
    }


    private fun initView() {
        binding.apply {
            if (args.isFromBottom) {
                binding.root.setPaddingEx(bottom = getDimensionPx(R.dimen.tab_layout_height))
            } else {
                binding.root.setPaddingEx(bottom = 0)
                binding.ivBackBtn.visible()
                binding.ivBackBtn.setOnAntiViolenceClickListener {
                    navigateUp()
                }
            }
            ivClear.visible(PandoraToggle.isNewMessage)
            ivClear.setOnAntiViolenceClickListener {
                viewLifecycleOwner.lifecycleScope.launch {
                    val isClear = viewModel.clearAllUnread()
                    if (isClear) toast(R.string.success) else toast(R.string.account_and_password_set_failed)
                }
            }
            rvConversationList.layoutManager = LinearLayoutManager(requireContext())
            rvConversationList.itemAnimator = null

            rvConversationList.removeItemDecoration(itemSpacingDecoration)
            rvConversationList.addItemDecoration(itemSpacingDecoration)

            ivSearch.setOnAntiViolenceClickListener { view ->
                Analytics.track(EventConstants.CHAT_ADD_CLICK)

                val haveCreateGroupPower = viewModel.haveCreateGroupPower.value
                visibleList(
                    popupAddBinding.divider1,
                    popupAddBinding.tvCreateGroup,
                    popupAddBinding.tvCreateGroupClick,
                    visible = haveCreateGroupPower
                )
                val groupCreateCount = viewModel.groupChatCount.value?.createCount ?: 0
                if (haveCreateGroupPower) {
                    popupAddBinding.viewCreateGroupRedDot.visible(
                        groupChatViewModel.showCreateGroupChatRedDot() && groupCreateCount <= 0
                    )
                }
                // TODO 一期不做群搜索
                visibleList(
                    popupAddBinding.divider2,
                    popupAddBinding.tvSearchGroup,
                    popupAddBinding.tvSearchGroupClick,
                    visible = false
                )
                // 在 Android 6.0 的设备上, 在 popupWindow 第一次显示出来之前, 测量得到的结果是对的
                // 在显示一次之后, 测量的结果就不对了, 所以这里需要对测量结果缓存一下
                if (popupWindowMeasuredWidth < 0) {
                    popupAddBinding.root.measure(
                        View.MeasureSpec.UNSPECIFIED,
                        View.MeasureSpec.UNSPECIFIED
                    )
                    popupWindowMeasuredWidth = popupAddBinding.root.measuredWidth
                }
                popupWindowAdd.showAsDropDownByLocation(
                    view,
                    -popupWindowMeasuredWidth + ivSearch.width - dp(6), -dp(8)
                )
            }

            ivContacts.setOnAntiViolenceClickListener {
                MetaRouter.IM.goContacts(this@ConversationListFragment)
            }

            refresh.setOnRefreshListener {
                viewModel.refreshConversationList()
                viewModel.getUnReadCount()
                viewModel.refreshFriendsUnreadRequests()
                viewModel.getEditorNoticeUnreadCount()
                viewModel.getPostUnread()
                viewModel.getNotice()
                viewModel.refreshFriends()
                viewModel.loadSys()
                viewModel.getAiBotConversationList(true)
                viewModel.checkCreateGroupPower()
                viewModel.getGroupChatCount()
                viewModel.getGroupPendingRequestCount()
            }

            listStatusAdapter.setEmptyClickListener {
                viewModel.refreshConversationList()
                viewModel.refreshFriends()
                viewModel.getUnReadCount()
            }

            listStatusAdapter.setErrorClickListener {
                viewModel.refreshConversationList()
                viewModel.refreshFriends()
                viewModel.getUnReadCount()
            }

            val loadMoreAdapter = HomeLoadMoreFooterAdapter {
                viewModel.refreshConversationList()
                viewModel.getUnReadCount()
                viewModel.refreshFriends()
            }

            conversationListAdapter.setOnItemClickListener { view, position ->
                val item = conversationListAdapter.getItem(position)
                when (item) {
                    is ConversationMessage -> {
                        toPrivateChat(item.friendInfo)
                    }

                    is GroupConversationMessage -> {
                        // 跳转群聊页面
                        item.conversation?.also { conversation ->
                            toGroupChat(conversation)
                        }
                    }

                    is AbsConversationMessage.NoticeConversation -> {
                        when (item.notice.type) {
                            EditorNotice.OuterShowNotice.TYPE_EDITOR -> {
                                Analytics.track(EventConstants.NBLAND_NOTICE_CLICK)
                                MetaRouter.Notice.editorNotice(this@ConversationListFragment)
                            }

                            EditorNotice.OuterShowNotice.TYPE_OPERATION -> {
                                MetaRouter.Notice.operationNotice(this@ConversationListFragment)
                            }
                        }
                    }

                    is AbsConversationMessage.SysConversation -> {
                        MetaRouter.IM.goSys(
                            this@ConversationListFragment,
                            item.info.groupId,
                            item.info.groupContentType,
                            item.info.title,
                            (item.info.unread).toLong(),
                            1,
                            item.info.gameId,
                            item.info.tabList.toJSON(),
                            -1,
                            item.info.icon
                        )
                        Analytics.track(
                            EventConstants.EVENT_NOTICE_CLICK_SEND_MESSAGE,
                            "group_id" to item.info.groupId
                        )
                    }
                }
            }

            friendRequestsHeader.setOnItemClickListener { _, _ ->
                MetaRouter.IM.goFriendGroupsRequestList(this@ConversationListFragment)
            }
            sysHeaderAdapter.setOnItemClickListener { _, position ->
                val item = sysHeaderAdapter.getItem(position)
                MetaRouter.IM.goSys(
                    this@ConversationListFragment,
                    item.groupId,
                    item.groupContentType,
                    item.title,
                    (item.unread).toLong(),
                    0,
                    item.gameId,
                    item.tabList.toJSON(),
                    -1,
                    item.icon
                )
                Analytics.track(
                    EventConstants.EVENT_NOTICE_CLICK_SEND_MESSAGE,
                    "group_id" to item.groupId
                )
            }

            conversationListAdapter.setOnItemLongClickListener { view, position ->
                Analytics.track(EventConstants.EVENT_IM_LONG_CLICK_LIST_ITEM_CLICK)
                when (val item = conversationListAdapter.getItem(position)) {
                    is ConversationMessage -> {
                        item.conversation?.let { showPopMenu(view, it) }
                    }

                    else -> {}
                }
                return@setOnItemLongClickListener true
            }
            val concatAdapter = if (PandoraToggle.isNewMessage) {
                ConcatAdapter(
                    sysListAdapter,
                    conversationListAdapter,
                    listStatusAdapter,
                    loadMoreAdapter
                )
            } else {
                ConcatAdapter(
                    friendRequestsHeader,
                    sysHeaderAdapter,
                    conversationListAdapter,
                    listStatusAdapter,
                    loadMoreAdapter
                )
            }
            rvConversationList.adapter = concatAdapter

            // 监听 concatAdapter 的 item 数量
            // 当 concatAdapter 的数量, 和上次 fragment 的 concatAdapter 的数量相等时
            // 表示页面的数据加载完成了, 这时就可以尝试恢复 fragment 的滚动位置
            // 如果以后im的会话列表的读取, 改成分页读取了, 那就不能使用当前的实现方案了, 可能将整个 Fragment 缓存下来比较好
            val lastAdapterItemCount = mLastAdapterItemCount
            val layoutManagerSavedState = mLayoutManagerSavedState
            mLayoutManagerSavedState = null
            mLastAdapterItemCount = null
            var isRestoreScrollPosition = false
            if (lastAdapterItemCount != null && lastAdapterItemCount > 0 && layoutManagerSavedState != null) {
                concatAdapter.registerAdapterDataObserver(
                    viewLifecycleOwner,
                    object : RecyclerView.AdapterDataObserver() {
                        override fun onChanged() {
                            super.onChanged()
                            if (!isRestoreScrollPosition && concatAdapter.itemCount >= lastAdapterItemCount) {
                                isRestoreScrollPosition = true
                                binding.rvConversationList.layoutManager?.onRestoreInstanceState(
                                    layoutManagerSavedState
                                )
                                concatAdapter.unregisterAdapterDataObserver(this)
                            }
                        }
                    })
            }
        }
        initAiBotView()
        initAppNotification()
    }

    private fun initPopup() {
        popupWindowAdd = PopupWindowCompat(
            popupAddBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = 0
        }
        popupAddBinding.root.setOnClickListener {
            popupWindowAdd.dismiss()
        }
        popupAddBinding.tvAddFriendClick.setOnAntiViolenceClickListener {
            popupWindowAdd.dismiss()
            Analytics.track(EventConstants.EVENT_ADD_FRIENDS_CLICK) {
                put("source", "chat")
            }
            MetaRouter.IM.goSearchFriend(this@ConversationListFragment)
        }
        popupAddBinding.tvCreateGroupClick.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.GROUP_CREATE_CLICK)
            val groupCreateCount = viewModel.groupChatCount.value?.createCount
            if (groupCreateCount == null || groupCreateCount >= GroupChatConfig.MAX_CREATE_GROUP_COUNT) {
                toast(R.string.toast_create_group_failed_maximum_limit)
                return@setOnAntiViolenceClickListener
            }
            groupChatViewModel.clickCreateGroupChatMenu()
            binding.viewSearchRedDot.visible(false)
            MetaRouter.IM.goContacts(
                this,
                isCreateGroup = true
            )
            popupWindowAdd.dismiss()
        }
        popupAddBinding.tvSearchGroupClick.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.CHAT_SEARCH_GROUP_CLICK)
            // TODO 一期不做群搜索
            popupWindowAdd.dismiss()
        }
    }

    private fun toPrivateChat(friendInfo: FriendInfo, navigatorExtras: Navigator.Extras? = null) {
        viewModel.refreshConversationList()
        viewModel.getUnReadCount()
        Analytics.track(EventConstants.EVENT_IM_CHAT_CLICK) { put("from", 1) }

        MetaRouter.IM.gotoConversation(
            fragment = this@ConversationListFragment,
            otherUid = friendInfo.uuid,
            title = friendInfo.name,
            tagIds = friendInfo.tagIds,
            labelInfo = friendInfo.labelInfo,
            navigatorExtras = navigatorExtras
        )
    }

    private fun toGroupChat(
        conversation: MetaConversation,
        navigatorExtras: Navigator.Extras? = null
    ) {
        viewModel.refreshConversationList()
        viewModel.getUnReadCount()
        val groupId = groupChatViewModel.getGroupIdByImId(conversation.targetId)
        if (groupId == GroupChatRepository.DISBANDED_GROUP_ID) {
            // 群已解散
            toast(R.string.toast_cant_jump_group_chat_page_because_disbanded)
            return
        }
        if (groupId != null) {
            MetaRouter.IM.goGroupChat(
                fragment = this@ConversationListFragment,
                imId = conversation.targetId,
                groupChatId = groupId.toString(),
                groupName = conversation.showName,
                navigatorExtras = navigatorExtras
            )
        }
    }

    private fun initData() {
        lifecycleScope.launch {
            viewModel.sysHeaderList.collect { sysHeaderList ->
                if (sysHeaderList.isNotEmpty()) {
                    listStatusAdapter.setStatus(ListStatus.Success)
                }
                if (PandoraToggle.isNewMessage) {
                    val topList: MutableList<SysHeaderInfo> = mutableListOf()
                    topList.add(
                        SysHeaderInfo(
                            title = getString(R.string.new_friend),
                            isRequestFriend = true,
                            unread = FriendBiz.friendRequestUnreadCount.value
                        )
                    )
                    sysHeaderList.toMutableList().forEach { item ->
                        if (item.topping != false) {
                            topList.add(item)
                        }
                    }
                    sysListAdapter.setList(listOf(topList))
                } else {
                    withContext(Dispatchers.IO) {
                        sysHeaderAdapter.preLoad(context, sysHeaderList, true)
                        withContext(Dispatchers.Main) {
                            sysHeaderAdapter.setList(sysHeaderList)
                        }
                    }
                }
                sysHeaderList.forEach { item ->
                    Analytics.track(
                        EventConstants.EVENT_NOTICE_SEND_MESSAGE_SHOW,
                        "group_id" to item.groupId,
                        "unread_count" to item.unread
                    )
                }
            }
        }
        viewModel.conversationChangedEvent.observe(viewLifecycleOwner) {
            viewModel.refreshConversationList()
            viewModel.getUnReadCount()
        }

        viewModel.apply {
            viewLifecycleOwner.lifecycleScope.launchWhenCreated {
                conversationMessageList.collect { conversations ->
                    withContext(Dispatchers.IO) {
                        conversationListAdapter.preLoad(context, conversations, true)
                        withContext(Dispatchers.Main) {
                            conversationListAdapter.setList(conversations)
                        }
                    }
                    listStatusAdapter.setStatus(
                        if (conversations.isEmpty() && viewModel.sysHeaderList.value.isEmpty()) ListStatus.Empty(
                            getString(R.string.no_messages)
                        ) else ListStatus.Success
                    )
                    binding.refresh.isRefreshing = false

                    val imIds = conversations.filter { absCon ->
                        absCon is GroupConversationMessage
                                && absCon.conversation?.conversationType == ConversationType.GROUP
                                && absCon.conversation.targetId.isNotEmpty()
                    }.map { con -> (con as GroupConversationMessage).conversation!!.targetId }
                    groupChatViewModel.fetchGroupIdByImIds(imIds)
                }
            }

            viewLifecycleOwner.lifecycleScope.launchWhenCreated {
                friendAndGroupRequestUnreadCountFlow.collect {
                    if (PandoraToggle.isNewMessage) {
                        viewModel.loadSys()
                    } else {
                        withContext(Dispatchers.IO) {
                            friendRequestsHeader.preLoad(context, listOf(it), true)
                            withContext(Dispatchers.Main) {
                                friendRequestsHeader.setFriendRequestUnreadCount(it)
                            }
                        }
                    }
                }
            }

            imInteractor.IMConnectionStatus.observe(viewLifecycleOwner) {
                Timber.i("rongConnectionStatus:" + it)
                val isShow = !it || !NetUtil.isNetworkAvailable()
                binding.groupConnectionStatus.visible(isShow)
                if (isShow) {
                    binding.groupNotificationTips.gone()
                } else {
                    updateNotificationView(viewModel.oldState.needShowNotification)
                }
                if (it) {
                    viewModel.refreshConversationList()
                    viewModel.getUnReadCount()
                }
            }
        }
        viewModel.onEach(ConversationListState::needShowNotification) { result ->
            updateNotificationView(result)
            Timber.d("needShowNotification_change")
        }
        if (groupChatViewModel.showCreateGroupChatRedDot()) {
            viewModel.showCreateGroupChatRedDot.asLiveData().observe(viewLifecycleOwner) { show ->
                binding.viewSearchRedDot.visible(groupChatViewModel.showCreateGroupChatRedDot() && show)
            }
        } else {
            binding.viewSearchRedDot.gone()
        }
    }

    private fun initAppNotification() {
        if (PandoraToggle.isChatPushNotification) {
            binding.appNotificationOpen.setOnAntiViolenceClickListener {
                isGotSet = true
                PermissionRequest.goNotification(requireActivity())
            }
            binding.appNotificationClose.setOnAntiViolenceClickListener {
                binding.groupNotificationTips.gone()
                viewModel.updateNotificationStatus(false)
                Analytics.track(EVENT_FIRST_PUSH_POST_CLOSE)
            }
            binding.tvNotification.requestFocus()
        }
    }

    private fun updateNotificationView(isShow: Boolean) {
        if (!PandoraToggle.isChatPushNotification) return
        if (binding.groupConnectionStatus.isVisible) {
            binding.groupNotificationTips.gone()
        } else {
            binding.groupNotificationTips.visible(isShow)
            Analytics.track(EVENT_FIRST_PUSH_POST_SHOW)
        }
    }


    /**
     * 添加aiBot会话列表
     */
    private fun initAiBotView() {
        if (PandoraToggle.isOpenAiBot) {
            binding.dragDeleteView.visible()
            viewModel.onEach(ConversationListState::count) {
                binding.tvAiTitle.visible(it > 0)
                binding.ryAiBot.visible(it > 0)
            }
            binding.ryAiBot.layoutManager =
                LinearLayoutManager(requireContext(), RecyclerView.HORIZONTAL, false)
            aiConversationController = buildAiConversationController()
            binding.ryAiBot.setController(aiConversationController!!)

            binding.dragDeleteView.setOnListener(
                binding.ryAiBot,
                object : DeleteDragView.DragListener {
                    override fun onRemoveItem() {
                        removeItem?.let { showDeleteConfirmDialog(it) }
                    }

                    override fun onActionUp() {
                        removeItem = null

                    }

                })
            val target = CustomEpoxyTouchHelper.initDragging(aiConversationController!!)
                .withRecyclerView(binding.ryAiBot)
                .withDirections(ItemTouchHelper.DOWN or ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT)
                .withTarget(AiBotConversationItem::class.java)
            touchHelper = target.andCallbacks(
                viewLifecycleOwner,
                object : CustomEpoxyTouchHelper.DragCallbacks<AiBotConversationItem>() {
                    override fun onDragStarted(
                        model: AiBotConversationItem,
                        itemView: View,
                        adapterPosition: Int
                    ) {
                        if (!imgCleared) return
                        imgCleared = false
                        removeItem = model
                        SimpleVibratorUtil.vibrateClick()
                        binding.dragDeleteView.setDragView(itemView)

                    }

                    override fun onModelMoved(
                        fromPosition: Int,
                        toPosition: Int,
                        modelBeingMoved: AiBotConversationItem,
                        itemView: View
                    ) {

                    }

                    override fun clearView(model: AiBotConversationItem, itemView: View) {
                        imgCleared = true

                    }
                }
            )
            viewModel.getAiBotConversationList(true)
        }
    }

    private fun buildAiConversationController() = simpleController(
        viewModel,
        ConversationListState::aiConversationList,
        ConversationListState::loadMore,
    ) { list, loadMore ->
        val width = if (list.size <= 4) {
            75.dp.toFloat()
        } else {
            (((ScreenUtil.screenWidth - (16.dp * 5)) / 5) * 1.1).toFloat()
        }
        list.forEachIndexed { index, conversation ->
            AiBotConversation(index, width, conversation, ::glide) {
                MetaRouter.AiBot.gotoConversation(
                    this@ConversationListFragment,
                    it.botId,
                    it.id,
                    "2"
                )
            }
        }
        loadMoreFooter(loadMore, spanSize = 5, showEnd = false, width = width) {
            viewModel.getAiBotConversationList(false)
        }
    }

    private fun showDeleteConfirmDialog(modelBeingMoved: AiBotConversationItem) {
        SimpleVibratorUtil.vibrateClick()

        ConfirmDialog.Builder(this)
            .content(getString(R.string.conversation_remove_msg_resure))
            .cancelBtnTxt(getString(R.string.cancel), lightBackground = false)
            .confirmBtnTxt(getString(R.string.friend_chat_delete), lightBackground = true)
            .image(R.drawable.dialog_icon_cry)
            .isRed(true)
            .cancelCallback {
                binding.dragDeleteView.getDragView()
                    ?.let {
                        it.alpha = 1f
                        touchHelper?.onChildViewDetachedFromWindow(it)
                    }
                binding.dragDeleteView.removeView()
                removeItem = null
            }
            .confirmCallback {
                binding.dragDeleteView.getDragView()
                    ?.let {
                        it.alpha = 0f
                        touchHelper?.onChildViewDetachedFromWindow(it)
                    }
                viewModel.deleteAiConversation(modelBeingMoved)
                binding.dragDeleteView.removeView()
                removeItem = null
            }
            .navigate()
    }


    private fun showPopMenu(view: View, metaConversation: MetaConversation?) {
        metaConversation ?: return
        PopWindowUtil.PopupWindowBuilder(requireActivity())
            .setView(R.layout.pop_im_conversation_menu)
            .size(
                LinearLayout.LayoutParams.WRAP_CONTENT.toFloat(),
                LinearLayout.LayoutParams.WRAP_CONTENT.toFloat()
            )
            .setFocusable(true)
            .setTouchable(true)
            .setOutsideTouchable(true)
            .create().apply {
                showAtLocation(view)
                getView<TextView>(R.id.menu_top)?.let {
                    it.text =
                        if (metaConversation.isTop == true) requireContext().getString(R.string.im_conversation_menu_cancel_top)
                        else requireContext().getString(R.string.im_conversation_menu_top)
                    it.setOnAntiViolenceClickListener {
                        val opType = if (metaConversation.isTop == true) "untop" else "top"
                        Analytics.track(EventConstants.EVENT_LONG_CLICK_CONVERSATION_LIST_TOP) {
                            put("type", opType)
                        }
                        viewModel.setConversationToTop(metaConversation)
                        viewModel.refreshConversationList()
                        viewModel.getUnReadCount()
                        dissmiss()
                    }
                }
                getView<TextView>(R.id.menu_remove)?.setOnAntiViolenceClickListener {
                    Analytics.track(EventConstants.EVENT_LONG_CLICK_CONVERSATION_LIST_DELETE)
                    showDeleteConversationConfirm(metaConversation)
                    dissmiss()
                }
            }
    }

    private fun showDeleteConversationConfirm(metaConversation: MetaConversation) {
        ConfirmDialog.Builder(this)
            .content(resources.getString(R.string.conversation_remove_msg_resure))
            .cancelBtnTxt(resources.getString(R.string.dialog_cancel), lightBackground = false)
            .confirmBtnTxt(
                resources.getString(R.string.im_conversation_menu_remove),
                lightBackground = true
            )
            .isRed(true)
            .confirmCallback {
                viewModel.removeConversation(metaConversation)
                viewModel.refreshConversationList()
                viewModel.getUnReadCount()
            }.navigate()
    }

    override fun onResume() {
        super.onResume()
        Analytics.track(EventConstants.EVENT_IM_MESSAGES_TAB_SHOW)
        viewModel.getUnReadCount()
        viewModel.getEditorNoticeUnreadCount()
        viewModel.getNotice()
        viewModel.loadSys()
        viewModel.checkNotificationPermission(requireContext(), isGotSet)
        viewModel.checkCreateGroupPower()
        viewModel.getGroupChatCount()
        viewModel.getGroupPendingRequestCount()
        isGotSet = false
    }

    private var mLayoutManagerSavedState: Parcelable? = null
    private var mLastAdapterItemCount: Int? = null
    override fun onDestroyView() {
        // 记录当前 fragment 的滚动状态, 当页面重建时, 恢复滚动位置
        mLastAdapterItemCount = binding.rvConversationList.adapter?.itemCount
        mLayoutManagerSavedState = binding.rvConversationList.layoutManager?.onSaveInstanceState()

        binding.rvConversationList.adapter = null
        binding.refresh.setOnRefreshListener(null)
        binding.dragDeleteView.unDestroyView(binding.ryAiBot)
        touchHelper = null
        if (::popupWindowAdd.isInitialized) {
            popupAddBinding.tvAddFriendClick.unsetOnClick()
            popupAddBinding.tvCreateGroupClick.unsetOnClick()
            popupAddBinding.tvSearchGroupClick.unsetOnClick()
            popupWindowAdd.dismiss()
        }
        Timber.d("onViewDestroyed List")
        super.onDestroyView()
    }

    override fun invalidate() {

    }

}