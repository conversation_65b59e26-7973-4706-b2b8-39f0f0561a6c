package com.socialplay.gpark.ui.im

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.allViews
import androidx.core.view.children
import androidx.core.view.updateLayoutParams
import com.socialplay.gpark.data.model.ListStatus
import com.socialplay.gpark.databinding.ViewListStatusLayoutBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.extension.visible
import java.util.*

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2023/03/10
 *     desc   :
 */
class ListStatusAdapter : BaseAdapter<ListStatus, ViewListStatusLayoutBinding>() {

    private lateinit var mEmptyClickListener: View.OnClickListener
    private lateinit var mErrorClickListener: View.OnClickListener

    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: <PERSON><PERSON><PERSON>,
        viewType: Int
    ): ViewListStatusLayoutBinding {
        return ViewListStatusLayoutBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(
        holder: BindingViewHolder<ViewListStatusLayoutBinding>,
        item: ListStatus,
        position: Int
    ) {

//        holder.binding.vEmptyLayout.setOnClickListener(null)
//        holder.binding.vErrorLayout.setOnClickListener(null)

        when (item) {
            is ListStatus.Empty -> {
                holder.binding.loadingView.showEmpty(msg = item.message)
                holder.binding.loadingView.setOnClickListener(mEmptyClickListener)
//                holder.binding.vEmptyLayout.visible(false)
//                holder.binding.vErrorLayout.visible(false)
//                holder.binding.tvEmptyTipText.text = item.message
//                holder.binding.vEmptyLayout.setOnClickListener(mEmptyClickListener)

                val remainingHeight = recyclerView.measuredHeight - (recyclerView.children
                    .map { it.measuredHeight }
                    .reduceOrNull { acc, value -> acc + value } ?: 0)

                holder.itemView.updateLayoutParams<ViewGroup.LayoutParams> {
                    this.height = remainingHeight
                }
            }

            is ListStatus.Error -> {
                holder.binding.loadingView.showError()
                holder.binding.loadingView.setOnClickListener(mErrorClickListener)
//                holder.binding.vEmptyLayout.visible(false)
//                holder.binding.vErrorLayout.visible(false)
//                holder.binding.vErrorLayout.setOnClickListener(mErrorClickListener)

                val remainingHeight = recyclerView.measuredHeight - (recyclerView.children
                    .map { it.measuredHeight }
                    .reduceOrNull { acc, value -> acc + value } ?: 0)

                holder.itemView.updateLayoutParams<ViewGroup.LayoutParams> {
                    this.height = remainingHeight
                }
            }

            is ListStatus.Success -> {
                holder.binding.loadingView.hide()
//                holder.binding.vEmptyLayout.visible(false)
//                holder.binding.vErrorLayout.visible(false)

                holder.itemView.updateLayoutParams<ViewGroup.LayoutParams> {
                    this.height = ViewGroup.LayoutParams.WRAP_CONTENT
                }
            }
        }
    }

    fun setEmptyClickListener(clickListener: View.OnClickListener) {
        this.mEmptyClickListener = clickListener
    }

    fun setErrorClickListener(clickListener: View.OnClickListener) {
        this.mErrorClickListener = clickListener
    }

    fun setStatus(listStatus: ListStatus) {
        if (listStatus is ListStatus.Success) {
            this.setList(emptyList())
        } else {
            this.setList(Collections.singletonList(listStatus))
        }
    }
}