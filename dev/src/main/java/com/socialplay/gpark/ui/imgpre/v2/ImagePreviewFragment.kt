package com.socialplay.gpark.ui.imgpre.v2

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import com.flyjingfish.openimagelib.BaseInnerFragment
import com.flyjingfish.openimagelib.OpenImageActivity
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.LayoutImagePreviewOverlayBinding
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.util.ImageUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.zhpan.indicator.enums.IndicatorSlideMode
import com.zhpan.indicator.enums.IndicatorStyle

// 全屏显示图片的预览Fragment
class ImagePreviewFragment(
    private val urls: List<String>,
    private val onImageAction: (fragment: BaseInnerFragment) -> Unit = {}
) : BaseInnerFragment() {
    private var _binding: LayoutImagePreviewOverlayBinding? = null
    private val binding get() = _binding!!

    private var visibilityAnimatorSet: AnimatorSet? = null
    private var isHide: Boolean = false

    private var bgView: View? = null
    private var containerViewpager: ViewPager2? = null

    private var showSave: Boolean = false
    private var indicatorNum: Int = 0
    private var actionText: String = ""

    private var currentPosition: Int = 0

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        initData()
        _binding = LayoutImagePreviewOverlayBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()

        _binding = null
        visibilityAnimatorSet?.cancel()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initView()
    }

    override fun onTouchScale(scale: Float) {
        super.onTouchScale(scale)
        if(!isHide) {
            binding.apply {
                ibBack.translationY = -ibBack.height * (1 - scale) * 4
                bottomBar.translationY = bottomBar.height * (1 - scale) * 4
            }
        }
        containerViewpager?.alpha = scale
    }

    private fun initData() {
        showSave = arguments?.getBoolean(KEY_SHOW_SAVE) ?: false
        actionText = arguments?.getString(KEY_ACTION_TEXT) ?: ""
        indicatorNum = arguments?.getInt(KEY_INDICATOR_NUM) ?: 0
    }

    private fun initView() = binding.apply {
        val activity = requireActivity()
        if(activity is OpenImageActivity) {
            bgView = activity.bgView
            containerViewpager = activity.viewPager2
        }

        ibBack.setOnAntiViolenceClickListener { close() }
        if(urls.isNotEmpty()) {
            val dp8 = 8.dp.toFloat()
            indicatorImg.visible()
            binding.indicatorImg.apply {
                setIndicatorStyle(IndicatorStyle.CIRCLE)
                setSliderWidth(dp8)
                setSliderHeight(dp8)
                setSlideMode(IndicatorSlideMode.NORMAL)
                setSliderGap(dp8)
                setPageSize(urls.size)
                notifyDataChanged()
                setCurrentPosition(currentPosition)
            }
            addOnSelectMediaListener { _, position ->
                currentPosition = position
                indicatorImg.onPageSelected(position)
            }
        }

        if(actionText.isNotEmpty()) {
            tvImgAction.text = actionText
            tvImgAction.setOnAntiViolenceClickListener {
                onImageAction(this@ImagePreviewFragment)
            }
            tvImgAction.visible()
        } else {
            tvImgAction.gone()
        }

        addOnItemClickListener { _, _, _ ->
            close()
        }

        if(showSave) {
            addOnItemLongClickListener { _, openImageUrl, _ ->
                ConfirmDialog.Builder(this@ImagePreviewFragment)
                    .confirmBtnTxt(getString(R.string.save))
                    .cancelBtnTxt(getString(R.string.cancel))
                    .confirmCallback {
                        saveCurrentImage(openImageUrl.imageUrl)
                    }
                    .show()
            }
        }
    }

    private fun saveCurrentImage(imagePath: String) {
        context?.let { ctx ->
            ImageUtil.saveImageToGalleryByUrl(
                ctx,
                imagePath,
                viewLifecycleOwner.lifecycleScope
            ) { isSuccess ->
                if (isSuccess) {
                    toast(R.string.saved)
                } else {
                    toast(R.string.save_failed)
                }
            }
        }
    }

    companion object {
        const val KEY_SHOW_SAVE = "showSave"
        const val KEY_INDICATOR_NUM = "indicatorNum"
        const val KEY_ACTION_TEXT = "actionText"

        fun getBundle(
            showSave: Boolean = false,
            indicatorNum: Int = 0,
            actionText: String = ""
        ): Bundle {
            return Bundle().apply {
                putBoolean(KEY_SHOW_SAVE, showSave)
                putInt(KEY_INDICATOR_NUM, indicatorNum)
                putString(KEY_ACTION_TEXT, actionText)
            }
        }
    }
}