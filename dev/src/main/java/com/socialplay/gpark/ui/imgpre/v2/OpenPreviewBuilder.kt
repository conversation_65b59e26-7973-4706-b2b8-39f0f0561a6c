package com.socialplay.gpark.ui.imgpre.v2

import android.content.Context
import android.widget.ImageView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.RecyclerView
import com.flyjingfish.openimagelib.BaseInnerFragment
import com.flyjingfish.openimagelib.OpenImage
import com.flyjingfish.openimagelib.beans.OpenImageUrl
import com.flyjingfish.openimagelib.enums.MediaType
import com.flyjingfish.openimagelib.listener.SourceImageViewIdGet
import com.socialplay.gpark.R

/**
 * <pre>
 * author : qijijie
 * e-mail : <EMAIL>
 * time   : 2025/07/28
 * desc   :
 * </pre>
 */
class OpenPreviewBuilder {
    private val openImage: OpenImage
    private var showSaveBtn: Boolean = false
    private var actionText: String = ""
    private var onImageAction: (fragment: BaseInnerFragment) -> Unit = {}
    private var urls: List<String> = listOf()
    private var onExit: () -> Unit = {}

    private var imageViews: List<ImageView>? = null
    private var recyclerView: RecyclerView? = null
    private var sourceImageViewIdGet: SourceImageViewIdGet<OpenImageUrl>? = null

    constructor(openImage: OpenImage) {
        this.openImage = openImage
    }

    constructor(fragment: Fragment) {
        openImage = OpenImage.with(fragment)
    }

    constructor(activity: FragmentActivity) {
        openImage = OpenImage.with(activity)
    }

    constructor(context: Context) {
        openImage = OpenImage.with(context)
    }

    fun setShowSaveBtn(show: Boolean): OpenPreviewBuilder {
        showSaveBtn = show
        return this
    }

    fun setImageAction(text: String, onAction: (fragment: BaseInnerFragment) -> Unit): OpenPreviewBuilder {
        actionText = text
        onImageAction = onAction
        return this
    }

    fun setClickViews(imageViews: List<ImageView>): OpenPreviewBuilder {
        this.imageViews = imageViews
        return this
    }

    fun setOnExit(onExit: () -> Unit): OpenPreviewBuilder {
        this.onExit = onExit
        return this
    }

    fun setImageUrls(urls: List<String>): OpenPreviewBuilder {
        this.urls = urls
        return this
    }

    fun setRecyclerView(
        recyclerView: RecyclerView,
        sourceImageViewIdGet: SourceImageViewIdGet<OpenImageUrl>
    ): OpenPreviewBuilder {
        this.recyclerView = recyclerView
        this.sourceImageViewIdGet = sourceImageViewIdGet
        return this
    }

    fun setClickPosition(pos: Int): OpenPreviewBuilder {
        openImage.setClickPosition(pos)
        return this
    }

    fun show() {
        openImage.setUpperLayerFragmentCreate(
            PreviewLayerCreateImpl(urls, onImageAction),
            ImagePreviewFragment.getBundle(showSaveBtn, urls.size, actionText),
            false,
            false
        )
        .setSrcImageViewScaleType(ImageView.ScaleType.CENTER_CROP,true)
        .setImageUrlList(urls, MediaType.IMAGE)
        .setOnExitListener(onExit)
        .setOpenImageStyle(R.style.ImagePreviewFragment)

        if(recyclerView != null && sourceImageViewIdGet != null) {
            openImage.setClickRecyclerView(recyclerView, sourceImageViewIdGet)
        } else if(!imageViews.isNullOrEmpty()) {
            openImage.setClickImageViews(imageViews)
        } else {
            openImage.setNoneClickView()
        }

        openImage.show()
    }
}


