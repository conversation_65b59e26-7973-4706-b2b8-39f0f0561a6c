package com.socialplay.gpark.ui.imgpre.v2

import com.flyjingfish.openimagelib.BaseInnerFragment
import com.flyjingfish.openimagelib.listener.UpperLayerFragmentCreate

/**
 * <pre>
 * author : qijijie
 * e-mail : <EMAIL>
 * time   : 2025/07/28
 * desc   :
 * </pre>
 */
class PreviewLayerCreateImpl(
    private val urls: List<String>,
    private val onImageAction: (fragment: BaseInnerFragment) -> Unit = {}
) : UpperLayerFragmentCreate {
    override fun createLayerFragment(): BaseInnerFragment {
        return ImagePreviewFragment(urls, onImageAction)
    }
}