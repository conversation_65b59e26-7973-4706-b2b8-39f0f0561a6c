package com.socialplay.gpark.ui.kol

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.LoadStatus
import com.socialplay.gpark.data.model.LoadType
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.creator.CreatorMultiInfo
import com.socialplay.gpark.data.model.creator.CreatorMultiType
import com.socialplay.gpark.data.model.creator.ICreatorItem
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.data.model.creator.label.CommonLabelInfo
import com.socialplay.gpark.data.model.creator.label.KolCreatorLabel
import com.socialplay.gpark.data.model.creator.label.UgcPublishLabel
import com.socialplay.gpark.databinding.FragmentCreatorKolBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.kol.list.CreatorKolAdapter
import com.socialplay.gpark.ui.kol.list.IKolCreatorAdapterListener
import com.socialplay.gpark.ui.kol.list.adapter.RecommendCreatorAdapter
import com.socialplay.gpark.ui.kol.popup.KolGameLabelDialog
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.view.EmptyLoadMoreView
import com.socialplay.gpark.ui.view.LoadingView
import com.socialplay.gpark.ui.view.RecyclerTabLayout
import com.socialplay.gpark.util.UniJumpUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getDimensionPx
import com.socialplay.gpark.util.extension.ifEmptyNull
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setOnAntiViolenceItemClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.property.bundlePropertyNotNull
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2024/8/2
 * Desc: kol创作者社区
 * [需求文档] https://meta.feishu.cn/wiki/Q2gKwVmeuiI2vTkY4ZDcNzAYnlc
 * 社区-探索页
 */
class KolCreatorFragment : BaseFragment<FragmentCreatorKolBinding>() {

    companion object {
        fun newInstance(isFromBottom: Boolean, blockName: String) = KolCreatorFragment().apply {
            arguments = bundleOf(
                "isFromBottom" to isFromBottom,
                "blockName" to blockName,
            )
        }
    }

    private val kolViewModel: KolCreatorViewModel by viewModel()
    private val mainViewModel: MainViewModel by sharedViewModel()
    private var footerLoadingView: LoadingView? = null

    private val isFromBottom by bundlePropertyNotNull(false)
    private val blockName by bundlePropertyNotNull("")

    private val adapter: CreatorKolAdapter by lazy {
        CreatorKolAdapter(
            Glide.with(this),
            this,
            listener
        )
    }

    // 用于onDestroyView后再onCreate，恢复吸顶tab的显示状态
    private var lastLabelTabShowing = false

    // 用于onDestroyView后再onCreate，同步list数据
    private var updateList = false

    // 保存列表的滚动状态
    private val stateMap: HashMap<String, Parcelable?> by lazy { hashMapOf() }

    // 保存banner的滚动位置
    private var bannerPosition = 0

    private var dp16 = 0
    private var dp20 = 0

    private val listener: IKolCreatorAdapterListener = object : IKolCreatorAdapterListener {
        override fun goProfile(uuid: String, from: String) {
            MetaRouter.Profile.other(this@KolCreatorFragment, uuid, from)
        }

        override fun onClickUgc(ugId: String, gameCode: String, item: ICreatorItem) {
            val resId = ResIdBean.newInstance().setCategoryID(item.categoryId).setGameId(ugId)
            goUgcDetail(ugId, gameCode, resId)
        }

        override fun goUniJump(info: UniJumpConfig, source: String?, categoryId: Int) {
            UniJumpUtil.jump(
                this@KolCreatorFragment,
                info,
                LinkData.SOURCE_CHOICE_OPERATION,
                categoryId,
                mainViewModel,
                null
            )
        }

        override fun goMoreFollowedCreator(type: Int) {
            MetaRouter.Kol.moreTypeCreator(this@KolCreatorFragment)
        }

        override fun goMoreRecommendCreator() {
            kolViewModel.creatorLabelListLiveData.value.ifEmptyNull()?.let {
                MetaRouter.Kol.moreLabelCreator(this@KolCreatorFragment, it)
            }
        }

        override fun goMoreGame(type: Int) {
            when (type) {
                CreatorMultiType.TYPE_RECOMMEND_UGC_GAME -> {
                    MetaRouter.Kol.moreRecommendUgc(this@KolCreatorFragment)
                }
            }
        }

        override fun changeFollow(uuid: String, toFollow: Boolean) {
            kolViewModel.changeFollow(uuid, toFollow)
        }

        override fun selectUgcLabel(labelId: Int?) {
            kolViewModel.selectUgcLabel(labelId)
        }

        override fun selectCreatorLabel(tagId: Int) {
            Analytics.track(
                EventConstants.EVENT_STAR_CREATOR_TAG_CLICK,
                EventParamConstants.KEY_TAGID to "$tagId",
                EventParamConstants.KEY_TYPE to EventConstants.Type.TYPE_STAR_CREATOR_LABEL_TAB_BRIEF,
            )
            kolViewModel.selectCreatorLabel(tagId)
        }

        override fun sendStarCreatorShow(uuid: String) {
            Analytics.track(
                EventConstants.EVENT_STAR_CREATOR_SHOW,
                EventParamConstants.KEY_USERID to uuid,
                EventParamConstants.KEY_TAGID to kolViewModel.selectedCreatorTagId.toString(),
                EventParamConstants.KEY_TYPE to EventConstants.Type.TYPE_KOL_CREATOR_TAB_STAR,
            )
        }

        override fun popRvStoredState(key: String): Parcelable? {
            return stateMap.remove(key)
        }

        override fun saveRvState(key: String, state: Parcelable?) {
            stateMap[key] = state
        }

        override fun showMoreUgcLabel() {
            showUgcLabelDialog()
        }

        override fun showMoreCreatorLabel() {
            showCreatorLabelDialog()
        }

        override fun getUgcLabelList(): List<UgcPublishLabel> {
            return kolViewModel.ugcLabelListLiveData.value ?: emptyList()
        }

        override fun getCreatorLabelList(): List<KolCreatorLabel> {
            return kolViewModel.creatorLabelListLiveData.value ?: emptyList()
        }

        override fun getLabelCreatorList(): List<KolCreatorInfo> {
            return kolViewModel.labelCreatorListLiveData.value ?: emptyList()
        }

        override fun getLifecycle(): Lifecycle {
            return viewLifecycleOwner.lifecycle
        }

        override fun saveBannerPosition(position: Int) {
            bannerPosition = position
        }

        override fun getBannerPosition(): Int {
            return bannerPosition
        }

        override fun notifyNeedUpdateDailyTaskStatus() {
            needUpdateDailyTaskStatus = true
        }
    }

    private var needUpdateDailyTaskStatus = false

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentCreatorKolBinding? {
        return FragmentCreatorKolBinding.inflate(inflater, container, false)
    }

    private fun showUgcLabelDialog() {
        MetaRouter.Kol.showAllLabelDialog(
            this@KolCreatorFragment,
            CommonLabelInfo.buildByUgcLabel(kolViewModel.ugcLabelListLiveData.value.orEmpty()),
            KolGameLabelDialog.KEY_REQUEST_TAG_BOTTOM_UGC_GAME
        ) {
            kolViewModel.selectUgcLabel(it?.id?.toIntOrNull())
        }
    }

    private fun showCreatorLabelDialog() {
        MetaRouter.Kol.showAllLabelDialog(
            this@KolCreatorFragment,
            CommonLabelInfo.buildByCreatorLabel(kolViewModel.creatorLabelListLiveData.value.orEmpty()),
            KolGameLabelDialog.KEY_REQUEST_TAG_BOTTOM_CREATOR
        ) {
            val tagId = it?.id?.toIntOrNull() ?: 0
            Analytics.track(
                EventConstants.EVENT_STAR_CREATOR_TAG_CLICK,
                EventParamConstants.KEY_TAGID to "$tagId",
                EventParamConstants.KEY_TYPE to EventConstants.Type.TYPE_STAR_CREATOR_LABEL_TAB_ALL,
            )
            kolViewModel.selectCreatorLabel(tagId)
        }
    }

    override fun init() {
        dp16 = dp(16)
        dp20 = dp(20)
        initView()
        initEvent()
        initData()
    }

    private fun initView() {
        binding.rvCreate.skipIntercept = true
        visibleList(
            binding.statusBarPlaceholder,
            binding.clTitleBar,
            visible = !isFromBottom
        )
        binding.rvCreate.setPaddingEx(
            bottom = if (isFromBottom) {
                dp(24)
            } else {
                getDimensionPx(R.dimen.tab_layout_height) + dp(24)
            }
        )
//        binding.ivSearch.visible(PandoraToggle.isSearchOpen)
        binding.refresh.setOnRefreshListener {
            kolViewModel.refreshFrameApi()
        }
        binding.loading.setRetry {
            kolViewModel.refreshFrameApi()
        }
        initAdapter()
    }

    private fun initAdapter() {
        footerLoadingView = LoadingView(requireContext()).also {
            adapter.setFooterView(it)
            it.setHeight(100.dp)
            it.visible(false)
        }
        adapter.loadMoreModule.loadMoreView = EmptyLoadMoreView()
        adapter.loadMoreModule.isEnableLoadMore = true
        adapter.loadMoreModule.setOnLoadMoreListener {
            if (isBindingAvailable() && !binding.refresh.isRefreshing) {
                kolViewModel.loadMoreRecommendUgc()
            }
        }
        adapter.setOnAntiViolenceItemClickListener { _, view, position ->
            val item = adapter.getItemOrNull(position)?.toUgcGameInfo()
                ?: return@setOnAntiViolenceItemClickListener
            val resId =
                ResIdBean.newInstance().setCategoryID(item.categoryId).setGameId(item.game.id)
            Analytics.track(
                EventConstants.UGC_FEED_ITEM_CLICK,
                EventParamConstants.KEY_GAMEID to item.game.id,
                EventParamConstants.KEY_SHOW_CATEGORYID to CategoryId.CATEGORY_ID_KOL_LABEL_UGC,
                EventParamConstants.KEY_PACKAGENAME to item.game.packageName,
            )
            goUgcDetail(item.game.id, item.game.availableGameCode.orEmpty(), resId)
        }
        binding.rvCreate.layoutManager = object : GridLayoutManager(requireContext(), 2) {
            override fun onLayoutChildren(
                recycler: RecyclerView.Recycler?,
                state: RecyclerView.State?
            ) {
                kotlin.runCatching {
                    super.onLayoutChildren(recycler, state)
                }
            }
        }.apply {
            spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return when (adapter.getItemViewType(position)) {
                        CreatorMultiType.TYPE_LABEL_UGC_GAME_ITEM -> 1
                        else -> 2
                    }
                }
            }
        }
        binding.rvCreate.adapter = adapter
    }

    private fun goUgcDetail(
        ugId: String,
        gameCode: String,
        resIdBean: ResIdBean
    ) {
        if (!isAdded) return
        MetaRouter.MobileEditor.ugcDetail(
            this@KolCreatorFragment,
            ugId,
            gameCode,
            resIdBean
        )
    }

    private fun initEvent() {
//        binding.ivSearch.setOnAntiViolenceClickListener {
//            MetaRouter.Search.navigate(this)
//        }
        binding.ivEntrance.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.UGC_CREATE_ENTRY_ENTRY)
            MetaRouter.MobileEditor.creation(this)
        }
    }

    private var isLoadFinishTrack = false
    private var isLoadSuccess = false

    private fun initData() {
        kolViewModel.toastLifeCallback.observe(viewLifecycleOwner) {
            toast(it)
        }
        kolViewModel.combineListLiveData.observe(viewLifecycleOwner) {
            updateListView(it.first, it.second?.toMutableList())
            isLoadSuccess = !it.second.isNullOrEmpty()
            if (isLoadSuccess && !isLoadFinishTrack && isResumed) {
                isLoadFinishTrack = true
                Analytics.track(EventConstants.EVENT_COMMUNITY_MENU_SHOW) {
                    put("menu_name", blockName)
                }
            }
        }
        kolViewModel.ugcLabelListLiveData.observe(viewLifecycleOwner) {
            updateUgcLabelList(it ?: emptyList())
        }
        kolViewModel.creatorLabelListLiveData.observe(viewLifecycleOwner) {
            updateCreatorLabelList(it ?: emptyList())
        }
        kolViewModel.labelCreatorListLiveData.observe(viewLifecycleOwner) {
            updateLabelCreatorList(it ?: emptyList())
        }
    }

    private fun updateLabelCreatorList(list: List<KolCreatorInfo>) {
        findRvAdapterById<RecommendCreatorAdapter>(
            { it.isRecommendCreatorList() },
            R.id.rvHor
        )?.submitData(
            viewLifecycleOwner.lifecycle,
            list.toMutableList()
        )
    }

    private fun updateCreatorLabelList(list: List<KolCreatorLabel>) {
        val position = list.indexOfFirst { it.localSelected }
        val data = adapter.data
        val index = data.indexOfFirst { it.isRecommendCreatorList() }
        if (index in data.indices) {
            val holder = binding.rvCreate.findViewHolderForAdapterPosition(index)
            val tabLayout = holder?.itemView?.findViewById<RecyclerTabLayout>(R.id.tabLayout)
            tabLayout?.selectTab(position, true)
        }
    }

    private fun updateUgcLabelList(list: List<UgcPublishLabel>) {
        val position = list.indexOfFirst { it.localSelected }
        val index = adapter.data.indexOfFirst { it.isUgcLabelTab() }
        if (index in adapter.data.indices) {
            if (position >= 0) {
                // 选项有变化
                // 当前刷新方式会回调 KolUgcLabelListProvider.convert(helper: BaseViewHolder, item: CreatorMultiInfo, payloads: List<Any>)
                // 此时得到的 RecyclerTabLayout 对象不会变
                adapter.notifyItemChanged(index, "select_$position")
            } else {
                // 当前刷新方式会回调 KolUgcLabelListProvider.convert(helper: BaseViewHolder, item: CreatorMultiInfo)
                // 此时得到的 RecyclerTabLayout 对象为一个新的对象, indicator 的切换动画就不好做
                adapter.notifyItemChanged(index)
            }
        }
    }

    private fun <T : RecyclerView.Adapter<*>> findRvAdapterById(
        cardCallback: (CreatorMultiInfo) -> Boolean,
        rvId: Int
    ): T? {
        val data = adapter.data
        val index = data.indexOfFirst { cardCallback(it) }
        if (index in data.indices) {
            val holder = binding.rvCreate.findViewHolderForAdapterPosition(index)
            return holder?.itemView?.findViewById<RecyclerView>(rvId)?.adapter as? T
        }
        return null
    }

    private fun <T : RecyclerView.Adapter<*>> findRvById(
        cardCallback: (CreatorMultiInfo) -> Boolean,
        rvId: Int
    ): RecyclerView? {
        val data = adapter.data
        val index = data.indexOfFirst { cardCallback(it) }
        if (index in data.indices) {
            val holder = binding.rvCreate.findViewHolderForAdapterPosition(index)
            val rv = holder?.itemView?.findViewById<RecyclerView>(rvId)
            if (rv?.adapter as? T != null) {
                return rv
            }
        }
        return null
    }

    private fun updateListView(loadStatus: LoadStatus, data: MutableList<CreatorMultiInfo>?) {
        Timber.d("check_kol updateListView ${loadStatus.status}")
        when (loadStatus.status) {
            LoadType.Loading -> {
                if (data.isNullOrEmpty()) {
                    binding.loading.showLoading()
                }
            }

            LoadType.Fail -> {
                if (!loadStatus.isUsed) {
                    loadStatus.isUsed = true
                    toast(loadStatus.message)
                }
                binding.refresh.isRefreshing = false
                if (data.isNullOrEmpty()) {
                    binding.rvCreate.isVisible = false
                    binding.loading.showError()
                } else {
                    binding.loading.hide()
                    binding.rvCreate.isVisible = true
                    adapter.loadMoreModule.loadMoreFail()
                }
                footerLoadingView?.hide()
            }

            LoadType.Refresh -> {
                binding.refresh.isRefreshing = false
                adapter.submitData(viewLifecycleOwner.lifecycle, data, true)
                if (data.isNullOrEmpty()) {
                    binding.loading.showEmpty(getString(R.string.no_data))
                    adapter.loadMoreModule.loadMoreEnd()
                } else {
                    binding.loading.hide()
                    binding.rvCreate.isVisible = true
                    adapter.resetLoadMore()
                    adapter.loadMoreModule.loadMoreComplete()
                }
                if (data?.any { it.isLabelUgcGame() } == true) {
                    footerLoadingView?.hide()
                } else {
                    footerLoadingView?.showLoading()
                }
            }

            LoadType.LoadMore -> {
                binding.refresh.isRefreshing = false
                binding.loading.hide()
                binding.rvCreate.isVisible = true
                adapter.submitData(viewLifecycleOwner.lifecycle, data, false)
                adapter.loadMoreModule.loadMoreComplete()
                footerLoadingView?.hide()
            }

            LoadType.End -> {
                binding.refresh.isRefreshing = false
                binding.loading.hide()
                binding.rvCreate.isVisible = true
                adapter.submitData(viewLifecycleOwner.lifecycle, data, false)
                adapter.loadMoreModule.loadMoreEnd()
                footerLoadingView?.hide()
            }

            LoadType.Update -> {
                binding.refresh.isRefreshing = false
                binding.loading.hide()
                binding.rvCreate.isVisible = true
                if (updateList || loadStatus.message.isNullOrEmpty()) {
                    updateList = false
                    adapter.submitData(
                        viewLifecycleOwner.lifecycle,
                        data,
                        data.isNullOrEmpty()
                    )
                }
                footerLoadingView?.hide()
            }

            else -> {
                binding.refresh.isRefreshing = false
                binding.loading.hide()
                binding.rvCreate.isVisible = true
                footerLoadingView?.hide()
            }
        }
    }

    override fun loadFirstData() {
        kolViewModel.refreshFrameApi()
    }

    private var resumeTime: Long = -1
    override fun onResume() {
        super.onResume()
        resumeTime = System.currentTimeMillis()
        Analytics.track(EventConstants.EVENT_COMMUNITY_FEED_SHOW) {
            put("tag", blockName)
        }
        if (isLoadSuccess) {
            Analytics.track(EventConstants.EVENT_COMMUNITY_MENU_SHOW) {
                put("menu_name", blockName)
            }
        }
        // 发埋点
        binding.rvCreate.adapter?.notifyDataSetChanged()
        if (needUpdateDailyTaskStatus) {
            needUpdateDailyTaskStatus = false
            kolViewModel.refreshDailyTaskTipsStatus()
        }
    }

    override fun onPause() {
        super.onPause()
        if (resumeTime > 0) {
            Analytics.track(EventConstants.EVENT_COMMUNITY_FEED_SHOW) {
                put("tag", blockName)
                put("playtime", System.currentTimeMillis() - resumeTime)
            }
        }
        adapter.stopBanner()
    }

    override fun onDestroyView() {
        footerLoadingView = null
        updateList = true
        binding.rvCreate.adapter = null
        super.onDestroyView()
    }

    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_CREATOR_BOTTOM_TAB
}