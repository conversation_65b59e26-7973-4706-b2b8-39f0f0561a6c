package com.socialplay.gpark.ui.kol

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.ApiInvokeException
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.LoadStatus
import com.socialplay.gpark.data.model.LoadType
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.account.UserFollowEvent
import com.socialplay.gpark.data.model.creator.CreatorFrameResult
import com.socialplay.gpark.data.model.creator.CreatorMultiInfo
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.data.model.creator.LabelCreatorUgcGame
import com.socialplay.gpark.data.model.creator.RecommendUgcResult
import com.socialplay.gpark.data.model.creator.label.KolCreatorLabel
import com.socialplay.gpark.data.model.creator.label.UgcPublishLabel
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.RedBadgeData
import com.socialplay.gpark.data.model.user.RelationType
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.usecase.GetDailyTaskRewardStatusUseCase
import com.socialplay.gpark.util.extension.LifecycleCallback
import com.socialplay.gpark.util.extension.ifEmptyNull
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.lastOrNull
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by bo.li
 * Date: 2024/8/2
 * Desc: kol创作者社区
 */
class KolCreatorViewModel(
    private val repository: IMetaRepository,
    private val context: Application,
    private val accountInteractor: AccountInteractor,
    private val getDailyTaskRewardStatusUseCase: GetDailyTaskRewardStatusUseCase,
) : ViewModel() {

    // ugc游戏去重
    private val ugcIdSet = HashSet<String>()

    // 推荐标签ugc游戏页数
    private var ugcLabelPageOffset: Int? = null

    // toast
    val toastLifeCallback: LifecycleCallback<(String) -> Unit> = LifecycleCallback()

    // 混合大列表
    private val _combineListLiveData: MutableLiveData<Pair<LoadStatus, List<CreatorMultiInfo>?>> =
        MutableLiveData()
    val combineListLiveData: LiveData<Pair<LoadStatus, List<CreatorMultiInfo>?>> =
        _combineListLiveData

    // 推荐用户标签列表
    private val _creatorLabelListLiveData: MutableLiveData<List<KolCreatorLabel>?> =
        MutableLiveData()
    val creatorLabelListLiveData: LiveData<List<KolCreatorLabel>?> = _creatorLabelListLiveData

    // 标签用户列表
    private val _labelCreatorListLiveData: MutableLiveData<List<KolCreatorInfo>?> =
        MutableLiveData()
    val labelCreatorListLiveData: LiveData<List<KolCreatorInfo>?> = _labelCreatorListLiveData

    // ugc标签列表
    private val _ugcLabelListLiveData: MutableLiveData<List<UgcPublishLabel>?> = MutableLiveData()
    val ugcLabelListLiveData: LiveData<List<UgcPublishLabel>?> = _ugcLabelListLiveData

    // 当前选中的创作者标签
    val selectedCreatorTagId get() = _creatorLabelListLiveData.value?.firstOrNull { it.localSelected }?.tagId

    // 防止重复调用ugc列表接口
    private val fetchingUgcRecommend = AtomicBoolean(false)

    private val accountUserCallback = { old: MetaUserInfo?, new: MetaUserInfo? ->
        if (old?.uuid != new?.uuid) {
            refreshFrameApi()
        }
    }

    private var redBadgeObserver: Observer<RedBadgeData?>? = null
    private val _dailyTaskTipsShowFlow: MutableStateFlow<Boolean> = MutableStateFlow(false)

    init {
        accountInteractor.addUserChangedCallback(accountUserCallback)
        EventBus.getDefault().register(this)
        redBadgeObserver = Observer<RedBadgeData?> { badge ->
            updateFlyWheel(badge)
        }.apply {
            accountInteractor.badgeLiveData.observeForever(this)
        }

        viewModelScope.launch {
            _dailyTaskTipsShowFlow.collectLatest {
                updateFlyWheel(accountInteractor.badgeLiveData.value)
            }
        }
    }

    override fun onCleared() {
        accountInteractor.removeUserChangedCallback(accountUserCallback)
        EventBus.getDefault().unregister(this)
        redBadgeObserver?.let { accountInteractor.badgeLiveData.removeObserver(it) }
        super.onCleared()
    }

    private fun getSelectedUgcLabel(): UgcPublishLabel? {
        return _ugcLabelListLiveData.value?.firstOrNull { it.localSelected }
    }

    fun refreshFrameApi() {
        if (_combineListLiveData.value?.first?.status == LoadType.Loading) {
            return
        }
        _combineListLiveData.value =
            LoadStatus(status = LoadType.Loading) to _combineListLiveData.value?.second
        viewModelScope.launch {
            val frameResult =
                runCatching { repository.getKolFrameList().singleOrNull() }.getOrElse {
                    Timber.e("check_kol $it")
                    null
                }
            val frameData = frameResult?.data
            if (frameData == null) {
                _combineListLiveData.value = LoadStatus(
                    message = ApiInvokeException.getToastByError(context, frameResult?.exception),
                    status = LoadType.Fail
                ) to null
                return@launch
            }
            _creatorLabelListLiveData.value = frameData.starCreatorTypeList
            val combineList = buildFrame(frameData)
                .appendOperationFrame()
                .appendRecommendFrame()
            // 除了推荐的一行两个外，其他的进行排序
            _combineListLiveData.value =
                LoadStatus(status = LoadType.Refresh) to combineList.sortedBy { it.rvType }
            // 获取第一页ugc一行两个列表
            refreshRecommendUgc(getSelectedUgcLabel()?.tagId)
        }
    }

    private fun buildFrame(frame: CreatorFrameResult): MutableList<CreatorMultiInfo> {
        val list = mutableListOf<CreatorMultiInfo>().apply {
            frame.starUserInfo?.let {
                CreatorMultiInfo.buildRecommendUserInfo(frame.starUserInfo)?.let { recommendUser ->
                    frame.starUserInfo.userInfoList.also { _labelCreatorListLiveData.value = it }
                    _creatorLabelListLiveData.value =
                        frame.starCreatorTypeList?.map { it.copy(localSelected = it.tagId == frame.starUserInfo.configItem?.tagId) }
                    add(recommendUser)
                }
            }
        }
        return list
    }

    private suspend fun MutableList<CreatorMultiInfo>.appendRecommendFrame(): MutableList<CreatorMultiInfo> {
        runCatching {
            val pair = combine(
                repository.getKolRecommendUgcGameListByPageV2(null),
                repository.getAllUgcPublishedTag()
            ) { ugc, label ->
                ugc to label
            }.singleOrNull()
            pair?.first?.data?.dataList?.ifEmptyNull()?.let {
                add(CreatorMultiInfo.buildRecommendUgcInfo(it))
            }
            if (pair?.second?.succeeded != true) {
                toastLifeCallback.dispatchOnMainThread {
                    invoke(
                        ApiInvokeException.getToastByError(
                            GlobalContext.get().get(),
                            pair?.second?.exception
                        )
                    )
                }
            }
            val labelList = pair?.second?.data ?: listOf(UgcPublishLabel.buildAllLabel())
            _ugcLabelListLiveData.value = labelList.distinctBy { it.tagId }.mapIndexed { index, ugcPublishLabel ->
                ugcPublishLabel.copy(localSelected = index == 0)
            }
            CreatorMultiInfo.buildUgcLabelSelectorInfo(labelList)?.let { it -> add(it) }
        }.getOrElse {
            Timber.e("check_kol appendRecommendFrame $it")
        }
        return this
    }

    /**
     * 飞轮位、banner位
     */
    private suspend fun MutableList<CreatorMultiInfo>.appendOperationFrame(): MutableList<CreatorMultiInfo> {
        runCatching {
            val pair = combine(
                repository.getKolFlyWheel(),
                repository.getKolBanner()
            ) { flyWheel, banner ->
                flyWheel to banner
            }.lastOrNull()
            buildFlyWheelWithBadge(
                accountInteractor.badgeLiveData.value,
                pair?.first?.data
            ).first?.ifEmptyNull()?.let {
                add(CreatorMultiInfo.buildFlyWheelInfo(it))
            }
            pair?.second?.data?.ifEmptyNull()?.let {
                add(CreatorMultiInfo.buildBannerInfo(it))
            }
        }.getOrElse {
            Timber.e("check_kol appendOperationFrame $it")
        }
        return this
    }

    private fun refreshRecommendUgc(labelId: Int?) {
        if (_combineListLiveData.value?.first?.status == LoadType.Loading || fetchingUgcRecommend.get()) {
            return
        }
        ugcLabelPageOffset = 1
        fetchingUgcRecommend.set(true)
        viewModelScope.launch {
            repository.getUgcGameListByTag(labelId, null, 20)
                .collect { result ->
                    ugcIdSet.clear()
                    // 清空原本的标签ugc游戏
                    val newList = ArrayList<CreatorMultiInfo>(
                        _combineListLiveData.value?.second ?: emptyList()
                    ).apply {
                        removeAll { it.isLabelUgcGame() }
                    }
                    if (result.succeeded) {
                        ugcLabelPageOffset = result.data?.offset
                        val list = buildLabelUgcList(result, LabelCreatorUgcGame.COLUMN_TYPE_END)
                        list?.let { newList.addAll(it) }
                        _combineListLiveData.value =
                            LoadStatus(status = if (list.isNullOrEmpty() || result.data?.end == true || ugcLabelPageOffset == null) LoadType.End else LoadType.LoadMore) to newList
                    } else {
                        _combineListLiveData.value = LoadStatus(
                            message = ApiInvokeException.getToastByError(
                                context,
                                result.exception
                            ), status = LoadType.Fail
                        ) to newList
                    }
                    fetchingUgcRecommend.set(false)
                }
        }
    }

    private fun refreshRecommendUserList(tagId: Int) {
        if (_combineListLiveData.value?.first?.status == LoadType.Loading) {
            return
        }
        viewModelScope.launch {
            repository.getKolCreatorListByTag(tagId, null).collect { result ->
                _labelCreatorListLiveData.value = result.data?.userList
                if (!result.succeeded) {
                    toastLifeCallback.dispatchOnMainThread {
                        invoke(
                            ApiInvokeException.getToastByError(
                                GlobalContext.get().get(),
                                result.exception
                            )
                        )
                    }
                }
            }
        }
    }

    fun loadMoreRecommendUgc() {
        if (_combineListLiveData.value?.first?.status == LoadType.Loading || fetchingUgcRecommend.get()) {
            return
        }
        fetchingUgcRecommend.set(true)
        viewModelScope.launch {
            repository.getUgcGameListByTag(getSelectedUgcLabel()?.tagId, ugcLabelPageOffset, 20)
                .collect { result ->
                    if (result.succeeded) {
                        ugcLabelPageOffset = result.data?.offset
                        val originList = _combineListLiveData.value?.second
                        val lastColumnType = _combineListLiveData.value?.second?.lastOrNull {
                            it.isLabelUgcGame()
                        }?.game?.localColumnType ?: LabelCreatorUgcGame.COLUMN_TYPE_END
                        val list = buildLabelUgcList(result, lastColumnType)
                        if (list.isNullOrEmpty() || result.data?.end == true || ugcLabelPageOffset == null) {
                            _combineListLiveData.value =
                                LoadStatus(status = LoadType.End) to originList
                        } else {
                            _combineListLiveData.value =
                                LoadStatus(status = LoadType.LoadMore) to ArrayList<CreatorMultiInfo>(
                                    originList ?: emptyList()
                                ).apply {
                                    addAll(list)
                                }
                        }
                    } else {
                        _combineListLiveData.value = LoadStatus(
                            message = ApiInvokeException.getToastByError(
                                context,
                                result.exception
                            ), status = LoadType.Fail
                        ) to _combineListLiveData.value?.second
                    }
                    fetchingUgcRecommend.set(false)
                }
        }
    }

    private fun buildLabelUgcList(
        result: DataResult<RecommendUgcResult>,
        prevColumnType: Int
    ): List<CreatorMultiInfo>? {
        var newColumnType = prevColumnType
        return result.data?.dataList?.mapIndexedNotNull { index, labelCreatorUgcGame ->
            if (ugcIdSet.contains(labelCreatorUgcGame.id)) {
                null
            } else {
                ugcIdSet.add(labelCreatorUgcGame.id)
                newColumnType = LabelCreatorUgcGame.newColumnType(newColumnType)
                CreatorMultiInfo.buildLabelUgcInfo(labelCreatorUgcGame.copy(localColumnType = newColumnType))
            }
        }
    }

    fun changeFollow(uuid: String, toFollow: Boolean) {
        if (_combineListLiveData.value?.first?.status == LoadType.Loading) {
            return
        }
        viewModelScope.launch {
            Analytics.track(EventConstants.EVENT_FOLLOW_CLICK) {
                put(EventParamConstants.KEY_USERID, uuid)
                put(
                    EventParamConstants.KEY_LOCATION,
                    EventParamConstants.LOCATION_KOL_TAB_STAR_CREATOR
                )
                put(
                    EventParamConstants.KEY_TYPE,
                    if (toFollow) EventParamConstants.TYPE_FOLLOW else EventParamConstants.TYPE_UNFOLLOW
                )
            }
            if (toFollow) {
                repository.relationAdd(uuid, RelationType.Follow.value)
            } else {
                repository.relationDel(uuid, RelationType.Follow.value)
            }.collect { result ->
                if (result.succeeded) {
                    EventBus.getDefault()
                        .post(UserFollowEvent(uuid, toFollow, UserFollowEvent.FROM_KOL_TAB))
                    updateFollowState(uuid, toFollow)
                } else {
                    toastLifeCallback.dispatchOnMainThread {
                        invoke(
                            ApiInvokeException.getToastByError(
                                GlobalContext.get().get(),
                                result.exception
                            )
                        )
                    }
                }
            }
        }
    }

    private fun updateFollowState(uuid: String, toFollow: Boolean) {
        val newList = _labelCreatorListLiveData.value?.map {
            if (it.uuid == uuid) {
                it.copy(followUser = toFollow)
            } else {
                it
            }
        }
        _labelCreatorListLiveData.value = newList
    }


    @Subscribe
    fun onUserFollowEvent(event: UserFollowEvent) {
        if (event.from != UserFollowEvent.FROM_KOL_TAB && _labelCreatorListLiveData.value?.any { it.uuid == event.uuid && it.followUser != event.followStatus } == true) {
            updateFollowState(event.uuid, event.followStatus)
        }
    }

    fun selectUgcLabel(labelId: Int?) {
        Analytics.track(
            EventConstants.UGC_CATEGORYID_CLICK,
            EventParamConstants.KEY_TAGID to (labelId?.toString() ?: "all")
        )
        val selectedLabel = _ugcLabelListLiveData.value?.firstOrNull { it.localSelected }
        if (selectedLabel?.tagId == labelId) return
        val newList = _ugcLabelListLiveData.value?.map { card ->
            card.copy(localSelected = card.tagId == labelId)
        }
        _ugcLabelListLiveData.value = newList
        refreshRecommendUgc(labelId)
    }

    fun selectCreatorLabel(tagId: Int) {
        val selectedLabel = _creatorLabelListLiveData.value?.firstOrNull { it.localSelected }
        if (selectedLabel?.tagId == tagId) return
        val newList = _creatorLabelListLiveData.value?.map { card ->
            card.copy(localSelected = card.tagId == tagId)
        }
        _creatorLabelListLiveData.value = newList
        refreshRecommendUserList(tagId)
    }

    /**
     * @return 列表，是否改变信息
     */
    private fun buildFlyWheelWithBadge(
        badge: RedBadgeData?,
        flyWheelList: List<UniJumpConfig>?
    ): Pair<List<UniJumpConfig>?, Boolean> {
        if (flyWheelList.isNullOrEmpty()) return flyWheelList to false
        var changed = false
        val ocShortsShowBadge = badge?.newTemplate?.hasNew == true
        return flyWheelList.map {
            if (it.jumpType == UniJumpConfig.JUMP_TYPE_OC_SHORTS && it.localShowRedBadge != ocShortsShowBadge) {
                changed = true
                it.copy(
                    localShowRedBadge = ocShortsShowBadge
                )
            } else if (it.jumpType == UniJumpConfig.POS_DAILY_TASK_REWARD && it.localShowRedBadge != _dailyTaskTipsShowFlow.value){
                changed = true
                it.copy(localShowRedBadge = _dailyTaskTipsShowFlow.value)
            } else it
        } to changed
    }

    private fun updateFlyWheel(badge: RedBadgeData?) {
        if (_combineListLiveData.value?.first?.status == LoadType.Loading) {
            return
        }

        var update = false
        val newList = _combineListLiveData.value?.second?.map {
            if (it.isFlyWheel) {
                val (list, changed) = buildFlyWheelWithBadge(badge, it.operationList)
                if (changed) {
                    update = true
                    it.copy(operationList = list)
                } else {
                    it
                }
            } else {
                it
            }
        }
        if (!update) return
        _combineListLiveData.value = LoadStatus(status = LoadType.Update) to newList
    }

    fun refreshDailyTaskTipsStatus() {
        viewModelScope.launch {
            val result = kotlin.runCatching {
                getDailyTaskRewardStatusUseCase().invoke()
            }.getOrElse {
                Timber.e(it, "refreshDailyTaskTipsStatus error")
                false
            }
            _dailyTaskTipsShowFlow.update {
                result
            }
        }
    }
}