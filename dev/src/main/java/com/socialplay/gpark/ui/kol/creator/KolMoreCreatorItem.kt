package com.socialplay.gpark.ui.kol.creator

import android.content.Context
import android.view.View
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.databinding.ItemKolMoreCreatorBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.view.FollowView
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.getCslByColor
import com.socialplay.gpark.util.extension.getDrawableByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.visible

/**
 * Created by bo.li
 * Date: 2024/8/9
 * Desc: kol 更多创作者列表
 */
interface IKolMoreCreatorAction : IBaseEpoxyItemListener {
    fun goProfile(uuid: String)
    fun changeFollow(uuid: String, toFollow: Boolean)
    fun onItemShow(uuid: String)
}

fun MetaModelCollector.kolMoreCreatorItem(
    pageKey: String,
    item: KolCreatorInfo,
    useFollowFunc: Boolean,
    listener: IKolMoreCreatorAction?
) {
    add(
        KolMoreCreatorItem(
            item,
            useFollowFunc,
            listener
        ).id("${pageKey}_kolMoreCreatorItem_${item.uuid}")
    )
}

/**
 * @param item 用户数据
 * @param useFollowFunc 使用关注功能
 */
data class KolMoreCreatorItem(
    val item: KolCreatorInfo,
    val useFollowFunc: Boolean,
    val listener: IKolMoreCreatorAction?
) : ViewBindingItemModel<ItemKolMoreCreatorBinding>(
    R.layout.item_kol_more_creator,
    ItemKolMoreCreatorBinding::bind
) {

    override fun ItemKolMoreCreatorBinding.onBind() {
        val context = getItemView().context
        includeItem.tvName.text = item.nickname
        includeItem.tvId.text =
            context.getString(R.string.creation_num_param, item.releaseCount.toString())
        listener?.getGlideOrNull()?.run {
            load(item.avatar).placeholder(R.drawable.icon_default_avatar)
                .into(includeItem.ivPortrait)
        }
        root.setOnAntiViolenceClickListener {
            listener?.goProfile(item.uuid)
        }
        initActionBtn(context)
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE) {
            listener?.onItemShow(item.uuid)
        }
    }

    private fun ItemKolMoreCreatorBinding.initActionBtn(context: Context) {
        if (useFollowFunc) {
            followView.visible()
            tvAction.gone()
            if(item.followUser) {
                followView.status = FollowView.Status.FOLLOWING
            } else {
                followView.status = FollowView.Status.UNFOLLOW
            }
            followView.setOnAntiViolenceClickListener {
                listener?.changeFollow(item.uuid, !item.followUser)
            }
        } else {
            followView.gone()
            tvAction.visible()
            tvAction.setOnAntiViolenceClickListener {
                listener?.goProfile(item.uuid)
            }
        }
    }
}