package com.socialplay.gpark.ui.kol.game

import android.view.View
import androidx.core.view.isVisible
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.CreatorUgcGame
import com.socialplay.gpark.databinding.ItemKolMoreUgcBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.kol.list.adapter.KolCreatorUgcAdapter.Companion.DEFAULT_TAG_BG
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.backgroundTintListByColorStr
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setWidth
import com.socialplay.gpark.util.extension.visible

interface IKolMoreUgcGameAction : IBaseEpoxyItemListener {
    fun goUgcDetail(ugId: String, gameCode: String, packageName: String)
    fun onItemShow(ugId: String, packageName: String)
}

fun MetaEpoxyController.kolMoreUgcGame(
    item: CreatorUgcGame,
    position: Int,
    listener: IKolMoreUgcGameAction?
) {
    add(KolMoreUgcGameItem(item, position, listener).id("kolMoreUgcGame_${item.id}"))
}

/**
 * Created by bo.li
 * Date: 2024/8/8
 * Desc: Kol ugc游戏二级页面
 */
data class KolMoreUgcGameItem(
    val item: CreatorUgcGame,
    val position: Int,
    val listener: IKolMoreUgcGameAction?
) : ViewBindingItemModel<ItemKolMoreUgcBinding>(
    R.layout.item_kol_more_ugc,
    ItemKolMoreUgcBinding::bind
) {

    override fun ItemKolMoreUgcBinding.onBind() {
        initSize(this, position % 2 == 0)
        listener?.getGlideOrNull()?.run {
            load(item.banner).placeholder(R.drawable.placeholder_corner_12)
                .into(ivIcon)
            load(item.userIcon).placeholder(R.drawable.icon_default_avatar)
                .into(ivAuthorAvatar)
        }
        tvName.text = item.ugcGameName
        tvAuthorName.text = item.userName
        lvLike.setLikeText(UnitUtil.formatKMCount(item.loveQuantity))
        lvPlay.setLikeText(UnitUtil.formatKMCount(item.pvCount))
        val tagList = item.gameTagList?.map { it.name } ?: emptyList()
        tagContainer.setTags(tagList)
        when (item.gameShowTag) {
            CreatorUgcGame.GAME_SHOW_TAG_NEW -> {
                tvTag.visible()
                tvTag.setText(R.string.community_followed_work_tag_new)
                tvTag.setBackgroundResource(R.drawable.bg_followed_work_new)
            }

            CreatorUgcGame.GAME_SHOW_TAG_HOT -> {
                tvTag.visible()
                tvTag.setText(R.string.community_followed_work_tag_hot)
                tvTag.setBackgroundResource(R.drawable.bg_followed_work_hot)

            }

            CreatorUgcGame.GAME_SHOW_TAG_RECOMMEND -> {
                tvTag.visible()
                tvTag.setText(R.string.community_followed_work_tag_recommend)
                tvTag.setBackgroundResource(R.drawable.bg_followed_work_recommend)

            }

            else -> {
                tvTag.gone()
            }
        }
        root.setOnClickListener {
            listener?.goUgcDetail(item.id, item.availableGameCode.orEmpty(), item.packageName)
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE) {
            listener?.onItemShow(item.id, item.packageName)
        }
    }

    private fun initSize(binding: ItemKolMoreUgcBinding, isStart: Boolean) {
        val dp16 = 16.dp
        val dp6 = 6.dp
        binding.spaceStart.setWidth(if (isStart) dp16 else dp6)
        binding.spaceEnd.setWidth(if (isStart) dp6 else dp16)
    }
}