package com.socialplay.gpark.ui.kol.list

import android.annotation.SuppressLint
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.CreatorMultiInfo
import com.socialplay.gpark.data.model.creator.CreatorMultiType
import com.socialplay.gpark.ui.base.adapter.BaseProviderMultiDiffAdapter
import com.socialplay.gpark.ui.kol.list.provider.KolBannerProvider
import com.socialplay.gpark.ui.kol.list.provider.KolFlyWheelProvider
import com.socialplay.gpark.ui.kol.list.provider.KolHorUgcProvider
import com.socialplay.gpark.ui.kol.list.provider.KolLabelUgcGameProvider
import com.socialplay.gpark.ui.kol.list.provider.KolUgcLabelListProvider
import com.socialplay.gpark.ui.kol.list.provider.RecommendCreatorListProvider
import com.youth.banner.Banner
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2024/8/2
 * Desc: kol 创作者多类型列表
 */
class CreatorKolAdapter(
    val glide: RequestManager,
    val lifecycleOwner: LifecycleOwner,
    val listener: IKolCreatorAdapterListener
) : BaseProviderMultiDiffAdapter<CreatorMultiInfo>(DIFF_CALLBACK), LoadMoreModule {

    companion object {
        private val DIFF_CALLBACK = object : DiffUtil.ItemCallback<CreatorMultiInfo>() {
            override fun areItemsTheSame(
                oldItem: CreatorMultiInfo,
                newItem: CreatorMultiInfo
            ): Boolean {
                if (oldItem.isLabelUgcGame() && newItem.isLabelUgcGame()) {
                    return oldItem.game?.id == newItem.game?.id
                }
                return oldItem.rvType == newItem.rvType
            }

            @SuppressLint("DiffUtilEquals")
            override fun areContentsTheSame(
                oldItem: CreatorMultiInfo,
                newItem: CreatorMultiInfo
            ): Boolean {
                if (oldItem.rvType != newItem.rvType) return false
                val result = when (oldItem.rvType) {
                    CreatorMultiType.TYPE_LABEL_UGC_GAME_ITEM -> {
                        oldItem.game == newItem.game
                    }

                    CreatorMultiType.TYPE_RECOMMEND_CREATOR -> {
                        true
                    }

                    CreatorMultiType.TYPE_RECOMMEND_UGC_GAME -> {
                        oldItem.toUgcListInfo() == newItem.toUgcListInfo()
                    }

                    CreatorMultiType.TYPE_ALL_UGC_GAME_SELECTOR -> {
                        true
                    }

                    CreatorMultiType.TYPE_OPERATION_BANNER, CreatorMultiType.TYPE_OPERATION_FLY_WHEEL -> {
                        oldItem.operationList == newItem.operationList
                    }

                    else -> {
                        true
                    }
                }
                Timber.d("check_kol $result ${oldItem.rvType} ${newItem.rvType}")
                return result
            }

            override fun getChangePayload(
                oldItem: CreatorMultiInfo,
                newItem: CreatorMultiInfo
            ): Any? {
                return super.getChangePayload(oldItem, newItem)
            }
        }
    }

    init {
        initItemType()
    }

    private fun initItemType() {
        addItemProvider(KolFlyWheelProvider(glide, listener))
        addItemProvider(RecommendCreatorListProvider(glide, lifecycleOwner, listener))
        addItemProvider(
            KolHorUgcProvider(
                glide,
                CreatorMultiType.TYPE_RECOMMEND_UGC_GAME,
                listener
            )
        )
        addItemProvider(KolBannerProvider(glide, listener))
        addItemProvider(KolUgcLabelListProvider(glide, listener))
        addItemProvider(KolLabelUgcGameProvider(glide))
    }

    override fun getItemType(data: List<CreatorMultiInfo>, position: Int): Int {
        return data[position].rvType
    }

    fun stopBanner() {
        getBanner()?.stop()
    }

    private fun getBanner(): Banner<*, *>? {
        val bannerIndex = data.indexOfFirst { creatorMultiInfo ->
            creatorMultiInfo.isBannerOperation()
        }
        if (bannerIndex in data.indices) {
            return getViewByPosition(bannerIndex, R.id.banner) as? Banner<*, *>
        }
        return null
    }
}