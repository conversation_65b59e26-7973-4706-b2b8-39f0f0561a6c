package com.socialplay.gpark.ui.kol.list.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.CreatorUgcGame
import com.socialplay.gpark.databinding.AdapterKolCreatorWorkBinding
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.base.adapter.BasicQuickAdapter
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.backgroundTintListByColorStr
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setWidth
import com.socialplay.gpark.util.extension.visible

/**
 * Created by bo.li
 * Date: 2024/8/5
 * Desc: Kol创作者作品列表
 */
class KolCreatorUgcAdapter(
    data: MutableList<CreatorUgcGame>,
    private val itemWidth: Int,
    private val glide: RequestManager
) :
    BasicQuickAdapter<CreatorUgcGame, AdapterKolCreatorWorkBinding>(data) {

    companion object {
        const val DEFAULT_TAG_BG = "#FF5F42"
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterKolCreatorWorkBinding>,
        item: CreatorUgcGame
    ) {
        holder.binding.spaceEnd.setWidth(10.dp)
        holder.binding.ivIcon.setWidth(itemWidth)
        glide.load(item.banner).placeholder(R.drawable.placeholder_corner_12)
            .into(holder.binding.ivIcon)
        glide.load(item.userIcon).placeholder(R.drawable.icon_default_avatar)
            .into(holder.binding.ivAuthorAvatar)
        holder.binding.tvName.text = item.ugcGameName
        holder.binding.tvAuthorName.text = item.userName
        holder.binding.mlvPlayers.setLikeText(UnitUtil.formatKMCount(item.pvCount))

        when (item.gameShowTag) {
            CreatorUgcGame.GAME_SHOW_TAG_NEW -> {
                holder.binding.tvTag.visible()
                holder.binding.tvTag.setText(R.string.community_followed_work_tag_new)
                holder.binding.tvTag.setBackgroundResource(R.drawable.bg_followed_work_new)
            }

            CreatorUgcGame.GAME_SHOW_TAG_HOT -> {
                holder.binding.tvTag.visible()
                holder.binding.tvTag.setText(R.string.community_followed_work_tag_hot)
                holder.binding.tvTag.setBackgroundResource(R.drawable.bg_followed_work_hot)

            }

            CreatorUgcGame.GAME_SHOW_TAG_RECOMMEND -> {
                holder.binding.tvTag.visible()
                holder.binding.tvTag.setText(R.string.community_followed_work_tag_recommend)
                holder.binding.tvTag.setBackgroundResource(R.drawable.bg_followed_work_recommend)

            }

            else -> {
                holder.binding.tvTag.gone()
            }
        }

        holder.binding.mlvLike.setLikeText(UnitUtil.formatNumberWithUnit(num = item.likeCount ?: 0))

        val tags = item.gameTagList?.mapNotNull { it.name }?.take(2)
        if (tags.isNullOrEmpty()) {
            holder.binding.tags.gone()
        } else {
            holder.binding.tags.setTags(tags)
            holder.binding.tags.visible()
        }
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterKolCreatorWorkBinding {
        return AdapterKolCreatorWorkBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

}