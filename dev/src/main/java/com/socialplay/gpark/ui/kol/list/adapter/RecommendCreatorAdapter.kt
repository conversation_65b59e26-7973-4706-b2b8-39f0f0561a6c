package com.socialplay.gpark.ui.kol.list.adapter

import android.animation.Animator
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.lottie.LottieAnimationView
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.databinding.AdapterCommunityStarCreatorBinding
import com.socialplay.gpark.ui.base.adapter.BaseDifferAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.view.FollowView
import com.socialplay.gpark.util.extension.addAnimatorListener
import com.socialplay.gpark.util.extension.cancelAnimationIfAnimating
import com.socialplay.gpark.util.extension.dp
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.random.Random

/**
 * Created by bo.li
 * Date: 2024/8/5
 * Desc: ugc Kol创作者列表-样式2
 */
class RecommendCreatorAdapter(
    private val glide: RequestManager,
    val lifecycleOwner: LifecycleOwner,
) : BaseDifferAdapter<KolCreatorInfo, AdapterCommunityStarCreatorBinding>(DIFF_CALLBACK) {

    companion object {
        private val DIFF_CALLBACK = object : DiffUtil.ItemCallback<KolCreatorInfo>() {
            override fun areItemsTheSame(
                oldItem: KolCreatorInfo,
                newItem: KolCreatorInfo
            ): Boolean {
                return oldItem.uuid == newItem.uuid
            }

            override fun areContentsTheSame(
                oldItem: KolCreatorInfo,
                newItem: KolCreatorInfo
            ): Boolean {
                return oldItem == newItem
            }
        }
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterCommunityStarCreatorBinding>,
        item: KolCreatorInfo
    ) {
        holder.binding.iv.strokeWidth = (1.5f).dp.toFloat()
        glide.load(item.avatar)
            .centerCrop()
            .placeholder(R.drawable.icon_default_avatar)
            .into(holder.binding.iv)
        updateTvFollow(item, holder)
        holder.binding.tvUserName.text = item.nickname
        holder.binding.lav.apply {
            cancelAnimationIfAnimating()
            removeAllAnimatorListeners()
            addAnimatorListener(lifecycleOwner, object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                }

                override fun onAnimationEnd(animation: Animator) {
                    // 间隔3秒播放一次
                    lifecycleOwner.lifecycleScope.launch {
                        delay(3000)
                        setMinAndMaxProgress(0f, 1f)
                        progress = 0f
                        playAnimation()
                    }
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }
            })
            val randomProgress = Random.nextFloat()
            setMinAndMaxProgress(randomProgress, 1f)
            progress = randomProgress
            playAnimation()
        }
//        holder.binding.labelView.visible(false)
//        holder.binding.labelView.show(
//            item.tagIds,
//            item.labelInfo,
//            isOfficial = item.isOfficial == true,
//            glide = glide
//        )
//        holder.binding.tvSignature.text = item.signature ?: ""
    }

    override fun onViewRecycled(holder: BaseVBViewHolder<AdapterCommunityStarCreatorBinding>) {
        super.onViewRecycled(holder)
        holder.binding.lav.removeAllAnimatorListeners()
        holder.binding.lav.cancelAnimationIfAnimating()
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        for (i in 0 until recyclerView.childCount) {
            val view = recyclerView.getChildAt(i)
            val lav = view.findViewById<LottieAnimationView>(R.id.lav)
            lav?.removeAllAnimatorListeners()
            lav?.cancelAnimationIfAnimating()
        }
    }

    private fun updateTvFollow(
        item: KolCreatorInfo,
        holder: BaseVBViewHolder<AdapterCommunityStarCreatorBinding>
    ) {
        if (item.followUser) {
            holder.binding.followView.status = FollowView.Status.FOLLOWING
        } else {
            holder.binding.followView.status = FollowView.Status.UNFOLLOW
        }
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterCommunityStarCreatorBinding {
        return AdapterCommunityStarCreatorBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

}