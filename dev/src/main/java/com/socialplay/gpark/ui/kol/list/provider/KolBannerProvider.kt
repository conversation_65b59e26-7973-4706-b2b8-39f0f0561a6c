package com.socialplay.gpark.ui.kol.list.provider

import androidx.core.view.isVisible
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.provider.BaseItemProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.creator.CreatorMultiInfo
import com.socialplay.gpark.data.model.creator.CreatorMultiType
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.ui.editorschoice.adapter.BannerItemAdapter
import com.socialplay.gpark.ui.kol.list.IKolCreatorAdapterListener
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setHeight
import com.youth.banner.Banner
import com.youth.banner.listener.OnPageChangeListener
import com.zhpan.indicator.IndicatorView
import com.zhpan.indicator.enums.IndicatorSlideMode
import com.zhpan.indicator.enums.IndicatorStyle


/**
 * Kol banner位
 */
class KolBannerProvider(
    val glide: RequestManager,
    val listener: IKolCreatorAdapterListener?
) : BaseItemProvider<CreatorMultiInfo>() {

    override val layoutId: Int = R.layout.provider_creator_banner

    override val itemViewType: Int = CreatorMultiType.TYPE_OPERATION_BANNER

    companion object {
        private const val LOOP_TIME = 5_000L
    }

    override fun convert(helper: BaseViewHolder, item: CreatorMultiInfo) {
        val info = item.toOperationListInfo()
        val indicator = helper.getView<IndicatorView>(R.id.indicator)
        val banner = helper.getView<Banner<String, BannerItemAdapter>>(R.id.banner)
        val urlList = info.operationList.map { it.iconUrl.orEmpty() }
        val margin = helper.dp(16)
        val adapter = BannerItemAdapter(
            urlList,
            true,
            marginLeft = margin,
            marginRight = margin,
            glide = glide
        )
        adapter.setItemVisibilityChangeListener { position, isVisible ->
            if (isVisible) {
                val operation = runCatching { info.operationList[position] }.getOrNull()
                    ?: return@setItemVisibilityChangeListener
                Analytics.track(
                    EventConstants.UGC_TAB_EVENT_SHOW,
                    EventParamConstants.KEY_OPERATION_ID to operation.id.toString(),
                    EventParamConstants.KEY_TITLE to operation.title.toString(),
                    EventParamConstants.KEY_TYPE to EventConstants.Type.TYPE_OPERATION_BANNER
                )
            }
        }
        val singleBanner = urlList.size <= 1
        // 指示器
        indicator.isVisible = !singleBanner
        indicator.apply {
            val dp3 = helper.dp(3).toFloat()
            val dp8 = helper.dp(8).toFloat()
            setIndicatorStyle(IndicatorStyle.ROUND_RECT)
            setSliderWidth(dp3, dp8)
            setSliderHeight(dp3)
            setSlideMode(IndicatorSlideMode.NORMAL)
            setSliderGap(dp3)
            setPageSize(urlList.size)
            notifyDataChanged()
        }
        val startPosition = listener?.getBannerPosition()?.takeIf { it in urlList.indices } ?: 0
        // banner
        indicator.setCurrentPosition(startPosition)
        banner.setHeight(((helper.screenWidth - helper.dp(32)) / 343.0f * 142).toInt())
        banner.isAutoLoop(!singleBanner)
            .setStartPosition(startPosition + 1)
            .setAdapter(adapter)
            .setLoopTime(LOOP_TIME)
    }

    override fun onViewAttachedToWindow(holder: BaseViewHolder) {
        super.onViewAttachedToWindow(holder)
        getAdapter()?.let { adapter ->
            val position = holder.layoutPosition - adapter.headerLayoutCount
            if (position in adapter.data.indices) {
                adapter.getItemOrNull(position)?.toOperationListInfo()?.operationList?.let { list ->
                    onBannerAttach(holder, list)
                }
            }
        }
    }

    private fun onBannerAttach(holder: BaseViewHolder, operationList: List<UniJumpConfig>) {
        val indicator = holder.getViewOrNull<IndicatorView>(R.id.indicator)
        val banner = holder.getViewOrNull<Banner<String, BannerItemAdapter>>(R.id.banner) ?: return
        banner.setOnBannerListener { _, position ->
            val operation =
                runCatching { operationList[position] }.getOrNull() ?: return@setOnBannerListener
            Analytics.track(
                EventConstants.UGC_TAB_EVENT_CLICK,
                EventParamConstants.KEY_OPERATION_ID to operation.id.toString(),
                EventParamConstants.KEY_TITLE to operation.title.toString(),
                EventParamConstants.KEY_TYPE to EventConstants.Type.TYPE_OPERATION_BANNER
            )
            listener?.goUniJump(
                operation,
                LinkData.SOURCE_KOL_BANNER,
                CategoryId.CATEGORY_ID_KOL_BANNER
            )
        }
        banner.addOnPageChangeListener(object : OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
                indicator?.onPageScrolled(
                    position,
                    positionOffset,
                    positionOffsetPixels
                )
                if (position in operationList.indices) {
                    listener?.saveBannerPosition(position)
                }
            }

            override fun onPageSelected(position: Int) {
                indicator?.onPageSelected(position)
            }

            override fun onPageScrollStateChanged(state: Int) {
                indicator?.onPageScrollStateChanged(state)
            }
        })
        banner.start()
    }

    override fun onViewDetachedFromWindow(holder: BaseViewHolder) {
        holder.getViewOrNull<Banner<String, BannerItemAdapter>>(R.id.banner)?.apply {
            setOnBannerListener(null)
            addOnPageChangeListener(null)
            stop()
        }
        super.onViewDetachedFromWindow(holder)
    }
}