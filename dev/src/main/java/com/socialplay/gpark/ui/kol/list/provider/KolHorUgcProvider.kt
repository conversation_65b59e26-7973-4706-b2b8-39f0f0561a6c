package com.socialplay.gpark.ui.kol.list.provider

import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.CreatorMultiInfo
import com.socialplay.gpark.data.model.creator.CreatorMultiType
import com.socialplay.gpark.data.model.creator.CreatorUgcGame
import com.socialplay.gpark.data.model.creator.CreatorUgcGameListInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.ui.kol.list.IKolCreatorAdapterListener
import com.socialplay.gpark.ui.kol.list.adapter.KolCreatorUgcAdapter
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setPaddingEx
import kotlin.math.floor

/**
 * Kol创作者作品列表
 */
class KolHorUgcProvider(
    glide: RequestManager,
    private val rvType: Int,
    listener: IKolCreatorAdapterListener?
) : BaseListCreatorProvider(glide, listener) {

    override val itemViewType: Int = rvType

    companion object {
        const val SHOW_ITEM_COUNT = 2.4F
        const val RV_STATE_KEY = "KolHorUgc_"
    }

    override fun buildRv(helper: BaseViewHolder, item: CreatorMultiInfo) {
        val info = item.toUgcListInfo()
        val adapter = initAdapter(helper, info)
        val rv = helper.getView<RecyclerView>(R.id.rvHor)
        listener?.popRvStoredState("$RV_STATE_KEY${itemViewType}")?.let {
            rv.layoutManager?.onRestoreInstanceState(it)
        }
        rv.setPaddingEx(16.dp)
        rv.adapter = adapter
        adapter.setList(info.gameList)
    }

    override fun onViewDetachedFromWindow(holder: BaseViewHolder) {
        holder.getViewOrNull<RecyclerView>(R.id.rvHor)?.layoutManager?.onSaveInstanceState()?.let {
            listener?.saveRvState("$RV_STATE_KEY${itemViewType}", it)
        }
        super.onViewDetachedFromWindow(holder)
    }

    private fun initAdapter(
        holder: BaseViewHolder,
        info: CreatorUgcGameListInfo
    ): KolCreatorUgcAdapter {
        val itemWidth = calculateItemWidth(holder)
        val adapter = KolCreatorUgcAdapter(info.gameList.toMutableList(), itemWidth.toInt(), glide)
        adapter.setOnItemShowListener { item, position ->
            sendItemShow(item)
        }
        adapter.setOnItemClickListener { _, view, position ->
            val item = adapter.getItem(position)
            sendItemClick(item)
            listener?.onClickUgc(item.id, item.availableGameCode.orEmpty(), info)
        }
        return adapter
    }

    private fun sendItemShow(item: CreatorUgcGame) {
        when (rvType) {
            CreatorMultiType.TYPE_RECOMMEND_UGC_GAME -> {
                Analytics.track(
                    EventConstants.UGC_RECOMMEND_FEED_ITEM_SHOW,
                    EventParamConstants.KEY_GAMEID to item.id,
                    EventParamConstants.KEY_SHOW_CATEGORYID to CategoryId.CATEGORY_ID_KOL_RECOMMEND_UGC,
                    EventParamConstants.KEY_PACKAGENAME to item.packageName
                )
            }
        }
    }

    private fun sendItemClick(item: CreatorUgcGame) {
        when (rvType) {
            CreatorMultiType.TYPE_RECOMMEND_UGC_GAME -> {
                Analytics.track(
                    EventConstants.UGC_RECOMMEND_FEED_ITEM_CLICK,
                    EventParamConstants.KEY_GAMEID to item.id,
                    EventParamConstants.KEY_SHOW_CATEGORYID to CategoryId.CATEGORY_ID_KOL_RECOMMEND_UGC,
                    EventParamConstants.KEY_PACKAGENAME to item.packageName
                )
            }
        }
    }

    private fun calculateItemWidth(holder: BaseViewHolder): Float {
        val space = holder.dp(10)
        val firstSpace = holder.dp(16)
        val allSpaceWidth = firstSpace + (floor(SHOW_ITEM_COUNT).toInt() * space)
        val itemWidth = (holder.screenWidth - allSpaceWidth) / SHOW_ITEM_COUNT
        return itemWidth
    }

    override fun onClickGoMore(helper: BaseViewHolder, item: CreatorMultiInfo) {
        listener?.goMoreGame(item.rvType)
    }
}