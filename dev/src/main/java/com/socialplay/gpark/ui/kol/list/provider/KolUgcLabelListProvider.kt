package com.socialplay.gpark.ui.kol.list.provider

import android.view.View
import android.widget.ImageView
import androidx.core.view.isVisible
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.provider.BaseItemProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.CreatorMultiInfo
import com.socialplay.gpark.data.model.creator.CreatorMultiType
import com.socialplay.gpark.ui.kol.list.IKolCreatorAdapterListener
import com.socialplay.gpark.ui.view.MetaTextView
import com.socialplay.gpark.ui.view.RecyclerTabLayout
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible

/**
 * Kol ugc作品标签选择器
 */
class KolUgcLabelListProvider(
    private val glide: RequestManager,
    private val listener: IKolCreatorAdapterListener
) :
    BaseItemProvider<CreatorMultiInfo>() {

    override val layoutId: Int
        get() = R.layout.provider_creator_ugc_label_list

    override val itemViewType: Int = CreatorMultiType.TYPE_ALL_UGC_GAME_SELECTOR

    companion object {
        const val RV_STATE_KEY = "KolUgcLabelList"
    }

    override fun convert(helper: BaseViewHolder, item: CreatorMultiInfo, payloads: List<Any>) {
        convert(helper, item)
    }

    override fun convert(helper: BaseViewHolder, item: CreatorMultiInfo) {
        val ivMore = helper.getView<ImageView>(R.id.ivMore)
        val vMore = helper.getView<View>(R.id.vMore)
        val labelList = listener.getUgcLabelList()
        val showMore = labelList.size > 3
        ivMore.setOnAntiViolenceClickListener {
            listener.showMoreUgcLabel()
        }
        helper.getView<MetaTextView>(R.id.tvTitle).text = item.title
        vMore.isVisible = showMore
        ivMore.isVisible = showMore

        val tabLayout = helper.getView<RecyclerTabLayout>(R.id.tabLayout)
        val targetTabs = labelList.map { it.name ?: "" }
        tabLayout.visible(targetTabs.isNotEmpty())
        if (targetTabs.isNotEmpty()) {
            val currentTabs = tabLayout.getTabs()
            var selectedTagIndex = labelList.indexOfFirst { it.localSelected }.coerceAtLeast(0)
            if (currentTabs == targetTabs) {
                if (tabLayout.getSelectedIndex() != selectedTagIndex) {
                    tabLayout.selectTab(selectedTagIndex, false)
                }
            } else {
                listener.popRvStoredState(RV_STATE_KEY)?.let {
                    tabLayout.restoreInstanceState(it)
                }
                tabLayout.setTabs(targetTabs, selectedTagIndex)
            }
            tabLayout.onTabSelected = { index ->
                listener.selectUgcLabel(labelList[index].tagId)
            }
        }
    }

    override fun onViewDetachedFromWindow(holder: BaseViewHolder) {
        holder.getViewOrNull<RecyclerTabLayout>(R.id.tabLayout)?.saveInstanceState()?.let {
            listener.saveRvState(RV_STATE_KEY, it)
        }
        super.onViewDetachedFromWindow(holder)
    }
}