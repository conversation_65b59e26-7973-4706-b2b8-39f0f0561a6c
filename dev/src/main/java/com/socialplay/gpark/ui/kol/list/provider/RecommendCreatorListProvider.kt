package com.socialplay.gpark.ui.kol.list.provider

import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.provider.BaseItemProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.CreatorMultiInfo
import com.socialplay.gpark.data.model.creator.CreatorMultiType
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.data.model.creator.label.KolCreatorLabel
import com.socialplay.gpark.databinding.ProviderRecommendCreatorListBinding
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.kol.list.IKolCreatorAdapterListener
import com.socialplay.gpark.ui.kol.list.adapter.RecommendCreatorAdapter
import com.socialplay.gpark.ui.view.RecyclerTabLayout
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.visible

/**
 * Kol推荐创作者列表
 */
class RecommendCreatorListProvider(
    private val glide: RequestManager,
    val lifecycleOwner: LifecycleOwner,
    private val listener: IKolCreatorAdapterListener
) : BaseItemProvider<CreatorMultiInfo>() {

    override val layoutId: Int = R.layout.provider_recommend_creator_list

    override val itemViewType: Int = CreatorMultiType.TYPE_RECOMMEND_CREATOR

    companion object {
        const val RV_STATE_LABEL_KEY = "CREATOR_LABEL"
        const val RV_STATE_KEY = "CREATOR_LIST"
    }

    override fun convert(helper: BaseViewHolder, item: CreatorMultiInfo) {
        ProviderRecommendCreatorListBinding.bind(helper.itemView).apply {
            tvMore.setOnAntiViolenceClickListener {
                listener.goMoreRecommendCreator()
            }
            tvTitle.text = item.title
            initUserList(this, listener.getLabelCreatorList())
            initLabel(this, listener.getCreatorLabelList())
        }
    }

    override fun onViewDetachedFromWindow(holder: BaseViewHolder) {
        holder.getViewOrNull<RecyclerView>(R.id.rvHor)?.layoutManager?.onSaveInstanceState()?.let {
            listener.saveRvState(RV_STATE_KEY, it)
        }
        holder.getViewOrNull<RecyclerTabLayout>(R.id.tabLayout)?.saveInstanceState()?.let {
            listener.saveRvState(RV_STATE_LABEL_KEY, it)
        }
        super.onViewDetachedFromWindow(holder)
    }

    private fun initUserList(
        binding: ProviderRecommendCreatorListBinding,
        creatorList: List<KolCreatorInfo>
    ) {
        val userAdapter = initUserAdapter()
        binding.rvHor.setPaddingEx(binding.dp(16))
        binding.rvHor.itemAnimator = null
        binding.rvHor.adapter = userAdapter
        listener.popRvStoredState(RV_STATE_KEY)?.let {
            binding.rvHor.layoutManager?.onRestoreInstanceState(it)
        }
        userAdapter.setList(creatorList)
    }

    private fun initLabel(
        binding: ProviderRecommendCreatorListBinding,
        starCreatorTypeList: List<KolCreatorLabel>
    ) {
        val tabLayout = binding.tabLayout
        tabLayout.visible(starCreatorTypeList.isNotEmpty())
        if (starCreatorTypeList.isNotEmpty()) {
            val targetLabels = starCreatorTypeList.map { it.title ?: "" }
            val currentLabels = tabLayout.getTabs()
            var selectedLabelIndex =
                starCreatorTypeList.indexOfFirst { it.localSelected }.coerceAtLeast(0)
            if (currentLabels == targetLabels) {
                if (tabLayout.getSelectedIndex() != selectedLabelIndex) {
                    tabLayout.selectTab(selectedLabelIndex, false)
                }
            } else {
                listener.popRvStoredState(RV_STATE_LABEL_KEY)?.let {
                    tabLayout.restoreInstanceState(it)
                }
                tabLayout.setTabs(targetLabels, selectedLabelIndex)
            }
            tabLayout.onTabSelected = { index ->
                listener.selectCreatorLabel(starCreatorTypeList[index].tagId)
            }
        }
        val showLabel = starCreatorTypeList.size > 1
        binding.vMoreLabel.isVisible = showLabel
        binding.ivMoreLabel.isVisible = showLabel
        binding.ivMoreLabel.setOnAntiViolenceClickListener {
            listener.showMoreCreatorLabel()
        }
    }

    private fun initUserAdapter(): RecommendCreatorAdapter {
        val adapter = RecommendCreatorAdapter(glide, lifecycleOwner)
        adapter.setOnItemClickListener { _, view, position ->
            val item = adapter.getItem(position)
            listener.goProfile(item.uuid, EventConstants.From.FROM_KOL_CREATOR_TAB_STAR)
        }
        adapter.setOnItemShowListener { item, position ->
            listener.sendStarCreatorShow(item.uuid)
        }
        adapter.addChildClickViewIds(R.id.followView)
        adapter.setOnItemChildClickListener { _, view, position ->
            val item = adapter.getItem(position)
            listener.changeFollow(item.uuid, !item.followUser)
        }
        return adapter
    }
}