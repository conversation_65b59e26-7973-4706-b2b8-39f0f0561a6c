package com.socialplay.gpark.ui.main

import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.graphics.PorterDuff
import android.os.Parcelable
import android.view.WindowManager
import android.view.animation.AnimationUtils
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.outfit.UgcAssetEntrance
import com.socialplay.gpark.data.model.startup.GuideInfo
import com.socialplay.gpark.databinding.DialogMainAddBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.editor.BaseEditorDialogFragment
import com.socialplay.gpark.ui.editor.module.UgcModuleTabArgs
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.outfit.UgcAssetListState
import com.socialplay.gpark.util.extension.addUpdateListener
import com.socialplay.gpark.util.extension.backgroundTintListByColor
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.imageTintListByColor
import com.socialplay.gpark.util.extension.onAnimationEnd
import com.socialplay.gpark.util.extension.setListener
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.delay
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/10/10
 *     desc   :
 * </pre>
 */
@Parcelize
open class MainAddDialogArgs(
    val guide: GuideInfo?
) : Parcelable {

    val enableGuide get() = guide?.supported == true
    val blockBack get() = guide?.blockBack == true
    fun enableClick(vararg guideTypes: String) = guide == null || guideTypes.any { it == guide.type }
    fun needGuideEvent(guideType: String) = guide?.type == guideType
}

class MainAddDialog : BaseEditorDialogFragment() {

    companion object {
        const val GUIDE_SRC = "newbie_guide"

        fun show(fragment: Fragment, guide: GuideInfo?, onDismissCallback: ((Boolean) -> Unit)?) {
            if (!fragment.isAdded) return
            Analytics.track(
                EventConstants.EVENT_CREATE_TAB_CLICK
            )
            val dialog = MainAddDialog()
            dialog.arguments = MainAddDialogArgs(guide).asMavericksArgs()
            dialog.onDismissCallback = onDismissCallback
            dialog.show(fragment.childFragmentManager, "MainAddDialog")
        }
    }

    override val binding by viewBinding(DialogMainAddBinding::inflate)
    private val vm: MainAddViewModel by fragmentViewModel()
    private val args by args<MainAddDialogArgs>()
    private var loadingDialog: DialogFragment? = null
    private var onDismissCallback: ((Boolean) -> Unit)? = null

    override val navBlendDim = true

    override fun init() {
        super.init()

        if (!EnvConfig.isParty()) {
            binding.tvMaps.minLines = 2
        }

        if (!args.blockBack) {
            binding.root.setOnAntiViolenceClickListener {
                onDismissCallback?.invoke(true)
                onDismissCallback = null
                dismissAllowingStateLoss()
            }
        }
        if (args.enableClick(GuideInfo.TYPE_MAPS)) {
            binding.vMapsClick.setOnAntiViolenceClickListener {
                if (args.needGuideEvent(GuideInfo.TYPE_MAPS)) {
                    Analytics.track(
                        EventConstants.BUILD_NEWBIE_GUIDE_BUILD_MODE_CLICK
                    )
                }
                Analytics.track(
                    EventConstants.EVENT_CREATE_TAB_MAPS_CLICK
                )
                parentFragment?.let {
                    MetaRouter.MobileEditor.creation(
                        it,
                        source = if (args.needGuideEvent(GuideInfo.TYPE_MAPS)) {
                            GUIDE_SRC
                        } else {
                            null
                        }
                    )
                }
                onDismissCallback?.invoke(false)
                onDismissCallback = null
                dismissAllowingStateLoss()
            }
        }

        if (args.enableClick(GuideInfo.TYPE_CAR)) {
            binding.vCarClick.setOnAntiViolenceClickListener(233) {
                if (args.needGuideEvent(GuideInfo.TYPE_CAR)) {
                    Analytics.track(
                        EventConstants.BUILD_NEWBIE_GUIDE_BUILD_CAR_CLICK
                    )
                }
                Analytics.track(
                    EventConstants.EVENT_CREATE_TAB_CAR_CLICK
                )
                vm.getTemplateByType(GuideInfo.TYPE_CAR)
            }
        }

        if (args.enableClick(GuideInfo.TYPE_POSTS)) {
            binding.vPostsClick.setOnAntiViolenceClickListener {
                Analytics.track(
                    EventConstants.EVENT_CREATE_TAB_POSTS_CLICK
                )
                parentFragment?.let { f ->
                    MetaRouter.Post.goPublishPost(
                        f,
                        ResIdBean().setCategoryID(CategoryId.CATEGORY_ID_HOME_ADD_POSTS),
                        enableOutfitShare = true,
                    )
                }
                onDismissCallback?.invoke(false)
                onDismissCallback = null
                dismissAllowingStateLoss()
            }
        }

        if (args.enableClick(GuideInfo.TYPE_MODULES, GuideInfo.TYPE_MODULE_RECYCLE)) {
            binding.vModulesClick.setOnAntiViolenceClickListener {
                val needGuideEvent = args.needGuideEvent(GuideInfo.TYPE_MODULES)
                if (needGuideEvent) {
                    Analytics.track(
                        EventConstants.BUILD_NEWBIE_GUIDE_BUILD_MODULE_CLICK
                    )
                }
                if (args.needGuideEvent(GuideInfo.TYPE_MODULE_RECYCLE)) {
                    Analytics.track(
                        EventConstants.LOOP_GUIDE_MODULE_CLICK
                    )
                }
                Analytics.track(
                    EventConstants.MOD_ENTER_CLICK
                )
                parentFragment?.let { f ->
                    MetaRouter.UgcDesign.moduleTab(
                        f,
                        if (needGuideEvent) {
                            UgcModuleTabArgs.GUIDE_TYPE_NEWBIE
                        } else {
                            UgcModuleTabArgs.GUIDE_TYPE_NONE
                        }
                    )
                }
                onDismissCallback?.invoke(false)
                onDismissCallback = null
                dismissAllowingStateLoss()
            }
        }

        val anim = AnimationUtils.loadAnimation(requireContext(), R.anim.popup_in_bottom_top)
        anim.setListener(viewLifecycleOwner, onAnimationEnd {
            onAnimationEnd()
        })
        binding.clContainer.startAnimation(anim)

        vm.onAsync(MainAddState::template, onLoading = {
            loadingDialog?.dismissAllowingStateLoss()
            loadingDialog = MetaRouter.Dialog.loading(childFragmentManager)
        }, onFail = { _, _ ->
            loadingDialog?.dismissAllowingStateLoss()
            loadingDialog = null
        })
        vm.onAsync(MainAddState::template, deliveryMode = uniqueOnly()) {
            editorGameLaunchHelper?.startTemplateGame(
                this@MainAddDialog,
                it,
                ResIdBean().setCategoryID(CategoryId.MAIN_ADD_DIALOG)
            )
        }
        vm.registerAsyncErrorToast(MainAddState::template)
    }

    private fun onAnimationEnd() {
        if (!isBindingAvailable()) return
        if (!args.enableGuide) return
        viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            delay(123)
            if (!isBindingAvailable()) return@launchWhenResumed
            animColor(R.color.white, R.color.color_787878) {
                val color = it.animatedValue as Int
                binding.vBg.backgroundTintListByColor(color)
                binding.ivTri.imageTintListByColor(color)
            }
            binding.clGuideContainer.visible()
            binding.clGuideContainer.alpha = 0.0f
            binding.clGuideContainer.animate().alpha(1.0f).setDuration(350)
            when (args.guide?.type) {
                GuideInfo.TYPE_MAPS -> {
                    Analytics.track(
                        EventConstants.BUILD_NEWBIE_GUIDE_SKIP_SHOW
                    )
                    binding.vMapsClick.setBackgroundResource(R.drawable.bg_white_16)
                    binding.tvGuideContent.setText(R.string.guide_add_dialog_maps_desc)
                    binding.ivModules.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    binding.ivPosts.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    binding.ivCar.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    animColor(R.color.transparent, R.color.color_C78A8A8A) {
                        val color = it.animatedValue as Int
                        binding.ivModules.imageTintListByColor(color)
                        binding.ivPosts.imageTintListByColor(color)
                        binding.ivCar.imageTintListByColor(color)
                    }
                }

                GuideInfo.TYPE_POSTS -> {
                    Analytics.track(
                        EventConstants.BUILD_NEWBIE_GUIDE_POSTS_SKIP_SHOW
                    )
                    binding.vPostsClick.setBackgroundResource(R.drawable.bg_white_16)
                    binding.tvGuideContent.setText(R.string.guide_add_dialog_shoot_desc)
                    binding.ivMaps.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    binding.ivCar.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    binding.ivModules.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    animColor(R.color.transparent, R.color.color_C78A8A8A) {
                        val color = it.animatedValue as Int
                        binding.ivMaps.imageTintListByColor(color)
                        binding.ivCar.imageTintListByColor(color)
                        binding.ivModules.imageTintListByColor(color)
                    }
                }

//                GuideInfo.TYPE_SHOT -> {
//                    Analytics.track(
//                        EventConstants.BUILD_NEWBIE_GUIDE_SHOOT_SKIP_SHOW
//                    )
//                    binding.vPostsClick.setBackgroundResource(R.drawable.bg_white_16)
//                    binding.tvGuideContent.setText(R.string.guide_add_dialog_shoot_desc)
//                    binding.ivMaps.imageTintMode = PorterDuff.Mode.SRC_ATOP
//                    binding.ivCar.imageTintMode = PorterDuff.Mode.SRC_ATOP
//                    binding.ivModules.imageTintMode = PorterDuff.Mode.SRC_ATOP
//                    animColor(R.color.transparent, R.color.color_C78A8A8A) {
//                        val color = it.animatedValue as Int
//                        binding.ivMaps.imageTintListByColor(color)
//                        binding.ivCar.imageTintListByColor(color)
//                        binding.ivModules.imageTintListByColor(color)
//                    }
//                }

                GuideInfo.TYPE_CAR -> {
                    Analytics.track(
                        EventConstants.BUILD_NEWBIE_GUIDE_CAR_SKIP_SHOW
                    )
                    binding.vCarClick.setBackgroundResource(R.drawable.bg_white_16)
                    binding.tvGuideContent.setText(R.string.guide_add_dialog_shoot_desc)
                    binding.ivMaps.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    binding.ivModules.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    binding.ivPosts.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    animColor(R.color.transparent, R.color.color_C78A8A8A) {
                        val color = it.animatedValue as Int
                        binding.ivMaps.imageTintListByColor(color)
                        binding.ivPosts.imageTintListByColor(color)
                        binding.ivModules.imageTintListByColor(color)
                    }
                }

                GuideInfo.TYPE_MODULES -> {
                    Analytics.track(
                        EventConstants.BUILD_NEWBIE_GUIDE_MODULE_SKIP_SHOW
                    )
                    binding.vModulesClick.setBackgroundResource(R.drawable.bg_white_16)
                    binding.tvGuideContent.setText(R.string.guide_add_dialog_modules_desc)
                    binding.ivMaps.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    binding.ivPosts.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    binding.ivCar.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    animColor(R.color.transparent, R.color.color_C78A8A8A) {
                        val color = it.animatedValue as Int
                        binding.ivMaps.imageTintListByColor(color)
                        binding.ivPosts.imageTintListByColor(color)
                        binding.ivCar.imageTintListByColor(color)
                    }
                }

                GuideInfo.TYPE_MODULE_RECYCLE -> {
                    binding.tvExploreFirst.gone()
                    binding.vGuideBg.setMargin(bottom = dp(37))
                    binding.vModulesClick.setBackgroundResource(R.drawable.bg_white_16)
                    binding.tvGuideContent.setText(R.string.guide_add_dialog_modules_desc)
                    binding.ivMaps.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    binding.ivPosts.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    binding.ivCar.imageTintMode = PorterDuff.Mode.SRC_ATOP
                    animColor(R.color.transparent, R.color.color_C78A8A8A) {
                        val color = it.animatedValue as Int
                        binding.ivMaps.imageTintListByColor(color)
                        binding.ivPosts.imageTintListByColor(color)
                        binding.ivCar.imageTintListByColor(color)
                    }
                }
            }
            binding.tvExploreFirst.setOnAntiViolenceClickListener {
                Analytics.track(
                    EventConstants.BUILD_NEWBIE_GUIDE_SKIP_CLICK
                )
                onDismissCallback?.invoke(true)
                onDismissCallback = null
                dismissAllowingStateLoss()
            }
        }
    }

    private fun handleGoFullScreenRole(status: String) {
        Analytics.track(EventConstants.EVENT_GAME_AVATAR_LAUNCH) {
            put("from", "add")
            putAll(ResIdUtils.getAnalyticsMap(ResIdBean().setCategoryID(CategoryId.MAIN_ADD_DIALOG)))
        }
        MetaRouter.MobileEditor.fullScreenRole(
            requireActivity(),
            FullScreenEditorActivityArgs(
                status = status,
                categoryId = CategoryId.MAIN_ADD_DIALOG
            )
        )
    }

    private fun animColor(
        startRes: Int,
        endRes: Int,
        listener: ValueAnimator.AnimatorUpdateListener
    ) {
        ValueAnimator.ofObject(ArgbEvaluator(), getColorByRes(startRes), getColorByRes(endRes))
            .apply {
                duration = 350
                addUpdateListener(viewLifecycleOwner, listener = listener)
                start()
            }
    }

    override fun showLoadingUI() {
        loadingDialog?.dismissAllowingStateLoss()
        loadingDialog = null
        super.showLoadingUI()
        onDismissCallback?.invoke(false)
        onDismissCallback = null
        dismissAllowingStateLoss()
    }

    override fun hideLoadingUI(launchSuccess: Boolean, msg: String?, needGoMine: Boolean) {
        super.hideLoadingUI(launchSuccess, msg, needGoMine)
        if (launchSuccess) {
            onDismissCallback?.invoke(false)
            onDismissCallback = null
            dismissAllowingStateLoss()
        }
    }

    override fun dismissAllowingStateLoss() {
        if (isBindingAvailable()) {
            // 添加渐隐动画
            binding.clContainer.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction {
                    super.dismissAllowingStateLoss()
                    onDismissCallback?.invoke(true)
                    onDismissCallback = null
                }
                .start()
        } else {
            super.dismissAllowingStateLoss()
        }
        loadingDialog?.dismissAllowingStateLoss()
        loadingDialog = null
        onDismissCallback?.invoke(true)
        onDismissCallback = null
    }

    override fun onDestroyView() {
        binding.clContainer.animate().cancel() // 防止内存泄漏
        binding.clContainer.clearAnimation()
        super.onDestroyView()
    }

    //    // 背景淡出动画时长，默认250ms
//    override open fun backgroundFadeOutDuration(): Long = 300L
//
//    // 是否使用背景动画，默认为true
//    override open fun useBackgroundAnimation(): Boolean = true
    override fun dimAmount() = 0.3f
    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT
    override fun getStyle() = R.style.DialogStyleNonFullScreen
    override fun isCancelable() = !args.blockBack
    override fun isClickOutsideDismiss() = !args.blockBack
    override fun isBackPressedDismiss() = !args.blockBack
    override fun onBackPressed(): Boolean {
        return args.blockBack
    }

    override fun enableGoMine() = false
}