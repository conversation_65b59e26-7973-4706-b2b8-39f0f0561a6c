package com.socialplay.gpark.ui.main.maps

import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import androidx.compose.material.Colors
import androidx.compose.ui.graphics.Color
import androidx.core.graphics.toColorInt
import androidx.core.view.isInvisible
import androidx.fragment.app.Fragment
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceTabInfo
import com.socialplay.gpark.data.model.choice.TabTargetType
import com.socialplay.gpark.databinding.FragmentHomeMapsBinding
import com.socialplay.gpark.databinding.TabIndicatorHomeMapsBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.main.maps.newgame.NewGamesFragment
import com.socialplay.gpark.ui.recommend.RecommendFragment
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTouchSlop
import com.socialplay.gpark.util.extension.toColorIntSafe
import com.socialplay.gpark.util.extension.visible
import org.koin.androidx.viewmodel.ext.android.sharedViewModel

class HomeMapsFragment : BaseFragment<FragmentHomeMapsBinding>(R.layout.fragment_home_maps) {

    private val viewModel: HomeMapsViewModel by fragmentViewModel()
    private val mainViewModel: MainViewModel by sharedViewModel()
    private var tabLayoutMediator: TabLayoutMediator? = null
    private lateinit var pagerAdapter: HomeTabStateAdapter
    private val tabCallback = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, true)
        }

        override fun onTabUnselected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, false)
        }

        override fun onTabReselected(tab: TabLayout.Tab) {}
    }

    private val vpCallback = object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            viewModel.changeSelectedTag(position)
//            Timber.tag("Performance").d("HomeMapsFragment ViewPager2页面切换 - 位置: $position")
//        }
//
//        override fun onPageScrollStateChanged(state: Int) {
//            super.onPageScrollStateChanged(state)
//            val stateName = when (state) {
//                ViewPager2.SCROLL_STATE_IDLE -> "IDLE"
//                ViewPager2.SCROLL_STATE_DRAGGING -> "DRAGGING"
//                ViewPager2.SCROLL_STATE_SETTLING -> "SETTLING"
//                else -> "UNKNOWN"
//            }
//            Timber.tag("Performance").d("HomeMapsFragment ViewPager2滚动状态变化: $stateName")
        }
    }

    /**
     * 选中图片tab时, 应该隐藏 indicator
     */
    private val imgIndicatorColor by lazy { getColorByRes(R.color.transparent) }

    /**
     * 选中文本tab时, 应该显示 indicator
     */
    private val textIndicatorColor by lazy { getColorByRes(R.color.color_FFDE70) }

    private fun setTabSelectUI(tab: TabLayout.Tab, select: Boolean) {
        val cv = tab.customView ?: return
        val tabBinding = TabIndicatorHomeMapsBinding.bind(cv)
        val tag = tab.tag as? ChoiceTabInfo
        // 有图片配置的时候, 优先显示图片
        if (tag != null && tag.imgUrl.isNotEmpty()) {
            tabBinding.tvNormal.gone()
            tabBinding.tvSelected.gone()

            tabBinding.ivNormal.isInvisible = select
            tabBinding.ivSelected.isInvisible = !select
            if (select) {
                binding.tabLayout.setSelectedTabIndicatorColor(imgIndicatorColor)
            } else {
                binding.tabLayout.setSelectedTabIndicatorColor(textIndicatorColor)
            }
        } else {
            tabBinding.tvNormal.isInvisible = select
            tabBinding.tvSelected.isInvisible = !select

            tabBinding.ivNormal.gone()
            tabBinding.ivSelected.gone()
        }
        updateTabStyle()
    }

    private fun updateTabStyle() {
        val selectedTabPosition = binding.tabLayout.selectedTabPosition

        val selectedTab = binding.tabLayout.getTabAt(selectedTabPosition)
        val selectedTagType = selectedTab?.tag as? HomeMapsTab

        for (i in 0 until binding.tabLayout.tabCount) {
            val tab = binding.tabLayout.getTabAt(i)
            val tagType = tab?.tag as? HomeMapsTab
            val customView = tab?.customView

            if (customView != null && tagType != null) {
                val binding = TabIndicatorHomeMapsBinding.bind(customView)

                if (i != selectedTabPosition && selectedTagType?.otherTagTextColorWhenSelected != null) {
                    binding.tvNormal.setTextColor(selectedTagType.otherTagTextColorWhenSelected)
                } else {
                    binding.tvNormal.setTextColor(tagType.normalTextColor)
                }

                val textShadow = selectedTagType?.textShadowWhenSelected
                if (textShadow != null) {
                    binding.tvNormal.setShadowLayer(
                        textShadow.radius,
                        textShadow.dx,
                        textShadow.dy,
                        textShadow.color
                    )
                    binding.tvSelected.setShadowLayer(
                        textShadow.radius,
                        textShadow.dx,
                        textShadow.dy,
                        textShadow.color
                    )
                } else {
                    binding.tvNormal.setShadowLayer(0F, 0F, 0F, 0)
                    binding.tvSelected.setShadowLayer(0F, 0F, 0F, 0)
                }
            }
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentHomeMapsBinding? {
        return FragmentHomeMapsBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initViewPager()
        initData()
    }

    private fun initViewPager() {
        withState(viewModel) {

            val vc = ViewConfiguration.get(requireContext())
            binding.viewpagerTabs.setTouchSlop(vc.scaledPagingTouchSlop * 3)

            // 优化ViewPager2配置，减少内存占用和布局计算
            binding.viewpagerTabs.apply {
                offscreenPageLimit = 1
                // 减少滚动时的布局计算
                isNestedScrollingEnabled = false
            }

            // 等待viewpagerTabs布局加载完成后，给viewpagerTabs再设置一个固定高度，避免后续高度频繁变化？待定。担心平板设备旋转问题
            refreshViewPagerHeight()

            binding.tabLayout.addOnTabSelectedListener(tabCallback)
            binding.viewpagerTabs.registerOnPageChangeCallback(vpCallback)
            val homeTabs = viewModel.getHomeTabs()
            pagerAdapter = HomeTabStateAdapter(
                homeTabs,
                ::getFragmentByPosition,
                childFragmentManager,
                viewLifecycleOwner.lifecycle
            )

            binding.viewpagerTabs.adapter = pagerAdapter

            tabLayoutMediator = TabLayoutMediator(
                binding.tabLayout,
                binding.viewpagerTabs
            ) { tab: TabLayout.Tab, position: Int ->
                withState(viewModel) {
                    val tabBinding = TabIndicatorHomeMapsBinding.inflate(layoutInflater)
                    val tag = kotlin.runCatching { homeTabs[position] }.getOrNull() ?: return@withState
                    if (tag.imgUrl.isNotEmpty()) {
                        // 有图片的时候, 优先显示图片
                        glide?.apply {
                            load(tag.imgUrl)
                                .placeholder(R.drawable.placeholder_corner_4)
                                .fitCenter()
                                // 默认情况下, 图片比较糊, 需要手动指定一下图片大小, 如果指定的尺寸比实际的图片尺寸大, 并不会增大内存消耗
                                .override(tabBinding.ivNormal.maxWidth)
                                .into(tabBinding.ivNormal)
                            load(tag.imgUrl)
                                .placeholder(R.drawable.placeholder_corner_8)
                                .fitCenter()
                                // 默认情况下, 图片比较糊, 需要手动指定一下图片大小, 如果指定的尺寸比实际的图片尺寸大, 并不会增大内存消耗
                                .override(tabBinding.ivSelected.maxWidth)
                                .into(tabBinding.ivSelected)
                        }
                    } else {
                        tabBinding.tvNormal.text = tag.name
                        tabBinding.tvSelected.text = tag.name
                    }

                    // Use safe color parsing to prevent StringIndexOutOfBoundsException
                    tabBinding.tvSelected.setTextColor(tag.checkedColor.toColorIntSafe("#333333".toColorInt()))
                    tabBinding.tvNormal.setTextColor(tag.uncheckedColor.toColorIntSafe("#272829".toColorInt()))

                    tab.customView = tabBinding.root
                    tab.tag = tag
                }
            }
            tabLayoutMediator?.attach()
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
    }

    private fun refreshViewPagerHeight() {
        if (!isPad) {
            // 处理某些机型频繁变化列表高度问题（比如一加LE2100），pad理当不会，所以跳过处理，不然要再单独处理pad的横竖屏切换问题
            try {
                binding.viewpagerTabs?.post {
                    binding.viewpagerTabs.apply {
                        val mHeight = measuredHeight
                        layoutParams = layoutParams.apply {
                            width = ViewGroup.LayoutParams.MATCH_PARENT
                            height = mHeight
                        }
                    }
                }
            } catch (e: Exception) {

            }
        }
    }


    private fun getFragmentByPosition(tabInfo: ChoiceTabInfo): Fragment {
        return HomeTabContentFragment.newInstance(tabInfo)
    }

//    private fun createTabFragment(type: HomeMapsTab): Fragment {
//        return when (type) {
//            is HomeMapsTab.MapsTab -> {
//                // 地图
//                RecommendFragment()
//            }
//
//            is HomeMapsTab.NewestTab -> {
//                // 新游
//                NewGamesFragment()
//            }
//        }
//    }

    private fun initData() {

        viewModel.onEach(HomeMapsModelState::tabs, deliveryMode = uniqueOnly()) {
//            val tabs = it.map { type ->
//                { createTabFragment(type) }
//            }
//            pagerAdapter.fragmentCreators = tabs
//            pagerAdapter.notifyDataSetChanged()

            if (it.size == 1) {
                // 藏起来indicator
                binding.tabLayout.setSelectedTabIndicator(null)
            }
        }
        viewModel.registerToast(HomeMapsModelState::toastData)
        viewModel.onEach(HomeMapsModelState::selectedTag) {
            viewModel.oldState.tabs.getOrNull(it)?.let { type ->
                binding.viewpagerTabs.isUserInputEnabled = type.isNestedScrollEnabled
            }

            viewModel.getHomeTabs().getOrNull(it)?.let { tab->
                Analytics.track(EventConstants.EVENT_HOMEPAGE_MENU_CHOOSE) {
                    if (tab.target != TabTargetType.RECOMMEND.name
                        && tab.target != TabTargetType.NEWEST.name
                    ) {
                        put("menu_id", tab.id)
                    }
                    put("menu_name", tab.name.orEmpty())
                }
            }

            if (binding.viewpagerTabs.currentItem != it && it in 0 until pagerAdapter.itemCount) {
                binding.viewpagerTabs.setCurrentItem(it, false)
            }
        }
        mainViewModel.msgUnReadCountFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
            binding.vMsgRedDot.visible(it > 0)
        }
    }

    private fun initView() {

        binding.ivSearch.setOnAntiViolenceClickListener {
            MetaRouter.Search.navigate(this)
        }
        binding.ivSearch.visible(PandoraToggle.isSearchOpen)

        binding.ivMsg.setOnAntiViolenceClickListener {
            MetaRouter.IM.goChatTabFragment(
                this,
                source = EventParamConstants.SRC_MESSAGE_LIST_ENTRANCE_MAPS
            )
        }
    }

    override fun invalidate() {

    }

    override fun onDestroyView() {
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        binding.tabLayout.clearOnTabSelectedListeners()
        // 移除adapter引用，避免内存泄漏
        binding.viewpagerTabs.adapterAllowStateLoss = null
        binding.viewpagerTabs.unregisterOnPageChangeCallback(vpCallback)
        super.onDestroyView()
    }

    override fun getPageName(): String {
        return PageNameConstants.FRAGMENT_MAPS
    }
}