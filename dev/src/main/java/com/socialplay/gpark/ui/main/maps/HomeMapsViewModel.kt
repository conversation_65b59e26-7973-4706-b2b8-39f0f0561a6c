package com.socialplay.gpark.ui.main.maps

import com.airbnb.mvrx.MavericksState
import com.socialplay.gpark.data.model.post.TextShadow
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.util.getStringByGlobal
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.TabConfigInteractor
import com.socialplay.gpark.data.model.choice.ChoiceTabInfo
import com.socialplay.gpark.data.model.choice.HomeTabType
import com.socialplay.gpark.data.model.choice.TabTargetType
import com.socialplay.gpark.data.model.choice.checkIdAndModifyIfNeeded
import com.socialplay.gpark.data.model.post.CommunityBlockType
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.util.ToastData
import org.koin.core.context.GlobalContext

data class HomeMapsModelState(
    val isLoading: Boolean = false,
    val tabs: List<HomeMapsTab> = if (PandoraToggle.isShowNewGames) listOf(
        HomeMapsTab.MapsTab(),
        HomeMapsTab.NewestTab(),
    ) else listOf(
        HomeMapsTab.MapsTab(),
    ),
    val selectedTag: Int = 0,
    val toastData: ToastData = ToastData.EMPTY,
) : MavericksState {
    val blockNameList = tabs.map { it.name }
}

sealed class HomeMapsTab(
    val name: String,
    val normalTextColor: Int = 0xFF999999.toInt(),
    val selectedTextColor: Int = 0xFF333333.toInt(),
    val tabIndicatorColor: Int = 0xFF333333.toInt(),
    val otherTagTextColorWhenSelected: Int? = null,// 选中此Item时，修改除当前选中Tab以外的所有Tab的TextColor
    val textShadowWhenSelected: TextShadow? = null,// 选中此Item时，修改所有的tab的阴影参数，包含当前tab
    val isNestedScrollEnabled: Boolean = true
) {

    class MapsTab() : HomeMapsTab(getStringByGlobal(R.string.home_maps_tab_title)) {
    }

    class NewestTab() : HomeMapsTab(getStringByGlobal(R.string.home_new_games_tab_title)) {
    }
}

class HomeMapsViewModel(
    initialState: HomeMapsModelState
) : BaseViewModel<HomeMapsModelState>(initialState) {
    var tabList: List<ChoiceTabInfo> = ArrayList()
    private val tabConfigTabInteractor by lazy { GlobalContext.get().get<TabConfigInteractor>() }

    fun changeSelectedTag(toTag: CommunityBlockType) {
        withState {
            val toIndex = it.tabs.indexOfFirst { eachTag ->
                eachTag == toTag
            }
            if (it.selectedTag == toIndex || toIndex < 0) {
                return@withState
            }
            setState {
                copy(selectedTag = toIndex)
            }
        }
    }

    fun changeSelectedTag(tagIndex: Int) {
        withState {
            if (it.selectedTag == tagIndex || tagIndex !in tabList.indices) {
                return@withState
            }
            setState {
                copy(selectedTag = tagIndex)
            }
        }
    }

    fun getHomeTabs(): List<ChoiceTabInfo> {
        if (tabList.isNotEmpty()) {
            return tabList
        }
        val resultTabList: MutableList<ChoiceTabInfo> = ArrayList()
        resultTabList.addAll(createLocalTabs())
        tabConfigTabInteractor.getHomeConfigTab()?.apply {
            resultTabList.addAll(this)
        }
        tabList = resultTabList.checkIdAndModifyIfNeeded()
        return tabList
    }

    private fun createLocalTabs(): MutableList<ChoiceTabInfo> {
        return mutableListOf<ChoiceTabInfo>(
            ChoiceTabInfo(
                id = -1,
                name = getStringByGlobal(R.string.home_maps_tab_title),
                type = HomeTabType.NATIVE.name,
                target = TabTargetType.RECOMMEND.name,
            ),
            ChoiceTabInfo(
                id = -2,
                name = getStringByGlobal(R.string.home_new_games_tab_title),
                type = HomeTabType.NATIVE.name,
                target = TabTargetType.NEWEST.name,
            ),
        )
    }

//    fun getSelectedPosition(): Int {
//        val needJumpTsTab = PandoraToggle.isOpenHomeJumpTsTab && ChannelUtil.isTsGameChannel() && !metaKV.tsChannelKV.isTsChannelJump
//        return if (needJumpTsTab) {
//            metaKV.tsChannelKV.isTsChannelJump = true
//            metaKV.tsChannelKV.timeFirstShowTsSuperGame = System.currentTimeMillis()
//            return tabList.indexOfFirst { it.type == HomeTabType.NATIVE.name && it.target == TabTargetType.TS_ZONE.name }
//        } else {
//            -1
//        }
//    }

}