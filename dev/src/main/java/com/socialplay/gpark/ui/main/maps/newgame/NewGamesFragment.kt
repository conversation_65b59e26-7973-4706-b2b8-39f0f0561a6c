package com.socialplay.gpark.ui.main.maps.newgame

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.MapsNewestList
import com.socialplay.gpark.databinding.FragmentNewGamesBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.maverick.mapRetained
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.post.feed.tag.TagCommunityFeedFragmentArgs
import com.socialplay.gpark.ui.recommend.RecommendFragment
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.PerformanceMonitor
import com.socialplay.gpark.util.extension.attachV2
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

class NewGamesFragment : BaseFragment<FragmentNewGamesBinding>(R.layout.fragment_new_games) {
    companion object {
        fun newInstance(args: TagCommunityFeedFragmentArgs): RecommendFragment {
            return RecommendFragment().apply {
                arguments = args.asMavericksArgs()
            }
        }
    }
    private val args by args<TagCommunityFeedFragmentArgs>()

    private val viewModel: NewGamesViewModel by fragmentViewModel()
    private val newestController by lazy { buildNewestController() }
    private var isLoadFinishTrack = false
    private var isLoadSuccess = false

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentNewGamesBinding? {
        return FragmentNewGamesBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.recyclerView.itemAnimator = null
        initView()
        initRv()
        initData()
    }

    private fun initView() {
        PerformanceMonitor.monitorRecyclerViewDetailed(binding.recyclerView)
        binding.recyclerView.layoutManager = object : GridLayoutManager(requireContext(), 3, VERTICAL, false) {
            override fun onLayoutChildren(recycler: RecyclerView.Recycler?, state: RecyclerView.State?) {
                kotlin.runCatching {
                    super.onLayoutChildren(recycler, state)
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (isLoadSuccess) {
            Analytics.track(EventConstants.EVENT_HOMEPAGE_MENU_SHOW) {
                put("menu_name", args.blockName)
            }
        }
    }

    override fun onPause() {
        super.onPause()
    }

    private fun initRv() {
        viewModel.setupRefreshLoading(
            NewGamesModelState::refresh,
            binding.loadingView,
            binding.refreshLayout
        ) {
            if (NetUtil.isNetworkAvailable()) {
                viewModel.refresh()
            } else {
                binding.refreshLayout.isRefreshing = false
                binding.loadingView.showError()
            }
        }
        EpoxyVisibilityTracker().apply {
            partialImpressionThresholdPercentage = 100
            attachV2(viewLifecycleOwner, binding.recyclerView)
        }
        binding.recyclerView.setController(newestController)
        binding.recyclerView.setDelayMsWhenRemovingAdapterOnDetach(1)
    }

    private fun initData() {
        viewModel.onAsync(
            NewGamesModelState::refresh,
            onSuccess = { list->
                isLoadSuccess = list.isNotEmpty()
                if (isLoadSuccess && !isLoadFinishTrack && isResumed) {
                    isLoadFinishTrack = true
                    Analytics.track(EventConstants.EVENT_HOMEPAGE_MENU_SHOW) {
                        put("menu_name", args.blockName)
                    }
                }
            }
        )
    }


    private fun buildNewestController() = simpleController(
        viewModel,
        NewGamesModelState::refresh,
        NewGamesModelState::loadMore,
    ) { refresh, loadMore ->
//        addHeader(playedCount, operationList)

//        Timber.tag("Performance").d("buildNewestController start")
        val list = refresh.invoke() ?: emptyList()

        addItem(list)
        addFooter(refresh, list, loadMore)
    }

    private fun MetaEpoxyController.addItem(list: List<MapsNewestList>) {

        list.forEachIndexed { index, item ->

            val resIdBean =
                ResIdBean().setGameId(item.ugid).setCategoryID(CategoryId.HOME_NEW_GAMES)
                    .setParam1(index).setParam2(if (item.isStrongInsertionGame == true) 1 else 0)
                    .setParamExtra(item.ugcGameName)
                    .setReqId(item.orderNum.toString())
                    .setIconType("UGC")
                    .setTsType(ResIdBean.TS_TYPE_UCG)

            addGameItem(index, item, {
                // onItemShow
                lifecycleScope.launch(Dispatchers.Default) {
                    Analytics.track(EventConstants.EVENT_ITEM_SHOW) {
                        put("gameid", item.ugid)
                        put("packagename", item.packageName ?: "")
                        putAll(
                            ResIdUtils.getAnalyticsMap(resIdBean)
                        )
                    }
                }
            }, {
                Analytics.track(EventConstants.EVENT_ITEM_CLICK) {
                    put("gameid", item.ugid)
                    put("packagename", item.packageName ?: "")
                    putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                }
                // onItemClick
                MetaRouter.MobileEditor.ugcDetail(this@NewGamesFragment, item.ugid, null, resIdBean)
            }, ::glide)
        }
    }

    private fun MetaEpoxyController.addFooter(
        refresh: Async<List<MapsNewestList>>,
        list: List<MapsNewestList>,
        loadMore: Async<LoadMoreState>
    ) {
        if (list.isNotEmpty()) {
            loadMoreFooter(loadMore, spanSize = 3) {
                if (!binding.refreshLayout.isRefreshing) {
                    viewModel.loadMore()
                }
            }
        } else if (refresh is Fail && loadMore.shouldLoad) {
            loadMoreFooter(refresh.mapRetained(loadMore), spanSize = 3) {
                viewModel.refresh()
            }
        } else if (refresh is Loading) {
            loadMoreFooter(refresh.mapRetained(loadMore), spanSize = 3) {

            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.apiMonitor(
            this,
            NewGamesModelState::refresh
        )
        newestController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        newestController.onSaveInstanceState(outState)
    }

    override fun invalidate() {

    }

    override fun getPageName(): String {
        return PageNameConstants.FRAGMENT_MAPS_TAB_NEWEST
    }

    override fun onDestroyView() {
        super.onDestroyView()
    }
}
