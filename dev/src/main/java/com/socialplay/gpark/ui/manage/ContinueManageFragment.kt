package com.socialplay.gpark.ui.manage

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.meta.pandora.Pandora
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.MyPlayedGame
import com.socialplay.gpark.databinding.FragmentContinueManageBinding
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ConfirmDialog

/**
 * 2024/7/30
 */
class ContinueManageFragment :
    BaseRecyclerViewFragment<FragmentContinueManageBinding>(R.layout.fragment_continue_manage) {


    private val continueManageViewModel: ContinueManageViewModel by fragmentViewModel()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentContinueManageBinding? {
        return FragmentContinueManageBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Pandora.send(EventConstants.EVENT_PARTY_MANAGEPAGE_SHOW)
        binding.titleBar.setTitle(getString(R.string.continue_gamedelete_partypagemanager))
        binding.titleBar.setDividerVisibility(false)
        binding.titleBar.setOnBackAntiViolenceClickedListener {
            findNavController().popBackStack()
        }

        continueManageViewModel.refreshGameList()

        continueManageViewModel.setupRefreshLoading(
            ContinueManageUiState::list,
            binding.loading,
            binding.refresh,
            getString(R.string.no_data),
        ) {
            withState(continueManageViewModel) {
                continueManageViewModel.refreshGameList()
            }
        }
    }

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvList

    override fun epoxyController(): EpoxyController = simpleController(
        continueManageViewModel,
        ContinueManageUiState::list,
        ContinueManageUiState::loadMore
    ) { list, loadMore ->
        val games = list.invoke() ?: listOf()
        games.onEachIndexed { index, game ->
            MyPlayGameFeed(::glide, game) {
                showDeleteDialog(game)
            }.id("${game.id}-$index").addTo(this)
        }
        // 加载更多
        if (games.isNotEmpty()) {
            loadMoreFooter(
                loadMore,
                showEnd = true,
                endText = getString(R.string.brvah_load_end),
            ) {
                continueManageViewModel.loadMoreGameList()
            }
        }
    }

    private fun showDeleteDialog(game: MyPlayedGame) {
        ConfirmDialog.Builder(this)
            .content(getString(R.string.continue_gamedelete_1))
            .cancelBtnTxt(getString(R.string.continue_gamedelete_cancel))
            .confirmBtnTxt(getString(R.string.continue_gamedelete_ok))
            .isRed(true)
            .confirmCallback {
                continueManageViewModel.deleteGame(game)
            }
            .show()
    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_CONTINUE_MANAGE
}