package com.socialplay.gpark.ui.mgs.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.TextWatcher
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.widget.AppCompatEditText
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.groupchat.GroupChatMemberInfo
import com.socialplay.gpark.databinding.DialogCustomInputBinding
import com.socialplay.gpark.databinding.DialogCustomInputHorBinding
import com.socialplay.gpark.ui.dialog.GameCreateDialogHelper
import com.socialplay.gpark.ui.im.conversation.MentionSpan
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/07/24
 *     desc   :
 *
 */
class CustomInputDialog (
    val activity: Activity,
    private val metaApp: Context,
    private val top: Float,
    private val left: Float,
    private val right: Float,
) : Dialog(activity, android.R.style.Theme_Dialog) {
    companion object {
        const val INPUT_DIALOG_SHOW = -233123
        const val SHOW_DIALOG_DELAY_TIME = 200L
    }

    private var binding: DialogCustomInputBinding? = null
    private var horbinding: DialogCustomInputHorBinding? = null
    private lateinit var etMgsMessage: AppCompatEditText
    private lateinit var tvSendMessage: View

    private val delayHandler: Handler by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        Handler(Looper.getMainLooper()) {
            when (it.what) {
                INPUT_DIALOG_SHOW -> {
                    focusableInputDialog()
                }
            }
            return@Handler true
        }
    }
    private var onInputListener: OnInputListener? = null
    private var atSpanInfo:MentionSpan? = null
    private var isInitText = false

    private val textWatcher = object : TextWatcher {
        override fun beforeTextChanged(
            s: CharSequence?,
            start: Int,
            count: Int,
            after: Int
        ) {
            if (isInitText) {
                return
            }
            val spanInfo = atSpanInfo
            if (count > 0 && spanInfo != null) {
                if (spanInfo.end <= start) {
                    //删除点在span的后面, 不影响
                } else if (spanInfo.start >= start + count) {
                    //删除点在span的前面， 只影响后面的start,end
                    spanInfo.start -= count
                    spanInfo.end -= count
                } else {
                    //删除点在span的中间
                    etMgsMessage.text?.removeSpan(spanInfo)
                    atSpanInfo = null
                }
            }
        }

        override fun onTextChanged(
            s: CharSequence?,
            start: Int,
            before: Int,
            count: Int
        ) {
            if (isInitText) {
                return
            }
            val spanInfo = atSpanInfo
            if (count > 0 && spanInfo != null) {
                if (spanInfo.start < start && spanInfo.end > start) {
                    // 插入点在span的中间
                    etMgsMessage.text?.removeSpan(spanInfo)
                    atSpanInfo = null
                } else if (spanInfo.start >= start) {
                    // 插入点在span的前面
                    spanInfo.start += count
                    spanInfo.end += count
                } else {
                    // 插入点在span的后面, 不影响
                }
            }
        }

        override fun afterTextChanged(editable: Editable?) {
            if (isInitText) {
                return
            }
            val spanInfo = atSpanInfo
            if (editable != null && spanInfo != null) {
                val spans = editable.getSpans(
                    0,
                    editable.length,
                    MentionSpan::class.java
                )
                if (spans.isNullOrEmpty()) {
                    atSpanInfo = null
                } else {
                    val spanSet = spans.map { span -> span.key }.toSet()
                    if (!spanSet.contains(spanInfo.key)) {
                        etMgsMessage.text?.removeSpan(spanInfo)
                        atSpanInfo = null
                    }
                }
            }

            changeSendStatus()
            onInputListener?.onTextChange(etMgsMessage.text.toString())
        }
    }

    init {
        initView()
        initKeyboardListener()
        Timber.d("showActivity %s ", activity)
    }

    private fun initView() {
        if (window == null) {
            dismiss()
            return
        }
        initDialogParams()
    }



    @SuppressLint("ClickableViewAccessibility")
    private fun initDialogParams() {
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        if (ScreenUtil.isHorizontalScreen(activity)) {
            horbinding = DialogCustomInputHorBinding.inflate(LayoutInflater.from(metaApp))
            GameCreateDialogHelper.inputInflated(
                activity,
                metaApp,
                this,
                horbinding!!.root,
                width = WindowManager.LayoutParams.MATCH_PARENT,
                gravity = Gravity.BOTTOM,
                dimAmount = 0.2f,
                WindowManager.LayoutParams.MATCH_PARENT
            )
            tvSendMessage = horbinding!!.tvSend
            etMgsMessage = horbinding!!.etInputMessage
            initEvent(horbinding!!.viewRoot, horbinding!!.tvSend, horbinding!!.etInputMessage)
            horbinding?.imgCancel?.setOnClickListener {
                hideInputKeyBoard()
            }
        } else {
            binding = DialogCustomInputBinding.inflate(LayoutInflater.from(metaApp))
            GameCreateDialogHelper.inputInflated(
                activity,
                metaApp,
                this,
                binding!!.root,
                width = WindowManager.LayoutParams.MATCH_PARENT,
                gravity = Gravity.BOTTOM,
                dimAmount = 0.2f,
                WindowManager.LayoutParams.MATCH_PARENT
            )
            tvSendMessage = binding!!.tvSend
            etMgsMessage = binding!!.etInputMessage
            initEvent(binding!!.viewRoot, binding!!.tvSend, binding!!.etInputMessage)
        }
    }
    private fun setAtReplayText(atMemberInfo: GroupChatMemberInfo){
        val highlight = "@${atMemberInfo.nickname}"
        atSpanInfo = MentionSpan(
            color = activity.getColorByRes(R.color.color_4AB4FF),
            memberInfo = atMemberInfo
        ).apply {
            start = 0
            end = highlight.length
        }
        val builder = SpannableStringBuilder("$highlight ")
        builder.setSpan(
            atSpanInfo,
            0,
            highlight.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        isInitText = true
        etMgsMessage.post {
            etMgsMessage.setText(builder)
            etMgsMessage.setSelection(builder.length)
            changeSendStatus()
            isInitText = false
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initEvent(
        root:View,
        tvSendMessage: View,
        etMgsMessage: EditText
    ) {
        root.setOnTouchListener { v, event ->
            when (event.action) {

                MotionEvent.ACTION_DOWN    -> {
                    if (event.rawX > left && event.rawX < right && event.rawY < top) {
                        //当前弹窗位置不处理
                    } else {
                        dismiss()
                    }
                }
            }
            true
        }
        etMgsMessage.addTextChangedListener(textWatcher)
        etMgsMessage.setOnEditorActionListener(object : TextView.OnEditorActionListener {
            override fun onEditorAction(v: TextView?, actionId: Int, event: KeyEvent?): Boolean {
                if (actionId == EditorInfo.IME_ACTION_SEND) {
                    InputUtil.hideKeyboard(etMgsMessage)
                    sendMessage(etMgsMessage)
                    return true
                }
                return false
            }
        })
        tvSendMessage.setOnAntiViolenceClickListener {
            sendMessage(etMgsMessage)
        }

        etMgsMessage.setOnTouchListener { view, motionEvent ->
            when (motionEvent.action) {
                MotionEvent.ACTION_DOWN -> {
                    InputUtil.hideKeyboard(etMgsMessage)
                }
            }
            false
        }


    }
    private fun changeSendStatus() {
        if (etMgsMessage.text.toString().isNullOrEmpty()) {
            tvSendMessage.isEnabled = false
            tvSendMessage.alpha = 0.5f
        } else {
            tvSendMessage.isEnabled = true
            tvSendMessage.alpha = 1f
        }
    }

    override fun setOnDismissListener(listener: DialogInterface.OnDismissListener?) {
        super.setOnDismissListener(listener)
    }

    override fun dismiss() {
        etMgsMessage.removeTextChangedListener(textWatcher)
        onInputListener?.onInputDialogDismiss()
        delayHandler.removeCallbacksAndMessages(null)
        this.onInputListener = null
        super.dismiss()
    }

    override fun show() {
        super.show()
    }


    /**
     * 隐藏软件盘
     */
    private fun hideInputKeyBoard() {
        InputUtil.hideKeyboard(etMgsMessage)
        etMgsMessage.clearFocus()
    }



    private fun focusableInputDialog() {
        Timber.d("focusableInputDialog %s", etMgsMessage)
        GlobalScope.launch(Dispatchers.Main) {
            etMgsMessage.requestFocus()
            etMgsMessage.isFocusable = true
            etMgsMessage.isFocusableInTouchMode = true
            InputUtil.showSoftBoard(etMgsMessage)
        }
    }

    private fun initKeyboardListener() {

    }




    /**
     * 发送消息
     */
    private fun sendMessage(etMessage: EditText): Boolean {
        return if (etMessage.text.toString().isBlank()) {
            false
        } else {
            onInputListener?.sendMessage(etMessage.text)
            etMessage.setText("")
            dismiss()
            true
        }
    }


    fun sendEmptyMessageDelayed(str: SpannableString, onInputListener : OnInputListener) {
        Timber.d("sendEmptyMessageDelayed")
        delayHandler.sendEmptyMessageDelayed(
            INPUT_DIALOG_SHOW,
            SHOW_DIALOG_DELAY_TIME
        )
        etMgsMessage.hint = ""
        etMgsMessage.setText(str)
        etMgsMessage.setSelection(str.length)
        changeSendStatus()
        this.onInputListener = onInputListener
    }

    fun sendAtMessageDelayed(
        atMemberInfo: GroupChatMemberInfo? = null,
        onInputListener: OnInputListener
    ) {
        Timber.d("sendAtMessageDelayed")
        if (atMemberInfo == null) {
            sendEmptyMessageDelayed(SpannableString(""), onInputListener)
        } else {
            delayHandler.sendEmptyMessageDelayed(
                INPUT_DIALOG_SHOW,
                SHOW_DIALOG_DELAY_TIME
            )
            setAtReplayText(atMemberInfo)
            this.onInputListener = onInputListener
        }
    }
}
interface OnInputListener {
    fun sendMessage(str: Spannable)
    fun onTextChange(str: String)
    fun onInputDialogDismiss()
}