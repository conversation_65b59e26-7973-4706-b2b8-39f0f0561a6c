package com.socialplay.gpark.ui.outfit

import android.animation.ValueAnimator
import android.graphics.Rect
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.activity.addCallback
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.os.bundleOf
import androidx.core.view.ViewCompat
import androidx.core.view.doOnNextLayout
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.core.view.postDelayed
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.ApiResultCodeException
import com.socialplay.gpark.data.model.asset.AssetEditResultBatchEvent
import com.socialplay.gpark.data.model.asset.AssetEditResultEvent
import com.socialplay.gpark.data.model.outfit.UgcAssetMutable
import com.socialplay.gpark.data.model.outfit.UgcDesignDetail
import com.socialplay.gpark.data.model.outfit.UgcDesignFeed
import com.socialplay.gpark.data.model.post.AddPostCommentReplyTarget
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.data.model.share.ShareFeature
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.databinding.FragmentUgcDesignDetailBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.editor.RoleGameTryOn
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.IGlobalShareCallback
import com.socialplay.gpark.ui.account.AccPwdV7Dialog
import com.socialplay.gpark.ui.account.AccPwdV7DialogArgs
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.views.empty
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.dialog.LoadingDialogFragment
import com.socialplay.gpark.ui.dialog.TipDialog
import com.socialplay.gpark.ui.editor.detail.comment.CommentMorePopup
import com.socialplay.gpark.ui.editor.detail.comment.IUgcCommentListener
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentExpandItem
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentItem
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentLoading
import com.socialplay.gpark.ui.editor.detail.comment.ugcReplyItem
import com.socialplay.gpark.ui.editor.home.datalist.TSGameLaunchHelper
import com.socialplay.gpark.ui.editor.module.guide.UgcModuleGuideDialog
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.gamepay.PayDialogStyle
import com.socialplay.gpark.ui.gamepay.PayResult
import com.socialplay.gpark.ui.gamepay.PayScene
import com.socialplay.gpark.ui.imgpre.ImgPreDialogFragment
import com.socialplay.gpark.ui.mgs.danmu.advanced.PAGE_ANIMATION_DURATION
import com.socialplay.gpark.ui.imgpre.v2.OpenPreviewBuilder
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialog
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialogParams
import com.socialplay.gpark.ui.profile.BaseProfilePage
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialog
import com.socialplay.gpark.ui.reportBlock.ReportSuccessDialogAnalyticsParams
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.view.ExpandableTextView
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.util.DateUtil.formatWholeDate
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.addModelBuildListener
import com.socialplay.gpark.util.extension.addOnOffsetChangedListener
import com.socialplay.gpark.util.extension.addUpdateListener
import com.socialplay.gpark.util.extension.cancelAnimationIfAnimating
import com.socialplay.gpark.util.extension.doOnLayoutChanged
import com.socialplay.gpark.util.extension.doOnLayoutSized
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.getDimensionPx
import com.socialplay.gpark.util.extension.getDrawableByRes
import com.socialplay.gpark.util.extension.getLocationInWindow
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.goneIfValueEmpty
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.navColor
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setFragmentResultByHostFragment
import com.socialplay.gpark.util.extension.popBackStack
import com.socialplay.gpark.util.extension.setFragmentResultListener
import com.socialplay.gpark.util.extension.setFragmentResultListenerByHostFragment
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.toastLong
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import kotlinx.parcelize.Parcelize
import kotlin.math.abs
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/04
 *     desc   :
 * </pre>
 */
@Parcelize
data class UgcDesignDetailArgs(
    val itemId: String,
    val from: Int,
    val tabId: Int,
    val targetCommentId: String?,
    val targetReplyId: String?,
    val categoryId: Int,
    val needResult: Boolean
) : Parcelable

class UgcDesignDetailFragment : BaseFragment<FragmentUgcDesignDetailBinding>(R.layout.fragment_ugc_design_detail),
    IGlobalShareCallback {

    companion object {
        const val TAG = "UgcDesignDetailFragment"

        const val KEY_ITEM_ID = "itemId"
        const val KEY_LIKE_COUNT = "likeCount"
        const val KEY_IS_LIKE = "isLike"
        const val KEY_DELETED = "deleted"
        const val KEY_PUBLISHED = "published"
        const val KEY_PRICE = "price"
        const val KEY_GOT = "got"
        const val KEY_NAME = "name"

        const val TRACK_TAG = "ugc_design_detail"
        const val FEAT_DELETE = 1
        const val FEAT_ONLINE = 2
        const val FEAT_OFFLINE = 3
        const val FEAT_EDIT = 4
        const val FEAT_PIN = 5
        const val FEAT_UNPIN = 6
        const val FEAT_PUBLIC = 7
        const val FEAT_PRIVATE = 8

        fun getResult(bundle: Bundle): AssetEditResultEvent? {
            val itemId = bundle.getString(KEY_ITEM_ID) ?: return null
            val likeCount = bundle.getLong(KEY_LIKE_COUNT)
            val isLike = bundle.getBoolean(KEY_IS_LIKE)
            val deleted = bundle.getBoolean(KEY_DELETED)
            val published = bundle.getBoolean(KEY_PUBLISHED)
            val price = bundle.getLong(KEY_PRICE)
            val got = bundle.getBoolean(KEY_GOT)
            val name = bundle.getString(KEY_NAME)
            return AssetEditResultEvent(
                itemId,
                likeCount,
                isLike,
                deleted,
                published,
                price,
                got,
                name
            )
        }
    }

    private val vm: UgcDesignDetailViewModel by fragmentViewModel()
    private val buyViewModel: BuyUgcDesignViewModel by fragmentViewModel()
    private val args: UgcDesignDetailArgs by args()

    private val commentController by lazy { buildCommentController() }

    private val itemListener = UgcCommentListener()

    private var likeAnim = false
    private var contentHeight = 0
    private var descExpandState = ExpandableTextView.STATE_SHRINK

    private var scrollToTop = false
    private var needLocate = false
    private var locatePosition = 0
    private var shouldPlayLikeAnim = false

    private val commentMorePopup: CommentMorePopup by lazy { CommentMorePopup(layoutInflater) }

    private var loadingDialog: LoadingDialogFragment? = null

    private var tsGameLaunchHelper: TSGameLaunchHelper? = null

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentUgcDesignDetailBinding? {
        return FragmentUgcDesignDetailBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        view.postDelayed(PAGE_ANIMATION_DURATION) {
            init()
        }
    }

    fun init() {
        tsGameLaunchHelper = TSGameLaunchHelper(this)

        if (contentHeight != 0) {
            binding.clScrollable.minHeight = contentHeight
        }

        likeAnim = false

        binding.lavLikeAnim.setMinAndMaxProgress(0.0f, 1.0f)

        setFragmentResultListenerByHostFragment(
            BaseProfilePage.RESULT_FOLLOW, viewLifecycleOwner
        ) { _, bundle ->
            val uuid = bundle.getString(BaseProfilePage.KEY_UUID)
            if (vm.detail?.uuid == uuid) {
                vm.follow(bundle.getBoolean(BaseProfilePage.KEY_IS_FOLLOW))
            }
        }
        setFragmentResultListener(UgcDesignEditFragment.KEY, viewLifecycleOwner) { _, bundle ->
            val modifiable =
                bundle.getParcelable<UgcAssetMutable>(UgcDesignEditFragment.KEY_MODIFIABLE)
                    ?: return@setFragmentResultListener
            vm.edit(modifiable)
        }

        binding.lv.showLoading()
        binding.lv.setRetry {
            vm.initDetail()
        }
        binding.tbl.setOnBackAntiViolenceClickedListener {
            back()
        }
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
            back()
        }
        binding.vUserClick.setOnAntiViolenceClickListener {
            val authorId = vm.authorId
            if (!authorId.isNullOrEmpty()) {
                MetaRouter.Profile.other(this, authorId, "ugc_design_detail", checkFollow = true)
            }
        }
        binding.tvFollowBtn.setOnAntiViolenceClickListener {
            vm.follow()
        }
//        binding.vEditRedDot.visible(vm.accountInteractor.assetFirstEditRedDot)
//        binding.ivEditBtn.setOnAntiViolenceClickListener {
//            val s = vm.oldState
//            val detail = s.detail() ?: return@setOnAntiViolenceClickListener
//            Analytics.track(
//                EventConstants.LIBRARY_METARIAL_EDIT_CLICK
//            )
//            vm.accountInteractor.assetFirstEditRedDot = false
//            MetaRouter.UgcDesign.edit(
//                this,
//                s.itemId,
//                detail.cover,
//                s.mutable ?: detail.toMutable(),
//                detail.trackId,
//                UgcDesignEditFragment.FROM_ASSET_DETAIL
//            )
//        }


        binding.ivShareBtn.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.ASSET_MANAGEMENT_SOURCE_CLICK
            )
            vm.accountInteractor.assetFirstEditRedDot = false
            vm.detail?.let {
                GlobalShareDialog.show(
                    childFragmentManager,
                    ShareRawData.ugcDesignDetail(it),
                    features = buildList {
                        if (vm.isMe(it.uuid)) {
                            // 模组与服装的通用选项-编辑
                            add(
                                ShareFeature(
                                    FEAT_EDIT,
                                    R.drawable.ic_share_edit,
                                    titleRes = R.string.edit,
                                    showRedDot = vm.accountInteractor.assetFirstEditRedDot2
                                )
                            )

                            // 模组类型的选项
                            if (vm.oldState.isModule) {
                                add(
                                    ShareFeature(
                                        FEAT_DELETE,
                                        R.drawable.ic_share_delete,
                                        titleRes = R.string.delete_cap
                                    )
                                )
                                if (vm.oldState.isPublished) {
                                    add(
                                        ShareFeature(
                                            FEAT_OFFLINE,
                                            R.drawable.ic_share_unpublish,
                                            titleRes = R.string.asset_unPublish
                                        )
                                    )
                                } else {
                                    add(
                                        ShareFeature(
                                            FEAT_ONLINE,
                                            R.drawable.ic_share_publish,
                                            titleRes = R.string.asset_publish
                                        )
                                    )
                                }
                            }

                            if (vm.oldState.pinned) {
                                add(
                                    ShareFeature(
                                        FEAT_UNPIN,
                                        R.drawable.ic_share_feat_unpin,
                                        titleRes = R.string.unpin_cap
                                    )
                                )
                            } else {
                                add(
                                    ShareFeature(
                                        FEAT_PIN,
                                        R.drawable.ic_share_pin,
                                        titleRes = R.string.pin_cap
                                    )
                                )
                            }

                            if (vm.oldState.isDesign) {
                                if (vm.oldState.isPublished) {
                                    add(
                                        ShareFeature(
                                            FEAT_PRIVATE,
                                            R.drawable.icon_design_private,
                                            titleRes = R.string.asset_design_private
                                        )
                                    )
                                } else {
                                    add(
                                        ShareFeature(
                                            FEAT_PUBLIC,
                                            R.drawable.icon_design_public,
                                            titleRes = R.string.asset_design_public
                                        )
                                    )
                                }
                            }
                        }
                    }
                )
            }
        }
        binding.tvDress.setOnAntiViolenceClickListener {
//            detail.isUgcModule
            vm.detail?.let {
                Analytics.track(
                    EventConstants.LIBRARY_METARIAL_TRYON_CLICK,
                    "metrialidid" to it.trackId,
                    "authorid" to it.uuid.orEmpty()
                )

                if (it.isUgcModule) {
                    vm.oldState.template()?.let { template ->
                        tsGameLaunchHelper?.startTemplateGame(
                            template,
                            ResIdBean().setCategoryID(CategoryId.UGC_MODULE_TEMPLATE)
                        )
                    }
                } else {
                    MetaRouter.MobileEditor.fullScreenRole(
                        requireContext(), FullScreenEditorActivityArgs(
                            categoryId = CategoryId.UGC_DESIGN_DETAIL, tryOn = RoleGameTryOn.create(
                                tryOnUserId = it.uuid.orEmpty(),
                                from = RoleGameTryOn.FROM_HOME_PAGE_CLOTHES,
                                clothesItemId = it.itemId.orEmpty()
                            )
                        )
                    )
                }
            }
        }
        binding.tvLikeBtn.setOnAntiViolenceClickListener {
            shouldPlayLikeAnim = true
            vm.like()
        }
        binding.bottomCommentInput.setOnLikeClickListener {
            shouldPlayLikeAnim = true
            vm.like()
        }
        binding.tvGetBtn.setOnAntiViolenceClickListener {
            if (vm.oldState.needPurchase) {
                val detail = vm.oldState.detail.invoke() ?: return@setOnAntiViolenceClickListener
                val commodityId = detail.commodityId ?: return@setOnAntiViolenceClickListener
                loadingDialog?.dismissAllowingStateLoss()
                loadingDialog = MetaRouter.Dialog.loading(childFragmentManager)
                buyViewModel.buy(
                    viewLifecycleOwner,
                    commodityId,
                    onStartPay = { payInfo ->
                        loadingDialog?.dismissAllowingStateLoss()
                        loadingDialog = null
                        payInfo.copy(
                            productCode = commodityId,
                            productName = detail.title ?: "",
                            productCount = 1,
                            sceneCode = PayScene.MODULE.value,
                            source = "asset",
                            metrialidid = detail.trackId,
                            payDialogStyle = PayDialogStyle.BOTTOM,
                            productImageUrl = detail.cover ?: "",
                            productSoldCount = detail.purchaseCount ?: 0L,
                            creatorName = detail.userName ?: "",
                            creatorAvatarUrl = detail.userIcon ?: "",
                        )
                    },
                    onPayResult = {
                        loadingDialog?.dismissAllowingStateLoss()
                        loadingDialog = null
                        vm.notifyPurchased(it)
                        val s = vm.oldState
                        s.detail()?.let { detail ->
                            Analytics.track(
                                EventConstants.LIBRARY_METARIAL_GET_CLICK,
                                "metrialidid" to detail.trackId,
                                "show_categoryid" to CategoryId.UGC_DESIGN_DETAIL,
                                "authorid" to detail.uuid.orEmpty(),
                                "result" to (if (it.isSuccess) "0" else "1"),
                                "type" to (detail.feedType ?: UgcDesignFeed.FEED_TYPE_UGC_MODULE),
                                "is_price" to (if (s.mutable?.isPriced == true) "yes" else "no"),
                                "price" to (s.mutable?.price ?: 0L)
                            )
                        }
                    }
                )
            } else {
                buyViewModel.get(viewLifecycleOwner, args.itemId)
            }
        }

        binding.bottomCommentInput.setOnReplyClickListener {
            showReplyDialog(AddPostCommentReplyTarget(args.itemId, vm.detail?.userName))
        }
        binding.bottomCommentInput.setOnCommentClickListener {
            if (!tryScrollToComment()) {
                showReplyDialog(AddPostCommentReplyTarget(args.itemId, vm.detail?.userName))
            }
        }
        binding.bottomCommentInput.ivEmojiBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.itemId, vm.detail?.userName),
                showEmoji = true
            )
        }
        binding.bottomCommentInput.ivImageBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.itemId, vm.detail?.userName),
                showImage = true
            )
        }
        binding.etvOutfitDesc.setExpandListener(object : ExpandableTextView.OnExpandListener {
            override fun onExpand(view: ExpandableTextView) {
                descExpandState = ExpandableTextView.STATE_EXPAND
            }

            override fun onShrink(view: ExpandableTextView) {
                descExpandState = ExpandableTextView.STATE_SHRINK
            }
        })
        binding.ulv.setListener(viewLifecycleOwner) {
            UserLabelView.showDescDialog(this, it)
        }
        binding.ivRemixAllowedTips.setOnAntiViolenceClickListener {
            val rect = Rect()
            binding.ivRemixAllowedTips.getGlobalVisibleRect(rect)
            UgcAssetMixableTipsDialog.show(this, rect)
            Analytics.track(
                EventConstants.ASSET_REMIX_OR_NOT_BTN_CLICK
            )
        }
        commentController.addModelBuildListener(viewLifecycleOwner) {
            if (needLocate) {
                needLocate = false
                scrollToTop = false
                binding.abl.setExpanded(false, true)
                binding.rvComment.smoothScrollToPosition(
                    locatePosition.coerceIn(
                        0, commentController.adapter.itemCount - 1
                    )
                )
            } else if (scrollToTop) {
                scrollToTop = false
                binding.rvComment.scrollToPosition(0)
            }
        }

        val statusBarHeight = StatusBarUtil.getStatusBarHeight(requireContext())
        val scrollHeight = (statusBarHeight + dp(80)).toFloat()
        val topHeight = statusBarHeight + getDimensionPx(R.dimen.title_bar_height)
        binding.abl.addOnOffsetChangedListener(viewLifecycleOwner) { _, offset ->
            val absOffset = abs(offset)
            binding.vBgTopToolbar.alpha = (absOffset / scrollHeight)
        }
        binding.clScrollable.minimumHeight = topHeight
        binding.lv.setPaddingEx(top = topHeight)

        binding.rvComment.setController(commentController)

        initPopup()

        vm.onAsync(
            UgcDesignDetailState::detail,
            onFail = { e, _ ->
                if ((e as? ApiResultCodeException)?.code == 1016) {
                    toast(getString(R.string.current_work_offline))
                    binding.lv.showEmpty(
                        msg = getString(R.string.current_work_offline),
                        resId = R.drawable.icon_no_recent_activity
                    )
                } else {
                    binding.lv.showError()
                }
            }, onLoading = {
                binding.lv.showLoading()
            }
        ) {
            val isMe = vm.isMe(it.uuid)
            // 这里要分主客态

            if (it.published == true || vm.isMe(it.uuid)) {
                binding.lv.hide()
                initDetail(it)
            } else {
                toast(getString(R.string.current_work_offline))
                binding.lv.showEmpty(
                    msg = getString(R.string.current_work_offline),
                    resId = R.drawable.icon_no_recent_activity
                )
            }
            visibleList(
                commentMorePopup.sortPopupBinding.mtvAuthorOnly,
                commentMorePopup.sortPopupBinding.vAuthorOnlyClick,
                commentMorePopup.sortPopupBinding.vDivider4,
                visible = !isMe
            )
        }
        vm.onEach(UgcDesignDetailState::isFollow) {
            it ?: return@onEach
            updateFollowStatus(it)
        }
        vm.onEach(UgcDesignDetailState::mutable) {
            it ?: return@onEach
            updateMutable(it)
        }
        vm.onEach(
            UgcDesignDetailState::isLike,
            UgcDesignDetailState::likeCount,
            UgcDesignDetailState::isMe
        ) { isLike, likeCount, isMe ->
            isLike ?: return@onEach
            likeCount ?: return@onEach
            isMe ?: return@onEach
            if (isMe) {
                binding.tvLikeTimes.text = UnitUtil.formatKMCount(likeCount)
            } else {
                updateLikeStatus(likeCount, isLike)
            }
            binding.bottomCommentInput.updateLike(isLike, likeCount, shouldPlayLikeAnim)
            shouldPlayLikeAnim = false
        }
        vm.onEach(
            UgcDesignDetailState::detail,
            UgcDesignDetailState::got,
            UgcDesignDetailState::bought,
            UgcDesignDetailState::mutable,
            UgcDesignDetailState::isMe
        ) { detailAsync, got, bought, mutable, isMe ->
            val detail = detailAsync() ?: return@onEach
            got ?: return@onEach
            bought ?: return@onEach
            mutable ?: return@onEach
            isMe ?: return@onEach
            if (isMe) return@onEach
            updateGetStatus(got, bought, mutable)
        }
        vm.onEach(
            UgcDesignDetailState::commentList,
            UgcDesignDetailState::commentListLoadMore,
        ) { commentList, loadMore ->
            val commentCount = UnitUtil.formatNumberWithUnit(
                num = commentList.invoke()?.total?:0,
                decimal = 1,
                kCaps = true,
                mCaps = true,
                bCaps = true,
                showRawNumberBelow10W = false
            )
            binding.tvCommentCount.setTextWithArgs(
                R.string.comment_count_postfix, commentCount
            )
            binding.bottomCommentInput.tvComment.text = commentCount
            if (commentList is Success && loadMore is Success) {
                binding.tvSortBtn.visible()
            }
        }

        vm.onEach(
            UgcDesignDetailState::sortType,
            UgcDesignDetailState::filterType
        ) { sortType, filterType ->
            updateSortTypeText(sortType, filterType)
        }
        buyViewModel.onAsync(
            BuyUgcDesignState::getResult,
            deliveryMode = uniqueOnly(),
            onFail = { _ ->
                vm.updateGetResult(false)
                val s = vm.oldState
                s.detail()?.let {
                    Analytics.track(
                        EventConstants.LIBRARY_METARIAL_GET_CLICK,
                        "metrialidid" to it.trackId,
                        "show_categoryid" to CategoryId.UGC_DESIGN_DETAIL,
                        "authorid" to it.uuid.orEmpty(),
                        "result" to "1",
                        "type" to (it.feedType ?: UgcDesignFeed.FEED_TYPE_UGC_MODULE),
                        "is_price" to (if (s.mutable?.isPriced == true) "yes" else "no"),
                        "price" to (s.mutable?.price ?: 0L)
                    )
                }
            }
        ) {
            vm.updateGetResult(true)
            val s = vm.oldState
            s.detail()?.let {
                if (it.isUgcModule) {
                    toastLong(R.string.ugc_module_get_tips)
                } else {
                    toastLong(R.string.add_to_role_inventory_tips)
                }
                Analytics.track(
                    EventConstants.LIBRARY_METARIAL_GET_CLICK,
                    "metrialidid" to it.trackId,
                    "show_categoryid" to CategoryId.UGC_DESIGN_DETAIL,
                    "authorid" to it.uuid.orEmpty(),
                    "result" to "0",
                    "type" to (it.feedType ?: UgcDesignFeed.FEED_TYPE_UGC_MODULE),
                    "is_price" to (if (s.mutable?.isPriced == true) "yes" else "no"),
                    "price" to (s.mutable?.price ?: 0L)
                )
            }
        }
        vm.onAsync(
            UgcDesignDetailState::addCommentResult,
            deliveryMode = uniqueOnly(),
            onFail = { _, it ->
                it ?: return@onAsync
                Analytics.track(
                    EventConstants.LIBRARY_ITEM_REVIEW_PUBLISH_CLICK,
                    "metrialidid" to vm.trackId,
                    "count" to vm.commentSuccessCount,
                    "result" to "1",
                    "reviewtype" to 1,
                    "image_num" to it.imageCount,
                    "type" to vm.feedType
                )
            }
        ) {
            vm.commentSuccessCount++
            Analytics.track(
                EventConstants.LIBRARY_ITEM_REVIEW_PUBLISH_CLICK,
                "metrialidid" to vm.trackId,
                "count" to vm.commentSuccessCount,
                "result" to "0",
                "reviewtype" to 1,
                "image_num" to it.imageCount,
                "type" to vm.feedType
            )
        }
        vm.onAsync(
            UgcDesignDetailState::addReplyResult,
            deliveryMode = uniqueOnly(),
            onFail = { _, it ->
                it ?: return@onAsync
                Analytics.track(
                    EventConstants.LIBRARY_ITEM_REVIEW_PUBLISH_CLICK,
                    "metrialidid" to vm.trackId,
                    "count" to vm.commentSuccessCount,
                    "result" to "1",
                    "reviewtype" to 2,
                    "image_num" to it.imageCount,
                    "type" to vm.feedType
                )
            }
        ) {
            vm.commentSuccessCount++
            Analytics.track(
                EventConstants.LIBRARY_ITEM_REVIEW_PUBLISH_CLICK,
                "metrialidid" to vm.trackId,
                "count" to vm.commentSuccessCount,
                "result" to "0",
                "reviewtype" to 2,
                "image_num" to it.imageCount,
                "type" to vm.feedType
            )
        }
        vm.onEach(UgcDesignDetailState::guideInvoke, deliveryMode = uniqueOnly()) {
            if (it == null || vm.oldState.isDesign) return@onEach
            UgcModuleGuideDialog.show(this, PandoraToggle.MODULE_GUIDE_FIRST_ASSET_INTERACT)
        }
        vm.onEach(UgcDesignDetailState::payResult, deliveryMode = uniqueOnly()) {
            it ?: return@onEach
            val payResult = it.second
            if (payResult.isSuccess) {
                toast(R.string.asset_buy_success)
            } else if (payResult.resultCode == PayResult.RESULT_CODE_FAILED) {
                // 支付失败,处理支付失败逻辑
                toast(payResult.failedReason ?: getString(R.string.common_failed))
            }
        }

        // 删除回调
        vm.onAsync(
            UgcDesignDetailState::deleteCallback,
            deliveryMode = uniqueOnly(),
            onFail = { }
        ) {
            toast(R.string.action_delete_success_tip)
            back()
        }

        // 置顶回调
        vm.onAsync(
            UgcDesignDetailState::pinnedCallback,
            deliveryMode = uniqueOnly(),
            onFail = { }
        ) {
            val actionTip = if (it) R.string.asset_pin_success else R.string.asset_action_failed
            toast(actionTip)
        }

        // 取消顶置回调
        vm.onAsync(
            UgcDesignDetailState::unpinnedCallback,
            deliveryMode = uniqueOnly(),
            onFail = { }
        ) {
            val actionTip = if (it) R.string.asset_unpin_success else R.string.asset_action_failed
            toast(actionTip)
        }

        // 设计公开
        vm.onAsync(
            UgcDesignDetailState::onPublicCallback,
            deliveryMode = uniqueOnly(),
            onFail = { }
        ) {
            toast(getString(R.string.asset_design_public_tip))
        }

        // 模组下架 / 设计私密
        vm.onAsync(
            UgcDesignDetailState::onPrivateCallback,
            deliveryMode = uniqueOnly(),
            onFail = { }
        ) {
            // 判断是模组下架还是设计私密
            val tipText = if (vm.oldState.isModule) {
                getString(R.string.asset_unPublished_success)
            } else {
                getString(R.string.asset_design_private_tip)
            }
            toast(tipText)
        }

        vm.registerToast(UgcDesignDetailState::toast)
        vm.registerAsyncErrorToast(UgcDesignDetailState::detail, {
            (it as? ApiResultCodeException)?.code != 1016
        })
        vm.registerAsyncErrorToast(UgcDesignDetailState::addCommentResult)
        vm.registerAsyncErrorToast(UgcDesignDetailState::addReplyResult)
        buyViewModel.registerAsyncErrorToast(BuyUgcDesignState::getResult)
        vm.registerAsyncErrorToast(UgcDesignDetailState::deleteCallback)
    }

    fun tryScrollToComment(): Boolean {
        val commentDividerY = binding.vSplitOutfitBottom.getLocationInWindow().y + binding.vSplitOutfitBottom.height
        val bottomInputY = binding.bottomCommentInput.getLocationInWindow().y
        if (commentDividerY > bottomInputY) {
            val titleDividerY = binding.tbl.getLocationInWindow().y + binding.tbl.height
            val distance = commentDividerY - titleDividerY
            val layoutParams = binding.abl.layoutParams as CoordinatorLayout.LayoutParams
            val animator = ValueAnimator.ofInt(0, distance)
            animator.duration = 200
            animator.addUpdateListener(
                this,
                true,
                object : ValueAnimator.AnimatorUpdateListener {
                    var lastValue = 0
                    override fun onAnimationUpdate(valueAnimator: ValueAnimator) {
                        if (!isBindingAvailable()) {
                            return
                        }
                        val value = valueAnimator.animatedValue as Int
                        layoutParams.behavior?.onNestedPreScroll(
                            binding.cdl,
                            binding.abl,
                            binding.rvComment,
                            0,
                            value - lastValue,
                            intArrayOf(0, 0),
                            ViewCompat.TYPE_TOUCH
                        )
                        lastValue = value
                    }
                })
            animator.start()
            return true
        }
        return false
    }

    private fun initPopup() {
        commentMorePopup.initPopup(
            onDefaultClick = {
                vm.updateSortType(PostCommentListRequestBody.QUERY_TYPE_DEFAULT)
            },
            onNewestClick = {
                vm.updateSortType(PostCommentListRequestBody.QUERY_TYPE_TOP)
            },
            onHotClick = {
                vm.updateSortType(PostCommentListRequestBody.QUERY_TYPE_LIKE)
            },
            onAuthorOnlyClick = {
                vm.updateFilterType(PostCommentListRequestBody.FILTER_AUTHOR)
            },
            onSelfOnlyClick = {
                vm.updateFilterType(PostCommentListRequestBody.FILTER_SELF)
            },
        )

        binding.tvSortBtn.setOnAntiViolenceClickListener {
            commentMorePopup.showSortPopup(it, binding.root)
        }
    }

    private fun initDetail(detail: UgcDesignDetail) {
        glide?.run {
            load(detail.userIcon).placeholder(R.drawable.icon_default_avatar)
                .circleCrop()
                .into(binding.ivAvatar)
        }
        binding.tvUsername.text = detail.userName
        binding.ulv.show(null, detail.labelInfo, glide = glide)
        binding.vUserClick.visible()
        glide?.run {
            load(detail.cover).transform(CenterCrop(), RoundedCorners(dp(12)))
                .into(binding.ivOutfit)
        }
        binding.tvOutfitPv.text = UnitUtil.formatKMCount(detail.views ?: 0)
        binding.tvTryOnTimes.text = UnitUtil.formatKMCount(detail.popularity ?: 0)
        binding.tvAcquiredTimes.text = UnitUtil.formatKMCount(detail.sales ?: 0)

        binding.tvOutfitDescUpdateTime.text = (detail.createTime ?: System.currentTimeMillis()).formatWholeDate()

        binding.tagsView.setTags(detail.tags ?: emptyList())

        binding.ivShareBtn.visible()
        if (vm.isMe(detail.uuid)) {
            // 主态
            binding.ivShareBtn.setImageDrawable(getDrawableByRes(R.drawable.ic_feat_24_1a1a1a_more_dot))
            binding.vEditRedDot.visible(vm.accountInteractor.assetFirstEditRedDot)
            binding.llLikeTimes.visible()
            binding.tvFollowBtn.gone()
            binding.groupGuestViews.gone() // 客态View-消失
            if (detail.isUgcModule) {
                // 模组
                binding.tvTryOnTimesLabel.setText(R.string.ugc_asset_popularity)
                if (vm.detail?.published == true) {
                    // 上架状态
                    binding.tvOutfitUnpublish.gone()
                } else {
                    // 下架状态
                    binding.tvOutfitUnpublish.text = getString(R.string.asset_unPublished)
                    binding.tvOutfitUnpublish.visible()
                }
            } else {
                // 服装
                binding.tvTryOnTimesLabel.setText(R.string.try_ons)
                if (vm.detail?.published == true) {
                    // 公开状态
                    binding.tvOutfitUnpublish.gone()
                } else {
                    // 私密状态
                    binding.tvOutfitUnpublish.text = getString(R.string.asset_design_private)
                    binding.tvOutfitUnpublish.visible()
                }
            }
        } else {
            // 客态
            binding.ivShareBtn.setImageDrawable(getDrawableByRes(R.drawable.ic_feat_24_1a1a1a_share))
            binding.llLikeTimes.gone()
            binding.tvFollowBtn.visible()
            binding.groupGuestViews.visible() // 客态View-显示
            if (detail.isUgcModule) {
                // 模组
                binding.tvDress.setText(R.string.community_moment_take_try)
                binding.tvDress.invalidate()
                binding.tvTryOnTimesLabel.setText(R.string.ugc_asset_popularity)

                if (vm.detail?.published == true) {
                    // 上架状态
                    binding.tvOutfitUnpublish.gone()
                } else {
                    // 下架状态（记得是单独展示一个空白页面了，不用展示这个）
                    binding.tvOutfitUnpublish.visible()
                }
            } else {
                // 服装
                binding.tvDress.setText(R.string.try_ons)
                binding.tvDress.setIcon(R.drawable.ic_ugc_design_try_on)
                binding.tvDress.invalidate()
                binding.tvTryOnTimesLabel.setText(R.string.try_ons)
            }

            if (detail.isOwned != true) {
                initGuide(detail)
            }
        }

        binding.clScrollable.doOnNextLayout {
            binding.clScrollable.minHeight = 0
        }
    }

    private fun initGuide(detail: UgcDesignDetail) {
        if (detail.isUgcModule) {
            if (vm.ugcModelDetailGuide) return
            vm.ugcModelDetailGuide = true
        } else {
            if (vm.ugcDesignDetailGuide) return
            vm.ugcDesignDetailGuide = true
        }
        Analytics.track(
            EventConstants.LIBRARY_USE_STEER_SHOW
        )
        binding.groupGuideViews.visible()
        if (detail.isUgcModule) {
            binding.tvGetBtn.doOnLayoutSized {
                initModelGuide()
            }
        } else {
            binding.tvDress.doOnLayoutSized {
                initDesignGuide()
            }
        }
    }

    private fun initDesignGuide() {
        navColorRes = R.color.black_60
        requireActivity().window.navColor = getColorByRes(navColorRes)
        val rectRoot = Rect()
        val rect = Rect()
        binding.root.getGlobalVisibleRect(rectRoot)
        binding.tvDress.getGlobalVisibleRect(rect)
        updateGuidePositionByView(
            binding.tvDress,
            getString(R.string.ugc_design_try_on_tips), rectRoot, rect
        )
        binding.mlGuide.setOnClickListener {
            hideGuide()
        }
        binding.tvDress.doOnLayoutChanged(viewLifecycleOwner) {
            if (!binding.mlGuide.isVisible) return@doOnLayoutChanged
            it.getGlobalVisibleRect(rect)
            updateGuidePositionByView(
                binding.tvDress,
                getString(R.string.ugc_design_try_on_tips), rectRoot, rect
            )
        }
    }

    private fun initModelGuide() {
        navColorRes = R.color.black_60
        requireActivity().window.navColor = getColorByRes(navColorRes)
        val rectRoot = Rect()
        val rect = Rect()
        binding.root.getGlobalVisibleRect(rectRoot)
        binding.tvGetBtn.getGlobalVisibleRect(rect)

        val guideText = if (vm.oldState.needPurchase) {
            getString(R.string.ugc_design_click_to_buy_tips)
        } else {
            getString(R.string.ugc_design_acquire_tips)
        }
        updateGuidePositionByView(binding.tvGetBtn, guideText, rectRoot, rect)
        binding.mlGuide.setOnClickListener {
            hideGuide()
        }
        binding.tvGetBtn.doOnLayoutChanged(viewLifecycleOwner) {
            if (!binding.mlGuide.isVisible) return@doOnLayoutChanged
            it.getGlobalVisibleRect(rect)
            updateGuidePositionByView(binding.tvGetBtn, guideText, rectRoot, rect)
        }
    }

    // 根据设定的目标View来更新引导View的位置
    private fun updateGuidePositionByView(
        targetView: View,
        guideText: String,
        rectRoot: Rect,
        rect: Rect
    ) {
        val radius = targetView.height.coerceAtLeast(binding.tvGetBtn.measuredHeight) / 2
        binding.mlGuide.setClipArea(
            rect.left,
            rect.top,
            rect.right,
            rect.bottom,
            rx = radius.toFloat(),
            ry = radius.toFloat(),
        )
        binding.ivGuideTri.translationY = (rect.bottom + dp(8)).toFloat()
        binding.ivGuideTri.translationX = (rect.right - rectRoot.right - dp(57)).toFloat()
        binding.tvGuide.translationY = (rect.bottom + dp(20)).toFloat()
        binding.tvGuide.translationX = (rect.right - rectRoot.right - dp(15)).toFloat()

        binding.tvGuide.text = guideText
    }

    private fun updateFollowStatus(isFollow: Boolean) {
        if (isFollow) {
            binding.tvFollowBtn.setBackgroundResource(R.drawable.sp_f5f5f5_r24_eeeeee_s1)
            binding.tvFollowBtn.setText(R.string.following_cap)
            // 关注状态：不显示图标
            binding.tvFollowBtn.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, 0, 0)
        } else {
            binding.tvFollowBtn.setBackgroundResource(R.drawable.bg_ffdc1c_round_24)
            binding.tvFollowBtn.setText(R.string.follow)
            binding.tvFollowBtn.setCompoundDrawablesRelativeWithIntrinsicBounds(
                R.drawable.icon_send_flower_dialog_coins_add, 0, 0, 0
            )
        }
    }

    private fun updateMutable(mutable: UgcAssetMutable) {
        binding.tvOutfitTitle.goneIfValueEmpty(mutable.title)

        if (mutable.published) {
            binding.tvOutfitUnpublish.gone()
        } else {
            val stateText = if (mutable.isModule) {
                getText(R.string.asset_unPublished)
            } else {
                getText(R.string.asset_design_private)
            }
            binding.tvOutfitUnpublish.text = stateText
            binding.tvOutfitUnpublish.visible()
        }

        // 开启商业化开关且为模组时
        if (mutable.enablePrice) {
            // 定价模式开启
            if (mutable.isShowFree) {
                // 免费
                binding.apply {
                    tvSales.gone()
                    llAcquiredTimes.visible()

                    // 显示获取数 & Free字样
                    tvAcquiredTimes.text = UnitUtil.formatKMCount(mutable.sales)
                    tvPrice.text = getText(R.string.cloth_price_free)
                }
            } else {
                // 付费
                binding.apply {
                    tvSales.visible()
                    llAcquiredTimes.gone()

                    // 显示售出数 & 价格
                    tvSales.text = getString(
                        R.string.purchase_sold_count,
                        UnitUtil.formatKMCount(mutable.purchaseCount)
                    )
                    tvPrice.text = UnitUtil.numWithComma(mutable.price)
                }
            }
            binding.tvPrice.visible()
            binding.tvPrice.setMargin(
                top = if (binding.tvOutfitTitle.isGone) 24.dp else 8.dp
            )
        } else {
            // 定价模式关闭
            binding.tvSales.gone()
            binding.tvPrice.gone()
        }

        if (mutable.comment.isNullOrEmpty()) {
            binding.etvOutfitDesc.gone()
        } else {
            binding.etvOutfitDesc.visible()
            binding.etvOutfitDesc.updateForRecyclerView(
                mutable.comment,
                screenWidth - dp(32), descExpandState
            )
            binding.etvOutfitDesc.setMargin(
                top = if (binding.tvPrice.isGone && binding.tvOutfitTitle.isGone) {
                    dp(24)
                } else {
                    dp(8)
                }
            )
        }
        /**
         * etvOutfitDesc在文本更新导致高度变化后似乎不会通知父布局更新高度，
         * 导致下方的tv_comment_count的位置不会更新，此处手动通知etvOutfitDesc的父布局更新高度
         */
        binding.etvOutfitDesc.post {
            if (isBindingAvailable()) {
                binding.etvOutfitDesc.parent?.requestLayout()
            }
        }

        if (mutable.editable) {
            binding.groupRemixAllowed.visible()
            binding.groupRemixAllowed.setMargin(
                top = if (binding.etvOutfitDesc.isVisible
                    || binding.tvPrice.isVisible
                    || binding.tvOutfitTitle.isVisible
                ) {
                    dp(8)
                } else {
                    dp(24)
                }
            )
        } else {
            binding.groupRemixAllowed.gone()
        }

        binding.flowOutfitStat.setMargin(
            top = with(binding) {
                if (tvOutfitTitle.isVisible
                    || etvOutfitDesc.isVisible
                    || tvPrice.isVisible
                    || groupRemixAllowed.isVisible
                ) {
                    dp(8)
                } else {
                    dp(24)
                }
            }
        )
    }

    private fun updateLikeStatus(likeCount: Long, isLike: Boolean) {
        binding.tvLikeCount.text = UnitUtil.formatKMCount(likeCount)
        if (isLike) {
            binding.tvLikeCount.setTextColorByRes(R.color.color_FF5F42)
            binding.ivLikeCount.invisible()
            binding.lavLikeAnim.visible()
            if (!likeAnim) {
                binding.lavLikeAnim.progress = 1.0f
            } else {
                binding.lavLikeAnim.progress = 0.0f
                binding.lavLikeAnim.playAnimation()
            }
        } else {
            binding.tvLikeCount.setTextColorByRes(R.color.color_757575)
            binding.ivLikeCount.visible()
            binding.lavLikeAnim.cancelAnimationIfAnimating()
            binding.lavLikeAnim.gone()
        }
        likeAnim = true
    }

    private fun updateGetStatus(
        got: Boolean,
        bought: Boolean,
        mutable: UgcAssetMutable
    ) {
        if (got) {
            binding.tvGetBtn.setText(R.string.claimed_cap)
            binding.tvGetBtn.setTextColor(getColorByRes(R.color.color_1A1A1A_50))
        } else if (mutable.enablePrice && mutable.isPriced && !bought) {
            binding.tvGetBtn.setText(R.string.buy_now)
            binding.tvGetBtn.setTextColor(getColorByRes(R.color.color_1A1A1A))
        } else {
            binding.tvGetBtn.setText(R.string.claim_cap)
            binding.tvGetBtn.setTextColor(getColorByRes(R.color.color_1A1A1A))
        }
        binding.tvGetBtn.enableWithAlpha(!got, if (got) 0.3f else 1.0f)
    }

    private fun buildCommentController() = simpleController(
        vm,
        UgcDesignDetailState::commentList,
        UgcDesignDetailState::commentListLoadMore,
        UgcDesignDetailState::uniqueTag,
        UgcDesignDetailState::showCommentPinRedDot
    ) { comments, loadMore, uniqueTag, showCommentPinRedDot ->
        when (comments) {
            is Success -> {
                val list = comments().dataList
                if (list.isNullOrEmpty()) {
                    empty(
                        idStr = "UgcAssetDetailCommentEmpty",
                        iconRes = R.drawable.icon_no_recent_activity,
                        descRes = R.string.let_comm_begin_with_your_comment,
                        top = dp(32)
                    ) {
                        vm.getCommentList(true)
                    }
                } else {
                    val dp05 = dp(0.5)
                    val dp8 = dp(8)
                    val dp62 = dp(62)
                    val dp16 = dp(16)
                    val commentContentWidth = screenWidth - 78.dp
                    val replyContentWidth = screenWidth - 112.dp
                    val atColor = getColorByRes(R.color.color_0083FA)
                    list.forEachIndexed { commentPosition, comment ->
                        if (comment.isNewAdd) {
                            comment.isNewAdd = false
                            scrollToTop = true
                        }
                        ugcCommentItem(
                            uniqueTag = uniqueTag,
                            item = comment,
                            position = commentPosition,
                            contentWidth = commentContentWidth,
                            enableImage = true,
                            firstPin = showCommentPinRedDot,
                            listener = itemListener
                        )
                        if (comment.needLocate) {
                            comment.needLocate = false
                            needLocate = true
                            locatePosition = buildItemIndex
                        }
                        var showReplyItem = false
                        if (!comment.collapse) {
                            showReplyItem = (comment.authorReply?.size
                                ?: 0) + (comment.replyCommonPage?.dataList?.size ?: 0) > 0
                            comment.authorReply?.forEachIndexed { replyPosition, reply ->
                                ugcReplyItem(
                                    uniqueTag,
                                    reply,
                                    replyPosition,
                                    commentPosition,
                                    true,
                                    atColor,
                                    replyContentWidth,
                                    true,
                                    itemListener
                                )
                                if (reply.needLocate) {
                                    reply.needLocate = false
                                    needLocate = true
                                    locatePosition = buildItemIndex
                                }
                            }
                            comment.replyCommonPage?.dataList?.forEachIndexed { replyPosition, reply ->
                                ugcReplyItem(
                                    uniqueTag,
                                    reply,
                                    replyPosition,
                                    commentPosition,
                                    false,
                                    atColor,
                                    replyContentWidth,
                                    true,
                                    itemListener
                                )
                                if (reply.needLocate) {
                                    reply.needLocate = false
                                    needLocate = true
                                    locatePosition = buildItemIndex
                                }
                            }
                        } else {
                            comment.replyCommonPage?.dataList?.forEachIndexed { replyPosition, reply ->
                                if (reply.forceShow) {
                                    showReplyItem = true
                                    ugcReplyItem(
                                        uniqueTag,
                                        reply,
                                        replyPosition,
                                        commentPosition,
                                        false,
                                        atColor,
                                        replyContentWidth,
                                        true,
                                        itemListener
                                    )
                                    if (reply.needLocate) {
                                        reply.needLocate = false
                                        needLocate = true
                                        locatePosition = buildItemIndex
                                    }
                                }
                            }
                        }
                        val showReplyButtons = comment.showReplyButtons
                        if (comment.loading) {
                            ugcCommentLoading(
                                uniqueTag, comment
                            )
                        } else if (showReplyButtons) {
                            ugcCommentExpandItem(
                                uniqueTag, comment, commentPosition, showReplyItem, itemListener
                            )
                        }
                    }
                    loadMoreFooter(
                        loadMore,
                        idStr = "UgcDesignCommentFooter-${uniqueTag}",
                        endText = getString(R.string.community_article_comment_empty),
                        endTextColorRes = R.color.textColorSecondary
                    ) {
                        vm.getCommentList(false)
                    }
                }
            }

            is Loading -> {
                loadMoreFooter(idStr = "UgcDesignCommentFooterLoading") {}
            }

            is Fail -> {
                empty(
                    idStr = "UgcAssetDetailEmpty-Fail",
                    iconRes = R.drawable.icon_no_recent_activity,
                    descRes = R.string.footer_load_failed,
                    top = dp(32)
                ) {
                    vm.initCommentList()
                }
            }

            else -> {}
        }
    }

    private fun showReplyDialog(
        target: AddPostCommentReplyTarget,
        showEmoji: Boolean = false,
        showImage: Boolean = false,
    ) {
        AccPwdV7Dialog.show(this, AccPwdV7DialogArgs.SOURCE_PUBLISH_UGC_DESIGN_COMMENT) {
            if (it && isBindingAvailable()) {
                val replyType: Long
                val reviewId: String
                val type: Int
                if (target.isTargetComment) {
                    replyType = 0L
                    reviewId = target.asComment.commentId
                    type = ArticleCommentInputDialog.TYPE_COMMENT_COMMENT
                } else if (target.isTargetReply) {
                    replyType = 1L
                    reviewId = target.asReply.replyId
                    type = ArticleCommentInputDialog.TYPE_COMMENT_REPLAY
                } else {
                    Analytics.track(
                        EventConstants.LIBRARY_ITEM_DETAIL_PAGE_REVIEW_CLICK, "metrialidid" to vm.trackId
                    )
                    replyType = -1L
                    reviewId = vm.oldState.itemId
                    type = ArticleCommentInputDialog.TYPE_COMMENT_ARTICLE
                }
                vm.setReplyTarget(target)
                ArticleCommentInputDialog.show(
                    this,
                    replyUniqueKey = "$${args.itemId}-$reviewId",
                    target.toNickname,
                    null,
                    null,
                    type,
                    0.7f,
                    showEmoji,
                    showImage,
                    getPageName(),
                    ArticleCommentInputDialogParams.CONTENT_TYPE_UGC_DESIGN_DETAIL,
                    1,
                    true,
                    pageName = getPageName()
                ) {
                    if (it == null || !it.valid) return@show
                    if (target.isTargetPost) {
                        vm.addCommentViaNet(it)
                    } else {
                        vm.addReplyViaNet(it)
                    }
                }
            }
        }
    }

    private fun handleOperateComment(
        view: View, comment: PostComment, commentPosition: Int, showRedDot: Boolean
    ) {
        handleOperationHelper(
            view, comment = comment, showRedDot = showRedDot, commentPosition = commentPosition
        )
    }

    private fun handleOperateReply(
        view: View,
        reply: PostReply,
        replyPosition: Int,
        commentPosition: Int,
        isAuthorReply: Boolean
    ) {
        handleOperationHelper(
            view,
            reply = reply,
            commentPosition = commentPosition,
            replyPosition = replyPosition,
            isAuthorReply = isAuthorReply
        )
    }

    private fun handleOperationHelper(
        view: View,
        comment: PostComment? = null,
        showRedDot: Boolean = false,
        reply: PostReply? = null,
        commentPosition: Int = 0,
        replyPosition: Int = 0,
        isAuthorReply: Boolean = false
    ){
        val isMe = vm.isMe(comment?.uid ?: reply?.uid)
        val isCreator = vm.isCreator(vm.myUuid)
        commentMorePopup.operateComment(
            fragment = this@UgcDesignDetailFragment,
            rootLayout = binding.root,
            view = view,
            comment = comment,
            reply = reply,
            showRedDot = showRedDot,
            isMe = isMe,
            isCreator = isCreator,
            onCopyClick = {
                val reviewId: String = comment?.commentId ?: reply?.replyId.orEmpty()
                val reviewType: Long = if (comment != null) 1L else 2L
                Analytics.track(
                    EventConstants.LIBRARY_REVIEW_COPY_CLICK,
                    "metrialidid" to vm.trackId,
                    "reviewid" to reviewId,
                    "reviewtype" to reviewType
                )
            },
            onPinClick = {
                if (comment != null) {
                    vm.pinComment(comment, commentPosition, showRedDot)
                }
            },
            onUnpinClick = {
                if (comment != null) {
                    vm.pinComment(comment, commentPosition, showRedDot)
                }
            },
            onReportClick = {
                if (comment != null) {
                    goReport(comment.commentId, ReportType.UgcClothingComment)
                } else if (reply != null) {
                    goReport(reply.replyId, ReportType.UgcClothingReply)
                }
            },
            onDeleteCancel = {},
            onDeleteConfirm={
                if (comment != null) {
                    vm.deleteComment(comment, commentPosition)
                } else if (reply != null) {
                    vm.deleteReply(
                        reply, replyPosition, commentPosition, isAuthorReply
                    )
                }
            }
        )
    }

    fun updateSortTypeText(sortType: Int, filterType: Int?) {
        val resId = when (filterType) {
            PostCommentListRequestBody.FILTER_AUTHOR -> {
                R.string.sort_author_only
            }

            PostCommentListRequestBody.FILTER_SELF -> {
                R.string.sort_self_only
            }

            else -> {
                when (sortType) {
                    PostCommentListRequestBody.QUERY_TYPE_DEFAULT -> {
                        R.string.sort_hot
                    }

                    PostCommentListRequestBody.QUERY_TYPE_TOP -> {
                        R.string.sort_newest
                    }

                    PostCommentListRequestBody.QUERY_TYPE_LIKE -> {
                        R.string.sort_hottest
                    }

                    else -> {
                        return
                    }
                }
            }
        }
        binding.tvSortBtn.setText(resId)
    }

    private fun goReport(reportId: String, reportType: ReportType) {
        MetaRouter.Report.postReport(this, reportId, reportType) {
            if (it) {
                Analytics.track(
                    EventConstants.LIBRARY_REVIEW_REPORT_SUCCESS,
                    "metrialidid" to vm.trackId,
                    "reviewid" to reportId,
                    "reviewtype" to if (reportType == ReportType.UgcClothingComment) 1L else 2L
                )
                val analyticsParams = when (reportType) {
                    ReportType.UgcClothingComment -> {
                        ReportSuccessDialogAnalyticsParams.ClothingComment(
                            feedId = args.itemId,
                            commentId = reportId,
                        )
                    }

                    ReportType.UgcClothingReply -> {
                        ReportSuccessDialogAnalyticsParams.ClothingCommentReply(
                            feedId = args.itemId,
                            replyId = reportId,
                        )
                    }

                    else -> {
                        null
                    }
                }
                ReportReasonDialog.showReportSuccessDialog(this, analyticsParams)
            }
        }
    }

    private fun back() {
        if (BuildConfig.DEBUG) {
            Timber.tag("FragmentNavigatorPlus").d("UgcDesignDetailFragment back() called, mlGuide.isVisible=${binding.mlGuide.isVisible}")
        }
        if (binding.mlGuide.isVisible) {
            hideGuide()
        } else {
            if (BuildConfig.DEBUG) {
                Timber.tag("FragmentNavigatorPlus").d("UgcDesignDetailFragment 尝试返回操作")
            }
            try {
                // 使用项目中已有的扩展方法
                popBackStack()

                if (BuildConfig.DEBUG) {
                    Timber.tag("FragmentNavigatorPlus").d("UgcDesignDetailFragment popBackStack调用完成")
                }
            } catch (e: Exception) {
                Timber.tag("FragmentNavigatorPlus").e("UgcDesignDetailFragment popBackStack异常，尝试navigateUp: ${e?.message}")
                e.printStackTrace()
                try {
                    navigateUp()
                } catch (e2: Exception) {
                    Timber.tag("FragmentNavigatorPlus").e("UgcDesignDetailFragment navigateUp也失败: ${e2?.message}")
                    e2.printStackTrace()
                }
            }
        }
    }

    private fun hideGuide() {
        navColorRes = R.color.white
        requireActivity().window.navColor = getColorByRes(navColorRes)
        binding.mlGuide.unsetOnClick()
        binding.groupGuideViews.gone()
    }
//
//    private fun startPay(data: MWPlaceOrderResponse) {
//        val payRawInfo = data.toPayInfo(data, vm.payInteractor)
//        val payInfo = payRawInfo.copy(
//            source = "asset",
//            needPreprocess = false,
//            needToastWhenSucceeded = false,
//            onPayResult = {
//                vm.notifyPurchased(it)
//                val s = vm.oldState
//                s.detail()?.let { detail ->
//                    Analytics.track(
//                        EventConstants.LIBRARY_METARIAL_GET_CLICK,
//                        "metrialidid" to detail.trackId,
//                        "authorid" to detail.uuid.orEmpty(),
//                        "result" to (if (it.isSuccess) "0" else "1"),
//                        "type" to detail.feedType,
//                        "is_price" to (if (s.mutable?.isPriced == true) "yes" else "no"),
//                        "price" to (s.mutable?.price ?: 0L)
//                    )
//                }
//            }
//        )
//        MetaRouter.Pay.startPay(payInfo)
//    }

    override fun invokeShareFeature(feature: ShareFeature) {
        when (feature.featureId) {
            FEAT_EDIT -> {
                val s = vm.oldState
                val detail = s.detail() ?: return
                Analytics.track(
                    EventConstants.LIBRARY_METARIAL_EDIT_CLICK
                )
                vm.accountInteractor.assetFirstEditRedDot2 = false
                val fromArg = when (args.categoryId) {
                    CategoryId.UGC_MODULE_MINE -> "3"
                    CategoryId.UGC_DESIGN_PROFILE_DESIGN_TAB,
                    CategoryId.UGC_ASSET_LIST -> "2"

                    else -> null
                }
                MetaRouter.UgcDesign.edit(
                    this,
                    s.itemId,
                    detail.cover,
                    s.mutable ?: detail.toMutable(),
                    detail.trackId,
                    from = fromArg
                )
            }

            FEAT_DELETE -> {
                Analytics.track(
                    EventConstants.PROFILE_MANAGE_MODULE_DELETE_CLICK,
                    "from" to "0"
                )
                TipDialog.Builder(this)
                    .content(getString(R.string.assets_detail_delete_confirm_tip))
                    .contentColor(R.color.color_333333)
                    .cancelText(getString(R.string.dialog_cancel))
                    .confirmText(getString(R.string.delete_cap))
                    .confirmTextColor(R.color.color_FF5F42)
                    .setConfirmIconVisible(false)
                    .confirmCallback {
                        Analytics.track(
                            EventConstants.PROFILE_MANAGE_MODULE_CONFIRM_DELETE_CLICK,
                            "from" to "0"
                        )
                        vm.delete()
                    }
                    .show()
            }

            FEAT_OFFLINE -> {
                TipDialog.Builder(this)
                    .content(getString(R.string.asset_unpublish_dialog_content))
                    .confirmText(getString(R.string.ok))
                    .cancelText(getString(R.string.cancel))
                    .confirmCallback {
                        vm.putOffSale()
                    }
                    .show()
            }

            FEAT_ONLINE -> {
                vm.oldState.isPublished
                vm.oldState.price

                val s = vm.oldState
                val detail = s.detail() ?: return
                Analytics.track(
                    EventConstants.LIBRARY_METARIAL_EDIT_CLICK
                )
                vm.accountInteractor.assetFirstEditRedDot2 = false
                val fromArg = when (args.categoryId) {
                    CategoryId.UGC_MODULE_MINE -> "3"
                    CategoryId.UGC_DESIGN_PROFILE_DESIGN_TAB,
                    CategoryId.UGC_ASSET_LIST -> "2"

                    else -> null
                }
                MetaRouter.UgcDesign.edit(
                    this,
                    s.itemId,
                    detail.cover,
                    s.mutable ?: detail.toMutable(),
                    detail.trackId,
                    from = fromArg
                )
            }

            FEAT_PIN -> {
                Analytics.track(
                    EventConstants.MY_LIBRARY_TAB_PIN_CLICK,
                    "result" to "0",
                    "from" to "1",
                    "type" to "0"
                )
                vm.pin()
            }

            FEAT_UNPIN -> {
                Analytics.track(
                    EventConstants.MY_LIBRARY_TAB_PIN_CLICK,
                    "result" to "0",
                    "from" to "1",
                    "type" to "1"
                )
                vm.unpin()
            }

            FEAT_PUBLIC -> {
                Analytics.track(
                    EventConstants.MY_LIBRARY_TAB_UNHIDE_TAB_CLICK,
                    "result" to "0",
                    "count" to "1",
                    "from" to "1",
                    "type" to if (vm.oldState.isModule) "0" else "1"
                )
                vm.putOnSale()
            }

            FEAT_PRIVATE -> {
                Analytics.track(
                    EventConstants.MY_LIBRARY_TAB_HIDE_TAB_CLICK,
                    "result" to "0",
                    "count" to "1",
                    "from" to "1",
                    "type" to if (vm.oldState.isModule) "0" else "1"
                )
                vm.putOffSale()
            }
        }
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_UGC_DESIGN_DETAIL

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vm.apiMonitor(
            this,
            UgcDesignDetailState::detail
        )
        commentController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        commentController.onSaveInstanceState(outState)
    }

    override fun onDestroyView() {
        loadingDialog?.dismissAllowingStateLoss()
        loadingDialog = null
        contentHeight = binding.clScrollable.height
        commentMorePopup.release()
        super.onDestroyView()
    }

    override fun onDestroy() {
        val state = vm.oldState
        AssetEditResultBatchEvent(
            assets = listOf(
                AssetEditResultEvent(
                    feedId = state.itemId,
                    likeCount = state.likeCount,
                    isLike = state.isLike == true,
                    deleted = state.deleteCallback is Success,
                    published = state.isPublished,
                    price = state.price,
                    got = state.got == true,
                    name = state.detail.invoke()?.title,
                )
            )
        ).sendEvent()
        if (args.needResult) {
            state.likeCount?.let {
                setFragmentResultByHostFragment(
                    TAG, bundleOf(
                        KEY_ITEM_ID to state.itemId,
                        KEY_IS_LIKE to (state.isLike == true),
                        KEY_LIKE_COUNT to it,
                        KEY_DELETED to (state.deleteCallback is Success),
                        KEY_PUBLISHED to state.isPublished,
                        KEY_PRICE to state.price,
                        KEY_GOT to (state.got == true),
                        KEY_NAME to state.detail.invoke()?.title,
                    )
                )
            }
        }
        super.onDestroy()
    }

    private inner class UgcCommentListener : IUgcCommentListener {
        override fun isMe(uid: String?): Boolean {
            return vm.isMe(uid)
        }

        override fun isCreator(uid: String?): Boolean {
            return vm.isCreator(uid)
        }

        override fun iAmCreator(): Boolean {
            return vm.iAmCreator
        }

        override fun goUserPage(uid: String?) {
            if (!uid.isNullOrBlank()) {
                MetaRouter.Profile.other(this@UgcDesignDetailFragment, uid, TRACK_TAG)
            }
        }

        override fun operateComment(
            view: View, comment: PostComment, commentPosition: Int, showRedDot: Boolean
        ) {
            handleOperateComment(view, comment, commentPosition, showRedDot)
        }

        override fun likeComment(comment: PostComment, commentPosition: Int) {
            Analytics.track(
                EventConstants.LIBRARY_REVIEW_LIKE_CLICK,
                "metrialidid" to vm.trackId,
                "reviewtype" to "1",
                "fromwho" to comment.uid.orEmpty()
            )
            vm.likeComment(comment, commentPosition)
            DialogShowManager.triggerLike(this@UgcDesignDetailFragment)
        }

        override fun reply2Comment(comment: PostComment, commentPosition: Int) {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_REVIEW_REPLAY_CLICK,
                "metrialidid" to vm.trackId,
                "replaytype" to "0",
                "fromwho" to comment.uid.orEmpty()
            )
            showReplyDialog(AddPostCommentReplyTarget(comment, commentPosition))
        }

        override fun operateReply(
            view: View,
            reply: PostReply,
            replyPosition: Int,
            commentPosition: Int,
            isAuthorReply: Boolean
        ) {
            handleOperateReply(view, reply, replyPosition, commentPosition, isAuthorReply)
        }

        override fun likeReply(
            reply: PostReply, replyPosition: Int, commentPosition: Int, isAuthorReply: Boolean
        ) {
            Analytics.track(
                EventConstants.LIBRARY_REVIEW_LIKE_CLICK,
                "metrialidid" to vm.trackId,
                "reviewtype" to "2",
                "fromwho" to reply.uid.orEmpty()
            )
            vm.likeReply(reply, replyPosition, commentPosition, isAuthorReply)
            DialogShowManager.triggerLike(this@UgcDesignDetailFragment)
        }

        override fun reply2Reply(reply: PostReply, commentPosition: Int) {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_REVIEW_REPLAY_CLICK,
                "metrialidid" to vm.trackId,
                "replaytype" to "1",
                "fromwho" to reply.uid.orEmpty()
            )
            showReplyDialog(AddPostCommentReplyTarget(reply, reply.commentId, commentPosition))
        }

        override fun loadMoreReply(comment: PostComment, commentPosition: Int) {
            if (comment.isCollapse) {
                Analytics.track(
                    EventConstants.LIBRARY_ITEM_DETAIL_REVIEW_COLLAPSE_CLICK,
                    "metrialidid" to vm.trackId
                )
            }
            vm.loadMoreReplies(comment, commentPosition)
        }

        override fun collapseReply(comment: PostComment, commentPosition: Int) {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_DETAIL_PAGE_REVIEW_REPLISE_CLICK,
                "metrialidid" to vm.trackId
            )
            vm.collapseReply(comment, commentPosition, true)
        }

        override fun goCommentListPage() {}

        override fun previewImage(
            mediaList: List<String>?,
            imageViews: List<ImageView>,
            imagePosition: Int
        ) {
            OpenPreviewBuilder(this@UgcDesignDetailFragment)
                .setImageUrls(mediaList ?: emptyList())
                .setClickViews(imageViews)
                .setClickPosition(imagePosition)
                .show()
        }

        override fun showComment(comment: PostComment, commentPosition: Int) {}

        override fun clickLabel(data: Pair<Int, LabelInfo?>) {
            UserLabelView.showDescDialog(this@UgcDesignDetailFragment, data)
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }
}