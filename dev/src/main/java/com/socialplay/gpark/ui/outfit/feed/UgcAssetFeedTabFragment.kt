package com.socialplay.gpark.ui.outfit.feed

import android.content.Intent
import android.graphics.Rect
import android.net.Uri
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.data.model.outfit.UgcAssetNotice
import com.socialplay.gpark.data.model.outfit.UgcDesignFeed
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.databinding.FragmentUgcAssetFeedTabBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ListDialog
import com.socialplay.gpark.ui.editor.module.guide.UgcModuleGuideDialog
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.outfit.IUgcDesignFeedListener
import com.socialplay.gpark.ui.outfit.UgcAssetFeedSortDialog
import com.socialplay.gpark.ui.outfit.UgcDesignDetailFragment
import com.socialplay.gpark.ui.outfit.UgcDesignFeedState
import com.socialplay.gpark.ui.outfit.UgcDesignFeedViewModel
import com.socialplay.gpark.ui.outfit.ugcAssetFeedNoticesItem
import com.socialplay.gpark.ui.outfit.ugcDesignFeedItem
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialog
import com.socialplay.gpark.ui.reportBlock.ReportSuccessDialogAnalyticsParams
import com.socialplay.gpark.ui.view.MStaggeredLayoutManager
import com.socialplay.gpark.util.extension.addModelBuildListener
import com.socialplay.gpark.util.extension.attachV2
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.screenWidth
import org.koin.core.context.GlobalContext

class UgcAssetFeedTabFragment :
    BaseRecyclerViewFragment<FragmentUgcAssetFeedTabBinding>(R.layout.fragment_ugc_asset_feed_tab) {

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rv

    private val vm: UgcAssetFeedTabViewModel by fragmentViewModel()
    private val tabVM: UgcDesignFeedViewModel by parentFragmentViewModel()

    private val itemListener = UgcDesignFeedListener()

    private var spanSize = 2
    private var layoutManagerState: Parcelable? = null

    private var scrollToTop = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vm.apiMonitor(
            this,
            UgcAssetFeedTabState::loadMore
        )
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentUgcAssetFeedTabBinding? {
        return FragmentUgcAssetFeedTabBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        spanSize = if (isPad) {
            ((screenWidth - dp(20)) / dp(177.5f)).coerceAtLeast(2)
        } else {
            2
        }

        EpoxyVisibilityTracker().attachV2(viewLifecycleOwner, recyclerView)
        recyclerView.setDelayMsWhenRemovingAdapterOnDetach(1)
        recyclerView.itemAnimator = null
        recyclerView.layoutManager =
            MStaggeredLayoutManager(spanSize, RecyclerView.VERTICAL).apply {
                setOnDetachedCallback {
                    layoutManagerState = it
                }
                setOnRestoreCallback {
                    return@setOnRestoreCallback layoutManagerState
                }
            }
        recyclerView.adapter?.stateRestorationPolicy?.let {
            epoxyController.adapter.stateRestorationPolicy = it
        }

        vm.setupRefreshLoading(
            UgcAssetFeedTabState::outfits,
            binding.lv,
            binding.rl
        ) {
            vm.getFeed(true)
            vm.fetchOperationNotices()
        }
        vm.onEach(UgcAssetFeedTabState::uniqueTag, uniqueOnly()) {
            scrollToTop = true
        }
        vm.onEach(UgcAssetFeedTabState::guideInvoke, deliveryMode = uniqueOnly()) {
            it ?: return@onEach
            UgcModuleGuideDialog.show(this, PandoraToggle.MODULE_GUIDE_FIRST_ASSET_INTERACT)
        }
        vm.registerAsyncErrorToast(UgcAssetFeedTabState::loadMore)

        tabVM.onEach(UgcDesignFeedState::event, deliveryMode = uniqueOnly()) {
            when (it?.first) {
                UgcDesignFeedViewModel.EVENT_CLICK_SORT -> {
                    vm.fetchTagsIfNeed()
                    UgcAssetFeedSortDialog.show(this, it.third as? Rect)
                }

                UgcDesignFeedViewModel.EVENT_ASSET_DETAIL_LIKE -> {
                    val bundle = it.third as? Bundle ?: return@onEach
                    vm.updateItem(UgcDesignDetailFragment.getResult(bundle))
                }
            }
        }

        epoxyController.addModelBuildListener(viewLifecycleOwner) {
            if (scrollToTop) {
                scrollToTop = false
                recyclerView.scrollToPosition(0)
            }
        }
    }

    override fun epoxyController() = simpleController(
        vm,
        UgcAssetFeedTabState::outfits,
        UgcAssetFeedTabState::loadMore,
        UgcAssetFeedTabState::uniqueTag,
        UgcAssetFeedTabState::notices,
    ) { outfits, outfitsLoadMore, uniqueTag, notices ->
        val noticesData = notices()
        if (!noticesData.isNullOrEmpty()) {
            ugcAssetFeedNoticesItem(noticesData, spanSize, itemListener)
        }
        val items = outfits()
        if (items.isNullOrEmpty()) return@simpleController
        val uuid = vm.curUuid
        items.forEachIndexed { index, item ->
            ugcDesignFeedItem(item, index, uuid, uniqueTag, "UgcAssetFeed", itemListener)
        }
        loadMoreFooter(
            outfitsLoadMore,
            idStr = "UgcDesignFeedFooter-${uniqueTag}",
            spanSize = spanSize,
            showEnd = false,
            staggerFullSpan = true
        ) {
            vm.getFeed(outfitsLoadMore()?.needRefresh == true)
        }
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_UGC_DESIGN_FEED

    private inner class UgcDesignFeedListener : IUgcDesignFeedListener {
        override fun clickOutfit(item: UgcDesignFeed, position: Int) {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_CLICK,
                "metrialidid" to item.trackId,
                "type" to item.feedType,
                "authorid" to item.uuid.orEmpty(),
                "tab_id" to 0
            )
            MetaRouter.UgcDesign.detail(
                this@UgcAssetFeedTabFragment,
                item.feedId,
                CategoryId.UGC_DESIGN_FEED,
                tabId = 0,
                needResult = true
            )
        }

        override fun clickLike(item: UgcDesignFeed, position: Int) {
            vm.likeFeed(item, position)
            if (!item.isFavorite) {
                Analytics.track(
                    EventConstants.LIBRARY_ITEM_DETAIL_LIKE_CLICK,
                    "authorid" to item.uuid.orEmpty(),
                    "metrialidid" to item.trackId,
                    "type" to item.feedType,
                    "from" to 0
                )
            } else {
                Analytics.track(
                    EventConstants.LIBRARY_ITEM_DETAIL_CANCEL_LIKE_CLICK,
                    "authorid" to item.uuid.orEmpty(),
                    "metrialidid" to item.trackId,
                    "type" to item.feedType,
                    "from" to 0
                )
            }
        }

        override fun more(item: UgcDesignFeed, position: Int) {
            val report = SimpleListData(getString(R.string.report))
            val cancel = SimpleListData(getString(R.string.cancel))

            ListDialog()
                .list(listOf(report, cancel))
                .clickCallback {
                    when (it) {
                        report -> {
                            MetaRouter.Report.postReport(
                                this@UgcAssetFeedTabFragment,
                                item.feedId,
                                ReportType.UgcClothing
                            ) {
                                if (it) {
                                    ReportReasonDialog.showReportSuccessDialog(
                                        this@UgcAssetFeedTabFragment,
                                        ReportSuccessDialogAnalyticsParams.Clothing(
                                            feedId = item.feedId
                                        )
                                    )
                                }
                            }
                        }
                    }
                }
                .show(childFragmentManager, "UgcDesignFeedMoreDialog")
        }

        override fun showDesign(item: UgcDesignFeed, position: Int) {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_SHOW,
                "metrialidid" to item.trackId,
                "show_categoryid" to CategoryId.ASSETS_FEED,
                "type" to item.feedType,
                "authorid" to item.uuid.orEmpty(),
                "tab_id" to 0,
                "is_recreate" to item.trackIsRecreate,
                "is_price" to (if (item.isPriced == true) "yes" else "no")
            )
        }

        override fun clickOperation(item: UgcAssetNotice, position: Int) {
            Analytics.track(
                EventConstants.LIBRARY_NOTICE_CLICK,
                "noticeid" to item.noticeId
            )
            val url = item.noticeUrl ?: return
            val uri = kotlin.runCatching { Uri.parse(url) }.getOrNull() ?: return
            when (uri.scheme) {
                "http", "https" -> {
                    MetaRouter.Web.navigate(
                        this@UgcAssetFeedTabFragment,
                        item.noticeTitle,
                        url
                    )
                }

                BuildConfig.SCHEME_URI -> {
                    MetaDeepLink.handle(
                        requireActivity(),
                        this@UgcAssetFeedTabFragment,
                        GlobalContext.get().get<MainViewModel>(),
                        uri,
                        LinkData.SOURCE_UGC_ASSET_FEED_NOTICE
                    )
                }

                else -> {
                    kotlin.runCatching {
                        val intent = Intent(Intent.ACTION_VIEW, uri)
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        startActivity(intent)
                    }
                }
            }
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }
}