package com.socialplay.gpark.ui.post.comment

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.DialogInterface
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.DecelerateInterpolator
import androidx.core.os.bundleOf
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.luck.picture.lib.style.PictureSelectorStyle
import com.luck.picture.lib.style.PictureWindowAnimationStyle
import com.ly123.tes.mgs.im.emoticon.AndroidEmoji.getEmojiTypeList
import com.ly123.tes.mgs.im.emoticon.AndroidEmoji.netEmojiMap
import com.ly123.tes.mgs.im.emoticon.IEmojiItemClickListener
import com.ly123.tes.mgs.im.emoticon.adapter.EmoticonTabAdapter
import com.ly123.tes.mgs.im.panel.IExtensionModule
import com.ly123.tes.mgs.im.panel.RongExtensionManager
import com.ly123.tes.mgs.im.util.TextChangeUtil.onEmojiClick
import com.ly123.tes.mgs.im.util.TextChangeUtil.staticEmojiClick
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.QuickInputTag
import com.socialplay.gpark.data.model.community.CommentDraftPool
import com.socialplay.gpark.data.model.community.GifEmojiInfoWrapper
import com.socialplay.gpark.data.model.community.PostCommentContent
import com.socialplay.gpark.data.model.community.PostCommentDraft
import com.socialplay.gpark.data.model.community.PostCommentImageUpload
import com.socialplay.gpark.databinding.CommunityDialogCommentBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.gamereview.CommentAnalyticsHelper
import com.socialplay.gpark.ui.account.AccPwdV7Dialog
import com.socialplay.gpark.ui.core.BaseRecyclerViewDialogFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.core.views.spacer
import com.socialplay.gpark.ui.gamedetail.unify.BaseGameDetailCommonFragment
import com.socialplay.gpark.ui.imgpre.v2.OpenPreviewBuilder
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.util.CustomEpoxyTouchHelper
import com.socialplay.gpark.util.FileUtil
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.KeyboardHeightUtilV2
import com.socialplay.gpark.util.LengthCallbackFilter
import com.socialplay.gpark.util.PermissionUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.attach
import com.socialplay.gpark.util.extension.canShowDialog
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getDimension
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.navParams
import com.socialplay.gpark.util.extension.onTouch
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toBundle
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.glide.GlideEngine
import com.socialplay.gpark.util.glide.LubanCompressEngine
import com.socialplay.gpark.util.ime.KeyboardListener
import com.socialplay.gpark.util.ime.WindowInsetsHelper
import com.socialplay.gpark.util.property.viewBinding
import java.util.Locale

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2023/06/12
 *     desc   :
 *
 */
class ArticleCommentInputDialog : BaseRecyclerViewDialogFragment() {

    override var navColorRes = R.color.white

    override val binding by viewBinding(CommunityDialogCommentBinding::inflate)
    private val args: ArticleCommentInputDialogParams by navParams()

    private var textWatcher: TextWatcher? = null
    private var isKeyBoardActive = false
    private var mEmotionTabAdapter: EmoticonTabAdapter? = null
    private var mExtensionModuleList: List<IExtensionModule>? = null
    private val handler by lazy { Handler(Looper.getMainLooper()) }
    private var result: PostCommentContent? = null

    private var showEmoji: Boolean = false
    private var showImage: Boolean = false
    private var showKeyboardFirstTime = true
    private var hasSent: Boolean = false

    private var checkDraft = true

    override val recyclerView: EpoxyRecyclerView
        get() = binding.ryView
    private val vm: ArticleCommentInputViewModel by fragmentViewModel()

    private val emojiController by lazy { buildEmojiController() }

    private val quickInputController by lazy { quickInputController() }

    private var visibilityTracker: EpoxyVisibilityTracker? = null

    private val itemListener = object : IArticleCommentInputListener {
        override fun deleteEmoji(item: GifEmojiInfoWrapper, position: Int) {
            vm.deleteEmoji(item, position)
        }

        override fun deleteImage(item: PostCommentImageUpload, position: Int) {
            vm.deleteImage(item, position)
        }

        override fun addUploadListener(
            key: String,
            listener: ArticleCommentImageUploadProgressListener
        ) {
            vm.addUploadListener(key, listener)
        }

        override fun removeUploadListener(key: String) {
            vm.removeUploadListener(key)
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }
    private val imageItemClickedListener = object : IImageItemClickedListener {
        override fun onClicked(url: String) {
            OpenPreviewBuilder(this@ArticleCommentInputDialog)
                .setImageUrls(listOf(url))
                .setRecyclerView(binding.ryView) { _, _ ->
                    R.id.iv_image
                }
                .setClickPosition(0)
                .show()
        }
    }
    private val quickItemClickedListener = object : IQuickInputItemListener {
        override fun onClicked(tag: QuickInputTag) {
            Analytics.track(
                EventConstants.EVENT_GUESS_RECOMMENT_CLICK,
                "source" to args.pageName,
                "tagid" to tag.tagId.toString()
            )
            val editText = binding.etInputMessage
            val editable = binding.etInputMessage.text ?: return
            val tagContent = tag.content ?: return
            var cursorPosition = editText.selectionStart
            if (cursorPosition < 0) {
                cursorPosition = editText.text?.length ?: 0
            }
            editable.insert(cursorPosition, tagContent)
            cursorPosition += tagContent.length
            editText.setSelection(cursorPosition.coerceAtMost(MAX_LENGTH - 1))
        }

        override fun onItemVisibilityChange(tag: QuickInputTag, visible: Boolean) {
            if (visible) {
                Analytics.track(
                    EventConstants.EVENT_GUESS_RECOMMENT_SHOW,
                    "source" to args.pageName,
                    "tagid" to tag.tagId.toString()
                )
            }
        }
    }

    private var toastTs = 0L

    companion object {
        const val KEY = "ArticleCommentDialog"
        const val IMAGE_MAX = 3

        const val TYPE_PUBLISH_ARTICLE = 0
        const val TYPE_COMMENT_ARTICLE = 1
        const val TYPE_COMMENT_COMMENT = 2
        const val TYPE_COMMENT_REPLAY = 3

        const val MAX_LENGTH = 2000

        fun show(
            fragment: Fragment,
            replyUniqueKey: String,
            hint: String?,
            resId: String?,
            gameCircleName: String?,
            type: Int,
            dimAmount: Float,
            showEmoji: Boolean,
            showImage: Boolean,
            request: String,
            contentType: Int,
            imageCount: Int,
            acceptImageOnly: Boolean,
            pageName: String,
            callback: (PostCommentContent?) -> Unit = {}
        ) {
            val bindSource =
                CommentAnalyticsHelper.getBindAccountSource(type, contentType) ?: return
            AccPwdV7Dialog.show(
                fragment,
                bindSource
            ) accountBindCallback@{
                if (!it || !fragment.canShowDialog) return@accountBindCallback
                fragment.childFragmentManager.setFragmentResultListener(
                    request,
                    fragment.viewLifecycleOwner
                ) { _, bundle ->
                    fragment.childFragmentManager.clearFragmentResultListener(request)
                    callback(bundle.getParcelable(request) as? PostCommentContent)
                }
                val dialog = ArticleCommentInputDialog()
                val dialogArgs = ArticleCommentInputDialogParams(
                    replyUniqueKey = replyUniqueKey,
                    hint = hint,
                    resId = resId,
                    gameCircleName = gameCircleName,
                    type = type,
                    requestKey = request,
                    dimAmount = dimAmount,
                    showEmoji = showEmoji,
                    showImage = showImage,
                    contentType = contentType,
                    imageCount = imageCount,
                    acceptImageOnly = acceptImageOnly,
                    pageName = pageName
                )
                dialog.arguments = dialogArgs.toBundle()
                dialog.show(fragment.childFragmentManager, request)
            }
        }
    }


    override fun onDismiss(dialog: DialogInterface) {
        if (!hasSent) {
            val text = binding.etInputMessage.text?.toString()?.trim()
            val images = vm.oldState.images?.filter { !it.url.isNullOrEmpty() }
            CommentDraftPool.saveDraft(
                args.replyUniqueKey,
                PostCommentDraft(
                    text,
                    images
                )
            )
        }
        removeEmojiView()
        super.onDismiss(dialog)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun init() {
        vm.initQuickInput(args.contentType)
        binding.etInputMessage.filters = arrayOf(LengthCallbackFilter(MAX_LENGTH) {
            context?.let {
                val curTs = SystemClock.elapsedRealtime()
                if (curTs - toastTs > 2000) {
                    toastTs = curTs
                    toast(getString(R.string.up_to_x_chars, MAX_LENGTH))
                }
            }
        })

        if (checkDraft) {
            checkDraft = false
            showEmoji = args.showEmoji
            showImage = args.showImage
            val draft = CommentDraftPool.retrieveDraft(args.replyUniqueKey)
            draft?.let {
                if (!it.images.isNullOrEmpty()) {
                    vm.init(it.images)
                }
                if (!it.text.isNullOrEmpty()) {
                    binding.etInputMessage.setText(it.text)
                }
            }
        }

        binding.root.setOnClickListener {
            dismissAllowingStateLoss()
        }

        enableSend()
        if (args.type == TYPE_COMMENT_ARTICLE || args.hint.isNullOrBlank()) {
            binding.etInputMessage.setHint(R.string.comment_hint_map)
        } else {
            binding.etInputMessage.hint = "${getString(R.string.community_reply)} @${args.hint}："
        }
        textWatcher = binding.etInputMessage.doAfterTextChanged {
            enableSend()
        }
        binding.tvSend.setOnAntiViolenceClickListener {
            when (args.contentType) {
                ArticleCommentInputDialogParams.CONTENT_TYPE_PGC_DETAIL -> {
                    Analytics.track(
                        EventConstants.GAME_REVIEW_PUBLISH_CLICK,
                        "gameid" to args.resId.orEmpty(),
                        "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_PGC,
                        "type" to if (args.type == 1) "1" else "2"
                    )
                }

                ArticleCommentInputDialogParams.CONTENT_TYPE_UGC_DETAIL -> {
                    Analytics.track(
                        EventConstants.GAME_REVIEW_PUBLISH_CLICK,
                        "gameid" to args.resId.orEmpty(),
                        "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_UGC,
                        "type" to if (args.type == 1) "1" else "2"
                    )
                }
            }
            sendMessage()
        }
        binding.ivMore.setOnAntiViolenceClickListener {
            if (isKeyBoardActive()) {
                hideInputKeyBoard()
                setEmoticonBoard()
            } else {
                when (args.contentType) {
                    ArticleCommentInputDialogParams.CONTENT_TYPE_PGC_DETAIL -> {
                        Analytics.track(
                            EventConstants.GAME_EMOJI_REVIEW_CLICK,
                            "gameid" to args.resId.orEmpty(),
                            "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_PGC
                        )
                    }

                    ArticleCommentInputDialogParams.CONTENT_TYPE_UGC_DETAIL -> {
                        Analytics.track(
                            EventConstants.GAME_EMOJI_REVIEW_CLICK,
                            "gameid" to args.resId.orEmpty(),
                            "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_UGC
                        )
                    }
                }
                setEmoticonBoard()
            }
        }
        binding.ivImageBtn.visible(args.imageCount > 0)
        binding.ivImageBtn.setOnAntiViolenceClickListener {
            when (args.contentType) {
                ArticleCommentInputDialogParams.CONTENT_TYPE_PGC_DETAIL -> {
                    Analytics.track(
                        EventConstants.GAME_PICTURE_REVIEW_CLICK,
                        "gameid" to args.resId.orEmpty(),
                        "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_PGC
                    )
                }

                ArticleCommentInputDialogParams.CONTENT_TYPE_UGC_DETAIL -> {
                    Analytics.track(
                        EventConstants.GAME_PICTURE_REVIEW_CLICK,
                        "gameid" to args.resId.orEmpty(),
                        "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_UGC
                    )
                }
            }
            clickImageBtn()
        }
        initEmoticons()
        binding.etInputMessage.onTouch(viewLifecycleOwner) { _, motionEvent ->
            onEditTextTouch(view, motionEvent)
        }
        initView()
        showInput()

        vm.onEach(ArticleCommentInputState::images) { images ->
            binding.ryView.gone(images.isNullOrEmpty())
            enableSend()
        }
        vm.onEach(ArticleCommentInputState::emojis) { emojis ->
            binding.rvEmojiPreview.gone(emojis.isNullOrEmpty())
            enableSend()
        }
        vm.onEach(ArticleCommentInputState::quickInputTags) { quickInputTags ->
            if (!isEmoticonBoardShow()) {
                binding.quickInputLayout.visible(
                    visible = !quickInputTags.isNullOrEmpty(),
                    transition = !quickInputTags.isNullOrEmpty(),
                    animDuration = keyboardListener.keyboardAnimateShowTime ?: 300
                )
            }
        }
        vm.onEach(ArticleCommentInputState::delayKeyboard, uniqueOnly()) {
            if (it != null && binding.etInputMessage.hasFocus()) {
                InputUtil.showSoftBoard(this.binding.etInputMessage)
            }
        }
        vm.onEach(ArticleCommentInputState::permissionCallback, uniqueOnly()) {
            val count = it?.first ?: return@onEach
            if (count > 0) {
                choosePictureHelper(count)
            } else {
                toast(R.string.base_permission_photo_error)
            }
        }
        vm.registerToast(ArticleCommentInputState::toast)
    }

    private fun initView() {
        binding.rvEmojiPreview.layoutManager = LinearLayoutManager(
            requireContext(),
            LinearLayoutManager.HORIZONTAL,
            false
        )
        binding.rvEmojiPreview.setController(emojiController)
        binding.rvQuickInput.layoutManager = LinearLayoutManager(
            requireContext(),
            LinearLayoutManager.HORIZONTAL,
            false
        )
        visibilityTracker =
            EpoxyVisibilityTracker().attach(viewLifecycleOwner, binding.rvQuickInput)
        // Item 有 50% 的部分显示出来时
        visibilityTracker?.partialImpressionThresholdPercentage = 50
        binding.rvQuickInput.setController(quickInputController)

        binding.ryView.layoutManager = LinearLayoutManager(
            requireContext(),
            LinearLayoutManager.HORIZONTAL,
            false
        )
        CustomEpoxyTouchHelper.initDragging(epoxyController)
            .withRecyclerView(binding.ryView)
            .forHorizontalList()
            .withTarget(ArticleCommentImageItem::class.java)
            .andCallbacks(
                viewLifecycleOwner,
                object : CustomEpoxyTouchHelper.DragCallbacks<ArticleCommentImageItem>() {
                    override fun onModelMoved(
                        fromPosition: Int,
                        toPosition: Int,
                        modelBeingMoved: ArticleCommentImageItem,
                        itemView: View
                    ) {
                        vm.moveImage(fromPosition, toPosition)
                    }
                }
            )
    }

    private fun sendMessage() {
        if (vm.isUploadingImage) {
            toast(R.string.community_plz_wait_image_upload)
            return
        }
        hasSent = true
        enableSend()
        val text: String = binding.etInputMessage.text?.toString()?.trim().orEmpty()
        binding.etInputMessage.setText("")
        recyclerView.clear()
        CommentDraftPool.removeDraft(args.replyUniqueKey)
        val sb = StringBuilder(text)
        val emj = StringBuilder()
        vm.oldState.emojis?.forEach {
            emj.append(it.info.code)
        }
        sb.append(emj)
        result = PostCommentContent(text = sb.toString(), mediaList = vm.images)
        dismissAllowingStateLoss()
    }

    private fun initEmoticons() {
        mEmotionTabAdapter = EmoticonTabAdapter()
        mExtensionModuleList = RongExtensionManager.getInstance().extensionModules ?: return
        mExtensionModuleList?.forEach { module ->
            module.onAttachedToExtension(object : IEmojiItemClickListener {
                override fun onEmojiClick(emoji: String?) {
                    onEmojiClick(emoji, binding.etInputMessage)

                }

                override fun staticEmojiClick(emoji: String?) {
                    if (emoji.isNullOrEmpty()) {
                        return
                    }
                    staticEmojiClick(emoji, binding.etInputMessage, 24f, 17f)
                    if (args.isPostDetail) {
                        val map = java.util.HashMap<String, String>()
                        map["source"] = "2"
                        map["series"] = "2"
                        map["stickers_name"] = emoji
                        Analytics.track(
                            EventConstants.EVENT_STICKERS_POST,
                            map
                        )
                    }
                }

                override fun onDeleteClick() {
                    binding.etInputMessage.dispatchKeyEvent(KeyEvent(0, 67))
                }

                override fun gifEmojiClick(code: String?) {
                    if (vm.emojiCount >= 3) {
                        toast(getString(R.string.community_emoji_maximum))
                    } else {
                        val gifEmojiInfo = netEmojiMap[code]
                        if (gifEmojiInfo != null) {
                            vm.addEmoji(gifEmojiInfo)
                        }
                    }
                    if (args.isPostDetail) {
                        val map = java.util.HashMap<String, String>()
                        map["source"] = "2"
                        map["series"] = "1"
                        map["stickers_name"] = code!!
                        Analytics.track(
                            EventConstants.EVENT_STICKERS_POST,
                            map
                        )
                    }
                }
            })
            val tabs = module.getEmoticonTabs(getEmojiTypeList(true, true, true, 4, 7, true, true))
            mEmotionTabAdapter!!.initTabs(tabs)
        }

    }

    private fun enableSend() {
        val hasContent = !binding.etInputMessage.text.isNullOrBlank()
        val hasEmoji = vm.emojiCount > 0
        val hasImage = args.acceptImageOnly && vm.imageCount > 0
        if (hasSent || (!hasContent && !hasEmoji && !hasImage)) {
            binding.tvSend.isEnabled = false
            binding.tvSend.alpha = 0.3F
        } else {
            binding.tvSend.isEnabled = true
            binding.tvSend.alpha = 1F
        }
    }

    private fun isKeyBoardActive(): Boolean {
        return isKeyBoardActive
    }

    /**
     * 隐藏软件盘
     */
    private fun hideInputKeyBoard() {
        InputUtil.hideKeyboard(binding.etInputMessage)
        binding.etInputMessage.clearFocus()
        isKeyBoardActive = false
    }

    //显示软键盘
    private fun showInputKeyBoard() {
        this.binding.etInputMessage.requestFocusFromTouch()
        InputUtil.showSoftBoard(this.binding.etInputMessage)
        this.binding.ivMore.isSelected = false
        isKeyBoardActive = true
        this.binding.ivMore.setImageResource(R.drawable.community_comment_icon_emoji)
    }

    private fun isEmoticonBoardShow(): Boolean {
        return this.binding.ivMore.isSelected
    }

    //隐藏表情包列表
    private fun hideEmoticonBoard() {
        isPanelBoardShow = false
        binding.quickInputLayout.visible(
            visible = !vm.quickInputTags.isNullOrEmpty(),
            transition = !vm.quickInputTags.isNullOrEmpty(),
            animDuration = keyboardListener.keyboardAnimateShowTime ?: 300
        )
        this.binding.ivMore.isSelected = false
        this.binding.ivMore.setImageResource(R.drawable.community_comment_icon_emoji)
    }

    private fun updatePanelContainerHeight(height: Int) {
        if (isBindingAvailable()) {
            binding.bottomContainer.setHeight(height)
        }
    }

    /**
     * 将面板调整为指定高度(带动画)
     */
    private fun animatePanelContainerHeight(targetHeight: Int) {
        val startHeight = binding.bottomContainer.height
        val animator = ValueAnimator.ofInt(startHeight, targetHeight)
        animator.duration = 200
        animator.interpolator = AccelerateDecelerateInterpolator() // 加速减速插值器
        animator.addUpdateListener { animation ->
            updatePanelContainerHeight(animation.animatedValue as Int)
        }
        animator.start()
    }

    //展示表情包
    private fun showEmotionBord() {
        isPanelBoardShow = true

        val ctx = context
        if (ctx == null) {
            updatePanelContainerHeight(ViewGroup.LayoutParams.WRAP_CONTENT)
        } else {
            // EmoticonTabAdapter.initView 方法中写死了键盘高度
            val emotionPanelHeight = if (ScreenUtil.isHorizontalScreen(ctx)) {
                getDimension(R.dimen.dp_180)
            } else {
                getDimension(R.dimen.dp_282)
            }
            animatePanelContainerHeight(emotionPanelHeight.toInt())
        }

        binding.quickInputLayout.visible(visible = false, transition = false)
        mEmotionTabAdapter?.setVisibility(View.VISIBLE)
        this.binding.ivMore.isSelected = true
        this.binding.ivMore.setImageResource(R.drawable.community_ic_post_comment_keyboard)
    }

    private fun setEmoticonBoard() {
        if (mEmotionTabAdapter?.isInitialized() == true) {
            if (isPanelBoardShow) {
                hideEmoticonBoard()
                showInputKeyBoard()
            } else {
                hideInputKeyBoard()
                showEmotionBord()
            }
        } else {
            mEmotionTabAdapter?.bindView(binding.bottomContainer)
            hideInputKeyBoard()
            showEmotionBord()
        }
        enableSend()
    }

    private fun removeEmojiView() {
        vm.clear()
    }

    private fun onEditTextTouch(v: View?, event: MotionEvent): Boolean {
        if (0 == event.action) {
            requestInput()
        }
        return false
    }

    private fun requestInput() {
        if (Build.BRAND.lowercase(Locale.getDefault()).contains("meizu")) {
            binding.etInputMessage.requestFocusFromTouch()
            isKeyBoardActive = true
        } else {
            showInputKeyBoard()
        }
        hideEmoticonBoard()
    }

    private var isPanelBoardShow = false
    private var windowInsetsHelper: WindowInsetsHelper = WindowInsetsHelper()
    private val keyboardListener = object : KeyboardListener() {
        private var maxKeyBoardHeight = 0

        /**
         * 输入法是否弹出
         */
        private var upKeyboard = false

        /**
         * 输入法是否收起
         */
        private var downKeyboard = false
        override fun onKeyboardShowStart() {
            if (!isBindingAvailable()) {
                return
            }
            upKeyboard = true
            downKeyboard = false
            if (maxKeyBoardHeight > 0 && binding.bottomContainer.height < maxKeyBoardHeight) {
                // 键盘开始弹出, 并且此时面板比键盘最终高度还要要高时, 将面板高度直接调整为键盘的最终高度
                animatePanelContainerHeight(maxKeyBoardHeight)
            }
        }

        override fun onKeyboardShowEnd() {
            if (!isBindingAvailable()) {
                return
            }
            if (currentKeyboardHeight > 0 && currentKeyboardHeight != binding.bottomContainer.height) {
                updatePanelContainerHeight(currentKeyboardHeight)
            }
        }

        override fun onKeyboardHideStart() {
            upKeyboard = false
            downKeyboard = true
        }

        /**
         * 键盘弹出是个动画, keyboardHeight 为键盘当前帧的高度
         */
        override fun onProgress(keyboardHeight: Int) {
            if (!isBindingAvailable()) {
                return
            }
            maxKeyBoardHeight = maxKeyBoardHeight.coerceAtLeast(keyboardHeight)

            if (upKeyboard && binding.bottomContainer.height < keyboardHeight) {
                // 键盘弹出时, 如果面板的高度比此刻的键盘低, 则将面板高度设置成和键盘一样高
                updatePanelContainerHeight(keyboardHeight)
            }
            if (downKeyboard && !isPanelBoardShow) {
                // 键盘收起时, 如果接下来不显示面板, 则将面板高度设置成和键盘一样高
                updatePanelContainerHeight(keyboardHeight)
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val window = dialog?.window
        if (window != null) {
            windowInsetsHelper.apply(
                window,
                binding.root,
                keyboardListener
            )
        }
    }

    override fun onResume() {
        super.onResume()
        visibilityTracker?.clearVisibilityStates()
        visibilityTracker?.requestVisibilityCheck()
        KeyboardHeightUtilV2.registerKeyboardHeightListener(requireActivity()) {
            isKeyBoardActive = it > 200
        }
        if (showKeyboardFirstTime) {
            showKeyboardFirstTime = false
            this.binding.etInputMessage.requestFocusFromTouch()
            vm.delayKeyboard()
            // DialogFragment 在 onResume 的时候可能还没准备好接收输入法, 所以需要等到 WindowFocusChange 的时候做一次兜底
            dialog?.window?.decorView?.viewTreeObserver?.addOnWindowFocusChangeListener(
                object : ViewTreeObserver.OnWindowFocusChangeListener {
                    override fun onWindowFocusChanged(hasFocus: Boolean) {
                        if (hasFocus) {
                            InputUtil.showSoftBoard(binding.etInputMessage)
                            dialog?.window?.decorView?.viewTreeObserver?.removeOnWindowFocusChangeListener(
                                this
                            )
                        }
                    }
                })
            updateBottomContainerOnKeyboardFirstShow()
        }
    }

    private fun updateBottomContainerOnKeyboardFirstShow() {
        val endHeight = keyboardListener.keyboardFullHeight ?: 700
        val animator = ValueAnimator.ofInt(
            0,
            endHeight
        )
        animator.duration = keyboardListener.keyboardAnimateShowTime ?: 200
        animator.interpolator = DecelerateInterpolator()
        animator.addUpdateListener { animation ->
            val targetHeight = animation.animatedValue as Int
            if (keyboardListener.currentKeyboardHeight < targetHeight) {
                updatePanelContainerHeight(targetHeight)
            }
        }
        animator.start()
    }

    private fun showInput() {
        if (showEmoji) {
            when (args.contentType) {
                ArticleCommentInputDialogParams.CONTENT_TYPE_PGC_DETAIL -> {
                    Analytics.track(
                        EventConstants.GAME_EMOJI_REVIEW_CLICK,
                        "gameid" to args.resId.orEmpty(),
                        "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_PGC
                    )
                }

                ArticleCommentInputDialogParams.CONTENT_TYPE_UGC_DETAIL -> {
                    Analytics.track(
                        EventConstants.GAME_EMOJI_REVIEW_CLICK,
                        "gameid" to args.resId.orEmpty(),
                        "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_UGC
                    )
                }
            }
            showEmoji = false
            showImage = false
            showKeyboardFirstTime = false
            handler.postDelayed({ setEmoticonBoard() }, 100L)
        } else if (showImage) {
            when (args.contentType) {
                ArticleCommentInputDialogParams.CONTENT_TYPE_PGC_DETAIL -> {
                    Analytics.track(
                        EventConstants.GAME_PICTURE_REVIEW_CLICK,
                        "gameid" to args.resId.orEmpty(),
                        "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_PGC
                    )
                }

                ArticleCommentInputDialogParams.CONTENT_TYPE_UGC_DETAIL -> {
                    Analytics.track(
                        EventConstants.GAME_PICTURE_REVIEW_CLICK,
                        "gameid" to args.resId.orEmpty(),
                        "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_UGC
                    )
                }
            }
            showImage = false
            showKeyboardFirstTime = false
            clickImageBtn()
        } else {
            showInputKeyBoard()
        }
    }

    override fun onPause() {
        InputUtil.hideKeyboard(binding.etInputMessage)
        isKeyBoardActive = false
        KeyboardHeightUtilV2.unregisterKeyboardHeightListener(requireActivity())
        super.onPause()
    }

    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT

    override fun getStyle() = R.style.DialogStyleV2_Input_NoAnimation

    override fun dimAmount(): Float {
        return args.dimAmount ?: 0f
    }


    override fun onDestroyView() {
        if (textWatcher != null) {
            binding.etInputMessage.removeTextChangedListener(textWatcher)
            textWatcher = null
        }
        mEmotionTabAdapter = null
        super.onDestroyView()
    }

    override fun onDestroy() {
        windowInsetsHelper.unApply()
        visibilityTracker = null
        args.requestKey?.let { setFragmentResult(it, bundleOf(args!!.requestKey to result)) }
        handler.removeCallbacksAndMessages(null)
        mExtensionModuleList?.forEach { module ->
            module.onDetachedFromExtension()
        }
        mExtensionModuleList = null
        mEmotionTabAdapter = null
        super.onDestroy()
    }

    private fun clickImageBtn() {
        val count = args.imageCount - vm.imageCount
        if (count <= 0) {
            toast(getString(R.string.community_select_max_pic_x, args.imageCount))
        } else {
            choosePicture(count)
        }
    }

    private fun choosePicture(count: Int) {
        if (PermissionUtil.needPermission4PublishPost(requireActivity())) {
            PermissionRequest.with(requireActivity())
                .permissions(*PermissionRequest.getMediaPermission().toTypedArray())
                .allowAnyPermissionIsOk()
                .enableGoSettingDialog()
                .granted {
                    vm.handlePermissionResult(count)
                }
                .denied { goSysSettings ->
                    if (!goSysSettings) {
                        vm.handlePermissionResult(0)
                    }
                }
                .branch(PermissionRequest.SCENE_ALBUM)
                .request()
        } else {
            choosePictureHelper(count)
        }
    }

    private fun choosePictureHelper(count: Int) {
        if (args.isPostDetail) {
            Analytics.track(
                EventConstants.EVENT_COMMUNITY_POST_REPLY_ADD_PICTURE_CLICK,
                "resid" to args.resId.orEmpty(),
                "gamecirclename" to args.gameCircleName.orEmpty(),
                "type" to args.type.toString(),
            )
        }
        val type = SelectMimeType.ofImage()
        if (!FileUtil.havePhotoPath(requireContext(), type)) {
            toast(R.string.base_permission_photo_error)
            return
        }
        val style = PictureSelectorStyle().apply {
            windowAnimationStyle = PictureWindowAnimationStyle(
                com.luck.picture.lib.R.anim.ps_anim_up_in,
                com.luck.picture.lib.R.anim.ps_anim_down_out
            )
        }

        PictureSelector.create(this@ArticleCommentInputDialog)
            .openGallery(type)
            .setMaxSelectNum(count)
            .setImageEngine(GlideEngine)
            .setSelectorUIStyle(style)
            .setCompressEngine(LubanCompressEngine())
            .setSelectLimitTipsListener { _, _, _, _ ->
                toast(getString(R.string.community_select_max_pic_x, args.imageCount))
                true
            }
            .forResult(object : OnResultCallbackListener<LocalMedia> {
                override fun onResult(result: java.util.ArrayList<LocalMedia>?) {
                    if (result.isNullOrEmpty()) return
                    vm.addImages(result)
                }

                override fun onCancel() {

                }
            })
    }

    override fun epoxyController() = simpleController(
        vm,
        ArticleCommentInputState::images,
    ) { images ->
        images?.forEachIndexed { index, item ->
            articleCommentImageItem(item, index, itemListener, imageItemClickedListener)
        }
    }

    private fun buildEmojiController() = simpleController(
        vm,
        ArticleCommentInputState::emojis
    ) { emojis ->
        emojis?.forEachIndexed { index, item ->
            articleCommentEmojiItem(item, index, itemListener)
        }
    }

    private fun quickInputController() = simpleController(
        vm,
        ArticleCommentInputState::quickInputTags
    ) { quickInputTags ->
        quickInputTags?.forEachIndexed { index, item ->
            spacer(width = 8.dp)
            quickInputItem(item, index, quickItemClickedListener)
        }
        spacer(width = 8.dp)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        emojiController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        emojiController.onSaveInstanceState(outState)
    }
}