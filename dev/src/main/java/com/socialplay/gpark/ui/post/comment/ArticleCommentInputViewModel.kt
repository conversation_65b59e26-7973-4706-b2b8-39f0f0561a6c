package com.socialplay.gpark.ui.post.comment

import android.content.ComponentCallbacks
import android.os.SystemClock
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.ViewModelContext
import com.luck.picture.lib.entity.LocalMedia
import com.ly123.tes.mgs.im.emoticon.GifEmojiInfo
import com.meta.pandora.utils.ConcurrentSet
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.interactor.UploadFileInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.QuickInputConfig
import com.socialplay.gpark.data.model.QuickInputTag
import com.socialplay.gpark.data.model.community.GifEmojiInfoWrapper
import com.socialplay.gpark.data.model.community.PostCommentImageUpload
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.extension.collectWithTimeout
import com.socialplay.gpark.util.extension.dropAtWithResult
import com.socialplay.gpark.util.extension.insertAt
import com.socialplay.gpark.util.extension.moveTo
import com.socialplay.gpark.util.extension.replaceSingle
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.single
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import java.io.File

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/06/26
 *     desc   :
 * </pre>
 */
data class ArticleCommentInputState(
    val emojis: List<GifEmojiInfoWrapper>? = null,
    val images: List<PostCommentImageUpload>? = null,
    val quickInputTags: List<QuickInputTag>? = null,
    val toast: ToastData = ToastData.EMPTY,
    val delayKeyboard: Boolean? = null,
    val permissionCallback: Pair<Int, Long>? = null,
) : MavericksState {
    val needUploadImage get() = images?.any { it.url.isNullOrEmpty() } == true
}

class ArticleCommentInputViewModel(
    initialState: ArticleCommentInputState,
    private val uploadFileInteractor: UploadFileInteractor,
    private val tTaiInteractor: TTaiInteractor,
) : BaseViewModel<ArticleCommentInputState>(initialState) {

    val imageCount get() = oldState.images?.size ?: 0
    val emojiCount get() = oldState.emojis?.size ?: 0

    val isUploadingImage: Boolean
        get() {
            return oldState.needUploadImage
        }
    val images get() = oldState.images?.map { it.toPostCommentImage() }

    private val uploadPool = ConcurrentSet<String>()
    private val uploadObserver = HashMap<String, ArticleCommentImageUploadProgressListener>()

    val quickInputTags: List<QuickInputTag>?
        get() = oldState.quickInputTags

    fun init(images: List<PostCommentImageUpload>?) {
        setState { copy(images = images) }
    }

    fun initQuickInput(contentType: Int) = viewModelScope.launch {
        val quickInputConfig =
            tTaiInteractor.getTTaiWithTypeV3<QuickInputConfig>(TTaiKV.ID_QUICK_INPUT_CONFIG)
                .collectWithTimeout(1000) ?: QuickInputConfig.defaultConfig()
        if (quickInputConfig != null) {
            if (contentType == ArticleCommentInputDialogParams.CONTENT_TYPE_PGC_DETAIL || contentType == ArticleCommentInputDialogParams.CONTENT_TYPE_UGC_DETAIL) {
                setState { copy(quickInputTags = quickInputConfig.gameDetail) }
            } else if (contentType == ArticleCommentInputDialogParams.CONTENT_TYPE_UGC_DESIGN_DETAIL) {
                setState { copy(quickInputTags = quickInputConfig.libraryDetail) }
            } else if (contentType == ArticleCommentInputDialogParams.CONTENT_TYPE_POST_DETAIL){
                setState { copy(quickInputTags = quickInputConfig.postDetail) }
            }
        }
    }

    fun addImages(images: List<LocalMedia>) = withState { s ->
        val newTaskId = "comment_image" + SystemClock.elapsedRealtime()
        val filterImages = images.filter {
            !it.availablePath.isNullOrEmpty()
        }
        val filterFiles = filterImages.map {
            File(it.availablePath)
        }
        val uploadImages = filterFiles.mapIndexed { index, item ->
            val result = PostCommentImageUpload(
                "${newTaskId}_${index}",
                item.absolutePath,
                newTaskId,
                "${newTaskId}_${item.absolutePath}",
                null,
                filterImages[index].width,
                filterImages[index].height
            )
            result
        }
        val newImages = s.images.insertAt(-1, uploadImages)
        setState { copy(images = newImages) }
        uploadImageIfNeed()
    }

    private fun uploadImageIfNeed(): Unit = withState { s ->
        if (!s.needUploadImage || uploadPool.isNotEmpty()) return@withState
        val toUpload = s.images?.firstOrNull { it.url.isNullOrEmpty() } ?: return@withState
        uploadPool.add(toUpload.objectKey)
        viewModelScope.launch {
            val uploadResult = uploadFileInteractor.uploadSingle(
                UploadFileInteractor.BIZ_CODE_COMMUNITY,
                File(toUpload.path),
                taskId = toUpload.taskId,
                progressHandler = { taskId, _, _, path, percent ->
                    runMainThread {
                        val listener = uploadObserver["${taskId}_${path}"] ?: return@runMainThread
                        val progress = (percent * 100).toInt()
                        listener(progress)
                    }
                },
                isCancelled = { taskId, filePath ->
                    !uploadPool.contains("${taskId}_${filePath}")
                }
            ).single()
            val uploadData = uploadResult.data
            if (uploadResult.succeeded && uploadData != null) {
                if (!uploadData.isCancelled) {
                    uploadImageSuccess(
                        "${uploadData.taskId}_${uploadData.filePath}",
                        uploadData.filePath,
                        uploadData.url
                    )
                }
                uploadImageIfNeed()
            } else {
                uploadImageFailed(uploadResult.message)
            }
        }
    }

    fun deleteImage(image: PostCommentImageUpload, position: Int) = withState { s ->
        uploadPool.remove(image.objectKey)
        uploadObserver.remove(image.objectKey)
        val (ok, _, newImages) = s.images.dropAtWithResult(position, image)
        if (!ok) return@withState
        setState {
            copy(images = newImages)
        }
    }

    fun moveImage(from: Int, to: Int) = withState { s ->
        val newImages = s.images.moveTo(from, to)
        setState {
            copy(images = newImages)
        }
    }

    private fun uploadImageFailed(message: String?) = withState { s ->
        uploadPool.clear()
        uploadObserver.clear()
        if (s.images.isNullOrEmpty()) {
            setState { copy(toast = toast.toMsg(message)) }
            return@withState
        }
        val newImages = s.images.filter {
            !it.url.isNullOrEmpty()
        }
        if (s.images.size == newImages.size) {
            setState { copy(toast = toast.toMsg(message)) }
            return@withState
        }
        setState {
            copy(images = newImages, toast = toast.toMsg(message))
        }
    }

    private fun uploadImageSuccess(
        key: String,
        localPath: String,
        remotePath: String
    ) = withState { s ->
        uploadObserver.remove(key)
        if (s.images.isNullOrEmpty()) return@withState
        val (ok, newImages) = s.images.replaceSingle({
            it.objectKey == key && it.path == localPath
        }) {
            it.copy(url = remotePath)
        }
        if (!ok) return@withState
        setState {
            copy(images = newImages)
        }
    }

    fun addEmoji(emoji: GifEmojiInfo) = withState { s ->
        val ts = SystemClock.elapsedRealtime()
        val newEmoji = GifEmojiInfoWrapper("${emoji.code}-$ts", emoji)
        val newEmojis = s.emojis.insertAt(-1, newEmoji) ?: return@withState
        setState {
            copy(emojis = newEmojis)
        }
    }

    fun deleteEmoji(emoji: GifEmojiInfoWrapper, position: Int) = withState { s ->
        val (ok, _, newEmojis) = s.emojis.dropAtWithResult(position, emoji)
        if (!ok) return@withState
        setState {
            copy(emojis = newEmojis)
        }
    }

    fun addUploadListener(key: String, listener: ArticleCommentImageUploadProgressListener) {
        uploadObserver[key] = listener
    }

    fun removeUploadListener(key: String) {
        uploadObserver.remove(key)
    }

    fun clear() {
        setState {
            copy(emojis = null, images = null)
        }
    }

    fun delayKeyboard() = viewModelScope.launch {
        delay(300)
        setState { copy(delayKeyboard = if (delayKeyboard == null) false else !delayKeyboard) }
    }

    fun handlePermissionResult(result: Int) {
        val ts = SystemClock.elapsedRealtime()
        setState { copy(permissionCallback = result to ts) }
    }

    override fun onCleared() {
        uploadPool.clear()
        uploadObserver.clear()
        super.onCleared()
    }

    companion object :
        KoinViewModelFactory<ArticleCommentInputViewModel, ArticleCommentInputState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: ArticleCommentInputState
        ): ArticleCommentInputViewModel {
            return ArticleCommentInputViewModel(state, get(), get())
        }
    }
}