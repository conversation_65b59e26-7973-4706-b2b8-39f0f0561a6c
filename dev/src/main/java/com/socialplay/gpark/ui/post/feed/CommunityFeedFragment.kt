package com.socialplay.gpark.ui.post.feed

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.withState
import com.bumptech.glide.RequestManager
import com.google.android.exoplayer2.MediaItem
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.FriendInteractor
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.MomentLocalTSStartUp
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostMomentCard
import com.socialplay.gpark.data.model.post.event.PostMetaData
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.databinding.FragmentCommunityFeedBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.editor.RoleGameTryOn
import com.socialplay.gpark.function.exoplayer.SharedVideoPlayerControllerInteractor
import com.socialplay.gpark.function.mw.TSLaunchWrapper
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchOptionAppendTsStartUp
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.post.CommunityUtil
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.IGlobalShareCallback
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.statusbar.StatusBarStateProvider
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.imgpre.v2.OpenPreviewBuilder
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModel
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelState
import com.socialplay.gpark.ui.post.v2.PostDetailFragment
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.view.LoadingView
import com.socialplay.gpark.ui.view.TitleBarLayout
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.ui.view.scroll.ScrollLinearLayoutManager
import com.socialplay.gpark.ui.view.video.FeedVideoHelper
import com.socialplay.gpark.ui.view.video.FeedVideoHelper.SCENE_COMMUNITY_TAB_FEED
import com.socialplay.gpark.ui.view.video.VideoPageListView
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.clearFragmentResultListenersByHostFragment
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setFragmentResultListenersByHostFragment
import com.socialplay.gpark.util.extension.visible
import org.koin.android.ext.android.inject
import org.koin.core.context.GlobalContext


/**
 * Created by bo.li
 * Date: 2023/9/14
 * Desc:
 */
abstract class CommunityFeedFragment :
    BaseRecyclerViewFragment<FragmentCommunityFeedBinding>(R.layout.fragment_community_feed),
    ICommunityFeedListener, StatusBarStateProvider, IGlobalShareCallback {

    protected abstract val feedViewModel: BaseCommunityFeedViewModel<ICommunityFeedModelState>
    val accountInteractor: AccountInteractor by lazy {
        GlobalContext.get().get()
    }
    private lateinit var epoxyVisibilityTracker: EpoxyVisibilityTracker
    protected lateinit var layoutManager: ScrollLinearLayoutManager


    /**
     * 现在单个帖子被分成了多个item来展示, 所以单个帖子是否可见, 需要参考所有的item的可见性
     * 实际写代码时, 也不用所有的item都需要监听可见性, 如果帖子内容不足一屏的话, 其实只需要监听第一个item和最后一个item即可
     * 但帖子内容很多, 手机屏幕很小时, 就需要监听多个item的显示情况
     * 可见帖子map<postId, <每个item是否可见>
     */
    private val visiblePostMap: HashMap<String, CommunityFeedVisibleInfoV2> = hashMapOf()

    private val tsLaunchWrapper by lazy { TSLaunchWrapper(this) }

    private val friendInteractor by inject<FriendInteractor>()

    override fun isStatusBarDarkText() = true

    /**
     * 展示顶部占位（feed底栏兼容视频流用）
     */
    open fun placeholderVisible() = false

    /**
     * 展示状态栏占位
     */
    open fun statusBarVisible() = false

    open fun initTitleBar(titleBar: TitleBarLayout) {
        titleBar.isVisible = false
    }

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvFeed

    private var videoTimeMap = hashMapOf<String, Long>()

    private val scrollListener = object : RecyclerView.OnScrollListener() {
        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            //滑动停止后，判断视频播放
            if (newState == RecyclerView.SCROLL_STATE_IDLE && isResumed) {
                FeedVideoHelper.notifyVideoPlay(SCENE_COMMUNITY_TAB_FEED)
            }
        }
    }

    companion object {
        const val POST_TAG = "check_tag"

        const val TAB_SOURCE_COMMUNITY = 1
        const val TAB_SOURCE_PROFILE = 2
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        feedViewModel.apiMonitor(
            this,
            ICommunityFeedModelState::loadMore
        )
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentCommunityFeedBinding? {
        return FragmentCommunityFeedBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.root.needIntercept = needInterceptParent
        binding.rvFeed.skipIntercept = true

        initLoadingView(binding.loadingFeed)
        epoxyVisibilityTracker = EpoxyVisibilityTracker().apply {
            /**
             * 不设置会导致 [com.airbnb.epoxy.EpoxyModel.onVisibilityStateChanged]
             * 缺少 [com.airbnb.epoxy.VisibilityState.PARTIAL_IMPRESSION_VISIBLE] 情况的回调，
             * recyclerview中所有model都公用此值，改动需多加注意
             */
//            partialImpressionThresholdPercentage = 10
            attach(recyclerView)
        }

        initTitleBar(binding.titleBar)
        binding.vTabBarPlaceholder.visible(placeholderVisible())
        binding.statusBar.visible(statusBarVisible())

        layoutManager = ScrollLinearLayoutManager(requireContext())
        recyclerView.layoutManager = layoutManager
        recyclerView.addOnScrollListener(scrollListener)
        // 处理跳转页面时carousel不保存滚动位置的问题，好像还没有正规的解决方案 [issue] https://github.com/airbnb/epoxy/issues/1297
        recyclerView.setDelayMsWhenRemovingAdapterOnDetach(1)
        feedViewModel.registerAsyncErrorToast(ICommunityFeedModelState::loadMore)
        feedViewModel.onAsync(
            ICommunityFeedModelState::notifyCheckVideo,
            onFail = {},
            onSuccess = {
                FeedVideoHelper.notifyVideoPlay(SCENE_COMMUNITY_TAB_FEED)
            })
        feedViewModel.setupRefreshLoading(
            ICommunityFeedModelState::refresh,
            binding.loadingFeed,
            binding.refresh,
            emptyMsgBlock = ::getEmptyTip
        ) {
            refreshFeed()
            refreshParent()
        }
        friendInteractor.friendList.observe(viewLifecycleOwner) { friendList ->
            if (friendList.isNullOrEmpty()) {
                return@observe
            }
            feedViewModel.updateFriendState(friendList)
        }
    }

    override fun calculateVideoPlay(
        resId: String,
        url: String,
        position: Int,
        videoView: VideoPageListView,
        percentVisibleHeight: Float
    ) {
        FeedVideoHelper.handleVideoPlay(
            SCENE_COMMUNITY_TAB_FEED,
            resId,
            url,
            position,
            percentVisibleHeight,
            videoView
        )
    }

    override fun changeLike(postMetaData: PostMetaData){
        feedViewModel.changePostLike(postMetaData,likeLocation)
        if (!postMetaData.isLike) {
            DialogShowManager.triggerLike(this)
        }
    }

    private fun clearVisibleMap() {
        visiblePostMap.clear()
    }

    // todo 埋点参数再多的话，考虑传一个map
    override fun onItemVisibilityChange(
        item: CommunityFeedInfo,
        itemUniqueKey: String,
        visible: Boolean
    ) {
        val needPut = !visiblePostMap.contains(item.postId)
        val info = visiblePostMap[item.postId] ?: CommunityFeedVisibleInfoV2()
        val lastVisible = info.visible()
        info.itemsVisible[itemUniqueKey] = visible
        val newVisible = info.visible()
        if (!lastVisible && newVisible) {
            Analytics.track(EventConstants.EVENT_COMMUNITY_POST_SHOW) {
                getFeedTag()?.let { put(EventParamConstants.KEY_TAG, it) }
                put(EventParamConstants.KEY_POSTID, item.postId)
                put(
                    EventParamConstants.COMMUNITY_POST_TYPE,
                    if (item.top) EventParamConstants.ANALYTIC_COMMUNITY_TYPE_PIN else EventParamConstants.ANALYTIC_COMMUNITY_TYPE_NORMAL
                )
                item.tagList?.map { it.tagId }?.let { put(EventParamConstants.KEY_TAG_LIST, it) }
                put("source", tabSource)
                put(EventParamConstants.KEY_REVIEW_TAG, item.reviewStatus2TrackParam)
            }
            val outfit = item.outfit
            if (outfit != null) {
                Analytics.track(
                    EventConstants.POST_LIST_OUTFIT_SHOW,
                    "resid" to item.postId,
                    "shareid" to outfit.roleId,
                    "tag" to item.tagList?.map { it.tagId }?.joinToString(",").orEmpty(),
                    "source" to "0"
                )
            }
        }
        if (needPut) {
            visiblePostMap[item.postId] = info
        }
    }

    protected fun setDetailFragmentResultListener() {
        setFragmentResultListenersByHostFragment(
            viewLifecycleOwner,
            PostDetailFragment.KEY_ACTION_POST to { _, bundle ->
                val postId = bundle.getString(PostDetailFragment.KEY_PARAM_POST_ID) ?: return@to
                val deleted = bundle.getBoolean(PostDetailFragment.KEY_PARAM_IS_DELETE)
                val opinion = bundle.getInt(PostDetailFragment.KEY_PARAM_OPINION)
                val likeCount = bundle.getLong(PostDetailFragment.KEY_PARAM_LIKE_COUNT)
                val commentCount = bundle.getLong(PostDetailFragment.KEY_PARAM_COMMENT_COUNT)
                val shareCount = bundle.getLong(PostDetailFragment.KEY_PARAM_SHARE_COUNT)
                feedViewModel.handleChangeFromDetail(
                    postId,
                    deleted,
                    opinion,
                    likeCount,
                    commentCount,
                    shareCount
                )
            })
    }

    override fun goGameDetail(item: CommunityFeedInfo, gameCard: PostCardInfo) {
        CommunityUtil.goGameDetail(
            this@CommunityFeedFragment,
            gameCard,
            CategoryId.COMMUNITY_GAME_CARD
        )
    }

    override fun openImagePreview(url: List<String>, imageViews: List<ImageView>, position: Int) {
        OpenPreviewBuilder(this@CommunityFeedFragment)
            .setImageUrls(url)
            .setShowSaveBtn(true)
            .setClickPosition(position)
            .setShowSaveBtn(true)
            .setClickViews(imageViews)
            .show()
    }

    override fun showVideoDetail(postId: String, url: String) {
        goFullScreenPage(postId, url)
    }

    override fun showRoomDialog(item: CommunityFeedInfo) {
        item.userStatus?.let {
            CommunityUtil.showRoomDialog(
                this@CommunityFeedFragment,
                it,
                item.uid,
                item.nickname,
                CategoryId.COMMUNITY_JOIN_ROOM
            )
        }
    }

    /**
     * 自定义列表布局与需要的数据
     */
    protected open fun getFeedController(): EpoxyController = simpleController(
        feedViewModel,
        ICommunityFeedModelState::list,
        ICommunityFeedModelState::loadMore,
    ) { list, loadMore ->
        val screenWidth = ScreenUtil.getScreenWidth(requireContext())
        val contentWidth = screenWidth - 32.dp
        list.forEachIndexed { index, communityFeedInfo ->
            val playLikeAnim = feedViewModel.playLikeAnimPostSet.contains(communityFeedInfo.postId)
            if (playLikeAnim) {
                feedViewModel.playLikeAnimPostSet.remove(communityFeedInfo.postId)
            }
            communityFeed(
                contentWidth,
                communityFeedInfo,
                index,
                showUserStatus,
                showPin,
                firstFeedTop,
                this@CommunityFeedFragment,
                playLikeAnim,
                accountInteractor.curUuid,
                this@CommunityFeedFragment
            )
        }
        if (list.isNotEmpty()) {
            loadMoreFooter(loadMore, showEnd = true) {
                loadMoreFeed()
            }
        }
    }

    override fun epoxyController(): EpoxyController = getFeedController()

    private fun goFullScreenPage(postId: String, videoUrl: String) {
        val playerController by inject<SharedVideoPlayerControllerInteractor>()
        val toRestoreUri = MediaItem.fromUri(videoUrl)
        playerController.setMediaItem(toRestoreUri)
        playerController.changeMuteState(false)
        MetaRouter.Video.fullScreenPlayer(
            this@CommunityFeedFragment,
            "feed",
            bundleOf("postId" to postId)
        )
    }

    override fun onVideoStateChange(postId: String, resume: Boolean) {
        if (resume) {
            videoTimeMap[postId] = System.currentTimeMillis()
        } else {
            videoTimeMap[postId]?.let { resumeTime ->
                val duration = System.currentTimeMillis() - resumeTime
                if (duration > 0) {
                    Analytics.track(EventConstants.EVENT_FEED_POST_VIDEO_PLAY_TIME) {
                        put("playtime", duration)
                        put("postid", postId)
                    }
                }
            }
        }
    }

    override fun showShareDialog(item: CommunityFeedInfo) {
        GlobalShareDialog.show(
            childFragmentManager,
            ShareRawData.post(item)
        )
    }

    override fun goOutfit(item: CommunityFeedInfo) {
        item.outfit?.let { outfit ->
            feedViewModel.visitOutfitCard(item, outfit)
            MetaRouter.MobileEditor.fullScreenRole(
                requireContext(),
                FullScreenEditorActivityArgs(
                    categoryId = CategoryId.OUTFIT_SHARE_POST_FEED,
                    tryOn = RoleGameTryOn.create(
                        tryOnUserId = item.uid,
                        from = RoleGameTryOn.FROM_COMMUNITY_POST,
                        roleId = outfit.roleId,
                        allowTryOn = true
                    )
                )
            )
        }
    }

    override fun goMoment(item: PostMomentCard) {
        tsLaunchWrapper.callLaunch {
            val info = createTSGameDetailInfo(item.gameId ?: "", "", item.templateName ?: "")
            val resIdBean = ResIdBean.newInstance().setCategoryID(CategoryId.PLOT_PAGE)
                .setGameId(item.gameId).setTypeID("${item.templateId}")
            val tsLaunchParams = TSLaunchParams(info, resIdBean, option = TSLaunchOptionAppendTsStartUp())
            tsLaunchParams.tsStartUp = item.extendParams ?: ""
            tsLaunchParams.localTsStartUp =
                MomentLocalTSStartUp("2", "${item.templateId}").toMap()
            tsLaunchParams.setGameUniqueKey("${item.gameId}_${item.templateId}")
            launchPlot(requireContext(), tsLaunchParams)
        }
    }

    override fun goUgcDesign(item: CommunityFeedInfo) {
        item.ugcDesign?.let {
            MetaRouter.UgcDesign.detail(this, it.itemId, CategoryId.UGC_DESIGN_POST_FEED)
        }
    }

    override fun onResume() {
        super.onResume()
        setDetailFragmentResultListener()
        withState(feedViewModel) {
            if (it.refresh is Uninitialized) {
                refreshFeed()
            } else {
                notifyRvCheck()
            }
        }
    }

    private fun notifyRvCheck() {
        epoxyVisibilityTracker.clearVisibilityStates()
        epoxyVisibilityTracker.requestVisibilityCheck()
        feedViewModel.notifyCheckVideo()
    }

    override fun onPause() {
        clearVisibleMap()
        FeedVideoHelper.clearPlayingItem(SCENE_COMMUNITY_TAB_FEED)
        super.onPause()
    }

    override fun onDestroyView() {
        if (useVideoFunc) {
            FeedVideoHelper.releaseByScene(SCENE_COMMUNITY_TAB_FEED)
        }
        clearFragmentResultListenersByHostFragment(PostDetailFragment.KEY_ACTION_POST)
        epoxyVisibilityTracker.detach(recyclerView)
        super.onDestroyView()
    }

    protected fun refreshFeed() {
        clearVisibleMap()
        FeedVideoHelper.clearPlayingItem(SCENE_COMMUNITY_TAB_FEED)
        refreshPage()
    }

    /**
     * 帖子是否展示用户状态
     */
    protected abstract val showUserStatus: Boolean

    /**
     * 点赞埋点用，来源
     */
    protected abstract val likeLocation: String

    /**
     * 帖子是否展示置顶标志
     */
    protected abstract val showPin: Boolean

    /**
     * 帖子是否展示全部tag列表
     */
    protected abstract val showTagList: Boolean

    /**
     * 初始化loadingView
     */
    protected abstract fun initLoadingView(loading: LoadingView)

    /**
     * 埋点用，仅feed底栏需要
     */
    protected abstract fun getFeedTag(): String?

    /**
     * 刷新整个页面
     */
    protected abstract fun refreshPage()

    /**
     * feed列表加载更多
     */
    protected abstract fun loadMoreFeed()

    /**
     * 页面空白占位文字
     */
    open fun getEmptyTip(): String = getString(R.string.footer_load_end)

    /**
     * 刷新父fragment
     */
    protected abstract fun refreshParent()

    /**
     * 是否管理视频功能，推荐父fragment没有重复使用此fragment（viewPager等情况）时使用
     * @return [Boolean]
     * true: onDestroyView时释放视频资源
     * false: onDestroyView时不释放视频资源，可交给父fragment管理
     */
    protected abstract val useVideoFunc: Boolean

    override fun getPageName(): String = PageNameConstants.FRAGMENT_COMMUNITY_FEED

    protected open val needInterceptParent: Boolean = false

    override fun clickLabel(data: Pair<Int, LabelInfo?>) {
        UserLabelView.showDescDialog(this, data)
    }

    override fun changeFollow(item: CommunityFeedInfo, uuid: String, follow: Boolean){
        feedViewModel.followUser(item, uuid, follow)
    }

    override fun getGlideOrNull(): RequestManager? {
        return glide
    }

    protected open val tabSource: Int = TAB_SOURCE_COMMUNITY

    override fun onShareCountIncrease(data: ShareRawData) {
        if (data.scene == ShareHelper.SCENE_POST_DETAIL && data.postDetail != null) {
            feedViewModel.addShareCount(data.postDetail.postId)
        }
    }

    protected open val firstFeedTop get() = dp(16)
}