package com.socialplay.gpark.ui.post.feed.base

import android.util.SparseArray
import androidx.core.util.forEach
import androidx.core.util.isNotEmpty
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Success
import com.meta.box.biz.friend.internal.model.FriendStatus
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.ApiDataException
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.account.UserFollowEvent
import com.socialplay.gpark.data.model.community.PostFeedCard
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.OpinionRequestBody
import com.socialplay.gpark.data.model.post.PostStyleCard
import com.socialplay.gpark.data.model.post.event.GameMetaData
import com.socialplay.gpark.data.model.post.event.GameMetaDataUpdateEvent
import com.socialplay.gpark.data.model.post.event.PostMetaData
import com.socialplay.gpark.data.model.post.event.PostMetaDataUpdateEvent
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.util.ToastError
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe

/**
 * @exception [java.lang.reflect.InvocationTargetException] 继承BaseCommunityFeedViewModel时，init代码块里加东西，可能会导致fragmentViewModel找不到类
 */
abstract class BaseCommunityFeedViewModel<S>(
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    initialState: S
) : BaseViewModel<S>(initialState) where S : ICommunityFeedModelState {

    init {
        EventBus.getDefault().register(this)
    }

    val playLikeAnimPostSet: MutableSet<String> = mutableSetOf()

    fun updateFriendState(friendInfoList: List<FriendInfo>) {
        val feedInfoList = oldState.refresh.invoke() ?: return

        val map: MutableMap<String, FriendInfo> = mutableMapOf()
        friendInfoList.forEach {
            map[it.uuid] = it
        }
        val updateRecord: SparseArray<FriendStatus> = SparseArray()
        feedInfoList.forEachIndexed { index, communityFeedInfo ->
            val friendStatus = map[communityFeedInfo.uid]?.status
            val friendStatusFlag = friendStatus?.status
            if (friendStatus != null) {
                if (friendStatusFlag != communityFeedInfo.userStatus?.status
                    || friendStatus.gameStatus != communityFeedInfo.userStatus?.gameStatus
                ) {
                    updateRecord.put(index, friendStatus)
                }
            }
        }
        if (updateRecord.isNotEmpty()) {
            val newFeedInfoList = feedInfoList.toMutableList()
            updateRecord.forEach { index, friendStatus ->
                newFeedInfoList[index] = newFeedInfoList[index].copy(
                    userStatus = friendStatus
                )
            }
            setState {
                updateFeedData(newFeedInfoList) as S
            }
        }
    }

    /**
     * 点赞，取消点赞
     */
    fun changePostLike(post: PostMetaData, location: String) {
        val toLike = !post.isLike
        Analytics.track(EventConstants.EVENT_POST_LIKE_CLICK) {
            put(EventParamConstants.KEY_POSTID, post.postId)
            put(EventParamConstants.KEY_LOCATION, location)
            put(
                EventParamConstants.KEY_TYPE,
                if (toLike) EventParamConstants.V_LIKE else EventParamConstants.V_UNLIKE
            )
            post.tagList?.map { it.tagId }?.let { put(EventParamConstants.KEY_TAG_LIST, it) }
        }
        viewModelScope.launch {
            val oldState = awaitState()
            if (oldState.refresh !is Success) return@launch

            // 通过 onPostMetaDataUpdateEvent 方法接收事件更新列表
            // 先直接更新点赞状态, 如果点赞请求失败了, 再恢复之前的点赞状态
            EventBus.getDefault().post(
                PostMetaDataUpdateEvent(
                    post.copy(
                        isLike = toLike,
                        likeCount = (if (toLike) {
                            post.likeCount + 1
                        } else {
                            post.likeCount - 1
                        }).coerceAtLeast(0)
                    )
                )
            )
            try {
                val succeed = repository.saveOpinion(
                    OpinionRequestBody.postLike(post.postId, toLike)
                ).invoke()
                if (succeed) {
                    return@launch
                } else {
                    throw ApiDataException(Boolean::class)
                }
            } catch (e: Throwable) {
                setState {
                    toast(ToastError(e)) as S
                }
            }
            // 通过 onPostMetaDataUpdateEvent 方法接收事件更新列表
            // 点赞请求失败, 恢复之前的点赞状态
            EventBus.getDefault().post(PostMetaDataUpdateEvent(post))
        }
    }

    /**
     * 从帖子详情回来，更新feed数据
     */
    fun handleChangeFromDetail(
        postId: String,
        deleted: Boolean,
        opinion: Int,
        likeCount: Long,
        commentCount: Long,
        shareCount: Long
    ) {
        withState { oldState ->
            if (oldState.refresh !is Success || oldState.refresh().isNullOrEmpty()) return@withState
            val operateList = ArrayList(oldState.refresh.invoke())
            if (deleted) {
                operateList.removeAll { it.postId == postId }
                setState {
                    updateFeedData(operateList.toList()) as S
                }
            } else {
                val oldIndex = operateList.indexOf(operateList.find { it.postId == postId })
                if (oldIndex in 0 until operateList.size) {
                    val oldItem = operateList[oldIndex]
                    if (oldItem.likeCount != likeCount || oldItem.opinion != opinion || oldItem.commentCount != commentCount || oldItem.shareCount != shareCount) {
                        operateList[oldIndex] = oldItem.copy(
                            likeCount = likeCount,
                            opinion = opinion,
                            commentCount = commentCount,
                            shareCount = shareCount
                        )
                        setState {
                            updateFeedData(operateList.toList()) as S
                        }
                    }
                }
            }
        }
    }

    fun visitOutfitCard(item: CommunityFeedInfo, outfit: PostStyleCard) {
        Analytics.track(
            EventConstants.POST_LIST_OUTFIT_CLICK,
            "uuid" to item.uid,
            "resid" to item.postId,
            "tag" to item.tagList?.map { it.tagId }?.joinToString(",").orEmpty(),
            "shareid" to outfit.roleId,
            "source" to "0"
        )
        if (!accountInteractor.isMe(item.uid)) {
            GlobalScope.launch {
                runCatching {
                    repository.visitPostOutfitCard(item.postId, outfit.roleId).invoke()
                }
            }
        }
    }

    fun addShareCount(postId: String) = viewModelScope.launch {
        val operateList = ArrayList(oldState.refresh.invoke() ?: emptyList())
        val oldIndex = operateList.indexOf(operateList.find { it.postId == postId })
        if (oldIndex in 0 until operateList.size) {
            val oldItem = operateList[oldIndex]
            operateList[oldIndex] = oldItem.copy(
                shareCount = oldItem.shareCount + 1
            )
            setState {
                updateFeedData(operateList.toList()) as S
            }
        }
    }

    fun followUser(communityFeed: CommunityFeedInfo, uuid: String, follow: Boolean) = withState { s ->
        Analytics.track(EventConstants.EVENT_FOLLOW_CLICK) {
            put(EventParamConstants.KEY_USERID, uuid)
            put(EventParamConstants.KEY_LOCATION, EventParamConstants.LOCATION_FOLLOW_POST_FEED)
            put(
                EventParamConstants.KEY_TYPE,
                if (follow) EventParamConstants.TYPE_FOLLOW else EventParamConstants.TYPE_UNFOLLOW
            )
        }
        if (follow) {
            repository.followUser(uuid)
        } else {
            repository.unfollowUser(uuid)
        }.execute { result ->
            when (result) {
                is Success -> {
                    EventBus.getDefault().post(
                        UserFollowEvent(
                            uuid,
                            follow,
                            UserFollowEvent.FROM_POST_DETAIL
                        )
                    )
                    // 这里不刷新, 通过 onUserFollowEvent 方法刷新
                    s
                }

                is Fail -> {
                    toast(toastMsg.toError(result)) as S
                }

                else -> {
                    this
                }
            }
        }
    }

    /**
     * 通知检查feed播放视频状态
     */
    fun notifyCheckVideo() {
        suspend {
            delay(1000)
            System.currentTimeMillis()
        }.execute {
            checkVideo(it) as S
        }
    }

    private fun updateFollowStatus(uuid:String, followStatus: Boolean) = withState { s ->
        val oldList = s.list
        val newList = oldList.toMutableList()

        // 帖子列表同一个人发了多个帖子时, 用户的关注状态都需要更新
        oldList.forEachIndexed { index, item ->
            if (item.uid == uuid) {
                newList[index] = item.copy(
                    followStatus = followStatus
                )
            }
        }

        setState {
            updateFeedData(newList) as S
        }
    }

    @Subscribe
    fun onUserFollowEvent(event: UserFollowEvent) {
        updateFollowStatus(event.uuid, event.followStatus)
    }

    private fun updatePostMetaData(metaData: PostMetaData) {
        val operateList = ArrayList(oldState.list)
        val oldIndex = operateList.indexOfFirst{
            it.postId == metaData.postId
        }
        if (oldIndex in 0 until operateList.size) {
            val oldItem = operateList[oldIndex]
            operateList[oldIndex] = oldItem.copy(
                opinion = CommunityFeedInfo.getOpinion(metaData.isLike),
                likeCount = metaData.likeCount,
                commentCount = metaData.commentCount,
                shareCount = metaData.shareCount,
            )
        }
        setState {
            updateFeedData(operateList.toList()) as S
        }
    }

    @Subscribe
    fun onPostMetaDataUpdateEvent(event: PostMetaDataUpdateEvent){
        updatePostMetaData(event.metaData)
    }

    private fun updateGameMetaData(metaData: GameMetaData) {
        val operateList = ArrayList(oldState.list)
        val toUpdateIndex = operateList.withIndex().filter { item ->
            item.value.existsGameCard(metaData.gameId)
        }.map { it.index }
        if (toUpdateIndex.isNotEmpty()) {
            toUpdateIndex.forEach { updateIndex ->
                if (updateIndex in 0 until operateList.size) {
                    val oldItem = operateList[updateIndex]
                    if (!oldItem.gameCardList.isNullOrEmpty()) {
                        val newGameCardList = oldItem.gameCardList.toMutableList()
                        oldItem.gameCardList.forEachIndexed { index, gameCard ->
                            if (gameCard.gameId == metaData.gameId) {
                                newGameCardList[index] = gameCard.copy(
                                    likeCount = metaData.likeCount,
                                    player = metaData.playCount,
                                )
                            }
                        }
                        operateList[updateIndex] = oldItem.copy(
                            gameCardList = newGameCardList,
                        )
                    }
                }
            }
        }
        setState {
            updateFeedData(operateList.toList()) as S
        }
    }

    @Subscribe
    fun onGameMetaDataUpdateEvent(event: GameMetaDataUpdateEvent) {
        updateGameMetaData(event.metaData)
    }

    override fun onCleared() {
        super.onCleared()
        EventBus.getDefault().unregister(this)
    }
}