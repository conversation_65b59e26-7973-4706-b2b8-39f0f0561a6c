package com.socialplay.gpark.ui.post.feed.tag

import android.os.Bundle
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.airbnb.epoxy.EpoxyController
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.community.CommentInfo
import com.socialplay.gpark.data.model.community.PostFeedCard
import com.socialplay.gpark.data.model.community.RecommendFeedCard
import com.socialplay.gpark.data.model.community.Topic
import com.socialplay.gpark.data.model.community.TopicFeedCard
import com.socialplay.gpark.data.model.community.UserFeedCard
import com.socialplay.gpark.data.model.creator.CreatorUgcGame
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.request.PostTagFeedRequest
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.views.empty
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.post.feed.CommunityFeedFragmentV2
import com.socialplay.gpark.ui.post.feed.CommunityFollowFrom
import com.socialplay.gpark.ui.post.feed.CommunitySubListTitle
import com.socialplay.gpark.ui.post.feed.ICommunityTopicListenerV2
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModelV2
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelStateV2
import com.socialplay.gpark.ui.post.feed.communityFeedV2
import com.socialplay.gpark.ui.post.feed.communityFollowedWorks
import com.socialplay.gpark.ui.post.feed.communityFollowingUsers
import com.socialplay.gpark.ui.post.feed.communityRecommendCreatorV2
import com.socialplay.gpark.ui.post.feed.communityTopicsV2
import com.socialplay.gpark.ui.post.tab.CommunityFeedTabModelState
import com.socialplay.gpark.ui.post.tab.CommunityFeedTabViewModel
import com.socialplay.gpark.ui.view.LoadingView
import com.socialplay.gpark.ui.view.video.FeedVideoHelper
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import kotlin.reflect.KProperty1

class FollowingCommunityFeedFragment : CommunityFeedFragmentV2(), ICommunityTopicListenerV2 {

    private val viewModel: FollowingCommunityFeedViewModel by fragmentViewModel()
    private val args by args<TagCommunityFeedFragmentArgs>()
    private val parentViewModel: CommunityFeedTabViewModel by parentFragmentViewModel()
    private val scrollTargetPosition = 2

    private val feedOrder by lazy { if (PandoraToggle.communityNewOrder) PostTagFeedRequest.ORDER_TYPE_CUSTOM else PostTagFeedRequest.ORDER_TYPE_NEWEST }

    override val feedViewModel: BaseCommunityFeedViewModelV2<ICommunityFeedModelStateV2>
        get() = viewModel as BaseCommunityFeedViewModelV2<ICommunityFeedModelStateV2>

    companion object {
        fun newInstance(args: TagCommunityFeedFragmentArgs): FollowingCommunityFeedFragment {
            return FollowingCommunityFeedFragment().apply {
                arguments = args.asMavericksArgs()
            }
        }
    }

    private var isLoadFinishTrack = false
    private var isLoadSuccess = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        parentViewModel.onEach(
            CommunityFeedTabModelState::currentUid,
            uniqueOnly()
        ) { uid ->
            refreshFeed()
        }
        parentViewModel.onEach(CommunityFeedTabModelState::scrollFeed, uniqueOnly()) {
            Timber.d("checkcheck_refresh, $it")
            if (isResumed) {
                scrollToTop()
            }
        }

        viewModel.onEach(
            FollowingCommunityFeedModelState::followUserList,
            FollowingCommunityFeedModelState::followGames,
            FollowingCommunityFeedModelState::list,
        ) { followUserList, followGames, list ->
            isLoadSuccess = !followUserList.isNullOrEmpty()
                    || !followGames.isNullOrEmpty()
                    || list.isNotEmpty()
            if (isLoadSuccess && !isLoadFinishTrack && isResumed) {
                isLoadFinishTrack = true
                Analytics.track(EventConstants.EVENT_COMMUNITY_MENU_SHOW) {
                    put("menu_name", args.blockName)
                }
            }
        }
    }

    private fun scrollToTop() {
        viewLifecycleOwner.lifecycleScope.launch {
            if (layoutManager.findFirstVisibleItemPosition() > scrollTargetPosition) {
                recyclerView.scrollToPosition(scrollTargetPosition)
                delay(50)
            }
            recyclerView.smoothScrollToPosition(0)
        }
    }

    override fun initLoadingView(loading: LoadingView) {
    }

    override fun getEmptyTip(): String {
        return getString(R.string.follow_feed_empty_tip)
    }

    override val showUserStatus: Boolean
        get() = true

    override val likeLocation: String = EventParamConstants.LOCATION_LIKE_FEED

    override val showPin: Boolean = true

    override val showTagList: Boolean = false

    override fun getFeedTag(): String =
        if (args.blockId == null) args.type else args.blockId.toString()

    override fun refreshLoadingProperty(): KProperty1<ICommunityFeedModelStateV2, Async<List<*>>> {
        return FollowingCommunityFeedModelState::pageRefresh as KProperty1<ICommunityFeedModelStateV2, Async<List<*>>>
    }

    override fun refreshPage() {
        viewModel.refreshPage()
    }

    override fun refreshParent() {
        // 不refresh tag了
//        parentViewModel.refresh()
    }

    override fun onResume() {
        super.onResume()
        Analytics.track(EventConstants.EVENT_COMMUNITY_FEED_SHOW) {
            put("tag", args.blockName)
        }
        if (isLoadSuccess) {
            Analytics.track(EventConstants.EVENT_COMMUNITY_MENU_SHOW) {
                put("menu_name", args.blockName)
            }
        }
    }

    override fun onNewDuration(duration: Long) {
        super.onNewDuration(duration)
        Analytics.track(EventConstants.EVENT_COMMUNITY_FEED_SHOW) {
            put("tag", args.blockName)
            put("playtime", duration)
        }
    }

    override fun goPost(item: PostFeedCard, commentInfo: CommentInfo?) {
        Analytics.track(EventConstants.EVENT_COMMUNITY_POST_CLICK) {
            put(EventParamConstants.KEY_POSTID, item.postId.orEmpty())
            put(EventParamConstants.KEY_TAG, getFeedTag())
            put(
                EventParamConstants.COMMUNITY_POST_TYPE,
                if (item.top == true) EventParamConstants.ANALYTIC_COMMUNITY_TYPE_PIN else EventParamConstants.ANALYTIC_COMMUNITY_TYPE_NORMAL
            )
            item.tagList?.map { it.tagId }?.let { put(EventParamConstants.KEY_TAG_LIST, it) }
        }
        if (!item.postId.isNullOrEmpty()) {
            val videoProgress = FeedVideoHelper.getVideoProgressByResId(item.localUniqueId)
            MetaRouter.Post.goPostDetail(
                this,
                item.postId,
                "feed",
                targetCommentId = commentInfo?.commentId,
                videoProgress = videoProgress
            )
        }
    }

    override fun goProfile(uuid: String, from: String) {
        MetaRouter.Profile.other(this, uuid, from)
    }

    override fun goTopic(tag: PostTag, postId: String) {
        if (tag.tagId <= 0) {
            toast(R.string.tag_reviewing_toast)
            return
        }
        topDetail(tag, postId)
    }

    override fun changeFollow(
        item: RecommendFeedCard,
        uuid: String,
        follow: Boolean,
        followFrom: CommunityFollowFrom
    ) {
        val trackLoc = if (followFrom == CommunityFollowFrom.FEED) {
            EventParamConstants.LOCATION_FOLLOW_FOLLOW_FEED
        } else if (followFrom == CommunityFollowFrom.SUGGESTED_USER) {
            EventParamConstants.LOCATION_FOLLOW_FOLLOW_COLD
        } else {
            ""
        }
        Analytics.track(EventConstants.EVENT_FOLLOW_CLICK) {
            put(EventParamConstants.KEY_USERID, uuid)
            put(EventParamConstants.KEY_LOCATION, trackLoc)
            put(
                EventParamConstants.KEY_TYPE,
                if (follow) EventParamConstants.TYPE_FOLLOW else EventParamConstants.TYPE_UNFOLLOW
            )
        }
        viewModel.followUser(item, uuid, follow)
    }

    private fun topDetail(tag: PostTag, postId: String?) {
        MetaRouter.Post.topicDetail(this, tag, EventParamConstants.SOURCE_TOPIC_FEED, postId)
    }

    override fun loadMoreFeed() {
        viewModel.loadMoreRecommendedFeed()
    }

    override fun clickMoreRecommendCreator() {
        MetaRouter.IM.goSearchFriend(this)
    }

    override fun moreFollowingUsers() {
        MetaRouter.Kol.moreTypeCreator(this@FollowingCommunityFeedFragment)
    }

    override fun moreFollowedWorks() {
        MetaRouter.Kol.moreFollowedUgc(this@FollowingCommunityFeedFragment)
    }

    override fun clickFollowedWork(game: CreatorUgcGame) {
        Analytics.track(
            EventConstants.EVENT_FOLLOW_UGC_SHOW,
            EventParamConstants.KEY_GAMEID to game.id,
            EventParamConstants.KEY_SHOW_CATEGORYID to CategoryId.CATEGORY_ID_KOL_FOLLOWED_UGC,
            EventParamConstants.KEY_PACKAGENAME to game.packageName
        )
        MetaRouter.MobileEditor.ugcDetail(
            this@FollowingCommunityFeedFragment,
            game.id,
            game.availableGameCode.orEmpty(),
            // 没有资源位, 资源位暂时填0
            ResIdBean.newInstance().setCategoryID(0).setGameId(game.id)
        )
    }

    override fun onFollowedWorkVisibilityChange(game: CreatorUgcGame, visible: Boolean) {
        if (visible) {
            Analytics.track(
                EventConstants.EVENT_FOLLOW_UGC_SHOW,
                EventParamConstants.KEY_GAMEID to game.id,
                EventParamConstants.KEY_SHOW_CATEGORYID to CategoryId.CATEGORY_ID_KOL_FOLLOWED_UGC,
                EventParamConstants.KEY_PACKAGENAME to game.packageName
            )
        }
    }

    override fun onFollowingUserVisibilityChange(user: KolCreatorInfo, visible: Boolean) {
        if (visible) {
            Analytics.track(
                EventConstants.EVENT_FOLLOW_CREATOR_SHOW,
                EventParamConstants.KEY_USERID to user.uuid,
                EventParamConstants.KEY_TYPE to EventConstants.Type.TYPE_KOL_CREATOR_TAB_FOLLOWED
            )
        }
    }

    override fun onPostHeadVisibilityChange(item: PostFeedCard, visible: Boolean){
        if(visible){
            Analytics.track(EventConstants.COMMUNITY_USER_RECOMMEND) {
                put("location", "follow_feed")
                put("userid", item.uid.orEmpty())
            }
        }
    }

    override fun getFeedController(): EpoxyController = simpleController(
        viewModel,
        FollowingCommunityFeedModelState::followUserList,
        FollowingCommunityFeedModelState::followGames,
        FollowingCommunityFeedModelState::list,
        FollowingCommunityFeedModelState::loadMore,
    ) { followUserList, followGames, list, loadMore ->
        var isEmpty = true
        if (!followUserList.isNullOrEmpty()) {
            isEmpty = false
            communityFollowingUsers(followUserList, 0, this@FollowingCommunityFeedFragment)
        }
        if (!followGames.isNullOrEmpty()) {
            isEmpty = false
            communityFollowedWorks(followGames, 1, this@FollowingCommunityFeedFragment)
        }
        if(!isEmpty || !list.isEmpty()) {
            // 显示"为你推荐"的标题
            add {
                CommunitySubListTitle(
                    title = getString(R.string.recommend_for_you),
                    showMore = false,
                    top = 20.dp,
                    bottom = 0.dp
                ) {}.id("CommunitySubListTitle-recommendForYou")
            }
        }
        if (!isEmpty && list.isEmpty()) {
            empty(
                descRes = R.string.no_recent_activity,
                top = dp(18)
            ) {
                viewModel.refreshRecommendedFeed()
            }
        } else {
            // feed
            val screenWidth = ScreenUtil.getScreenWidth(requireContext())
            val contentWidth = screenWidth - 32.dp
            list.forEachIndexed { index, feedCard ->
                if (feedCard is PostFeedCard) {
                    val playLikeAnimResId: String? =
                        if (feedViewModel.playLikeAnimPostSet.contains(feedCard.postId)) {
                            feedCard.postId
                        } else if (feedViewModel.playLikeAnimPostSet.contains(feedCard.commentInfo?.commentId)) {
                            feedCard.commentInfo?.commentId
                        } else {
                            null
                        }
                    feedViewModel.playLikeAnimPostSet.remove(feedCard.postId)
                    feedViewModel.playLikeAnimPostSet.remove(feedCard.commentInfo?.commentId)
                    communityFeedV2(
                        contentWidth,
                        feedCard,
                        index,
                        showUserStatus,
                        showPin,
                        16.dp,
                        this@FollowingCommunityFeedFragment,
                        playLikeAnimResId,
                        accountInteractor.curUuid,
                        this@FollowingCommunityFeedFragment
                    )
                } else if (feedCard is TopicFeedCard) {
                    communityTopicsV2(
                        feedCard,
                        index,
                        this@FollowingCommunityFeedFragment
                    )
                } else if (feedCard is UserFeedCard) {
                    communityRecommendCreatorV2(
                        feedCard,
                        index,
                        this@FollowingCommunityFeedFragment
                    )
                } else {
                    // 不支持的 CardType 类型
                }
            }
            // 加载更多
            if (list.isNotEmpty()) {
                loadMoreFooter(loadMore, showEnd = true) {
                    loadMoreFeed()
                }
            }
        }
    }

    override val useVideoFunc: Boolean = false

    override fun goTopicDetail(topic: Topic) {
        topDetail(topic.toPostTag(), null)
    }

    override fun goTopicSquare() {
        MetaRouter.Post.topicSquare(this)
    }

    override fun trackTopicRankShow(
        topic: Topic,
        rank: Int
    ) {
        Analytics.track(EventConstants.COMMUNITY_TAG_RECOMMEND_SHOW) {
            put(EventParamConstants.KEY_TAG_ID, topic.tagId ?: -1)
            put(
                EventParamConstants.KEY_HOT,
                if (topic.hot == true) EventParamConstants.V_HOT else EventParamConstants.V_NOT_HOT
            )
            put(EventParamConstants.KEY_RK, "$rank")
        }
    }
}