package com.socialplay.gpark.ui.post.feed.tag

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.mw.develop.ui.core.epoxy.map
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.account.UserFollowEvent
import com.socialplay.gpark.data.model.community.PostFeedCard
import com.socialplay.gpark.data.model.community.RecommendFeedCard
import com.socialplay.gpark.data.model.community.RecommendFeedCardListResponse
import com.socialplay.gpark.data.model.creator.CreatorUgcGame
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModelV2
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelStateV2
import com.socialplay.gpark.util.ToastData
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get

/**
 * Created by bo.li
 * Date: 2023/9/15
 * Desc:
 */
data class FollowingCommunityFeedModelState(
    override val refresh: Async<List<RecommendFeedCard>> = Uninitialized,
    override val toastMsg: ToastData = ToastData.EMPTY,
    override val loadMore: Async<LoadMoreState> = Uninitialized,
    override val nextPage: Int = 1,
    override val notifyCheckVideo: Async<Long> = Uninitialized,
    val pageRefresh: Async<List<Any>> = Uninitialized,
    val followUserList: List<KolCreatorInfo>? = null,
    val followGames: List<CreatorUgcGame>? = null,
    val offset: String? = null,
    val scrollToTop: Async<Boolean> = Uninitialized,
) : ICommunityFeedModelStateV2 {

    override fun updateFeedData(list: List<RecommendFeedCard>): ICommunityFeedModelStateV2 {
        return copy(refresh = refresh.copyEx(list))
    }

    override fun toast(toastMsg: ToastData): ICommunityFeedModelStateV2 {
        return copy(toastMsg = toastMsg)
    }

    override fun checkVideo(checkVideo: Async<Long>): ICommunityFeedModelStateV2 {
        return copy(notifyCheckVideo = checkVideo)
    }

    /**
     * 帖子按照 postId 去重
     */
    private fun List<RecommendFeedCard>.distinctPostFeedCard(): List<RecommendFeedCard>? {
        val postIds = mutableSetOf<String>()
        return this.filter { item ->
            if (item is PostFeedCard) {
                if (item.postId.isNullOrEmpty()) {
                    false
                } else {
                    postIds.add(item.postId)
                }
            } else {
                true
            }
        }
    }

    override fun feedRefresh(result: Async<RecommendFeedCardListResponse>): ICommunityFeedModelStateV2 {
        val newRefresh = result.map { response ->
            response.list?.distinctPostFeedCard() ?: emptyList()
        }
        return copy(
            refresh = newRefresh,
            offset = result()?.offset,
            loadMore = result.map { LoadMoreState(isEnd = result()?.hasMore == false) }
        )
    }

    override fun feedLoadMore(result: Async<RecommendFeedCardListResponse>): ICommunityFeedModelStateV2 {
        return copy(
            refresh = if (result is Success) {
                val oldList = refresh.invoke() ?: emptyList()
                result.map { response ->
                    if (oldList.isNullOrEmpty()) {
                        response.list ?: emptyList()
                    } else {
                        oldList + (response.list ?: emptyList())
                    }.distinctPostFeedCard() ?: emptyList()
                }
            } else {
                refresh
            },
            offset = if (result is Success) {
                result().offset
            } else {
                offset
            },
            loadMore = result.map { LoadMoreState(result()?.hasMore == false) }
        )
    }
}

class FollowingCommunityFeedViewModel(
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    initialState: FollowingCommunityFeedModelState
) : BaseCommunityFeedViewModelV2<FollowingCommunityFeedModelState>(
    repository,
    accountInteractor,
    initialState
) {

    /**
     * 刷新页面一共会请求两个接口, loadFollowCreatorAndWorks() 和 refreshRecommendedFeed()
     * 并行请求的话, 状态刷新不好把控, 用串行请求
     */
    fun refreshPage() = withState { oldState ->
        if (oldState.pageRefresh is Loading) {
            return@withState
        }
        setState {
            copy(
                pageRefresh = Loading(pageRefresh.invoke())
            )
        }
        loadFollowCreatorAndWorks()
    }

    private fun getNewPageRefresh(
        oldState: FollowingCommunityFeedModelState,
        followUserList: List<KolCreatorInfo>?,
        followGames: List<CreatorUgcGame>?,
        feedResponse: Async<RecommendFeedCardListResponse>?
    ): Async<List<Any>> {
        if (!followUserList.isNullOrEmpty() || !followGames.isNullOrEmpty()) {
            return Success(listOf(true))
        }
        if (feedResponse != null) {
            return feedResponse.map { it.list ?: emptyList() }
        }
        return oldState.pageRefresh
    }

    private fun loadFollowCreatorAndWorks() {
        withState { s ->
            viewModelScope.launch {
                val frameResult = runCatching {
                    repository.getKolFrameList().singleOrNull()
                }.getOrElse { null }
                val frameData = frameResult?.data
                if (frameData != null) {
                    setState {
                        copy(
                            pageRefresh = getNewPageRefresh(
                                oldState,
                                frameData.followUserList,
                                frameData.followGames,
                                null
                            ),
                            followUserList = frameData.followUserList,
                            followGames = frameData.followGames,
                        )
                    }
                }
                refreshRecommendedFeed()
            }
        }
    }

    fun refreshRecommendedFeed() {
        withState { s ->
            if (oldState.loadMore is Loading) return@withState
            // 处理type的string和int转换
            repository.getCommunityRecommendFollows(
                pageSize = PAGE_SIZE,
            ).map {
                notifyCheckVideo()
                it.filterInvalidElements(accountInteractor.curUuid)
            }.execute { resultAsync ->
                val result = feedRefresh(resultAsync) as FollowingCommunityFeedModelState
                result.copy(
                    pageRefresh = getNewPageRefresh(
                        oldState,
                        null,
                        null,
                        resultAsync
                    ),
                )
            }
        }
    }

    fun loadMoreRecommendedFeed() {
        withState { oldState ->
            if (oldState.loadMore is Loading) return@withState
            repository.getCommunityRecommendFollows(
                pageSize = PAGE_SIZE,
                offset = oldState.offset,
            ).map {
                it.filterInvalidElements(accountInteractor.curUuid)
            }.execute { result ->
                feedLoadMore(result) as FollowingCommunityFeedModelState
            }
        }
    }

    override fun receivedFollowEvent(event: UserFollowEvent) {
        // 关注状态变化时, 应该刷新关注tab的数据
        refreshPage()
    }

    companion object :
        KoinViewModelFactory<FollowingCommunityFeedViewModel, FollowingCommunityFeedModelState>() {

        private const val PAGE_SIZE = 20

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: FollowingCommunityFeedModelState
        ): FollowingCommunityFeedViewModel {
            return FollowingCommunityFeedViewModel(get(), get(), state)
        }
    }
}