package com.socialplay.gpark.ui.post.feed.tag

import android.os.Bundle
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.airbnb.epoxy.EpoxyController
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.community.CommentInfo
import com.socialplay.gpark.data.model.community.PostFeedCard
import com.socialplay.gpark.data.model.community.RecommendFeedCard
import com.socialplay.gpark.data.model.community.Topic
import com.socialplay.gpark.data.model.community.TopicFeedCard
import com.socialplay.gpark.data.model.community.UserFeedCard
import com.socialplay.gpark.data.model.post.PostPublishStatus
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.request.PostTagFeedRequest
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.post.feed.CommunityFeedFragmentV2
import com.socialplay.gpark.ui.post.feed.CommunityFollowFrom
import com.socialplay.gpark.ui.post.feed.ICommunityTopicListenerV2
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModelV2
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelStateV2
import com.socialplay.gpark.ui.post.feed.communityFeedV2
import com.socialplay.gpark.ui.post.feed.communityRecommendCreatorV2
import com.socialplay.gpark.ui.post.feed.communityTopicsV2
import com.socialplay.gpark.ui.post.tab.CommunityFeedTabModelState
import com.socialplay.gpark.ui.post.tab.CommunityFeedTabViewModel
import com.socialplay.gpark.ui.view.LoadingView
import com.socialplay.gpark.ui.view.video.FeedVideoHelper
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2023/9/15
 * Desc:
 */
class RecommendCommunityFeedFragmentV2 : CommunityFeedFragmentV2(), ICommunityTopicListenerV2 {

    private val viewModel: RecommendCommunityFeedViewModelV2 by fragmentViewModel()
    private val args by args<TagCommunityFeedFragmentArgs>()
    private val parentViewModel: CommunityFeedTabViewModel by parentFragmentViewModel()
    private val scrollTargetPosition = 2

    private val feedOrder by lazy { if (PandoraToggle.communityNewOrder) PostTagFeedRequest.ORDER_TYPE_CUSTOM else PostTagFeedRequest.ORDER_TYPE_NEWEST }

    override val feedViewModel: BaseCommunityFeedViewModelV2<ICommunityFeedModelStateV2>
        get() = viewModel as BaseCommunityFeedViewModelV2<ICommunityFeedModelStateV2>

    companion object {
        fun newInstance(args: TagCommunityFeedFragmentArgs): RecommendCommunityFeedFragmentV2 {
            return RecommendCommunityFeedFragmentV2().apply {
                arguments = args.asMavericksArgs()
            }
        }
    }

    private var isLoadFinishTrack = false
    private var isLoadSuccess = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        parentViewModel.onEach(
            CommunityFeedTabModelState::currentUid,
            uniqueOnly()
        ) { uid ->
            refreshFeed()
        }
        viewModel.onAsync(
            RecommendCommunityFeedModelStateV2::scrollToTop,
            onFail = {},
            onSuccess = {
                if (it) {
                    recyclerView.scrollToPosition(0)
                    viewModel.removeScrollToTop()
                }
            }
        )
        parentViewModel.onEach(
            CommunityFeedTabModelState::publishingState,
            uniqueOnly()
        ) { publishingState ->
            if (publishingState == null) {
                viewModel.removePublishingItem(null)
                return@onEach
            }
            when {
                publishingState.status == PostPublishStatus.STATUS_SUCCEEDED -> {
                    publishingState.post?.toPublishFeed(
                        GlobalContext.get().get<AccountInteractor>().accountLiveData.value,
                        publishingState.ts,
                    )?.let { it1 ->
                        viewModel.addPublishingItem(it1.toPostCard(), false)
                    }
                }

                publishingState.status == PostPublishStatus.STATUS_FAILED -> {
                    viewModel.removePublishingItem(publishingState.ts)
                }

                !publishingState.publishOver() -> {
                    publishingState.post?.toPublishFeed(
                        GlobalContext.get().get<AccountInteractor>().accountLiveData.value,
                        publishingState.ts,
                    )?.let { it1 ->
                        viewModel.addPublishingItem(it1.toPostCard(), true)
                    }
                }
            }
        }
        parentViewModel.onEach(CommunityFeedTabModelState::scrollFeed, uniqueOnly()) {
            Timber.d("checkcheck_refresh, ${it}")
            if (isResumed) {
                scrollToTop()
            }
        }
        viewModel.onEach(RecommendCommunityFeedModelStateV2::list) { feedList ->
            isLoadSuccess = feedList.isNotEmpty()
            if (isLoadSuccess && !isLoadFinishTrack && isResumed) {
                isLoadFinishTrack = true
                Analytics.track(EventConstants.EVENT_COMMUNITY_MENU_SHOW) {
                    put("menu_name", args.blockName)
                }
            }
        }
    }

    private fun scrollToTop() {
        viewLifecycleOwner.lifecycleScope.launch {
            if (layoutManager.findFirstVisibleItemPosition() > scrollTargetPosition) {
                recyclerView.scrollToPosition(scrollTargetPosition)
                delay(50)
            }
            recyclerView.smoothScrollToPosition(0)
        }
    }

    override fun getFeedController(): EpoxyController = simpleController(
        viewModel,
        RecommendCommunityFeedModelStateV2::list,
        RecommendCommunityFeedModelStateV2::loadMore,
    ) { list, loadMore ->
        // feed
        val screenWidth = ScreenUtil.getScreenWidth(requireContext())
        val contentWidth = screenWidth - 32.dp
        list.forEachIndexed { index, feedCard ->
            if (feedCard is PostFeedCard) {
                val playLikeAnimResId: String? =
                    if (feedViewModel.playLikeAnimPostSet.contains(feedCard.postId)) {
                        feedCard.postId
                    } else if (feedViewModel.playLikeAnimPostSet.contains(feedCard.commentInfo?.commentId)) {
                        feedCard.commentInfo?.commentId
                    } else {
                        null
                    }
                feedViewModel.playLikeAnimPostSet.remove(feedCard.postId)
                feedViewModel.playLikeAnimPostSet.remove(feedCard.commentInfo?.commentId)
                communityFeedV2(
                    contentWidth,
                    feedCard,
                    index,
                    showUserStatus,
                    showPin,
                    16.dp,
                    this@RecommendCommunityFeedFragmentV2,
                    playLikeAnimResId,
                    accountInteractor.curUuid,
                    this@RecommendCommunityFeedFragmentV2
                )
            } else if (feedCard is TopicFeedCard) {
                communityTopicsV2(
                    feedCard,
                    index,
                    this@RecommendCommunityFeedFragmentV2
                )
            } else if (feedCard is UserFeedCard) {
                communityRecommendCreatorV2(
                    feedCard,
                    index,
                    this@RecommendCommunityFeedFragmentV2
                )
            } else {
                // 不支持的 CardType 类型
            }
        }
        // 加载更多
        if (list.isNotEmpty()) {
            loadMoreFooter(loadMore, showEnd = true) {
                loadMoreFeed()
            }
        }
    }

    override fun initLoadingView(loading: LoadingView) {
    }

    override fun getEmptyTip(): String {
        return getString(R.string.footer_load_end)
    }

    override val showUserStatus: Boolean
        get() = true

    override val likeLocation: String = EventParamConstants.LOCATION_LIKE_FEED

    override val showPin: Boolean = true

    override val showTagList: Boolean = false

    override fun getFeedTag(): String =
        if (args.blockId == null) args.type else args.blockId.toString()

    override fun refreshPage() {
        viewModel.refreshAll()
    }

    override fun refreshParent() {

    }

    override fun onResume() {
        super.onResume()
        Analytics.track(EventConstants.EVENT_COMMUNITY_FEED_SHOW) {
            put("tag", args.blockName)
        }
        if (isLoadSuccess) {
            Analytics.track(EventConstants.EVENT_COMMUNITY_MENU_SHOW) {
                put("menu_name", args.blockName)
            }
        }
    }

    override fun onNewDuration(duration: Long) {
        super.onNewDuration(duration)
        Analytics.track(EventConstants.EVENT_COMMUNITY_FEED_SHOW) {
            put("tag", args.blockName)
            put("playtime", duration)
        }
    }

    override fun goPost(item: PostFeedCard, commentInfo: CommentInfo?) {
        Analytics.track(EventConstants.EVENT_COMMUNITY_POST_CLICK) {
            put(EventParamConstants.KEY_POSTID, item.postId.orEmpty())
            put(EventParamConstants.KEY_TAG, getFeedTag())
            put(
                EventParamConstants.COMMUNITY_POST_TYPE,
                if (item.top == true) EventParamConstants.ANALYTIC_COMMUNITY_TYPE_PIN else EventParamConstants.ANALYTIC_COMMUNITY_TYPE_NORMAL
            )
            item.tagList?.map { it.tagId }?.let { put(EventParamConstants.KEY_TAG_LIST, it) }
        }
        if (!item.postId.isNullOrEmpty()) {
            val videoProgress = FeedVideoHelper.getVideoProgressByResId(item.localUniqueId)
            MetaRouter.Post.goPostDetail(
                this,
                item.postId,
                "feed",
                targetCommentId = commentInfo?.commentId,
                videoProgress = videoProgress
            )
        }
    }

    override fun goProfile(uuid: String, from: String) {
        MetaRouter.Profile.other(this, uuid, from)
    }

    override fun goTopicDetail(topic: Topic) {
        topDetail(topic.toPostTag(), null)
    }

    override fun goTopicSquare() {
        MetaRouter.Post.topicSquare(this)
    }

    override fun trackTopicRankShow(topic: Topic, rank: Int) {
        Analytics.track(EventConstants.COMMUNITY_TAG_RECOMMEND_SHOW) {
            put(EventParamConstants.KEY_TAG_ID, topic.tagId ?: -1)
            put(
                EventParamConstants.KEY_HOT,
                if (topic.hot == true) EventParamConstants.V_HOT else EventParamConstants.V_NOT_HOT
            )
            put(EventParamConstants.KEY_RK, "$rank")
        }
    }

    override fun goTopic(tag: PostTag, postId: String) {
        if (tag.tagId <= 0) {
            toast(R.string.tag_reviewing_toast)
            return
        }
        topDetail(tag, postId)
    }

    private fun topDetail(tag: PostTag, postId: String?) {
        MetaRouter.Post.topicDetail(this, tag, EventParamConstants.SOURCE_TOPIC_FEED, postId)
    }

    override fun loadMoreFeed() {
        viewModel.loadMoreTagFeed()
    }

    override fun changeFollow(
        item: RecommendFeedCard,
        uuid: String,
        follow: Boolean,
        followFrom: CommunityFollowFrom
    ) {
        val trackLoc = if (followFrom == CommunityFollowFrom.FEED) {
            EventParamConstants.LOCATION_FOLLOW_RECOMMEND
        } else if (followFrom == CommunityFollowFrom.SUGGESTED_USER) {
            EventParamConstants.LOCATION_FOLLOW_FOLLOW_COLD
        } else {
            ""
        }
        Analytics.track(EventConstants.EVENT_FOLLOW_CLICK) {
            put(EventParamConstants.KEY_USERID, uuid)
            put(EventParamConstants.KEY_LOCATION, trackLoc)
            put(
                EventParamConstants.KEY_TYPE,
                if (follow) EventParamConstants.TYPE_FOLLOW else EventParamConstants.TYPE_UNFOLLOW
            )
        }
        viewModel.followUser(item, uuid, follow)
    }

    override fun onPostHeadVisibilityChange(item: PostFeedCard, visible: Boolean){
        if(visible){
            Analytics.track(EventConstants.COMMUNITY_USER_RECOMMEND) {
                put("location", "recommend")
                put("userid", item.uid.orEmpty())
            }
        }
    }

    override fun clickMoreRecommendCreator() {
        MetaRouter.IM.goSearchFriend(this)
    }

    override val useVideoFunc: Boolean = false
}