package com.socialplay.gpark.ui.post.feed.tag

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.community.PostFeedCard
import com.socialplay.gpark.data.model.community.RecommendFeedCard
import com.socialplay.gpark.data.model.community.RecommendFeedCardListResponse
import com.socialplay.gpark.data.model.post.PostDetail
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModelV2
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelStateV2
import com.socialplay.gpark.util.ToastData
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2023/9/15
 * Desc:
 */
data class RecommendCommunityFeedModelStateV2(
    override val refresh: Async<List<RecommendFeedCard>> = Uninitialized,
    override val toastMsg: ToastData = ToastData.EMPTY,
    override val loadMore: Async<LoadMoreState> = Uninitialized,
    override val nextPage: Int = 1,
    override val notifyCheckVideo: Async<Long> = Uninitialized,
    val offset: String? = null,
    val scrollToTop: Async<Boolean> = Uninitialized,
    val hotTopics: List<PostTag>? = null,
    val hotTopicPage: Int = 1,
) : ICommunityFeedModelStateV2 {

    override fun updateFeedData(list: List<RecommendFeedCard>): ICommunityFeedModelStateV2 {
        return copy(refresh = refresh.copyEx(list))
    }

    override fun toast(toastMsg: ToastData): ICommunityFeedModelStateV2 {
        return copy(toastMsg = toastMsg)
    }

    override fun checkVideo(checkVideo: Async<Long>): ICommunityFeedModelStateV2 {
        return copy(notifyCheckVideo = checkVideo)
    }

    /**
     * 帖子按照 postId 去重
     */
    private fun List<RecommendFeedCard>.distinctPostFeedCard(): List<RecommendFeedCard>? {
        val postIds = mutableSetOf<String>()
        return this.filter { item ->
            if (item is PostFeedCard) {
                if (item.postId.isNullOrEmpty()) {
                    false
                } else {
                    postIds.add(item.postId)
                }
            } else {
                true
            }
        }
    }

    override fun feedRefresh(result: Async<RecommendFeedCardListResponse>): ICommunityFeedModelStateV2 {
        val newRefresh = result.map { response ->
            response.list?.distinctPostFeedCard() ?: emptyList()
        }
        return copy(
            refresh = newRefresh,
            offset = result()?.offset,
            loadMore = result.map { LoadMoreState(isEnd = result()?.hasMore == false) }
        )
    }

    override fun feedLoadMore(result: Async<RecommendFeedCardListResponse>): ICommunityFeedModelStateV2 {
        return copy(
            refresh = if (result is Success) {
                val oldList = refresh.invoke() ?: emptyList()
                result.map { response ->
                    if (oldList.isNullOrEmpty()) {
                        response.list ?: emptyList()
                    } else {
                        oldList + (response.list ?: emptyList())
                    }.distinctPostFeedCard() ?: emptyList()
                }
            } else {
                refresh
            },
            offset = if (result is Success) {
                result().offset
            } else {
                offset
            },
            loadMore = result.map { LoadMoreState(result()?.hasMore == false) }
        )
    }
}

class RecommendCommunityFeedViewModelV2(
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    initialState: RecommendCommunityFeedModelStateV2
) : BaseCommunityFeedViewModelV2<RecommendCommunityFeedModelStateV2>(
    repository,
    accountInteractor,
    initialState
) {

    @Throws
    private fun refreshFeedFlow() = flow {
        emit(repository.getCommunityRecommendFeeds(pageSize = FEED_PAGE_SIZE).map {
            notifyCheckVideo()
            it
        }.invoke())
    }

    fun refreshAll() {
        withState { oldState ->
            if (oldState.loadMore is Loading) return@withState
            refreshFeedFlow().map {
                it.filterInvalidElements(accountInteractor.curUuid)
            }.execute { result ->
                oldState.feedRefresh(result) as RecommendCommunityFeedModelStateV2
            }
        }
    }

    fun loadMoreTagFeed() {
        withState { oldState ->
            if (oldState.loadMore is Loading) return@withState
            repository.getCommunityRecommendFeeds(
                pageSize = FEED_PAGE_SIZE,
                offset = oldState.offset,
            ).map {
                it.filterInvalidElements(accountInteractor.curUuid)
            }.execute { result ->
                feedLoadMore(result) as RecommendCommunityFeedModelStateV2
            }
        }
    }

    fun removePublishingItem(ts: Long?) {
        withState { oldState ->
            if (oldState.list.isEmpty()) return@withState
            val newList = ArrayList(oldState.list).apply {
                if (ts != null) {
                    removeAll { item ->
                        if (item is PostFeedCard) {
                            item.localId == ts
                        } else {
                            false
                        }
                    }
                } else {
                    removeAll { item ->
                        if (item is PostFeedCard) {
                            item.localPublishing
                        } else {
                            false
                        }
                    }
                }
            }
            Timber.d("checkcheck_publish, removePublishingItem ts: ${ts}")
            setState {
                copy(refresh = refresh.copyEx(newList))
            }
        }
    }

    fun addPublishingItem(info: PostFeedCard, publishing: Boolean) = viewModelScope.launch {
        val state = awaitState()
        val skip = state.list.any { item ->
            if (item is PostFeedCard) {
                item.localId == info.localId
            } else {
                false
            }
        } && publishing
        if (skip) {
            return@launch
        }
        val newList = state.list.toMutableList()
        val oldIndex = newList.indexOfFirst { item ->
            if (item is PostFeedCard) {
                item.localId == info.localId || item.postId == info.postId
            } else {
                false
            }
        }
        if (oldIndex in 0..newList.lastIndex) {
            val item = newList[oldIndex]
            if (item is PostFeedCard) {
                item.copy(
                    localPublishing = publishing,
                    postId = info.postId,
                    mediaList = info.mediaList,
                    user = info.user,
                    status = if (!publishing) {
                        PostDetail.STATUS_REVIEW_AUTO_IN_PROGRESS
                    } else {
                        PostDetail.STATUS_OK
                    },
                    plotCardList = info.plotCardList
                )
            }
            if (!publishing) {
                scrollToTop()
            }
        } else {
            scrollToTop()
            newList.add(
                0,
                info.copy(
                    localPublishing = publishing, status = if (!publishing) {
                        PostDetail.STATUS_REVIEW_AUTO_IN_PROGRESS
                    } else {
                        PostDetail.STATUS_OK
                    }
                )
            )
        }
        Timber.d("checkcheck_publish, addPublishingItem localId: ${info.localId}, postId: ${info.postId}, publishing: ${publishing}")
        setState {
            copy(refresh = refresh.copyEx(newList))
        }
    }

    fun removeScrollToTop() {
        suspend {
            false
        }.execute {
            copy(scrollToTop = it)
        }
    }

    private fun scrollToTop() {
        withState {
            if (it.scrollToTop is Loading) return@withState
            suspend {
                delay(500)
                true
            }.execute {
                copy(scrollToTop = it)
            }
        }
    }

    @Throws
    private fun refreshHotTopicsFlow() = flow {
        val result = runCatching {
            repository.fetchHotTopics(TOPIC_PAGE_SIZE, oldState.hotTopicPage).invoke()
                ?.distinctBy { it.tagId }
        }.getOrNull()
        emit(result)
    }

    companion object :
        KoinViewModelFactory<RecommendCommunityFeedViewModelV2, RecommendCommunityFeedModelStateV2>() {

        private const val FEED_PAGE_SIZE = 20
        private const val TOPIC_PAGE_SIZE = 6

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: RecommendCommunityFeedModelStateV2
        ): RecommendCommunityFeedViewModelV2 {
            return RecommendCommunityFeedViewModelV2(get(), get(), state)
        }
    }
}