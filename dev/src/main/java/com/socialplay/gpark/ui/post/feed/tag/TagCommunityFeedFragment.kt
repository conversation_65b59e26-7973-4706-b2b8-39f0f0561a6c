package com.socialplay.gpark.ui.post.feed.tag

import android.os.Bundle
import android.os.Parcelable
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.CommunityBlockType
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.request.PostTagFeedRequest
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.post.feed.CommunityFeedFragment
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModel
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelState
import com.socialplay.gpark.ui.post.tab.CommunityFeedTabModelState
import com.socialplay.gpark.ui.post.tab.CommunityFeedTabViewModel
import com.socialplay.gpark.ui.view.LoadingView
import com.socialplay.gpark.ui.view.video.FeedVideoHelper
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2023/9/15
 * Desc:
 */
@Parcelize
data class TagCommunityFeedFragmentArgs(val blockId: Long?, val blockName: String, val type: String) :
    Parcelable

class TagCommunityFeedFragment : CommunityFeedFragment() {

    private val viewModel: TagCommunityFeedViewModel by fragmentViewModel()
    private val args by args<TagCommunityFeedFragmentArgs>()
    private val parentViewModel: CommunityFeedTabViewModel by parentFragmentViewModel()
    private val scrollTargetPosition = 2

    private val feedOrder by lazy { if (PandoraToggle.communityNewOrder) PostTagFeedRequest.ORDER_TYPE_CUSTOM else PostTagFeedRequest.ORDER_TYPE_NEWEST }

    override val feedViewModel: BaseCommunityFeedViewModel<ICommunityFeedModelState>
        get() = viewModel as BaseCommunityFeedViewModel<ICommunityFeedModelState>

    companion object {
        fun newInstance(args: TagCommunityFeedFragmentArgs): TagCommunityFeedFragment {
            return TagCommunityFeedFragment().apply {
                arguments = args.asMavericksArgs()
            }
        }
    }

    private var isLoadFinishTrack = false
    private var isLoadSuccess = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (args.type == CommunityBlockType.TYPE_FOLLOWING) {
            parentViewModel.onEach(
                CommunityFeedTabModelState::currentUid,
                uniqueOnly()
            ) { uid ->
                refreshFeed()
            }
        }
        parentViewModel.onEach(CommunityFeedTabModelState::scrollFeed, uniqueOnly()) {
            Timber.d("checkcheck_refresh, $it")
            if (isResumed) {
                scrollToTop()
            }
        }

        viewModel.onAsync(
            TagCommunityFeedModelState::refresh
        ) { feedList ->
            isLoadSuccess = feedList.isNotEmpty()
            if (isLoadSuccess && !isLoadFinishTrack && isResumed) {
                isLoadFinishTrack = true
                Analytics.track(EventConstants.EVENT_COMMUNITY_MENU_SHOW) {
                    put("menu_id", args.blockId?.toString() ?: "")
                    put("menu_name", args.blockName)
                }
            }
        }
    }

    private fun scrollToTop() {
        viewLifecycleOwner.lifecycleScope.launch {
            if (layoutManager.findFirstVisibleItemPosition() > scrollTargetPosition) {
                recyclerView.scrollToPosition(scrollTargetPosition)
                delay(50)
            }
            recyclerView.smoothScrollToPosition(0)
        }
    }

    override fun initLoadingView(loading: LoadingView) {
    }

    override fun getEmptyTip(): String {
        return if (args.type == CommunityBlockType.TYPE_FOLLOWING) {
            getString(R.string.follow_feed_empty_tip)
        } else {
            getString(R.string.footer_load_end)
        }
    }

    override val showUserStatus: Boolean
        get() = true

    override val likeLocation: String = EventParamConstants.LOCATION_LIKE_FEED

    override val showPin: Boolean = true

    override val showTagList: Boolean = false

    override fun getFeedTag(): String = if (args.blockId == null) args.type else args.blockId.toString()

    override fun refreshPage() {
        viewModel.refreshTagFeed(feedOrder, args.blockId, args.type)
    }

    override fun refreshParent() {
        // 不refresh tag了
//        parentViewModel.refresh()
    }

    override fun onResume() {
        super.onResume()
        Analytics.track(EventConstants.EVENT_COMMUNITY_FEED_SHOW) {
            put("tag", args.blockName)
        }
        if (isLoadSuccess) {
            Analytics.track(EventConstants.EVENT_COMMUNITY_MENU_SHOW) {
                put("menu_id", args.blockId?.toString() ?: "")
                put("menu_name", args.blockName)
            }
        }
    }

    override fun onNewDuration(duration: Long) {
        super.onNewDuration(duration)
        Analytics.track(EventConstants.EVENT_COMMUNITY_FEED_SHOW) {
            put("tag", args.blockName)
            put("playtime", duration)
        }
    }

    override fun goPost(item: CommunityFeedInfo) {
        Analytics.track(EventConstants.EVENT_COMMUNITY_POST_CLICK) {
            put(EventParamConstants.KEY_POSTID, item.postId)
            put(EventParamConstants.KEY_TAG, getFeedTag())
            put(EventParamConstants.COMMUNITY_POST_TYPE, if (item.top) EventParamConstants.ANALYTIC_COMMUNITY_TYPE_PIN else EventParamConstants.ANALYTIC_COMMUNITY_TYPE_NORMAL)
            item.tagList?.map { it.tagId }?.let { put(EventParamConstants.KEY_TAG_LIST, it) }
        }
        val videoProgress = FeedVideoHelper.getVideoProgressByResId(item.localUniqueId)
        MetaRouter.Post.goPostDetail(this, item.postId, "feed", videoProgress = videoProgress)
    }

    override fun goProfile(uuid: String) {
        MetaRouter.Profile.other(this, uuid, "communityFeed")
    }

    override fun goTopic(tag: PostTag, postId: String) {
        if (tag.tagId <= 0) {
            toast(R.string.tag_reviewing_toast)
            return
        }
        topDetail(tag, postId)
    }

    private fun topDetail(tag: PostTag, postId: String?) {
        MetaRouter.Post.topicDetail(this, tag, EventParamConstants.SOURCE_TOPIC_FEED, postId)
    }

    override fun loadMoreFeed() {
        viewModel.loadMoreTagFeed(feedOrder, args.blockId, args.type)
    }

    override val useVideoFunc: Boolean = false


}