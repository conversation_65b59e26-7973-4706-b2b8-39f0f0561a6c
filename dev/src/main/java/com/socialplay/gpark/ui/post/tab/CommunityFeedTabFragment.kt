package com.socialplay.gpark.ui.post.tab

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceTabInfo
import com.socialplay.gpark.data.model.choice.CommunityTabTargetType
import com.socialplay.gpark.databinding.FragmentCommunityFeedTabBinding
import com.socialplay.gpark.databinding.TabIndicatorHomeCommunityBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.main.MainBottomNavigationItem
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.main.OnBottomNavItemReselected
import com.socialplay.gpark.ui.videofeed.VideoFeedViewModel
import com.socialplay.gpark.ui.view.video.FeedVideoHelper
import com.socialplay.gpark.ui.view.viewpager.CommonTabStateAdapter
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTouchSlop
import com.socialplay.gpark.util.extension.visible
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2023/9/22
 * Desc:
 *
 * 页签显示的来源:
 * 参考: CommunityFeedTabViewModel.refresh() 方法中的页签数据来源
 * 1. 先显示本地写死的页签;
 * 2. 再显示通过接口: /tab/conf/v1/query 下发的页签;
 * 3. 最后显示通过接口: /community/block/query 下发的页签;
 */
class CommunityFeedTabFragment :
    BaseFragment<FragmentCommunityFeedTabBinding>(R.layout.fragment_community_feed_tab),
    OnBottomNavItemReselected {

    private val viewModel: CommunityFeedTabViewModel by fragmentViewModel()
    private var tabLayoutMediator: TabLayoutMediator? = null
    private lateinit var pagerAdapter: CommonTabStateAdapter

    private val publishAnimView: PublishSceneAnimView = PublishSceneAnimView()
    private val mainViewModel by sharedViewModel<MainViewModel>()

    private var isJumpDefaultTab = false

    private val tabCallback = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, true)
        }

        override fun onTabUnselected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, false)
        }

        override fun onTabReselected(tab: TabLayout.Tab) {}
    }

    private val vpCallback = object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            viewModel.changeSelectedTag(position)
        }
    }

    companion object {
        private const val MAX_PROGRESS = 100
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentCommunityFeedTabBinding? {
        return FragmentCommunityFeedTabBinding.inflate(inflater, container, false)
    }

    /**
     * 选中图片tab时, 应该隐藏 indicator
     */
    private val imgIndicatorColor by lazy { getColorByRes(R.color.transparent) }

    /**
     * 选中文本tab时, 应该显示 indicator
     */
    private val textIndicatorColor by lazy { getColorByRes(R.color.color_FFDE70) }

    private fun setTabSelectUI(tab: TabLayout.Tab, select: Boolean) {
        val cv = tab.customView ?: return
        val tabBinding = TabIndicatorHomeCommunityBinding.bind(cv)
        val tag = tab.tag as? ChoiceTabInfo
        // 有图片配置的时候, 优先显示图片
        if (tag != null && tag.imgUrl.isNotEmpty()) {
            tabBinding.tvNormal.gone()
            tabBinding.tvSelected.gone()

            tabBinding.ivNormal.isInvisible = select
            tabBinding.ivSelected.isInvisible = !select
            if (select) {
                binding.tlFeed.setSelectedTabIndicatorColor(imgIndicatorColor)
            } else {
                binding.tlFeed.setSelectedTabIndicatorColor(textIndicatorColor)
            }
        } else {
            tabBinding.tvNormal.isInvisible = select
            tabBinding.tvSelected.isInvisible = !select

            tabBinding.ivNormal.gone()
            tabBinding.ivSelected.gone()
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        publishAnimView.setDismissCallback {
            binding.includeChoosePublishScene.root.isVisible = true
        }
        initViewPager()
        initEvent()
        initData()
    }

    private fun initData() {
        viewModel.onEach(CommunityFeedTabModelState::tagList, deliveryMode = uniqueOnly()) {
            val tabs = it.map { type ->
                { createTabFragment(type) }
            }
            pagerAdapter.fragmentCreators = tabs
            pagerAdapter.notifyDataSetChanged()
        }
        viewModel.onEach(CommunityFeedTabModelState::jumpTabInfo) { bundle ->
            Timber.tag(VideoFeedViewModel.DATA_RELAY_TAG).d("feedTab receive jumpType:$bundle")
            bundle ?: return@onEach
            viewModel.changeSelectedTag(bundle.tab)
        }
        viewModel.onEach(
            CommunityFeedTabModelState::publishingState
        ) { publishingState ->
            binding.pbPublish.isVisible = publishingState != null && !publishingState.publishOver()
            publishingState ?: return@onEach
            binding.pbPublish.progress =
                ((publishingState.uploadStatus?.curTotalPercent ?: 0.0) * MAX_PROGRESS).toInt()
                    .coerceAtMost(MAX_PROGRESS)
            if (publishingState.publishOver()) {
                viewModel.clearPublishing()
            }
        }
        viewModel.registerToast(CommunityFeedTabModelState::toastData)
        viewModel.onEach(CommunityFeedTabModelState::selectedTag) {
            viewModel.oldState.tagList.getOrNull(it)?.let { type ->
                binding.includeChoosePublishScene.root.visible(type.isPostPublishVisible)
//                binding.vpFeed.isUserInputEnabled = type.isNestedScrollEnabled
                binding.vpFeed.isUserInputEnabled = true

                Analytics.track(EventConstants.EVENT_COMMUNITY_MENU_CHOOSE) {
                    if (type.target != CommunityTabTargetType.FOLLOW.name
                        && type.target != CommunityTabTargetType.RECOMMEND.name
                        && type.target != CommunityTabTargetType.DISCOVER.name
                    ) {
                        put("menu_id", type.id)
                    }
                    put("menu_name", type.name.orEmpty())
                }
            }

            if (binding.vpFeed.currentItem != it && it in 0 until pagerAdapter.itemCount) {
                binding.vpFeed.setCurrentItem(it, false)
            }
        }
        viewModel.collectRelay(mainViewModel.tabPendingConsumeDataFlow, mainViewModel)

        viewModel.onEach(CommunityFeedTabModelState::jumpTabInfo) { bundle ->
            Timber.tag(VideoFeedViewModel.DATA_RELAY_TAG).d("parent receive bundle: ${bundle}")
            if (bundle != null && bundle.tab.target != CommunityTabTargetType.VIDEO.name) {
                // 视频流有自己的操作，让他自己clear
                viewModel.clearRelay()
            }
        }

        mainViewModel.msgUnReadCountFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
            binding.vMsgRedDot.visible(it > 0)
        }

        if(!isJumpDefaultTab) {
            isJumpDefaultTab = true
            // 默认选中推荐tab
            val recommendTabIndex = viewModel.oldState.tagList.indexOfFirst {
                it.target == CommunityTabTargetType.RECOMMEND.name
            }
            if (recommendTabIndex >= 0) {
                binding.tlFeed.getTabAt(recommendTabIndex)?.select()
                viewModel.changeSelectedTag(recommendTabIndex)
                binding.vpFeed.setCurrentItem(recommendTabIndex, false)
            }
        }
    }

    private fun createTabFragment(tabInfo: ChoiceTabInfo): Fragment {
        return CommunityTabContentFragment.newInstance(tabInfo)
    }

    private fun initEvent() {
        binding.includeChoosePublishScene.root.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_COMMUNITY_ADD_POST_CLICK) {
                put(EventParamConstants.KEY_SOURCE, EventParamConstants.CLICK_PUBLISH_SOURCE_FEED)
            }
            if(PandoraToggle.openPlotHomeEntrance) {
                publishAnimView.show(this, binding.includeChoosePublishScene.root)
                binding.includeChoosePublishScene.root.isVisible = false
            } else {
                MetaRouter.Post.goPublishPost(
                    this,
                    ResIdBean.newInstance().setCategoryID(CategoryId.COMMUNITY_GAME_CARD)
                )
            }
        }

        binding.ivMsgEntrance.setOnAntiViolenceClickListener {
            MetaRouter.IM.goChatTabFragment(
                this,
                source = EventParamConstants.SRC_MESSAGE_LIST_ENTRANCE_MAPS
            )
        }
    }

    private fun initViewPager() {
        withState(viewModel) { state ->

            val vc = ViewConfiguration.get(requireContext())
            binding.vpFeed.setTouchSlop(vc.scaledPagingTouchSlop * 3)

            binding.vpFeed.offscreenPageLimit = 1
            binding.tlFeed.addOnTabSelectedListener(tabCallback)
            binding.vpFeed.registerOnPageChangeCallback(vpCallback)
            pagerAdapter = CommonTabStateAdapter(
                state.tagList.map { type ->
                    { createTabFragment(type) }
                },
                childFragmentManager,
                viewLifecycleOwner.lifecycle
            )
            binding.vpFeed.adapterAllowStateLoss = pagerAdapter
            tabLayoutMediator = TabLayoutMediator(
                binding.tlFeed,
                binding.vpFeed
            ) { tab: TabLayout.Tab, position: Int ->
                withState(viewModel) { state ->
                    val tabBinding = TabIndicatorHomeCommunityBinding.inflate(layoutInflater)
                    val tag = kotlin.runCatching { state.tagList[position] }.getOrNull() ?: return@withState
                    if (tag.imgUrl.isNotEmpty()) {
                        // 有图片的时候, 优先显示图片
                        glide?.apply {
                            load(tag.imgUrl)
                                .placeholder(R.drawable.placeholder_corner_4)
                                .fitCenter()
                                // 默认情况下, 图片比较糊, 需要手动指定一下图片大小, 如果指定的尺寸比实际的图片尺寸大, 并不会增大内存消耗
                                .override(tabBinding.ivNormal.maxWidth)
                                .into(tabBinding.ivNormal)
                            load(tag.imgUrl)
                                .placeholder(R.drawable.placeholder_corner_8)
                                .fitCenter()
                                // 默认情况下, 图片比较糊, 需要手动指定一下图片大小, 如果指定的尺寸比实际的图片尺寸大, 并不会增大内存消耗
                                .override(tabBinding.ivSelected.maxWidth)
                                .into(tabBinding.ivSelected)
                        }
                    } else {
                        tabBinding.tvNormal.text = tag.name
                        tabBinding.tvSelected.text = tag.name
                    }

                    tab.customView = tabBinding.root
                    tab.tag = tag
                }
            }
            tabLayoutMediator?.attach()
        }
    }

    override fun invalidate() {

    }

    override fun onDestroyView() {
        publishAnimView.setDismissCallback(null)
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        binding.tlFeed.clearOnTabSelectedListeners()
        binding.vpFeed.adapterAllowStateLoss = null
        binding.vpFeed.unregisterOnPageChangeCallback(vpCallback)
        FeedVideoHelper.releaseByScene(FeedVideoHelper.SCENE_COMMUNITY_TAB_FEED)
        super.onDestroyView()
    }

    override fun isEnableTrackPageExposure(): Boolean {
        return false
    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_NAME_FEED_TAB
    override fun onBottomNavReselected(item: MainBottomNavigationItem) {
        viewModel.scrollCurrentFeed()
    }
}