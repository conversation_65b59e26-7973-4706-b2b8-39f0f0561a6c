package com.socialplay.gpark.ui.post.tab

import android.content.ComponentCallbacks
import androidx.lifecycle.Observer
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.PublishPostInteractor
import com.socialplay.gpark.data.interactor.TabConfigInteractor
import com.socialplay.gpark.data.model.TabPendingConsumeData
import com.socialplay.gpark.data.model.choice.ChoiceTabInfo
import com.socialplay.gpark.data.model.choice.CommunityTabTargetType
import com.socialplay.gpark.data.model.choice.CommunityTabType
import com.socialplay.gpark.data.model.choice.checkIdAndModifyIfNeeded
import com.socialplay.gpark.data.model.post.FeedJumpTabInfo
import com.socialplay.gpark.data.model.post.FeedJumpTabRelayData
import com.socialplay.gpark.data.model.post.PostPublishStatus
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.main.MainBottomNavigationItem
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.videofeed.VideoFeedViewModel
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.extension.collectIn
import com.socialplay.gpark.util.getStringByGlobal
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2023/9/22
 * Desc:
 */

/**
 * @param [publishingState] 发帖状态
 * @param [jumpTabInfo] 需要跳转的tab数据 [com.socialplay.gpark.ui.post.tab.FeedJumpTabInfo]
 * @param [scrollFeed] 滚动feed标志位
 * @param [tagList] 所有的tag
 * @param [selectedTag] 当前选中的tag
 * @param [toastData] toast数据
 * @param [currentUid] 当前用户的uid
 */
data class CommunityFeedTabModelState(
    val publishingState: PostPublishStatus? = null,
    val jumpTabInfo: FeedJumpTabInfo? = null,
    val scrollFeed: Long = 0,
    val tagList: List<ChoiceTabInfo> = emptyList(),
    val selectedTag: Int = 0,
    val toastData: ToastData = ToastData.EMPTY,
    val currentUid: String? = null,
) : MavericksState

class CommunityFeedTabViewModel(
    private val repository: IMetaRepository,
    private val publishPostInteractor: PublishPostInteractor,
    private val accountInteractor: AccountInteractor,
    private val tabConfigTabInteractor: TabConfigInteractor,
    initialState: CommunityFeedTabModelState
) : BaseViewModel<CommunityFeedTabModelState>(initialState) {
    var tabList: List<ChoiceTabInfo> = mutableListOf()

    private val accountChangeListener = Observer<MetaUserInfo?> {
        setState {
            copy(currentUid = it?.uuid)
        }
    }

    init {
        initCurUid()
        refresh()
        observePublish()
    }

    private fun observePublish() = viewModelScope.launch {
        publishPostInteractor.publishPostSharedFlow.collectLatest {
            Timber.d("checkcheck_publish, ${it.status}")
            // 如果是发布视频则不提示在社区(等一个产品优化，现在视频发布没进度可以看)
            if (!it.isVideoPublish()) {
                distributePublishingItem(it)
            }
        }
    }

    private fun initCurUid() {
        setState {
            copy(currentUid = accountInteractor.curUuid)
        }
        accountInteractor.accountLiveData.observeForever(accountChangeListener)
    }

    override fun onCleared() {
        accountInteractor.accountLiveData.removeObserver(accountChangeListener)
        super.onCleared()
    }

    fun scrollCurrentFeed() {
        val timeStamp = System.currentTimeMillis()
        setState {
            copy(scrollFeed = timeStamp)
        }
    }

    fun refresh() {
        val tabs = getCommunityFeedTabs()
        repository.fetchBlockList().map { it ?: emptyList() }.execute { result ->
            if (result is Success) {
                val tagList = result().map { it.toCommunityTab() }
                copy(tagList = if (tagList.isEmpty()) tabs else (tabs + tagList).checkIdAndModifyIfNeeded())
            } else {
                copy(tagList = tabs)
            }
        }
    }

    fun changeSelectedTag(toTag: ChoiceTabInfo) {
        withState {
            val toIndex = it.tagList.indexOfFirst { eachTag ->
                eachTag == toTag
            }
            if (it.selectedTag == toIndex || toIndex < 0) {
                return@withState
            }
            setState {
                copy(selectedTag = toIndex)
            }
        }
    }

    fun changeSelectedTag(tagIndex: Int) {
        withState {
            if (it.selectedTag == tagIndex || tagIndex !in 0..it.tagList.lastIndex) {
                return@withState
            }
            setState {
                copy(selectedTag = tagIndex)
            }
        }
    }

    private fun getCommunityFeedTabs(): List<ChoiceTabInfo> {
        if (tabList.isNotEmpty()) {
            return tabList
        }
        val resultTabList: MutableList<ChoiceTabInfo> = ArrayList()
        resultTabList.addAll(createLocalTabs())
        tabConfigTabInteractor.getCommunityConfigTab()?.apply {
            resultTabList.addAll(this)
        }
        tabList = resultTabList.checkIdAndModifyIfNeeded()
        return tabList
    }

    private fun createLocalTabs(): MutableList<ChoiceTabInfo> {
        val tabs = mutableListOf<ChoiceTabInfo>(
            ChoiceTabInfo(
                id = -301,
                name = getStringByGlobal(R.string.user_follow_title),
                type = CommunityTabType.NATIVE.name,
                target = CommunityTabTargetType.FOLLOW.name,
            ),
            ChoiceTabInfo(
                id = -202,
                name = getStringByGlobal(R.string.recommend_cap),
                type = CommunityTabType.NATIVE.name,
                target = CommunityTabTargetType.RECOMMEND.name,
            ),
        )
        tabs.add(
            ChoiceTabInfo(
                id = -200,
                name = getStringByGlobal(R.string.kol_bottom_tab),
                type = CommunityTabType.NATIVE.name,
                target = CommunityTabTargetType.DISCOVER.name,
            )
        )
        return tabs
    }

    private fun distributePublishingItem(publishStatus: PostPublishStatus) {
        Timber.d("checkcheck_publish distributePublishingItem, next: ${publishStatus.status}, current: ${publishStatus.status}, errorMessage: ${publishStatus.errorMessage}")
        setState {
            val jumpToRecommend =
                (publishingState == null || publishingState.ts != publishStatus.ts) && publishStatus.hasPublishState()
            copy(
                publishingState = publishStatus,
                toastData = toastData.toMsg(publishStatus.errorMessage),
                jumpTabInfo = if (jumpToRecommend) FeedJumpTabInfo(
                    tagList.first { it.target == CommunityTabTargetType.RECOMMEND.name }
                ) else null
            )
        }
    }

    fun clearPublishing() {
        suspend {
            delay(2000)
        }.execute {
            if (it is Success) {
                copy(
                    publishingState = null,
                    jumpTabInfo = if (jumpTabInfo?.tab?.target == CommunityTabTargetType.RECOMMEND.name) null else jumpTabInfo,
                    toastData = ToastData.EMPTY
                )
            } else {
                copy()
            }
        }
    }

    fun clearRelay() {
        setState {
            copy(jumpTabInfo = null)
        }
    }

    /**
     * 接收处理跳转到feed底栏的中转数据
     * 当前支持类型：[FeedJumpTabRelayData.TYPE_JUMP_VIDEO_FEED]
     */
    fun collectRelay(
        tabNavigateRelayData: Flow<TabPendingConsumeData>,
        mainViewModel: MainViewModel
    ) {
        tabNavigateRelayData.collectIn(viewModelScope) { relayData ->
            if (relayData.targetTabId == MainBottomNavigationItem.FEED.itemId) {
                val data = GsonUtil.gsonSafeParseCollection<FeedJumpTabRelayData>(
                    relayData.data.getString(FeedJumpTabRelayData.BUNDLE_KEY)
                )
                mainViewModel.clearTabPendingConsumeData()
                withState { state ->
                    // 当前只支持视频流和推荐feed
                    val tab = state.tagList.firstOrNull { it.canJumpTab(data?.jumpReason) }
                    Timber.tag(VideoFeedViewModel.DATA_RELAY_TAG)
                        .d("feedTab collectRelay: $relayData, tab:$tab, currentJumpBundle:${state.jumpTabInfo}")
                    setState {
                        copy(
                            jumpTabInfo = tab?.let { FeedJumpTabInfo(it) }
                                ?: jumpTabInfo
                        )
                    }
                }
            }
        }
    }

    companion object :
        KoinViewModelFactory<CommunityFeedTabViewModel, CommunityFeedTabModelState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: CommunityFeedTabModelState
        ): CommunityFeedTabViewModel {
            return CommunityFeedTabViewModel(get(), get(), get(), get(), state)
        }
    }
}