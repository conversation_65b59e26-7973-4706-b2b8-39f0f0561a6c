package com.socialplay.gpark.ui.post.tab

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.commitNow
import com.meta.web.ui.contract.IWebContent
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.BgGradient
import com.socialplay.gpark.data.model.choice.ChoiceTabInfo
import com.socialplay.gpark.data.model.choice.CommunityTabTargetType
import com.socialplay.gpark.data.model.choice.CommunityTabType
import com.socialplay.gpark.databinding.FragmentCommunityTabParentBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.BaseLazyFragment
import com.socialplay.gpark.ui.kol.KolCreatorFragment
import com.socialplay.gpark.ui.post.feed.tag.FollowingCommunityFeedFragment
import com.socialplay.gpark.ui.post.feed.tag.RecommendCommunityFeedFragmentV2
import com.socialplay.gpark.ui.post.feed.tag.TagCommunityFeedFragment
import com.socialplay.gpark.ui.web.WebPageFactory
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.WebUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.safeEnumValueOf
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.sp
import com.socialplay.gpark.util.extension.visible
import okhttp3.HttpUrl.Companion.toHttpUrl
import org.koin.androidx.viewmodel.ext.android.activityViewModel

class CommunityTabContentFragment : BaseLazyFragment<FragmentCommunityTabParentBinding>(R.layout.fragment_community_tab_parent), IWebContent.IWebContentLoadListener {
    private var mChoiceTabInfo: ChoiceTabInfo? = null
    private var bgElement: BgGradient = BgGradient()
    private var translucentToolBar = false
    private var type: String? = null
    private var tabHeight: Int = HEIGHT_TOP_MENU.dp
    private var translucentTopHeight = 0
    private val isH5Tab: Boolean
        get() = CommunityTabType.H5.name == type
    private var isH5LoadSuccess: Boolean = false
//    private val skeletonViewModel: CommunitySkeletonViewModel by activityViewModel()


//    override fun hasChildFragment(): Boolean {
//        return true
//    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initStyle()
        tabHeight =
            arguments?.getInt(EXTRA_KEY_TAB_HEIGHT, HEIGHT_TOP_MENU.dp) ?: HEIGHT_TOP_MENU.dp
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentCommunityTabParentBinding? {
        return FragmentCommunityTabParentBinding.inflate(inflater, container, false)
    }

    private fun initStyle() {
        (arguments?.getSerializable(EXTRA_KEY_DATA) as? ChoiceTabInfo)?.let {
            mChoiceTabInfo = it
            bgElement = it.bgGradientColor
            translucentToolBar = it.translucentToolBar
            type = it.type
        }
    }

    fun updateStyle(tabInfo: ChoiceTabInfo) {
        if (isStateSaved || !isAdded) return
        arguments = arguments?.apply {
            putSerializable(EXTRA_KEY_DATA, tabInfo)
        }
        initStyle()
        setTopBgUi()
    }

    private fun isH5TranslucentToolBar(): Boolean {
        return isH5Tab && translucentToolBar
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        init()
    }

    fun init() {
        resizeTopView()
        setTopBgUi()
        childFragmentManager.commitNow {
            val tag = getChoiceTabFragmentTag(mChoiceTabInfo)

            // 在一些情况下，可能会出现多个fragment的情况，这里只保留一个
            childFragmentManager.fragments.forEach {
                if (it.tag != tag) {
                    remove(it)
                }
            }

            var fragment = childFragmentManager.findFragmentByTag(tag)
            if (fragment != null) {
                checkWebFragmentReload(fragment)
                this.show(fragment)
            } else {
                fragment = fillChildFragment()?.also {
                    add(R.id.fl_container, it, tag)
                }
            }

            if (fragment is IWebContent) {
                fragment.registerContentLoadListener(this@CommunityTabContentFragment)
            }
        }
    }

    private fun checkWebFragmentReload(fragment: Fragment) {
        if (fragment is IWebContent) {
            fragment.checkReload()
        }
    }

    private fun needH5TopPlaceHolder(): Boolean {
        return isH5TranslucentToolBar() && !isH5LoadSuccess
    }

    private fun resizeTopView() {
//        translucentTopHeight = StatusBarUtil.getStatusBarHeight(requireContext()) + tabHeight
//        val heightTop = if (isH5TranslucentToolBar() && isH5LoadSuccess) 0 else translucentTopHeight
//        binding.spaceTop.setSize(RelativeLayout.LayoutParams.MATCH_PARENT, heightTop)
//        if (needH5TopPlaceHolder()) {
//            binding.rlH5TopPlaceHolder.setSize(
//                RelativeLayout.LayoutParams.MATCH_PARENT,
//                translucentTopHeight
//            )
//            binding.rlH5TopPlaceHolder.visibility = View.VISIBLE
//        }
    }

    private fun safeParseColor(colorStr: String, resIdColorDefault: Int): Int {
        return kotlin.runCatching { Color.parseColor(colorStr) }
            .getOrDefault(ContextCompat.getColor(requireContext(), resIdColorDefault))
    }

    private fun setTopBgUi() {
        if (needH5TopPlaceHolder()) {
            binding.rlH5TopPlaceHolder.setBackgroundColor(
                safeParseColor(
                    bgElement.start,
                    R.color.color_f5f5f7
                )
            )
        }
        binding.viewTopBgGradient.gone()
        binding.spaceTop.gone()
//        binding.root.setBackgroundColor(
//            safeParseColor(
//                bgElement.solid,
//                R.color.color_home_new_bg
//            )
//        )
//        binding.viewTopBgGradient.setHeight((ScreenUtil.getScreenWidth(requireContext()) * 120f / 375).toInt())
//        if (isH5Tab) {
//            binding.viewTopBgGradient.background = GradientDrawable().apply {
//                shape = GradientDrawable.RECTANGLE
//                gradientType = GradientDrawable.LINEAR_GRADIENT
//                orientation = GradientDrawable.Orientation.TOP_BOTTOM
//                colors = intArrayOf(
//                    safeParseColor(bgElement.start, R.color.white),
//                    safeParseColor(bgElement.end, R.color.white)
//                )
//            }
//        } else {
//            if (mChoiceTabInfo?.isCommunityDiscoverTab() == true) {
//                skeletonViewModel.discoverTabSkeleton.observe(viewLifecycleOwner){
//                    if(!it.first){
//                        animateVisible()
//                    }
//                }
//            }
//            if (mChoiceTabInfo?.isCommunityCircleTab() == true) {
//                skeletonViewModel.circleTabSkeleton.observe(viewLifecycleOwner){
//                    if(!it){
//                        animateVisible()
//                    }
//                }
//            }
////            binding.viewTopBgGradient.setBackgroundResource(R.drawable.bg_top_community_tab)
//            binding.viewTopBgGradient.setBackgroundResource(R.color.white)
//        }

    }

    private fun animateVisible() {
        if (binding.viewTopBgGradient.alpha == 1f) {
            return
        }
        binding.viewTopBgGradient.animate().setDuration(200).alpha(1f).start()
    }

    private fun fillChildFragment(): Fragment? {
        val tabInfo = mChoiceTabInfo ?: return null
        return when (safeEnumValueOf<CommunityTabType>(tabInfo.type)) {
            CommunityTabType.H5 -> {
                WebPageFactory.createEmbedded(
                    url = buildNewUrl(targetUrl = tabInfo.target),
                    gamePackageName = null,
                    isCommunity = true,
                    from = "community",
                    needWebLifecycle = true,
                    fromTab = true
                )
            }

            CommunityTabType.NATIVE -> {
                getNativeFragmentByTarget(tabInfo)
            }

            else -> null
        }
    }

    private fun getNativeFragmentByTarget(choiceTabInfo: ChoiceTabInfo): Fragment? {
        return when (choiceTabInfo.target) {
            CommunityTabTargetType.FOLLOW.name -> {
                FollowingCommunityFeedFragment.newInstance(choiceTabInfo.toTagCommunityFeedFragmentArgs())
            }

            CommunityTabTargetType.RECOMMEND.name -> {
                RecommendCommunityFeedFragmentV2.newInstance(choiceTabInfo.toTagCommunityFeedFragmentArgs())
            }

            CommunityTabTargetType.DISCOVER.name -> {
                KolCreatorFragment.newInstance(
                    true,
                    choiceTabInfo.toTagCommunityFeedFragmentArgs().blockName
                )
            }

            CommunityTabTargetType.CIRCLE_BLOCK.name -> {
                TagCommunityFeedFragment.newInstance(choiceTabInfo.toTagCommunityFeedFragmentArgs())
            }

            else -> null
        }
    }

    private fun buildNewUrl(targetUrl: String): String {

        // Timber.i("Density====${ScreenUtil.getScreenDensity(requireContext())}, DensityDpi = ${ScreenUtil.getScreenDensityDpi(requireContext())}")
        val height1X =
            (translucentTopHeight / ScreenUtil.getScreenDensity(requireContext())).toInt()
        return if (WebUtil.isHttpOrHttpsScheme(targetUrl)) {
            val oldUrl = targetUrl.replace("#", "/%23/").toHttpUrl()
            val newUrl = oldUrl.newBuilder()
                .addQueryParameter("source", "community_tab")
                .addQueryParameter("isTranslucentTop", translucentToolBar.toString())
                .addQueryParameter("translucentTopHeight", height1X.toString())
                .build()
                .toString()
            newUrl.replace("/%23/", "#")
        } else {
            targetUrl
        }
    }

    override fun loadLazyData() {
//        if (mChoiceTabInfo?.isCommunityDiscoverTab() == true || mChoiceTabInfo?.isCommunityCircleTab() == true) {
//            binding.viewTopBgGradient.alpha = 0f
//        }
    }

    override fun onDestroyView() {
        val tag = getChoiceTabFragmentTag(mChoiceTabInfo)
        childFragmentManager.findFragmentByTag(tag)?.let {
            if (it is IWebContent) {
                it.unregisterContentLoadListener(this@CommunityTabContentFragment)
            }
        }
        super.onDestroyView()
    }


    companion object {
        const val TAG_PREFIX = "community_tab_"
        private const val TAG = "CommunityTabContentFragment"
        private const val EXTRA_KEY_DATA = "EXTRA_KEY_DATA"
        private const val EXTRA_KEY_TAB_HEIGHT = "EXTRA_TAB_HEIGHT"
        const val HEIGHT_TOP_MENU = 44

        /**
         * choiceTabInfo 精选tab信息
         * tabHeight: tablayout高度，默认是50
         */
        fun newInstance(
            choiceTabInfo: ChoiceTabInfo,
            tabHeight: Int = HEIGHT_TOP_MENU.dp
        ): CommunityTabContentFragment {
            return CommunityTabContentFragment().apply {
                arguments = Bundle().apply {
                    putSerializable(EXTRA_KEY_DATA, choiceTabInfo)
                    putInt(EXTRA_KEY_TAB_HEIGHT, tabHeight)
                }
            }
        }

        private fun getChoiceTabFragmentTag(choiceTabInfo: ChoiceTabInfo?): String {
            return "${TAG_PREFIX}${choiceTabInfo?.type ?: ""}_${choiceTabInfo?.id ?: "0"}"
        }
    }

    override fun onResume() {
        super.onResume()
        if (isH5LoadSuccess && mChoiceTabInfo != null) {
            Analytics.track(EventConstants.EVENT_COMMUNITY_MENU_SHOW) {
                put("menu_id", mChoiceTabInfo!!.id)
                put("menu_name", mChoiceTabInfo!!.name.orEmpty())
            }
        }
    }

    private var isLoadFinishTrack = false
    override fun onContentLoadFinish(success: Boolean) {
        if (success && !isLoadFinishTrack && mChoiceTabInfo != null) {
            isLoadFinishTrack = true
            Analytics.track(EventConstants.EVENT_COMMUNITY_MENU_SHOW) {
                put("menu_id", mChoiceTabInfo!!.id)
                put("menu_name", mChoiceTabInfo!!.name.orEmpty())
            }
        }
        isH5LoadSuccess = success
        if (success && isH5TranslucentToolBar()) {
            binding.rlH5TopPlaceHolder.gone()
            binding.spaceTop.setHeight(0)
        }
    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_COMMUNITY_TAB_CONTENT
}