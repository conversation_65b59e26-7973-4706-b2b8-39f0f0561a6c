package com.socialplay.gpark.ui.post.tab

import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.constraintlayout.motion.widget.MotionLayout
import androidx.core.view.WindowInsetsControllerCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ViewMotionPublishSceneBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by bo.li
 * Date: 2023/10/12
 * Desc:
 */
class PublishSceneAnimView {
    private var decorView: ViewGroup? = null
    private lateinit var binding: ViewMotionPublishSceneBinding
    private val isShowIng = AtomicBoolean(false)
    private lateinit var windowController: WindowInsetsControllerCompat
    private var dismissCallback: (() -> Unit)? = null

    fun setDismissCallback(callback: (() -> Unit)?) {
        dismissCallback = callback
    }

    fun show(fragment: Fragment, anchorView: View) {
        show(fragment) {
            startWidth = anchorView.layoutParams.width
            startHeight = anchorView.layoutParams.height
            val size = IntArray(2).apply { anchorView.getLocationInWindow(this) }
            posX = size[0]
            posY = size[1]
        }
    }

    private fun show(fragment: Fragment, builderInvoke: Builder.() -> Unit) {
        bindFragment(fragment)
        decorView = fragment.activity?.window?.decorView.let { if (it is ViewGroup) it else null }
        binding = ViewMotionPublishSceneBinding.inflate(fragment.layoutInflater, decorView, false)
        windowController =
            WindowInsetsControllerCompat(fragment.requireActivity().window, binding.root)
        val builder = Builder().apply(builderInvoke)
        initView(fragment, binding, builder)
        showView(fragment)
    }

    private fun initView(
        fragment: Fragment,
        binding: ViewMotionPublishSceneBinding,
        builder: Builder
    ) {
        binding.space.setHeight(ScreenUtil.getNavigationBarHeightIfRoom(fragment.requireActivity()))
        binding.tvTemplate.isVisible = PandoraToggle.openPlotHomeEntrance
        initMotion()
        initClick(fragment, binding)
    }

    private fun initMotion() {
        binding.motionLayout.post { binding.motionLayout.transitionToEnd() }
        binding.motionLayout.setTransitionListener(object : MotionLayout.TransitionListener {
            override fun onTransitionStarted(
                motionLayout: MotionLayout?,
                startId: Int,
                endId: Int
            ) {
            }

            override fun onTransitionChange(
                motionLayout: MotionLayout?,
                startId: Int,
                endId: Int,
                progress: Float
            ) {
            }

            override fun onTransitionCompleted(motionLayout: MotionLayout?, currentId: Int) {
                if (currentId == R.id.start) {
                    dismissView()
                }
            }

            override fun onTransitionTrigger(
                motionLayout: MotionLayout?,
                triggerId: Int,
                positive: Boolean,
                progress: Float
            ) {
            }
        })
    }

    private fun initClick(fragment: Fragment, binding: ViewMotionPublishSceneBinding) {
        binding.tvTemplate.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_FEED_CLICK_MOMENTS)
            MetaRouter.Plot.list(fragment, CategoryId.COMMUNITY_GAME_CARD)
            dismissView()
        }
        binding.tvPost.setOnAntiViolenceClickListener {
            MetaRouter.Post.goPublishPost(
                fragment,
                ResIdBean.newInstance().setCategoryID(CategoryId.COMMUNITY_GAME_CARD),
                enableOutfitShare = true
            )
            dismissView()
        }
        binding.vClick.setOnAntiViolenceClickListener {
            dismissWithTransition()
        }
        binding.root.setOnAntiViolenceClickListener {
            dismissWithTransition()
        }
    }

    private fun bindFragment(fragment: Fragment) {
        fragment.lifecycle.addObserver(LifecycleEventObserver { source, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                backPressedCallback.isEnabled = isShowing()
                fragment.requireActivity().onBackPressedDispatcher.addCallback(
                    source, backPressedCallback
                )
            }
        })
    }

    private val backPressedCallback: OnBackPressedCallback = object : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {
            dismissWithTransition()
        }
    }

    private fun showView(fragment: Fragment) {
        runCatching {
            if (isShowIng.compareAndSet(false, true)) {
                decorView?.also { viewGroup ->
                    // 取消沉浸式导航栏
                    viewGroup.addView(
                        binding.root, ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
                        )
                    )
                }
                backPressedCallback.isEnabled = true
                fragment.requireActivity().onBackPressedDispatcher.addCallback(
                    fragment.viewLifecycleOwner, backPressedCallback
                )
            }
        }
    }

    fun isShowing(): Boolean {
        return isShowIng.get()
    }

    fun dismissWithTransition() {
        binding.motionLayout.post {
            binding.motionLayout.transitionToStart()
        }
    }

    private fun dismissView() {
        runCatching {
            if (isShowIng.compareAndSet(true, false)) {
                decorView?.also { viewGroup ->
                    viewGroup.removeView(binding.root)
                }
                dismissCallback?.invoke()
                backPressedCallback.isEnabled = false
            }
        }
    }

    class Builder {
        var posX: Int = 0
        var posY: Int = 0
        var type: Int = -1
        var startWidth = 0
        var startHeight = 0
    }
}