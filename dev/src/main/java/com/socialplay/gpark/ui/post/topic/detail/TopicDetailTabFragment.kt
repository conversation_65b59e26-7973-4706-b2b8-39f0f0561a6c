package com.socialplay.gpark.ui.post.topic.detail

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.ApiResultCodeException
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.event.TopicMetaData
import com.socialplay.gpark.data.model.post.event.TopicMetaDataUpdateEvent
import com.socialplay.gpark.data.model.post.topic.TopicTabType
import com.socialplay.gpark.data.model.share.ShareContent
import com.socialplay.gpark.databinding.FragmentTopicDetailTabBinding
import com.socialplay.gpark.databinding.TabIndicatorProfileBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.view.FollowView
import com.socialplay.gpark.ui.view.video.FeedVideoHelper
import com.socialplay.gpark.ui.view.viewpager.CommonTabStateAdapter
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.addOnOffsetChangedListener
import com.socialplay.gpark.util.extension.addOnTabSelectedListener
import com.socialplay.gpark.util.extension.attach
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.getDrawableByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.registerOnPageChangeCallback
import com.socialplay.gpark.util.extension.setFontFamily
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import kotlinx.parcelize.Parcelize
import org.greenrobot.eventbus.EventBus
import kotlin.math.abs

/**
 * Created by bo.li
 * Date: 2024/4/7
 * Desc: 话题详情页-头部详情页
 */
@Parcelize
data class TopicDetailTabFragmentArgs(
    val tagInfo: PostTag,
    val initTab: Int
) : Parcelable

class TopicDetailTabFragment :
    BaseFragment<FragmentTopicDetailTabBinding>(R.layout.fragment_topic_detail_tab) {

    private val viewModel: TopicDetailTabViewModel by fragmentViewModel()
    private val args: TopicDetailTabFragmentArgs by args()
    private var tabLayoutMediator: TabLayoutMediator? = null
    private lateinit var pagerAdapter: CommonTabStateAdapter

    private val tabCallback = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, true)
        }

        override fun onTabUnselected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, false)
        }

        override fun onTabReselected(tab: TabLayout.Tab) {}
    }

    private val vpCallback = object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            viewModel.changeSelectedTag(position)
        }
    }

    private val appBarStateChangeListener by lazy {
        AppBarLayout.OnOffsetChangedListener { appBarLayout, verticalOffset ->
            val absVerticalOffset = abs(verticalOffset)
            binding.titleTopic.titleView.alpha =
                if (absVerticalOffset < appBarLayout.totalScrollRange) 0F else 1F
        }
    }

    companion object {
        private const val MAX_PROGRESS = 100
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentTopicDetailTabBinding? {
        return FragmentTopicDetailTabBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initClickEvent()
        initView()
        initData()
    }

    private fun initView() {
        initViewPager()
        binding.ablTopic.addOnOffsetChangedListener(viewLifecycleOwner, appBarStateChangeListener)
    }

    private fun initData() {
        viewModel.registerAsyncErrorToast(TopicDetailTabModelState::followed)
        viewModel.registerToast(TopicDetailTabModelState::toastData)

        viewModel.addTopicViewCount()

        viewModel.setupRefreshLoadingCustomFail(
            TopicDetailTabModelState::topicDetailInfo,
            binding.parentLoading,
            null,
            { e, _ ->
                if ((e as? ApiResultCodeException)?.code == 1016) {
                    binding.parentLoading.showEmpty(
                        msg = getString(R.string.tag_error_code),
                        resId = R.drawable.icon_no_recent_activity,
                        enableClick = false
                    )
                } else {
                    binding.parentLoading.showError()
                }
            }
        ) {
            viewModel.refresh()
        }
        // tab栏
        viewModel.onEach(TopicDetailTabModelState::sortTabList, deliveryMode = uniqueOnly()) {
            // TODO 目前不会更新tab
//            val tabs = it.map { type ->
//                { createTabFragment(type) }
//            }
//            pagerAdapter.fragmentCreators = tabs
//            pagerAdapter.notifyDataSetChanged()
        }
        // 切换tab
        viewModel.onEach(TopicDetailTabModelState::selectedTabIndex) {
            updatePagerIdx(it)
        }
        // 总体详情
        viewModel.onAsync(TopicDetailTabModelState::topicDetailInfo, onSuccess = {
            binding.ivShareTopic.visible()
        }, onFail = { _, _ ->
            binding.ivShareTopic.gone()
        })
        // 关注状态
        viewModel.onAsync(TopicDetailTabModelState::followed, onLoading = {
            binding.includeTopicHeader.tvTopicFollow.isEnabled = false
        }, onFail = { _, _ ->
            binding.includeTopicHeader.tvTopicFollow.isEnabled = true
        }, onSuccess = {
            binding.includeTopicHeader.tvTopicFollow.isEnabled = true
            if (it) {
                binding.includeTopicHeader.tvTopicFollow.apply {
                    text = getString(R.string.following_cap)
                    setTextColor(getColorByRes(R.color.color_B3B3B3))
                    setFontFamily(R.font.poppins_regular_400)
                    setBackgroundDrawable(getDrawableByRes(R.drawable.bg_d9d9d9_storke_1))
                }
            } else {
                binding.includeTopicHeader.tvTopicFollow.apply {
                    text = getString(R.string.follow)
                    setTextColor(getColorByRes(R.color.color_1A1A1A))
                    setFontFamily(R.font.poppins_semi_bold_600)
                    setBackgroundDrawable(getDrawableByRes(R.drawable.bg_ffdc1c_round_100))
                }
            }
        })
        // 浏览数 & 关注数
        viewModel.onEach(
            TopicDetailTabModelState::viewCount,
            TopicDetailTabModelState::followerCount
        ) { views, follows ->
            binding.includeTopicHeader.tvViews.text =
                getString(
                    R.string.community_topic_item_view_flowing_count,
                    UnitUtil.formatKMCount(views),
                    UnitUtil.formatKMCount(follows)
                )
        }
        // 标题
        viewModel.onEach(TopicDetailTabModelState::title) {
            if (it.isNullOrBlank()) {
                binding.titleTopic.setTitle("")
                binding.includeTopicHeader.tvTopicName.text = ""
            } else {
                val title = getString(R.string.community_hashtag_prefix, it)
                binding.titleTopic.setTitle(title)
                binding.includeTopicHeader.tvTopicName.text = title
            }
        }
        // 发帖进度
        viewModel.onEach(
            TopicDetailTabModelState::publishingState
        ) { publishingState ->
            binding.pbPublish.isVisible = publishingState != null && !publishingState.publishOver()
            publishingState ?: return@onEach
            binding.pbPublish.progress =
                ((publishingState.uploadStatus?.curTotalPercent ?: 0.0) * MAX_PROGRESS).toInt()
                    .coerceAtMost(MAX_PROGRESS)
            if (publishingState.publishOver()) {
                viewModel.clearPublishing()
            }
        }
        // 跳转最新tab
        viewModel.onEach(TopicDetailTabModelState::jumpToNew) {
            if (it) {
                viewModel.changeSelectedTag(TopicTabType.New)
            }
        }
    }

    private fun updatePagerIdx(idx: Int) {
        if (binding.vp.currentItem != idx && idx in 0 until pagerAdapter.itemCount) {
            binding.vp.setCurrentItem(idx, false)
        }
    }

    private fun setTabSelectUI(tab: TabLayout.Tab, select: Boolean) {
        val cv = tab.customView ?: return
        val tabBinding = TabIndicatorProfileBinding.bind(cv)
        tabBinding.tvNormal.isInvisible = select
        tabBinding.tvSelected.isInvisible = !select
    }

    private fun initViewPager() {
        withState(viewModel) {
            binding.vp.offscreenPageLimit = 1
            binding.tlTopicDetail.addOnTabSelectedListener(viewLifecycleOwner, tabCallback)
            binding.vp.registerOnPageChangeCallback(viewLifecycleOwner, vpCallback)
            pagerAdapter = CommonTabStateAdapter(
                it.sortTabList.map { type ->
                    { createTabFragment(type) }
                },
                childFragmentManager,
                viewLifecycleOwner.lifecycle
            )
            binding.vp.adapterAllowStateLoss = pagerAdapter
            updatePagerIdx(it.selectedTabIndex)
            tabLayoutMediator = TabLayoutMediator(
                binding.tlTopicDetail,
                binding.vp
            ) { tab: TabLayout.Tab, position: Int ->
                withState(viewModel) {
                    val tabBinding = TabIndicatorProfileBinding.inflate(layoutInflater)
                    val nameRes =
                        kotlin.runCatching { it.sortTabNameResList[position] }.getOrNull() ?: 0
                    val name = kotlin.runCatching { getString(nameRes) }.getOrNull()
                    tabBinding.tvNormal.text = name
                    tabBinding.tvSelected.text = name
                    val type = it.sortTabList[position].sortType
                    tab.customView = tabBinding.root
                    tab.tag = type
                }
            }
            tabLayoutMediator?.attach(viewLifecycleOwner)
        }
    }

    private fun createTabFragment(type: TopicTabType): Fragment {
        return TopicSortFeedFragment.newInstance(type.toTopicTabFeedFragmentArgs(args.tagInfo))
    }

    private fun initClickEvent() {
        binding.titleTopic.setOnBackAntiViolenceClickedListener {
            navigateUp()
        }
        binding.ivShareTopic.setOnAntiViolenceClickListener {
            withState(viewModel) {
                it.topicInfo?.let { item ->
                    MetaRouter.Share.share(
                        this,
                        ShareContent(ShareContent.TYPE_TOPIC, GsonUtil.safeToJson(item)),
                        "topic_detail_${args.tagInfo.tagId}"
                    ) { success, _ ->
                        if (success) {

                        }
                    }
                }
            }
        }
        binding.includeTopicHeader.tvTopicFollow.setOnAntiViolenceClickListener {
            viewModel.changeFollow()
        }
        binding.includeTopicPublish.root.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_COMMUNITY_ADD_POST_CLICK) {
                put(EventParamConstants.KEY_SOURCE, EventParamConstants.CLICK_PUBLISH_SOURCE_TOPIC)
                put(EventParamConstants.KEY_TAG_PAGE, args.tagInfo.tagId)
            }
            MetaRouter.Post.goPublishPost(
                this,
                resIdBean = ResIdBean.newInstance().setCategoryID(CategoryId.TOPIC_DETAIL),
                tags = listOf(args.tagInfo)
            )
        }
    }

    override fun invalidate() {

    }

    override fun onDestroyView() {
        FeedVideoHelper.releaseByScene(FeedVideoHelper.SCENE_COMMUNITY_TAB_FEED)
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().post(
            TopicMetaDataUpdateEvent(
                metaData = TopicMetaData(
                    topicId = args.tagInfo.tagId,
                    viewCount = viewModel.oldState.viewCount,
                    followCount = viewModel.oldState.followerCount,
                )
            )
        )
    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_TOPIC_DETAIL_PARENT
}