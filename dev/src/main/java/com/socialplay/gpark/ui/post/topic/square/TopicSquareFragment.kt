package com.socialplay.gpark.ui.post.topic.square

import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.view.isVisible
import com.airbnb.epoxy.EpoxyController
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.views.divider
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.core.views.spacer
import com.socialplay.gpark.ui.post.feed.CommunityFeedFragment
import com.socialplay.gpark.ui.post.feed.ICommunityLiveRoomListener
import com.socialplay.gpark.ui.post.feed.ICommunityTopicListener
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModel
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelState
import com.socialplay.gpark.ui.post.feed.communityFeed
import com.socialplay.gpark.ui.post.feed.communityTitle
import com.socialplay.gpark.ui.post.feed.liveRoomBlock
import com.socialplay.gpark.ui.post.feed.topicBlock
import com.socialplay.gpark.ui.room.HomeRoomAnalytics
import com.socialplay.gpark.ui.room.RoomDetailDialog
import com.socialplay.gpark.ui.view.LoadingView
import com.socialplay.gpark.ui.view.TitleBarLayout
import com.socialplay.gpark.ui.view.video.FeedVideoHelper
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getDrawableByRes
import com.socialplay.gpark.util.extension.ifEmptyNull
import com.socialplay.gpark.util.extension.toast
import org.koin.core.context.GlobalContext

/**
 * Created by bo.li
 * Date: 2024/4/7
 * Desc:
 */
class TopicSquareFragment : CommunityFeedFragment(), ICommunityTopicListener, ICommunityLiveRoomListener {

    private val viewModel: TopicSquareViewModel by fragmentViewModel()
    override val feedViewModel: BaseCommunityFeedViewModel<ICommunityFeedModelState>
        get() = viewModel as BaseCommunityFeedViewModel<ICommunityFeedModelState>

    override val showUserStatus: Boolean = true

    override val likeLocation: String = EventParamConstants.LOCATION_LIKE_TOPIC_SQUARE

    override val showPin: Boolean = false

    override val showTagList: Boolean = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.topicObserveAccount()
    }

    override fun initTitleBar(titleBar: TitleBarLayout) {
        val titleImage = ImageView(
            requireContext()
        ).apply {
            val layoutParams = FrameLayout.LayoutParams(
                78.dp,
                MATCH_PARENT
            ).apply {
                gravity = Gravity.CENTER
            }
            setLayoutParams(layoutParams)
            setImageDrawable(getDrawableByRes(R.drawable.img_topics_square_card_title))
        }

        titleBar.addView(titleImage)
        titleBar.setOnBackAntiViolenceClickedListener {
            navigateUp()
        }
        titleBar.isVisible = true
    }

    override fun goPost(item: CommunityFeedInfo) {
        Analytics.track(EventConstants.EVENT_COMMUNITY_POST_CLICK) {
            put(EventParamConstants.KEY_POSTID, item.postId)
            item.tagList?.map { it.tagId }?.let { put(EventParamConstants.KEY_TAG_LIST, it) }
        }
        val videoProgress = FeedVideoHelper.getVideoProgressByResId(item.localUniqueId)
        MetaRouter.Post.goPostDetail(this, item.postId, "topic_square", videoProgress = videoProgress)
    }

    override fun goProfile(uuid: String) {
        MetaRouter.Profile.other(this, uuid, "topic_square")
    }

    override fun goTopicDetail(tag: PostTag) {
        topDetail(tag, null)
    }

    override fun trackTopicRankShow(topic: PostTag, rank: Int) {
        // 我关注的话题不报埋点
    }

    override fun goTopic(tag: PostTag, postId: String) {
        if (tag.tagId <= 0) {
            toast(R.string.tag_reviewing_toast)
            return
        }
        topDetail(tag, postId)
    }

    private fun topDetail(tag: PostTag, postId: String?) {
        MetaRouter.Post.topicDetail(this, tag, EventParamConstants.SOURCE_TOPIC_SQUARE, postId)
    }

    override fun goAllLiveRoom() {
        MetaRouter.HomeRoom.goAllRoom(this)
    }

    override fun showRoomDetail(item: ChatRoomInfo) {
        HomeRoomAnalytics.trackRoomClick(item, CategoryId.TOPIC_SQUARE)
        RoomDetailDialog.show(this, item, ResIdBean().setGameId(item.platformGameId).setCategoryID(CategoryId.TOPIC_SQUARE))
    }

    override fun trackLiveRoomShow(item: ChatRoomInfo) {
        HomeRoomAnalytics.trackRoomShow(item, CategoryId.TOPIC_SQUARE)
    }

    override fun refreshPage() {
        viewModel.refresh()
    }

    override fun loadMoreFeed() {
        viewModel.loadMoreFeed()
    }

    override fun statusBarVisible(): Boolean = true

    override fun getFeedController(): EpoxyController = simpleController(
        viewModel,
        TopicSquareModelState::list,
        TopicSquareModelState::loadMore,
        TopicSquareModelState::myTopics,
        TopicSquareModelState::liveRooms,
    ) { feedList, loadMore, asyncTopics, liveRooms ->
        // 话题
        asyncTopics().ifEmptyNull()?.let {
            topicBlock(getString(R.string.my_topics), it, this@TopicSquareFragment)
        }
        // 语音房
        liveRooms.ifEmptyNull()?.let {
            liveRoomBlock(getString(R.string.live_rooms), it, this@TopicSquareFragment)
        }
        // feed
        if (feedList.isNotEmpty()) {
            spacer(height = 16.dp)
            communityTitle(getString(R.string.top_discussions))
        }
        val screenWidth = ScreenUtil.getScreenWidth(requireContext())
        val contentWidth = screenWidth - 32.dp
        feedList.forEachIndexed { index, communityFeedInfo ->
            val playLikeAnim = feedViewModel.playLikeAnimPostSet.contains(communityFeedInfo.postId)
            if (playLikeAnim) {
                feedViewModel.playLikeAnimPostSet.remove(communityFeedInfo.postId)
            }
            communityFeed(
                contentWidth,
                communityFeedInfo,
                index,
                showUserStatus,
                showPin,
                12.dp,
                this@TopicSquareFragment,
                playLikeAnim,
                accountInteractor.curUuid,
                this@TopicSquareFragment
            )
        }
        // 加载更多
        if (feedList.isNotEmpty()) {
            loadMoreFooter(loadMore, showEnd = true) {
                loadMoreFeed()
            }
        }
    }

    override fun getFeedTag(): String? = null

    override fun initLoadingView(loading: LoadingView) {

    }

    override fun refreshParent() {

    }

    override fun invalidate() {

    }

    override val useVideoFunc: Boolean = true

    override fun getPageName(): String = PageNameConstants.FRAGMENT_TOPIC_SQUARE
}