package com.socialplay.gpark.ui.post.v2

import android.app.Application
import android.content.ComponentCallbacks
import android.content.Context
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.meta.box.biz.friend.model.FriendInfo
import com.meta.pandora.utils.ConcurrentSet
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.ApiDataException
import com.socialplay.gpark.data.base.PagingApiResult
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.account.UserFollowEvent
import com.socialplay.gpark.data.model.community.PostCommentContent
import com.socialplay.gpark.data.model.community.UserMuteStatus
import com.socialplay.gpark.data.model.post.AddPostCommentReplyTarget
import com.socialplay.gpark.data.model.post.OpinionRequestBody
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.post.PostCommentRequestBody
import com.socialplay.gpark.data.model.post.PostDetail
import com.socialplay.gpark.data.model.post.PostPublish
import com.socialplay.gpark.data.model.post.PostPublish.Companion.filterEmptyUrl
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.post.PostReplyListRequestBody
import com.socialplay.gpark.data.model.post.PostReplyRequestBody
import com.socialplay.gpark.data.model.post.PostStyleCard
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.PostUgcDesignCard
import com.socialplay.gpark.data.model.post.event.PostMetaDataUpdateEvent
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.editor.detail.commentlist.BaseCommentListViewModel
import com.socialplay.gpark.ui.gamedetail.unify.BaseGameDetailCommonFragment
import com.socialplay.gpark.ui.notice.NotificationPermissionManager
import com.socialplay.gpark.util.ToastData
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.koin.android.ext.android.get
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/09/27
 *     desc   :
 * </pre>
 */
/**
 * @param fromTagDetailInfo 从话题详情页进入时，携带的话题信息
 * @param viewCountAdded 是否已经添加过话题浏览量
 */
data class PostDetailState(
    val postId: String,
    val targetCommentId: String?,
    val targetReplyId: String?,
    val showCommentPinRedDot: Boolean,
    val detail: Async<PostDetail> = Uninitialized,
    val followStatus: Boolean = false,
    val opinion: Int = 0,
    val likeCount: Long = 0,
    val commentCount: Long = 0,
    val shareCount: Long = 0,
    val queryType: Int = PostCommentListRequestBody.QUERY_TYPE_DEFAULT,
    val filterType: Int? = null,
    val commentList: Async<PagingApiResult<PostComment>> = Uninitialized,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val page: Int = 1,
    val addCommentResult: Async<PostComment> = Uninitialized,
    val isCommentListRefresh: Async<Boolean> = Uninitialized,
    val uniqueTag: Int = 0,
    val addReplyResult: Async<PostReply> = Uninitialized,
    val errorToast: ToastData = ToastData.EMPTY,
    val deletePostResult: Boolean? = null,
    val reportData: Triple<ReportType, String, Long>? = null,
    val muteStatus: Triple<AddPostCommentReplyTarget, PostCommentContent, Async<UserMuteStatus>>? = null,
    val morePost: Boolean = false,
    val moreCommentId: String? = null,
    val moreReplyId: String? = null,
    val fromTagDetailInfo: PostTag? = null,
    val needPermissionDialog: Boolean = false,
) : MavericksState {
    constructor(args: PostDetailFragmentArgs) : this(
        args.postId,
        args.targetCommentId,
        args.targetReplyId,
        showCommentPinRedDot = GlobalContext.get().get<AccountInteractor>().showCommentPinRedDot,
        fromTagDetailInfo = args.fromTagDetailInfo
    )

    val authorName get() = detail()?.nickname

    val reviewStatus2TrackParam get() = detail()?.reviewStatus2TrackParam ?: -1
}

class PostDetailViewModel(
    initialState: PostDetailState,
    context: Application,
    repo: IMetaRepository,
    accountInteractor: AccountInteractor,
    private val metaKV: MetaKV
) : BaseCommentListViewModel<PostDetailState>(initialState, context, repo, accountInteractor) {

    override val oldListResult: PagingApiResult<PostComment>?
        get() = oldState.commentList()
    override val authorId: String?
        get() = detail?.uid
    override val moduleContentId: String
        get() = oldState.postId
    override val pageType: Long
        get() = 0L
    override val moduleContentType: Int
        get() = MODULE_COMMUNITY
    override val PostDetailState.oldListResult: PagingApiResult<PostComment>?
        get() = commentList()

    override fun PostDetailState.updateCommentList(
        result: PagingApiResult<PostComment>?,
        msg: Any?
    ): PostDetailState {
        return if (result == null && msg == null) {
            this
        } else {
            copy(
                commentList = if (result != null) commentList.copyEx(result) else commentList,
                errorToast = if (msg != null) errorToast.tryToMsg(msg) else errorToast
            )
        }
    }

    override fun invokeToast(resId: Int) {
        setState { copy(errorToast = errorToast.toResMsg(resId)) }
    }

    private val commentSet = ConcurrentSet<String>()
    private val replySet = ConcurrentSet<String>()

    private var page = 0

    val ugcDesign: PostUgcDesignCard?
        get() = oldState.detail()?.ugcDesign

    val detail get() = oldState.detail()

    private var _showEditRedDot: Boolean? = null
    var showEditRedDot: Boolean
        get() {
            if (_showEditRedDot == null) {
                _showEditRedDot = metaKV.communityKV.showEditRedDot
            }
            return _showEditRedDot ?: false
        }
        set(value) {
            _showEditRedDot = value
            metaKV.communityKV.showEditRedDot = value
        }

    init {
        initData()
    }

    fun initData(isRefresh: Boolean = false) = withState { s ->
        if (isRefresh || s.detail.shouldLoad) {
            getPostDetail()
        }
        if (isRefresh || s.commentList.shouldLoad) {
            getCommentList(true, s.targetCommentId, s.targetReplyId)
        }
    }

    fun updateFriendState(friendInfoList: List<FriendInfo>) {
        val oldDetail = oldState.detail.invoke() ?: return

        friendInfoList.firstOrNull { friendInfo ->
            friendInfo.uuid == oldDetail.uid
        }?.let { friendInfo ->
            val status = friendInfo.status ?: return
            if (status.status != oldDetail.userStatus?.status || status.gameStatus != oldDetail.userStatus.gameStatus) {
                setState {
                    copy(detail = detail.copyEx(oldDetail.copy(userStatus = status)))
                }
            }
        }
    }

    fun checkPermission(context: Context) = withState { s ->
        val result = NotificationPermissionManager.postNeedPermission(context, false)
        Timber.d("needPermissionDialog $result")
        setState { copy(needPermissionDialog = result) }
    }

    fun updatePermissionDialog() = withState { s ->
        NotificationPermissionManager.updatePostPermissionTime(false)
        setState { copy(needPermissionDialog = false) }
    }

    fun getPostDetail() = withState { s ->
        val ts = System.currentTimeMillis()
        repo.getPostDetailV2(s.postId).map {
            it.copy(
                mediaList = PostPublish.validateMedias(it.mediaList, ts).filterEmptyUrl(),
                gameCardList = PostPublish.validateGames(it.gameCardList),
                tagList = PostPublish.validateTags(it.tagList),
                styleCardList = PostPublish.validateOutfits(it.styleCardList),
                clothesCardList = PostPublish.validateUgcDesigns(it.clothesCardList)
            )
        }.execute { result ->
            when (result) {
                is Success -> {
                    val detail = result.invoke()
                    copy(
                        detail = result.copy(detail),
                        followStatus = detail.followStatus,
                        opinion = detail.opinion,
                        likeCount = detail.likeCount,
                        commentCount = detail.commentCount,
                        shareCount = detail.shareCount
                    )
                }

                is Fail -> {
                    copy(detail = result)
                }

                else -> {
                    copy(detail = result)
                }
            }
        }
    }

    fun addShareCount(postId: String) = viewModelScope.launch {
        val oldDetail = oldState.detail() ?: return@launch
        if (oldDetail.postId != postId) return@launch
        val newDetail = oldDetail.copy(shareCount = oldState.shareCount + 1)
        setState {
            copy(shareCount = newDetail.shareCount, detail = detail.copyEx(newDetail))
        }
    }

    fun followUser() = withState { s ->
        val uid = s.detail.invoke()?.uid ?: return@withState
        val followOrNot = !s.followStatus
        Analytics.track(EventConstants.EVENT_FOLLOW_CLICK) {
            put(EventParamConstants.KEY_USERID, uid)
            put(EventParamConstants.KEY_LOCATION, EventParamConstants.LOCATION_FOLLOW_POST_DETAIL)
            put(
                EventParamConstants.KEY_TYPE,
                if (followOrNot) EventParamConstants.TYPE_FOLLOW else EventParamConstants.TYPE_UNFOLLOW
            )
        }
        if (followOrNot) {
            repo.followUser(uid)
        } else {
            repo.unfollowUser(uid)
        }.execute { result ->
            when (result) {
                is Success -> {

                    EventBus.getDefault()
                        .post(UserFollowEvent(uid, followOrNot, UserFollowEvent.FROM_POST_DETAIL))

                    if (followOrNot != followStatus) {
                        copy(followStatus = followOrNot)
                    } else {
                        this
                    }
                }

                is Fail -> {
                    copy(errorToast = errorToast.toError(result))
                }

                else -> {
                    this
                }
            }
        }
    }

    fun likePost() = withState { s ->
        val isLike = !OpinionRequestBody.isLike(s.opinion)
        Analytics.track(EventConstants.EVENT_POST_LIKE_CLICK) {
            put(EventParamConstants.KEY_POSTID, s.postId)
            put(EventParamConstants.KEY_LOCATION, EventParamConstants.LOCATION_LIKE_POST_DETAIL)
            put(
                EventParamConstants.KEY_TYPE,
                if (isLike) EventParamConstants.V_LIKE else EventParamConstants.V_UNLIKE
            )
            s.detail()?.tagList?.map { it.tagId }?.let { put(EventParamConstants.KEY_TAG_LIST, it) }
        }
        val postDetail = s.detail() ?: return@withState
        repo.saveOpinion(OpinionRequestBody.postLike(s.postId, isLike)).execute { result ->
            when (result) {
                is Success -> {
                    if (result.invoke()) {
                        if (OpinionRequestBody.isLike(opinion) != isLike) {
                            val nLikeCount = if (isLike) {
                                likeCount + 1
                            } else {
                                (likeCount - 1).coerceAtLeast(0)
                            }
                            EventBus.getDefault().post(
                                PostMetaDataUpdateEvent(
                                    postDetail.getPostMetaData().copy(
                                        isLike = isLike,
                                        likeCount = nLikeCount,
                                    )
                                )
                            )
                            copy(
                                opinion = OpinionRequestBody.like2Opinion(isLike),
                                likeCount = nLikeCount
                            )
                        } else {
                            this
                        }
                    } else {
                        this
                    }
                }

                is Fail -> {
                    copy(errorToast = errorToast.toError(result))
                }

                else -> {
                    this
                }
            }
        }
    }

    fun deletePost() = withState { s ->
        repo.deletePostV2(s.postId).execute { result ->
            when (result) {
                is Success -> {
                    copy(deletePostResult = true)
                }

                is Fail -> {
                    copy(errorToast = errorToast.toError(result))
                }

                else -> {
                    this
                }
            }
        }
    }

    fun initCommentList() = withState { s ->
        if (s.loadMore is Uninitialized) {
            getCommentList(true, s.targetCommentId, s.targetReplyId)
        }
    }

    fun getCommentList(
        refresh: Boolean,
        targetCommentId: String? = null,
        targetReplyId: String? = null
    ) = withState { s ->
        if (s.loadMore is Loading) return@withState
        getCommentList(
            moduleContentId = s.postId,
            sortType = s.queryType,
            page = LOAD_COMMENT_SIZE,
            pageSize = 10,
            replySize = 0,
            replySortType = PostReplyListRequestBody.QUERY_LATEST,
            refresh = refresh,
            targetCommentId = targetCommentId,
            targetReplyId = targetReplyId,
            commentCollapse = false,
            commentReplyStatus = PostComment.REPLY_STATUS_INIT,
            withAuthorReply = true,
            authorReplySize = 1,
            filterType = s.filterType,
        ) { result, targetPage ->
            when (result) {
                is Success -> {
                    copy(
                        commentList = result,
                        loadMore = Success(LoadMoreState(result().end)),
                        page = targetPage,
                        isCommentListRefresh = if (commentList is Success) {
                            Success(refresh)
                        } else {
                            isCommentListRefresh
                        },
                        uniqueTag = if (refresh) uniqueTag.xor(1) else uniqueTag
                    )
                }

                is Fail -> {
                    copy(
                        commentList = if (commentList is Success) {
                            commentList
                        } else {
                            Fail(result.error)
                        },
                        loadMore = Fail(result.error),
                        isCommentListRefresh = if (commentList is Success) {
                            Fail(result.error, refresh)
                        } else {
                            isCommentListRefresh
                        }
                    )
                }

                else -> {
                    copy(
                        commentList = if (commentList is Success) {
                            commentList
                        } else {
                            Loading()
                        },
                        loadMore = Loading(),
                        isCommentListRefresh = if (commentList is Success) {
                            Loading(refresh)
                        } else {
                            isCommentListRefresh
                        }
                    )
                }
            }
        }
    }

    fun setQueryType(queryType: Int) = withState { s ->
        if (s.loadMore is Loading) return@withState
        if (s.queryType != queryType || s.filterType != null) {
            setState { copy(queryType = queryType, filterType = null) }
        }
        getCommentList(true)
    }

    fun updateFilterType(filterType: Int) = withState { s ->
        if (s.loadMore is Loading) return@withState
        if (s.filterType != filterType) {
            setState {
                copy(
                    queryType = PostCommentListRequestBody.QUERY_TYPE_DEFAULT,
                    filterType = filterType
                )
            }
        }
        getCommentList(true)
    }

    fun reportPost() = withState { s ->
        report(s.postId, ReportType.Post)
    }

    fun reportComment(comment: PostComment) {
        report(comment.commentId, ReportType.PostComment)
    }

    fun reportReply(reply: PostReply) {
        report(reply.replyId, ReportType.PostReply)
    }

    private fun report(targetId: String, targetType: ReportType) {
        val ts = System.currentTimeMillis()
        setState {
            copy(reportData = Triple(targetType, targetId, ts))
        }
    }

    fun checkUserMuteStatus(target: AddPostCommentReplyTarget, postComment: PostCommentContent) =
        withState { s ->
            if (s.muteStatus?.third is Loading) return@withState
            repo.queryUserMuteStatus().execute { result ->
                copy(muteStatus = Triple(target, postComment, result))
            }
        }

    fun setMoreId(
        isPost: Boolean = false,
        commentId: String? = null,
        replyId: String? = null
    ) = setState {
        copy(
            morePost = isPost,
            moreCommentId = commentId,
            moreReplyId = replyId
        )
    }

    override fun onCleared() {
        commentSet.clear()
        replySet.clear()
        super.onCleared()
    }

    fun visitOutfitCard(): PostStyleCard? {
        val detail = oldState.detail.invoke() ?: return null
        val outfit = detail.outfit ?: return null
        Analytics.track(
            EventConstants.POST_LIST_OUTFIT_CLICK,
            "uuid" to detail.uid.orEmpty(),
            "resid" to detail.postId,
            "tag" to detail.tagList?.map { it.tagId }?.joinToString(",").orEmpty(),
            "shareid" to outfit.roleId,
            "source" to "1"
        )
        if (!accountInteractor.isMe(detail.uid)) {
            GlobalScope.launch {
                runCatching {
                    repo.visitPostOutfitCard(detail.postId, outfit.roleId).invoke()
                }
            }
        }
        return outfit
    }

    fun addCommentViaNet(commentContent: PostCommentContent) = withState { s ->
        if (!commentContent.valid || s.addCommentResult is Loading) return@withState
        clearReplyTarget()
        val ts = System.currentTimeMillis()
        val requestBody = PostCommentRequestBody(
            commentContent.text,
            MODULE_COMMUNITY,
            s.postId,
            mediaList = commentContent.mediaList
        )
        val tempComment = requestBody.toPostComment(
            "",
            accountInteractor.accountLiveData.value,
            ts
        )
        repo.addPostComment(requestBody).map {
            Analytics.track(
                EventConstants.GAME_REVIEW_REPLIES_SUCCESS,
                "type" to 1,
                "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_PGC,
                "gameid" to moduleContentId
            )
            val result = tempComment.copy(commentId = it.data.orEmpty())
            if (result.commentId.isNotEmpty()) {
                addComment(result)
            }
            result to it.toastMsg
        }.execute { result ->
            when (result) {
                is Success -> {
                    val (data, toastMsg) = result()
                    copy(
                        addCommentResult = if (data.commentId.isNotEmpty()) {
                            Success(data)
                        } else {
                            Fail(ApiDataException(String::class))
                        },
                        errorToast = if (toastMsg.isNullOrEmpty()) {
                            errorToast
                        } else {
                            errorToast.toMsg(toastMsg)
                        }
                    )
                }

                is Fail -> {
                    copy(addCommentResult = Fail(result.error, tempComment))
                }

                else -> {
                    copy(addCommentResult = Loading())
                }
            }
        }
    }

    fun addReplyViaNet(replyContent: PostCommentContent) = withState { s ->
        if (!replyContent.valid || s.addCommentResult is Loading) return@withState
        val replyTarget = getReplyTarget() ?: return@withState
        clearReplyTarget()
        val ts = System.currentTimeMillis()
        val userInfo = accountInteractor.accountLiveData.value
        val requestBody = if (replyTarget.isTargetComment) {
            PostReplyRequestBody(
                replyContent.text,
                userInfo?.uuid.orEmpty(),
                replyTarget.asComment.commentId,
                mediaList = replyContent.mediaList
            )
        } else {
            val targetReply = replyTarget.asReply
            PostReplyRequestBody(
                content = replyContent.text,
                uid = userInfo?.uuid.orEmpty(),
                commentId = replyTarget.commentId.orEmpty(),
                replyUid = targetReply.uid,
                replyNickname = targetReply.nickname,
                replyContentId = targetReply.replyId,
                mediaList = replyContent.mediaList
            )
        }
        repo.addPostReply(requestBody).map {
            Analytics.track(
                EventConstants.GAME_REVIEW_REPLIES_SUCCESS,
                "type" to 2,
                "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_PGC,
                "gameid" to moduleContentId
            )
            val result = requestBody.toPostReply(it, userInfo, ts)
            addReply(result to replyTarget)
            result
        }.execute {
            copy(addReplyResult = it)
        }
    }

    companion object : KoinViewModelFactory<PostDetailViewModel, PostDetailState>() {
        const val TASK_INVOKE_COMMENT_INPUT = 1

        const val LOAD_REPLY_SIZE = 10
        const val LOAD_COMMENT_SIZE = 20
        const val LOAD_COMMENT_REPLY_SIZE = 2

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: PostDetailState
        ): PostDetailViewModel {
            return PostDetailViewModel(state, get(), get(), get(), get())
        }
    }
}