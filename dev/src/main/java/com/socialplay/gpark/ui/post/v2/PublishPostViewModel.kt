package com.socialplay.gpark.ui.post.v2

import android.content.ComponentCallbacks
import android.os.Bundle
import android.os.Parcelable
import android.os.SystemClock
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.luck.picture.lib.entity.LocalMedia
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.PublishPostInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.account.PrivacySwitch
import com.socialplay.gpark.data.model.community.UserMuteStatus
import com.socialplay.gpark.data.model.editor.RoleData
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostMediaResource
import com.socialplay.gpark.data.model.post.PostMomentCard
import com.socialplay.gpark.data.model.post.PostPublish
import com.socialplay.gpark.data.model.post.PostStyleCard
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.PostUgcDesignCard
import com.socialplay.gpark.data.model.post.TopicBean
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.extension.dropAt
import com.socialplay.gpark.util.extension.likelyPath
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import org.koin.android.ext.android.get
import java.io.File

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/09/22
 *     desc   :
 * </pre>
 */
@Parcelize
data class PublishPostFragmentArgs(
    val content: String?,
    val medias: List<PostMediaResource>?,
    val games: List<PostCardInfo>?,
    val tags: List<PostTag>?,
    val clearTopBackFeed: Boolean,
    val fromGameId: String?,
    val fromPkgName: String?,
    val resIdBean: ResIdBean,
    val templateId: Int?,
    val customCacheKey: String?,
    val enableOutfitShare: Boolean,
    val isPublishVideo: Boolean,
    val isFromMoment: Boolean,
    val moments: List<PostMomentCard>?,
    val ugcDesign: PostUgcDesignCard?,
    val shareReqId: String?,
    val postPublish: PostPublish?
) : Parcelable

data class PublishPostExtra(
    val templateId: Int?,
    val showCategoryId: Int?,
    val isPublishVideo: Boolean,
    val enableOutfitShare: Boolean
)

data class PublishPostState(
    val content: String?,
    val medias: List<PostMediaResource>?,
    val games: List<PostCardInfo>?,
    val tags: List<PostTag>?,
    val communityTagList: List<TopicBean>?,
    val outfitCallback: Pair<Int, Long>? = null,
    val outfitGuideCallback: Int = GUIDE_OUTFIT_INIT,
    val randomTags: Async<List<PostTag>?> = Uninitialized,
    val muteStatus: Async<UserMuteStatus> = Uninitialized,
    val delayKeyboard: Boolean? = null,
    val extraInfo: PublishPostExtra? = null,
    val toast: ToastData = ToastData.EMPTY,
    val outfits: List<PostStyleCard>? = null,
    val permissionCallback: Pair<Int, Long>? = null,
    val recommendTopicPage: Int = 1,
    val isFromMoment: Boolean = false,
    val moments: List<PostMomentCard>? = null,
    val syncVideoFeedSelected: Boolean = ENABLE_SYNC_VIDEO_FEED,
    val ugcDesigns: List<PostUgcDesignCard>? = null,
    val canPublish: Boolean = true,
    val enablePublish: Boolean = false,
    val postId: String? = null
) : MavericksState {

    companion object {
        const val GUIDE_OUTFIT_INIT = -1
        const val GUIDE_OUTFIT_NO = 0
        const val GUIDE_OUTFIT_YES = 1
        const val GUIDE_OUTFIT_DISMISS = 2
        const val GUIDE_OUTFIT_HIDE = 3
        const val GUIDE_OUTFIT_HIDDEN = 4

        /**
         * G派合并之后, 不支持视频流了
         */
        const val ENABLE_SYNC_VIDEO_FEED = false
    }

    val outfit = outfits?.firstOrNull { it.isOutfit }
    val ugcDesign = ugcDesigns?.firstOrNull { it.isUgcDesign }
    val hasVideo get() = medias?.any { it.isVideo } == true
    val hasContent get() = content?.trim()?.isNotEmpty() == true
            || (medias?.count { it.isSupported } ?: 0) > 0
            || (games?.count { it.isSupported } ?: 0) > 0
            || (outfits?.count { it.isSupported } ?: 0) > 0
            || moments?.isNotEmpty() == true
            || (ugcDesigns?.count { it.isSupported } ?: 0) > 0

    constructor(args: PublishPostFragmentArgs) : this(
        args.postPublish?.content ?: args.content,
        args.postPublish?.medias ?: args.medias,
        args.postPublish?.games ?: args.games,
        args.tags,
        args.postPublish?.communityTagList ?: emptyList<TopicBean>(),
        outfits = args.postPublish?.styleCardList,
        extraInfo = PublishPostExtra(
            args.templateId,
            args.resIdBean.getCategoryID(),
            args.isPublishVideo,
            args.enableOutfitShare
        ),
        isFromMoment = args.isFromMoment,
        moments = args.moments,
        ugcDesigns = args.postPublish?.clothesCardList ?: args.ugcDesign?.let { listOf(it) },
        postId = args.postPublish?.postId
    )

    fun shouldRestoreDraft(): Boolean {
        return !hasContent
    }
}

class PublishPostViewModel(
    initialState: PublishPostState,
    private val repo: IMetaRepository,
    private val metaKV: MetaKV,
    private val accountInteractor: AccountInteractor,
    private val publishPostInteractor: PublishPostInteractor
) : BaseViewModel<PublishPostState>(initialState) {

    val enableOutfitShare get() = oldState.extraInfo?.enableOutfitShare == true
    val isPublishVideo get() = oldState.extraInfo?.isPublishVideo == true

    init {
        onEach(
            PublishPostState::content,
            PublishPostState::medias,
            PublishPostState::games,
            PublishPostState::outfits,
            PublishPostState::moments,
            PublishPostState::ugcDesigns,
            PublishPostState::canPublish,
        ) { content, medias, games, outfits, moments, ugcDesigns, canPublish ->
            if (!canPublish) {
                setState { copy(enablePublish = false) }
            } else if (content?.trim()?.isNotEmpty() == true
                || (medias?.count { it.isSupported } ?: 0) > 0
                || (games?.count { it.isSupported } ?: 0) > 0
                || (outfits?.count { it.isSupported } ?: 0) > 0
                || moments?.isNotEmpty() == true
                || (ugcDesigns?.count { it.isSupported } ?: 0) > 0
            ) {
                setState { copy(enablePublish = true) }
            } else {
                setState { copy(enablePublish = false) }
            }
        }

        if (initialState.shouldRestoreDraft()) {
            val draft = GsonUtil.gsonSafeParseCollection<PostPublish>(metaKV.communityKV.publishPostDraft)
            val fail = publishPostInteractor.getCurrentPost()
            val temp = if (draft != null) {
                metaKV.communityKV.publishPostDraft = null
                draft
            } else if (publishPostInteractor.isFailed() && fail != null) {
                fail
            } else {
                null
                }
            if (temp != null) {
                val validMedias = temp.medias?.filter {
                    if (it.isNetResource) {
                        true
                    } else if (!it.localPath.isNullOrEmpty()) {
                        File(it.localPath).exists()
                    } else {
                        false
                    }
                }
                val lackOfData = validMedias?.size != temp.medias?.size
                setState {
                    copy(
                        content = temp.content,
                        medias = validMedias,
                        games = temp.games,
                        communityTagList = temp.communityTagList,
                        outfits = temp.styleCardList,
                        moments = temp.plotCardList,
                        ugcDesigns = temp.clothesCardList,
                        postId = temp.postId,
                        toast = if (lackOfData) {
                            toast.toResMsg(R.string.draft_data_corrupt)
                        } else {
                            toast
                        }
                    )
                }
            }
        }

        initOotdPrivateSwitchIfNeed()
    }

    fun initOotdPrivateSwitchIfNeed() {
        if (!PandoraToggle.enableTryOnShare) return
        if (accountInteractor.accountLiveData.value?.ootdPrivateSwitch == null) {
            repo.getPrivacySwitch().map {
                accountInteractor.updatePrivacySwitch(
                    ootdPrivateSwitch = it.ootdPrivateSwitch,
                    updateCache = true
                )
            }.execute {
                this
            }
        }
    }

    fun updateContent(content: String?) = setState {
        copy(content = content)
    }
    fun updateTopicList(topicList: List<TopicBean>) = setState {
        topicList.map {
            it.tagName = it.tagName.replace(PublishPostFragment.TAG_RULE, "")
        }
        copy(communityTagList = topicList)
    }

    fun updateMedias(rawMedias: List<LocalMedia>?) {
        val ts = System.currentTimeMillis()
        val newMedias = rawMedias?.mapIndexedNotNull { index, localMedia ->
            localMedia.likelyPath?.let { path ->
                PostMediaResource(
                    resourceType = PostMediaResource.mimeTypeToResourceType(localMedia.mimeType),
                    resourceValue = "",
                    thumb = null,
                    cover = null,
                    localMedia.width,
                    localMedia.height,
                    path,
                    "$ts-$index"
                )
            }
        }
        setState {
            copy(
                medias = if (newMedias.isNullOrEmpty()) {
                    medias
                } else if (medias == null) {
                    newMedias
                } else {
                    medias + newMedias
                }
            )
        }
    }

    fun moveMedia(from: Int, to: Int) = withState {
        val medias = it.medias ?: return@withState
        val newMedias = medias.toMutableList()
        newMedias.add(to, newMedias.removeAt(from))
        setState { copy(medias = newMedias) }
    }

    fun removeMedia(item: PostMediaResource) = setState {
        copy(medias = medias.dropAt(-1, item))
    }

    fun replaceGame(game: List<PostCardInfo>) = setState {
        copy(games = game)
    }

    fun removeGame(item: PostCardInfo) = setState {
        copy(games = games.dropAt(-1, item))
    }

    fun addTag(tag: PostTag) = setState {
        copy(tags = listOf(tag))
    }

    fun removeTag() = setState {
        copy(tags = null)
    }

    fun fetchRandomTags() = withState { oldState ->
        if (oldState.randomTags is Loading) return@withState
        repo.getRandomPostTags().execute(retainValue = PublishPostState::randomTags) { result ->
            copy(randomTags = result)
        }
    }
    private var lastText = ""
    fun fetchTags(text: String) = withState { oldState ->
        this.lastText = text
        if (text.isEmpty()) {
            suspend {
                listOf<PostTag>()
            }.execute {
                copy(randomTags = it)
            }
            return@withState
        }
        if (text == PublishPostFragment.TAG_RULE) {
            repo.fetchHotTopics(RECOMMEND_TOPIC_PAGE_SIZE, oldState.recommendTopicPage)
                .execute(retainValue = PublishPostState::randomTags) { result ->
                    if (lastText.isEmpty()) {
                        return@execute this
                    }
                    copy(
                        randomTags = result,
                        recommendTopicPage = if (result is Success) recommendTopicPage + 1 else recommendTopicPage
                    )
                }
            return@withState
        }
        repo.getRecommendPostTags(text.replace(PublishPostFragment.TAG_RULE, "")).execute(retainValue = PublishPostState::randomTags) { result ->
            if (lastText.isEmpty()) {
                return@execute this
            }
            copy(randomTags = result)
        }
    }

    fun checkUserMuteStatus(resIdBean: ResIdBean) = withState { s ->
        if (s.muteStatus is Loading) return@withState

        val tagNamesStr = s.communityTagList?.let { it.map { it.tagName }.joinToString(",") } ?: ""
        val gameIdsStr = s.games?.let { it.map { it.gameId }.joinToString(",") } ?: ""

        val commonParams = ResIdUtils.getAnalyticsMap(resIdBean)

        val analyticParams =
            hashMapOf<String, Any>("image" to (s.medias?.count { it.isImage } ?: 0),
                "video" to (s.medias?.count { it.isVideo } ?: 0),
                "tag" to s.tags?.firstOrNull()?.tagName.orEmpty(),
                "game_amount" to (s.games?.count { it.isSupported } ?: 0),
                "tag_list" to tagNamesStr,
                "game_card_list" to gameIdsStr,
            )

        s.extraInfo?.templateId?.let {
            analyticParams["templateid"] = it
        }

        s.extraInfo?.showCategoryId?.let { analyticParams["show_categoryid"] = it }
        analyticParams["momentvideo"] = if (s.isFromMoment && s.syncVideoFeedSelected) 0 else 1

        Analytics.track(EventConstants.EVENT_ADD_POST_COMPLETE, commonParams + analyticParams)
        repo.queryUserMuteStatus().execute { result ->
            copy(muteStatus = result)
        }
    }

    fun checkPostPublish(): Pair<Boolean, Int> {
        val s = oldState
        if (s.extraInfo?.isPublishVideo == true && !s.hasVideo) {
            return false to R.string.video_publish_not_select_video_tip
        }
        if (s.isFromMoment && s.syncVideoFeedSelected && !s.hasVideo) {
            return false to R.string.post_less_than_one_video
        }
        return true to 0
    }


    fun canPublish(): Boolean = publishPostInteractor.canPublish()

    fun savePost(resIdBean: ResIdBean) = withState { s ->
        val moments = if (s.isFromMoment && !s.moments.isNullOrEmpty()) s.moments else listOf()
        publishPostInteractor.publishPost(
            PostPublish.validate(
                s.postId,
                s.content.orEmpty(),
                s.medias,
                s.games,
                s.communityTagList,
                s.outfits,
                s.extraInfo,
                moments,
                s.isFromMoment && s.syncVideoFeedSelected,
                s.ugcDesigns
            ),
            resIdBean = resIdBean,
            checkMute = false
        )
    }

    fun delayKeyboard() = viewModelScope.launch {
        delay(300)
        setState { copy(delayKeyboard = if (delayKeyboard == null) false else !delayKeyboard) }
    }

    fun toggleOutfitCard() = withState { s ->
        val ts = SystemClock.elapsedRealtime()
        if (s.outfits != null) {
            trackOutfitShareResult(ACTION_OFF, REASON_NO)
            setState { copy(outfits = null, outfitCallback = OUTFIT_OK to ts) }
        } else {
            val outfit = GsonUtil.gsonSafeParse<RoleData>(metaKV.account.roleData)
            if (outfit == null) {
                setState { copy(outfitCallback = OUTFIT_NO_DATA to ts) }
            } else if (accountInteractor.accountLiveData.value?.canTryOn() == true) {
                trackOutfitShareResult(ACTION_ON, REASON_OK, shareId = outfit.roleId)
                val outfits = outfit.toPostOutfits()
                setState { copy(outfits = outfits, outfitCallback = OUTFIT_OK to ts) }
            } else {
                setState { copy(outfitCallback = OUTFIT_NO_PERMISSION to ts) }
            }
        }
    }

    fun toggleSyncVideoFeed() = withState {
        setState { copy(syncVideoFeedSelected = !it.syncVideoFeedSelected) }
    }

    fun autoToggleOutfitCardOn() {
        val outfit = GsonUtil.gsonSafeParse<RoleData>(metaKV.account.roleData)
        if (outfit == null) {
            trackOutfitShareResult(ACTION_FAIL, REASON_NO_ROLE_DATA)
        } else {
            val ts = SystemClock.elapsedRealtime()
            if (accountInteractor.accountLiveData.value?.canTryOn() == true) {
                trackOutfitShareResult(ACTION_ON, REASON_OK, shareId = outfit.roleId)
                val outfits = outfit.toPostOutfits()
                setState { copy(outfits = outfits, outfitCallback = OUTFIT_OK to ts) }
            } else {
                setState { copy(outfitCallback = OUTFIT_NO_PERMISSION to ts) }
            }
        }
    }

    fun updateOutfitCard() {
        val outfits = GsonUtil.gsonSafeParse<RoleData>(metaKV.account.roleData)?.toPostOutfits() ?: return
        val ts = SystemClock.elapsedRealtime()
        setState { copy(outfits = outfits, outfitCallback = OUTFIT_OK to ts) }
    }

    fun enableTryOn() {
        val outfit = GsonUtil.gsonSafeParse<RoleData>(metaKV.account.roleData)
        if (outfit != null) {
            trackOutfitShareResult(ACTION_ON, REASON_OK, shareId = outfit.roleId)
            val outfits = outfit.toPostOutfits()
            val ts = SystemClock.elapsedRealtime()
            setState { copy(outfits = outfits, outfitCallback = OUTFIT_OK to ts) }
        } else {
            trackOutfitShareResult(ACTION_FAIL, REASON_NO_ROLE_DATA)
        }
        viewModelScope.launch {
            val result = repo.setPrivacySwitch(PrivacySwitch(true))
            if (result.succeeded && result.data == true) {
                accountInteractor.updatePrivacySwitch(ootdPrivateSwitch = true, updateCache = true)
            }
        }
    }

    fun checkShareOutfitGuide() = withState { s ->
        if (s.outfitGuideCallback >= PublishPostState.GUIDE_OUTFIT_NO) return@withState
        if (metaKV.account.isFirstPostShareOutfit) {
            metaKV.account.isFirstPostShareOutfit = false
            suspend {
                delay(1_000)
            }.execute {
                when (it) {
                    is Success -> {
                        copy(outfitGuideCallback = PublishPostState.GUIDE_OUTFIT_YES)
                    }

                    else -> {
                        this
                    }
                }
            }

        } else {
            setState { copy(outfitGuideCallback = PublishPostState.GUIDE_OUTFIT_NO) }
        }
    }

    fun delayShareOutfitGuideDismiss(delay: Boolean) {
        suspend {
            if (delay) {
                delay(3_300)
            }
        }.execute {
            when (it) {
                is Success -> {
                    if (outfitGuideCallback == PublishPostState.GUIDE_OUTFIT_YES) {
                        copy(outfitGuideCallback = PublishPostState.GUIDE_OUTFIT_DISMISS)
                    } else {
                        this
                    }
                }

                else -> {
                    this
                }
            }
        }
    }

    fun delayShareOutfitGuideHide() {
        suspend {
            delay(150)
        }.execute {
            when (it) {
                is Success -> {
                    copy(outfitGuideCallback = PublishPostState.GUIDE_OUTFIT_HIDE)
                }

                else -> {
                    this
                }
            }
        }
    }

    fun shareOutfitGuideHidden() {
        setState { copy(outfitGuideCallback = PublishPostState.GUIDE_OUTFIT_HIDDEN) }
    }

    fun trackOutfitShareResult(action: Int, reason: Int, shareId: String? = null) {
        Analytics.track(
            EventConstants.POST_SHARE_OUTFIT_CLICK,
            "action" to action.toString(),
            "shareid" to shareId.orEmpty(),
            "reason" to (if (reason == -1) "" else reason.toString())
        )
    }

    fun handlePermissionResult(result: Int) {
        val ts = SystemClock.elapsedRealtime()
        setState { copy(permissionCallback = result to ts) }
    }

    fun setCanPublish(canPublish: Boolean) = withState { s ->
        if (s.canPublish == canPublish) return@withState
        setState { copy(canPublish = canPublish) }
    }

    fun saveForDraft() {
        val s = oldState
        val temp = PostPublish(
            s.content.orEmpty(),
            s.medias,
            s.games,
            s.communityTagList,
            s.postId,
            null,
            s.outfits,
            null,
            s.moments,
            null,
            s.ugcDesigns
        )
        metaKV.communityKV.publishPostDraft = GsonUtil.safeToJson(temp)
    }

    companion object : KoinViewModelFactory<PublishPostViewModel, PublishPostState>() {
        const val OUTFIT_OK = 0
        const val OUTFIT_NO_DATA = 1001
        const val OUTFIT_NO_PERMISSION = 1002
        const val RECOMMEND_TOPIC_PAGE_SIZE = 10

        const val ACTION_FAIL = 0
        const val ACTION_ON = 1
        const val ACTION_OFF = 2

        const val REASON_NO = -1
        const val REASON_OK = 0
        const val REASON_NO_PERMISSION = 1
        const val REASON_NO_ROLE_DATA = 2

        const val PERMISSION_GRANTED = 1
        const val PERMISSION_DENIED = 2

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: PublishPostState
        ): PublishPostViewModel {
            return PublishPostViewModel(state, get(), get(), get(), get())
        }
    }

}