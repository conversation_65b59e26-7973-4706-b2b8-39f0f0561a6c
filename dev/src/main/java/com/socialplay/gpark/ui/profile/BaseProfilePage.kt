package com.socialplay.gpark.ui.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.StringRes
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.meta.pandora.data.entity.Event
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.EditorInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.data.model.profile.ProfileCountEvent
import com.socialplay.gpark.data.model.profile.UserProfileInfo
import com.socialplay.gpark.databinding.FragmentProfileBaseBinding
import com.socialplay.gpark.databinding.TabIndicatorProfileBinding
import com.socialplay.gpark.function.MetaRouterWrapper
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.apm.apiStart
import com.socialplay.gpark.function.deeplink.LinkData.Companion.SOURCE_LOCAL
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.imgpre.v2.OpenPreviewBuilder
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.profile.fans.UserFansTabFragmentDialog.Companion.TYPE_LOCATION_FANS
import com.socialplay.gpark.ui.profile.fans.UserFansTabFragmentDialog.Companion.TYPE_LOCATION_FOLLOW
import com.socialplay.gpark.ui.profile.fans.UserFansTabFragmentDialog.Companion.TYPE_LOCATION_FRIEND
import com.socialplay.gpark.ui.profile.like.LikeCountDialog
import com.socialplay.gpark.ui.profile.like.LikeCountDialogArgs
import com.socialplay.gpark.ui.profile.link.LinkLogoAdapter
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.ui.view.viewpager.CommonTabStateAdapter
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.apiMonitor
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.sharedViewModelFromParentFragment
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import timber.log.Timber

/**
 * created by liyanfeng on 2022/7/25 2:32 下午
 * @describe: ProfileTab 主页
 */
abstract class BaseProfilePage : BaseFragment<FragmentProfileBaseBinding>() {

    companion object {
        private const val TAG = "Profile-"
        const val KEY_UUID = "uuid"
        const val KEY_IS_FOLLOW = "isFollow"
        const val RESULT_WEB_REFRESH = "RESULT_WEB_REFRESH"
        const val RESULT_FOLLOW = "RESULT_FOLLOW"
    }

    val accountInteractor: AccountInteractor by inject()
    val viewModel by sharedViewModelFromParentFragment<BaseProfileViewModel>()
    private val mainViewModel: MainViewModel by sharedViewModel()
    val metaKV: MetaKV by inject()

    private lateinit var pagerAdapter: CommonTabStateAdapter
    private var tabLayoutMediator: TabLayoutMediator? = null
    private val editorInteractor: EditorInteractor by inject()
    private val roleAlphaDis by lazy { 103.dp }
    private lateinit var linkLogoAdapter :LinkLogoAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.userProfile.apiMonitor(this) {
            it != null
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentProfileBaseBinding? {
        return FragmentProfileBaseBinding.inflate(inflater, container, false)
    }

    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_NAME_PROFILETAB
    override fun ignoreUsingTime() = true

    abstract fun isBottomTab(): Boolean
    abstract fun otherUuid(): String
    abstract fun isMe(): Boolean
    open fun ivRightIcon(): Int {
        return R.drawable.ic_feat_24_1a1a1a_more_dot
    }
    abstract fun onIvRightClick(view: View)
    abstract fun initTopUserView(display: ProfileDisplayBean)

    private val tabCallback = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, true)
        }

        override fun onTabUnselected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, false)
        }

        override fun onTabReselected(tab: TabLayout.Tab) {}
    }

    override fun init() {
        Timber.tag(TAG).e("init isBottomTab:${isBottomTab()} mUuid:${otherUuid()}")
        initCommon()
        initViewPager()
        initObserve()
        apiStart()
        viewModel.getUserProfile(otherUuid(), isMe())
    }

    private fun initCommon() {
        binding.vp.offscreenPageLimit = 1
        binding.includeCount.llFriendNum.visible(PandoraToggle.isIMEntrance)
        binding.tvEmptyTipText.text = getString(R.string.no_games_played_cap)
        binding.clUserInfo.setOnClickListener {}
        binding.ulv.setListener(viewLifecycleOwner) {
            Analytics.track(EventConstants.EVENT_PROFILE_ACCOUNT_VERIFIED_ICON_CLICK)
            UserLabelView.showDescDialog(this, it)
        }
        binding.includeCount.apply {
            llLikesNum.setOnAntiViolenceClickListener {
                Analytics.track(EventConstants.EVENT_CLICK_LIKE_LIST)
                val info = viewModel.userProfile.value ?: return@setOnAntiViolenceClickListener
                LikeCountDialog.show(this@BaseProfilePage, LikeCountDialogArgs(info.nickname ?:"", info.likeCount))
            }

            llFollowersNum.setOnAntiViolenceClickListener {
                Analytics.track(EventConstants.EVENT_CLICK_FANS_LIST)
                if (!isMe() && cantShowRelation(viewModel.userProfile.value)) {
                    toast(R.string.refuse_fans_list_show)
                    return@setOnAntiViolenceClickListener
                }
                val info = viewModel.userProfile.value ?: return@setOnAntiViolenceClickListener
                if (isMe() && binding.includeCount.vFollowersNumRedDot.isVisible) {
                    viewModel.clearNewFollowerRecord()
                }
                MetaRouter.Profile.followAndFans(this@BaseProfilePage, isMe(), otherUuid(), info.followCount, info.fansCount, info.friendTotal, TYPE_LOCATION_FANS)
            }
            llFollowingNum.setOnAntiViolenceClickListener {
                Analytics.track(EventConstants.EVENT_CLICK_FOLLOW_LIST)
                if (!isMe() && cantShowRelation(viewModel.userProfile.value)) {
                    toast(R.string.refuse_follower_list_show)
                    return@setOnAntiViolenceClickListener
                }
                val info = viewModel.userProfile.value ?: return@setOnAntiViolenceClickListener
                MetaRouter.Profile.followAndFans(this@BaseProfilePage, isMe(), otherUuid(), info.followCount, info.fansCount, info.friendTotal, TYPE_LOCATION_FOLLOW)
            }
            llFriendNum.setOnAntiViolenceClickListener {
                Analytics.track(
                    EventConstants.EVENT_IM_FRIEND_CLICK
                )
                if (!isMe() && cantShowRelation(viewModel.userProfile.value)) {
                    toast(R.string.refuse_friend_list_show)
                    return@setOnAntiViolenceClickListener
                }
                val info = viewModel.userProfile.value ?: return@setOnAntiViolenceClickListener

                MetaRouter.Profile.followAndFans(this@BaseProfilePage, isMe(), otherUuid(), info.followCount, info.fansCount, info.friendTotal, TYPE_LOCATION_FRIEND)
            }
        }
        binding.tvJumpCreation.setOnClickListener {
            Analytics.track(EventConstants.PROFILE_UGC_CREATE)
            MetaRouter.MobileEditor.creation(this)
        }
        initUserLink()
        binding.tvId.setOnLongClickListener {
            val id = binding.tvId.text?.toString() ?: return@setOnLongClickListener false
            if (!viewLifecycleOwner.lifecycleScope.isActive) return@setOnLongClickListener false
            if (!isBindingAvailable()) return@setOnLongClickListener false
            viewLifecycleOwner.lifecycleScope.launch {
                context?.let { ClipBoardUtil.setClipBoardContent(id, it, "GPark ID") }
                context?.let { toast(R.string.copied_to_clipboard) }
            }
            true
        }
    }
    private fun initUserLink(){
        linkLogoAdapter = LinkLogoAdapter()
        binding.rylink.adapter = linkLogoAdapter
        linkLogoAdapter.setOnItemClickListener { view, position ->
            MetaRouterWrapper.ExternalLink.jump(
                this,
                linkLogoAdapter.getItem(position),
                SOURCE_LOCAL
            )
            Analytics.track(EventConstants.EVENT_LINK_CLICK, mapOf("userid" to otherUuid(), "link" to linkLogoAdapter.getItem(position).url.orEmpty()))
        }
    }

    fun onProfilePageShow(from: String?) {
        Analytics.track(EventConstants.EVENT_PROFILE_PAGE_SHOW) {
            put("type", if (isBottomTab()) 1 else 2)
            put("from", from ?: "")
        }
    }

    override fun loadFirstData() {
    }

    open fun initTopUserRoleView(display: ProfileDisplayBean) {

    }

    fun onIvAvatarClick(portrait: String?) {
        val array = arrayOf(portrait ?: "")
        if (isMe()) {
            val imgAction = getString(R.string.image_dialog_change_avatar)
            OpenPreviewBuilder(this@BaseProfilePage)
                .setImageUrls(array.toList())
                .setShowSaveBtn(true)
                .setImageAction(imgAction) {
                    it.close()
                    Analytics.track(EventConstants.EVENT_GAME_AVATAR_LAUNCH) {
                        putAll(ResIdUtils.getAnalyticsMap(ResIdBean().setCategoryID(CategoryId.JUMP_ROLE_GAME_FROM_PROFILE_PHOTO)))
                        put("from", "profile_photo")
                    }
                    mainViewModel.enterFullAvatar(null)
                }
                .show()
        } else {
            OpenPreviewBuilder(this@BaseProfilePage)
                .setImageUrls(array.toList())
                .setShowSaveBtn(true)
                .show()
        }
    }

    private fun initViewPager() {
        viewModel.initArgs(otherUuid(), isMe(), isBottomTab()) {
            apiStart()
        }
        binding.tlProfile.addOnTabSelectedListener(tabCallback)
        binding.vp.registerOnPageChangeCallback(viewPagerCallback)
        viewModel.tabItems.value?.let { tabItems ->
            initTabs(tabItems)
        }
    }

    private fun initTabs(tabItems: ArrayList<Pair<HomepageTab, () -> Fragment>>?) {
        tabItems ?: return
        val tabTitles = tabItems.map { it.first }
        val tabs = tabItems.map { it.second }
        pagerAdapter = CommonTabStateAdapter(
            tabs, childFragmentManager, viewLifecycleOwner.lifecycle
        )
        binding.vp.adapterAllowStateLoss = pagerAdapter
        tabLayoutMediator = TabLayoutMediator(binding.tlProfile, binding.vp) { tab, position ->
            val tabBinding = TabIndicatorProfileBinding.inflate(layoutInflater)
            val title = getString(tabTitles[position].title)

            tabBinding.tvNormal.text = title
            tabBinding.tvSelected.text = title

            tab.customView = tabBinding.root
            tab.tag = tabTitles[position]
        }
        tabLayoutMediator?.attach()
        viewModel.selectedItemLiveData.observe(viewLifecycleOwner) { currentItem ->
            binding.vp.currentItem = currentItem
        }
    }

    protected fun updateNumView(fansCount: Long, followCount: Long, likeCount: Long,
                                friendCount: Long
    ) {
        binding.includeCount.apply {
            tvFollowersNum.text = UnitUtil.formatKMCount(fansCount)
            tvFollowingNum.text = UnitUtil.formatKMCount(followCount)
            tvLikesNum.text = UnitUtil.formatKMCount(likeCount)
            tvFriendNum.text =  UnitUtil.formatKMCount(friendCount)
        }
    }

    private val viewPagerCallback by lazy {
        object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                viewModel.changeSelectTab(position)
                if (viewModel.tabItems.value?.getOrNull(position)?.first == HomepageTab.ASSETS) {
                    Analytics.track(
                        EventConstants.MY_LIBRARY_TAB_CLICK
                    )
                }
            }

            override fun onPageScrolled(
                position: Int, positionOffset: Float, positionOffsetPixels: Int
            ) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
                if (positionOffset <= 0) return
            }
        }
    }

    open fun initObserve() {
        EventBus.getDefault().register(this)

        viewModel.userProfile.observe(viewLifecycleOwner) {
            Timber.tag(TAG).e("userProfileLiveData:$it")
            it?.let {
                val data = ProfileDisplayBean().createByUserProfile(it)
                initTopUserView(data)
                initTopUserRoleView(data)
            }
            initUserLinkInfo(it?.externalLinks)
        }
        if (viewModel.tabItems.value == null) {
            viewModel.tabItems.observe(viewLifecycleOwner) { tabItems ->
                initTabs(tabItems)
            }
        }
        viewModel.toastLifeCallback.observe(viewLifecycleOwner) { res, txt ->
            if (res != null && res != 0) {
                toast(res)
            } else if (!txt.isNullOrBlank()) {
                toast(txt)
            }
        }
    }
    private fun initUserLinkInfo(link: List<ProfileLinkInfo>?) {
        if (link.isNullOrEmpty()) {
            binding.rylink.gone()
        } else {
            binding.rylink.visible()
            Analytics.track(EventConstants.EVENT_LINK_SHOW)
        }
        linkLogoAdapter.setList(link)
    }

    private fun setTabSelectUI(tab: TabLayout.Tab, select: Boolean) {
        val cv = tab.customView ?: return
        cv.findViewById<TextView>(R.id.tv_normal)?.isInvisible = select
        cv.findViewById<TextView>(R.id.tv_selected)?.isInvisible = !select
        if (select) {
            (tab.tag as? HomepageTab)?.event?.let {
                Analytics.track(it)
            }
        }
    }

    fun updateBuildBtnVisible(isVisible: Boolean) {
        binding.tvJumpCreation.isVisible = isMe() && isVisible
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onTokenInvalid(event: ProfileCountEvent) {
        Timber.tag(TAG).d("onTokenInvalid:$event")
    }

    fun cantShowRelation(detail: UserProfileInfo?): Boolean {
        return detail?.canShowFollower() != true && !accountInteractor.isMe(viewModel.userProfile.value?.uid)
    }

    override fun onDestroyView() {
        binding.tlProfile.removeOnTabSelectedListener(tabCallback)
        binding.vp.unregisterOnPageChangeCallback(viewPagerCallback)
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        binding.vp.adapterAllowStateLoss = null
        EventBus.getDefault().unregister(this)
        super.onDestroyView()
    }
}

enum class HomepageTab(@StringRes val title: Int, val stringKey: String, val event: Event?) {
    RECENT(R.string.tab_recent_playing, "tab_playing", null),
    POST(R.string.tab_post, "tab_posts", null),
    CLOTHES(R.string.outfits, "tab_clothes", null),
    UGC(R.string.profile_tab_ugc, "tab_ugc", EventConstants.PROFILE_MAPS_TAB_CLICK),
    ASSETS(R.string.assets, "tab_assets", null),
}