package com.socialplay.gpark.ui.profile.fans

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.model.aibot.AiBotFollowResult
import com.socialplay.gpark.data.model.aibot.BotInfo
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.profile.RelationListResult
import com.socialplay.gpark.data.model.profile.request.RelationListRequest
import com.socialplay.gpark.data.model.user.RelationDirection
import com.socialplay.gpark.data.model.user.RelationType
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.maverick.plus
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.util.getStringByGlobal
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import org.koin.android.ext.android.get

/**
 * Created by bo.li
 * Date: 2023/9/20
 * Desc:
 */
data class UserFansItemModelState(
    val uuid: String,
    val type: String,
    val refresh: Async<List<RelationListResult.RelationUserInfo>> = Uninitialized,
    val rollId: Long? = null,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val aiBotList: Async<List<BotInfo>> = Uninitialized,
    val isAiBotEnd: Boolean = false,
    val scrollId: String? = null,
    val pageNum :Int= 1,
) : MavericksState {

    val list: List<RelationListResult.RelationUserInfo> = refresh.invoke() ?: emptyList()

    constructor(args: UserFansItemFragmentArgs) : this(
        args.uuid,
        args.type
    )
}

class UserFansItemViewModel(
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    initialState: UserFansItemModelState
) : BaseViewModel<UserFansItemModelState>(initialState) {

    init {
        refreshList()
    }

    fun refreshList() {
        getAiBotFollowList(true)
    }

    fun loadMoreList() {
        getAiBotFollowList(false)
    }


    private fun refresh() {
        withState { oldState ->
            if (oldState.type == UserFansItemFragment.TYPE_FRIEND) {
                repository.getOthersFriendList(oldState.uuid, 1, PAGE_SIZE).execute {
                    val success = it is Success
                    if (success) {
                        val result = it.invoke()
                        if (result?.succeeded == true) {
                            val info = result.data?.convertToFansInfo()
                            if (info == null) {
                                copy(
                                    pageNum = 1,
                                    loadMore = it.map { LoadMoreState(true) }
                                )
                            } else {
                                val list = info.infos ?: emptyList()
                                copy(
                                    refresh = it.map { list },
                                    rollId = 0,
                                    pageNum = 1,
                                    loadMore = it.map { LoadMoreState(list.isEmpty()) }
                                )
                            }
                        } else {
                            copy(
                                refresh = Fail(
                                    result?.exception ?: Exception(getStringByGlobal(R.string.api_error_net)),
                                    oldState.refresh.invoke()
                                ),
                                pageNum = 1,
                                loadMore = it.map { LoadMoreState(true) }
                            )
                        }
                    } else {
                        copy(
                            pageNum = 1,
                            loadMore = it.map { LoadMoreState(false) }
                        )
                    }
                }
            } else {
                repository.getRelationList(
                    RelationListRequest(
                        oldState.uuid,
                        null,
                        if (oldState.type == UserFansItemFragment.TYPE_FOLLOWING) RelationDirection.Forward.value else RelationDirection.Reverse.value,
                        PAGE_SIZE,
                        RelationType.Follow.value
                    )
                ).execute { result ->
                    val success = result is Success
                    val list = result.map { it.infos ?: emptyList() }
                    copy(
                        refresh = list,
                        rollId = if (success) result()?.nextRollId else rollId,
                        loadMore = if (success) Uninitialized else loadMore,
                    )
                }
            }
        }
    }

   private fun loadMore() {
        withState { oldState ->
            if (oldState.loadMore is Loading) return@withState
            if (oldState.type == UserFansItemFragment.TYPE_FRIEND) {
                repository.getOthersFriendList(oldState.uuid, oldState.pageNum + 1, PAGE_SIZE).execute {
                    val success = it is Success
                    if (success) {
                        val info =  it.invoke()?.data?.convertToFansInfo()
                        if (info == null) {
                            copy(
                                pageNum = oldState.pageNum + 1,
                                loadMore = it.map { LoadMoreState(true) }
                            )
                        } else {
                            val list = info.infos ?: emptyList()
                            copy(
                                refresh = oldState.refresh + it.map { list },
                                rollId =  0,
                                pageNum = oldState.pageNum + 1,
                                loadMore = it.map { LoadMoreState(list.isEmpty()) }
                            )
                        }
                    } else {
                        copy(
                            loadMore = it.map { LoadMoreState(false) }
                        )
                    }

                }
            } else {
                repository.getRelationList(
                    RelationListRequest(
                        oldState.uuid,
                        oldState.rollId,
                        if (oldState.type == UserFansItemFragment.TYPE_FOLLOWING) RelationDirection.Forward.value else RelationDirection.Reverse.value,
                        PAGE_SIZE,
                        RelationType.Follow.value
                    )
                ).execute {
                    val success = it is Success
                    val list = it.map { it.infos ?: emptyList() }
                    copy(
                        refresh = oldState.refresh + list,
                        rollId = if (success) it()?.nextRollId else rollId,
                        loadMore = it.map { LoadMoreState(!success || list().isNullOrEmpty()) }
                    )
                }
            }
        }
    }
    private fun getAiBotFollowList(isRefresh: Boolean) = withState { oldState ->
        if (oldState.type == UserFansItemFragment.TYPE_FOLLOWING && PandoraToggle.isOpenAiBot && (isRefresh || (!oldState.isAiBotEnd))) {
            //打开aibot开关并且还有关注列表
            val scrollId = if (isRefresh) {
                null
            } else {
                oldState.scrollId
            }
            val otherUUid = if (oldState.uuid == accountInteractor.curUuid) null else oldState.uuid
            repository.getFollowAiBotList(scrollId, PAGE_SIZE, otherUUid).map { it.data }.execute {
                when(it) {
                    is Success -> {
                        val data = it.invoke()
                        val bitList = data?.dataList?.map { data ->
                            RelationListResult.RelationUserInfo(
                                uuid = data.id,
                                avatar = data.icon,
                                nickname = data.name,
                                type = RelationListResult.RelationUserInfo.TYPE_AI_BOT,
                                userNumber = data.botId
                            )
                        } ?: emptyList()
                        if (bitList.isNullOrEmpty() || (bitList.size < PAGE_SIZE)) {
                            //当前数据为空，或者一页请求完成
                            val req = RelationListRequest(
                                oldState.uuid,
                                oldState.rollId,
                                RelationDirection.Forward.value,
                                PAGE_SIZE,
                                RelationType.Follow.value
                            )
                            repository.getRelationList(req).execute { result ->
                                val success = it is Success
                                val dataList = bitList + (result.invoke()?.infos?: emptyList())
                                copy(
                                    refresh = if (isRefresh) result.map { dataList } else refresh + result.map { dataList },
                                    rollId = if (success) result()?.nextRollId else rollId,
                                    loadMore = result.map { LoadMoreState(dataList.isNullOrEmpty())},
                                    isAiBotEnd = true
                                )
                            }
                            this
                        } else {
                            copy(
                                refresh = if (isRefresh) it.map { bitList } else {
                                    refresh + it.map { bitList }
                                },
                                scrollId = data?.scrollId,
                                rollId = null,
                                isAiBotEnd = false,
                                loadMore = it.map { LoadMoreState(false) }
                            )
                        }
                    }
                    else->{
                      this
                    }
                }

            }
        } else {
            if (isRefresh) {
                refresh()
            } else {
                loadMore()
            }
        }
    }


    companion object : KoinViewModelFactory<UserFansItemViewModel, UserFansItemModelState>() {
        private const val PAGE_SIZE = 20

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: UserFansItemModelState
        ): UserFansItemViewModel {
            return UserFansItemViewModel(get(), get(), state )
        }
    }
}