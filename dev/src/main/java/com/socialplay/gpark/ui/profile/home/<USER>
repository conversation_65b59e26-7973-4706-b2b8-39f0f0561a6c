package com.socialplay.gpark.ui.profile.home

import android.animation.ValueAnimator
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.activity.addCallback
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.GravityCompat
import androidx.core.view.children
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.drawerlayout.widget.DrawerLayout
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.QrCodeInteractor
import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest
import com.socialplay.gpark.data.model.profile.friend.FriendReleation
import com.socialplay.gpark.data.model.qrcode.ScanEntry
import com.socialplay.gpark.data.model.qrcode.ScanRequestData
import com.socialplay.gpark.data.model.qrcode.ScanResultData
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.databinding.FragmentProfileBinding
import com.socialplay.gpark.databinding.TabIndicatorProfileV2Binding
import com.socialplay.gpark.databinding.ViewHomeDrawerBinding
import com.socialplay.gpark.function.MetaRouterWrapper
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkData.Companion.SOURCE_LOCAL
import com.socialplay.gpark.function.editor.RoleGameTryOn
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.pay.PayProvider
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.editor.home.popup.AvatarPopupDialog
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.imgpre.v2.OpenPreviewBuilder
import com.socialplay.gpark.ui.imgpre.ImgPreDialogFragment
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.ui.main.MainFragment
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.profile.HeProfileFragment.Companion.TAG_BLOCK
import com.socialplay.gpark.ui.profile.HomepageTab
import com.socialplay.gpark.ui.profile.fans.UserFansTabFragmentDialog.Companion.TYPE_LOCATION_FANS
import com.socialplay.gpark.ui.profile.fans.UserFansTabFragmentDialog.Companion.TYPE_LOCATION_FOLLOW
import com.socialplay.gpark.ui.profile.fans.UserFansTabFragmentDialog.Companion.TYPE_LOCATION_FRIEND
import com.socialplay.gpark.ui.profile.like.LikeCountDialog
import com.socialplay.gpark.ui.profile.like.LikeCountDialogArgs
import com.socialplay.gpark.ui.profile.link.LinkSelectDialog
import com.socialplay.gpark.ui.profile.reportBlock.BaseProfileReportBlockObserver
import com.socialplay.gpark.ui.qrcode.QRCodeScanFragment
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.view.DrawerTabLayoutHost
import com.socialplay.gpark.ui.view.DrawerViewPager2Host
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.ui.view.viewpager.CommonTabStateAdapter
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.UniJumpUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.addDrawerListener
import com.socialplay.gpark.util.extension.addListener
import com.socialplay.gpark.util.extension.addOnOffsetChangedListener
import com.socialplay.gpark.util.extension.addOnTabSelectedListener
import com.socialplay.gpark.util.extension.addTabGapFirstLast
import com.socialplay.gpark.util.extension.addUpdateListener
import com.socialplay.gpark.util.extension.clearCompoundDrawables
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getDimensionPx
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.goneIfValueEmpty
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.registerOnPageChangeCallback
import com.socialplay.gpark.util.extension.setCurrentItemIfValid
import com.socialplay.gpark.util.extension.setEdgeSize
import com.socialplay.gpark.util.extension.setFontFamily
import com.socialplay.gpark.util.extension.setFragmentResultListenerByActivity
import com.socialplay.gpark.util.extension.setHeightEx
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.setWidthEx
import com.socialplay.gpark.util.extension.statusBarHeight
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.glide.TransparentBoundCrop
import com.youth.banner.listener.OnPageChangeListener
import com.zhpan.indicator.enums.IndicatorSlideMode
import com.zhpan.indicator.enums.IndicatorStyle
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import org.koin.core.context.GlobalContext
import timber.log.Timber
import kotlin.math.abs

class ProfileFragment : BaseFragment<FragmentProfileBinding>(R.layout.fragment_profile) {

    private val vm: ProfileViewModel by fragmentViewModel()
    private val mainViewModel by sharedViewModel<MainViewModel>()
    private val args by args<ProfileArgs>()

    private var reportBlockObserver: BaseProfileReportBlockObserver? = null
    private var tabLayoutMediator: TabLayoutMediator? = null

    private val drawerLayout: DrawerLayout?
        get() = (activity as? MainActivity)?.drawerLayout

    private val drawerItemListener: IHomeDrawerListener = object : IHomeDrawerListener {
        override fun clickHomeDrawerEntrance(item: HomeDrawerEntrance) {
            onClickDrawerItem(item)
        }
    }

    private val entranceItemListener: IProfileEntranceListener = object : IProfileEntranceListener {
        override fun clickProfileEntrance(item: ProfileEntrance) {
            onClickEntranceItem(item)
        }
    }

    private val drawerController by lazy { buildDrawerController() }

    private val bannerAdapter by lazy {
        ProfileEntranceAdapter(
            ArrayList(),
            isPad = isPad,
            entranceItemListener
        )
    }
    private var bannerPosition = 1

    private var tryOnTipsShown = false

    private var onBackPressedCallback: OnBackPressedCallback? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vm.apiMonitor(
            this,
            ProfileState::userProfile
        )
        drawerController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): FragmentProfileBinding? {
        return FragmentProfileBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initScanner()
        initDrawerMenu()
        initTitleBar()
        initMain()
        initTabs()
        initOthers()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        drawerController.onSaveInstanceState(outState)
    }

    private fun initScanner() {
        setFragmentResultListenerByActivity(
            QRCodeScanFragment.KEY_PROFILE_REQUEST_SCAN_QRCODE,
            viewLifecycleOwner
        ) { _, bundle ->
            val request = ScanRequestData.from(bundle)
            val result = ScanResultData.from(bundle)

            if (result != null && request != null) {
                viewLifecycleOwner.lifecycleScope.launch {
                    GlobalContext.get().get<QrCodeInteractor>()
                        .dispatchQRCodeScanResult(this@ProfileFragment, request, result)
                }
            }
        }
    }

    private fun initTitleBar() {
        if (args.isFromBottom) {
            binding.ivDrawerBtn.visible()
            binding.root.setPaddingEx(bottom = getDimensionPx(R.dimen.tab_layout_height))
        } else {
            binding.ivDrawerBtn.gone()
            binding.ibBack.visible()
            binding.ibBack.setOnAntiViolenceClickListener {
                navigateUp()
            }
        }
        if (args.isMe) {
            binding.ivScanBtn.visible()
            binding.ivScanBtn.setOnAntiViolenceClickListener {
                vm.trackClick(
                    "scan",
                    "scan",
                    null
                )
                goScan()
            }
            binding.tvTitleBarFollowBtn.gone()
            binding.ivShareBtn.setOnAntiViolenceClickListener {
                vm.trackClick(
                    "share",
                    "share",
                    "share"
                )
                val recentGames = vm.oldState.shareProfileData()
                if (recentGames == null) {
                    toast(R.string.loading)
                    vm.getUserExtra4Share()
                } else {
                    share(recentGames)
                }
            }
            vm.onAsync(
                ProfileState::shareProfileData,
                deliveryMode = uniqueOnly(),
                onFail = {}
            ) {
                share(it)
            }
            binding.ivMsgBtn.visible()
            binding.ivMsgBtn.setOnAntiViolenceClickListener {
                vm.trackClick(
                    "im",
                    "im",
                    null
                )
                MetaRouter.IM.goChatTabFragment(
                    this, source = EventParamConstants.SRC_MESSAGE_LIST_ENTRANCE_AVATAR
                )
            }
            mainViewModel.msgUnReadCountFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
                binding.vMsgRedDot.visible(it > 0)
            }
        } else {
            binding.tvTitleBarFollowBtn.visible()
            binding.ivTitleBarAvatar.updateLayoutParams<FrameLayout.LayoutParams> {
                gravity = Gravity.CENTER_VERTICAL
                leftMargin = dp(48)
            }
            reportBlockObserver = BaseProfileReportBlockObserver(this)
            binding.ivShareBtn.setOnAntiViolenceClickListener {
                vm.trackClick(
                    "share",
                    "share",
                    "share"
                )
                share(null)
            }
            binding.ivMoreBtn.setOnAntiViolenceClickListener {
                vm.trackClick(
                    null,
                    null,
                    "more_thing"
                )
                reportBlockObserver?.showMoreDialog(it)
            }
            reportBlockObserver?.doBlockListener { isBlock ->
                Timber.tag(TAG_BLOCK).d("doBlockListener userProfile-> $isBlock ${args.uuid}")
                if (isBlock) {
                    vm.blockUser()
                } else {
                    vm.unblockUser()
                }
            }
        }
        binding.mAppBarLayout.addOnOffsetChangedListener(viewLifecycleOwner) { v, offset ->
            val absVerticalOffset = abs(offset)
            val alpha = (absVerticalOffset / v.totalScrollRange.toFloat()).coerceAtLeast(0f).coerceAtMost(1f)

            binding.vTitleBarBg.alpha = alpha
            binding.ivTitleBarAvatar.alpha = alpha
            if(!args.isMe){
                binding.tvTitleBarFollowBtn.isEnabled = alpha > 0.5f
                binding.tvTitleBarFollowBtn.alpha = alpha
            }
        }
        binding.clUserInfo.minimumHeight =
            statusBarHeight + getDimensionPx(R.dimen.title_bar_height) + dp(16)
    }

    private fun initMain() {
        binding.ivAvatar.setMargin(
            top = statusBarHeight + getDimensionPx(R.dimen.title_bar_height) + dp(8)
        )
        binding.etUsername.inputType = EditorInfo.TYPE_NULL
        binding.etUsername.setCallback(viewLifecycleOwner) {
            if (it) {
                binding.vUsernameMask.visible()
            } else {
                binding.vUsernameMask.gone()
            }
        }
        binding.llFriendNum.visible(PandoraToggle.isIMEntrance)
        if (EnvConfig.isParty()) {
            val cs = ConstraintSet()
            cs.clone(binding.clUserInfo)
            cs.connect(
                R.id.llLikesNum,
                ConstraintSet.END,
                ConstraintSet.UNSET,
                ConstraintSet.START,
            )
            cs.applyTo(binding.clUserInfo)
            binding.clFanNum.setMargin(left = if (binding.llFriendNum.isVisible) dp(24) else 0)
            binding.llFollowingNum.setMargin(left = dp(24))
            binding.llLikesNum.setMargin(left = dp(24))
        }
        binding.ivAvatar.setOnAntiViolenceClickListener {
            val avatar = vm.oldState.userProfile()?.portrait
            if (avatar.isNullOrBlank()) return@setOnAntiViolenceClickListener
            val imgArr = arrayOf(avatar)
            if (args.isMe) {
                val imgAction = getString(R.string.image_dialog_change_avatar)
                OpenPreviewBuilder(this@ProfileFragment)
                    .setImageUrls(imgArr.toList())
                    .setShowSaveBtn(true)
                    .setImageAction(imgAction) {
                        it.close()
                        Analytics.track(EventConstants.EVENT_GAME_AVATAR_LAUNCH) {
                            putAll(ResIdUtils.getAnalyticsMap(ResIdBean().setCategoryID(CategoryId.JUMP_ROLE_GAME_FROM_PROFILE_PHOTO)))
                            put("from", "profile_photo")
                        }
                        context?.let { context ->
                            MetaRouter.MobileEditor.fullScreenRole(
                                context,
                                FullScreenEditorActivityArgs(
                                    categoryId = CategoryId.JUMP_ROLE_GAME_FROM_PROFILE_PHOTO
                                )
                            )
                        }
                    }
                    .show()
            } else {
                OpenPreviewBuilder(this@ProfileFragment)
                    .setImageUrls(imgArr.toList())
                    .show()
            }
        }
        binding.tvId.setOnAntiViolenceClickListener {
            vm.oldState.userProfile()?.userNumber?.let {
                lifecycleScope.launch {
                    ClipBoardUtil.setClipBoardContent(it, requireContext(), label = "ID")
                }
                toast(R.string.id_copied_ok)
            }
        }
        binding.ivLabelOfficial.setOnAntiViolenceClickListener {
            UserLabelView.showDescDialog(this, UserLabelView.TYPE_OFFICIAL to null)
        }
        binding.ivLabelCreator.setOnAntiViolenceClickListener {
            UserLabelView.showDescDialog(this, UserLabelView.TYPE_CREATOR to null)
        }
        binding.ivFigure.setOnAntiViolenceClickListener {
            vm.trackClick(
                "dress",
                "dress",
                "try_on"
            )
            if (args.isMe) {
                MetaRouter.MobileEditor.fullScreenRole(
                    requireContext(),
                    FullScreenEditorActivityArgs(
                        categoryId = CategoryId.JUMP_ROLE_GAME_FROM_ME_PROFILE
                    )
                )
            } else {
                vm.oldState.let {
                    if (it.userProfile()?.canTryOn() == true) {
                        MetaRouter.MobileEditor.fullScreenRole(
                            requireContext(),
                            FullScreenEditorActivityArgs(
                                categoryId = CategoryId.JUMP_ROLE_GAME_FROM_OTHER_PROFILE,
                                tryOn = RoleGameTryOn.create(
                                    tryOnUserId = it.uuid,
                                    from = RoleGameTryOn.FROM_OTHER_PROFILE,
                                    allowTryOn = true
                                )
                            )
                        )
                    } else {
                        toast(R.string.profile_try_on_tap_tips_agree)
                    }
                }
            }
            vm.showTryOnTips = false
        }
        binding.llFriendNum.setOnAntiViolenceClickListener {
            vm.trackClick(
                "friends",
                "friends",
                "friends"
            )
            Analytics.track(
                EventConstants.EVENT_IM_FRIEND_CLICK
            )
            val s = vm.oldState
            val info = s.userProfile() ?: return@setOnAntiViolenceClickListener
            if (!args.isMe && !info.canShowFollower()) {
                toast(R.string.refuse_friend_list_show)
                return@setOnAntiViolenceClickListener
            }

            MetaRouter.Profile.followAndFans(
                this,
                args.isMe,
                s.uuid,
                info.followCount,
                s.fansCount,
                info.friendTotal,
                TYPE_LOCATION_FRIEND
            )
        }
        binding.clFanNum.setOnAntiViolenceClickListener {
            vm.trackClick(
                "fans",
                "fans",
                "fans"
            )
            Analytics.track(EventConstants.EVENT_CLICK_FANS_LIST)
            val s = vm.oldState
            val info = s.userProfile() ?: return@setOnAntiViolenceClickListener
            if (!args.isMe && !info.canShowFollower()) {
                toast(R.string.refuse_fans_list_show)
                return@setOnAntiViolenceClickListener
            }
            if (args.isMe && binding.vFanNumRedDot.isVisible) {
                vm.clearNewFollowerRecord()
            }
            MetaRouter.Profile.followAndFans(
                this,
                args.isMe,
                s.uuid,
                info.followCount,
                s.fansCount,
                info.friendTotal,
                TYPE_LOCATION_FANS
            )
        }
        binding.llFollowingNum.setOnAntiViolenceClickListener {
            vm.trackClick(
                "following",
                "following",
                "following"
            )
            Analytics.track(EventConstants.EVENT_CLICK_FOLLOW_LIST)
            val s = vm.oldState
            val info = s.userProfile() ?: return@setOnAntiViolenceClickListener
            if (!args.isMe && !info.canShowFollower()) {
                toast(R.string.refuse_follower_list_show)
                return@setOnAntiViolenceClickListener
            }
            MetaRouter.Profile.followAndFans(
                this,
                args.isMe,
                s.uuid,
                info.followCount,
                s.fansCount,
                info.friendTotal,
                TYPE_LOCATION_FOLLOW
            )
        }
        binding.llLikesNum.setOnAntiViolenceClickListener {
            vm.trackClick(
                "like",
                "like",
                "like"
            )
            Analytics.track(EventConstants.EVENT_CLICK_LIKE_LIST)
            val info = vm.oldState.userProfile() ?: return@setOnAntiViolenceClickListener
            LikeCountDialog.show(
                this,
                LikeCountDialogArgs(info.nickname.orEmpty(), info.likeCount)
            )
        }
        if (args.isMe) {
            binding.tvSignature.setOnAntiViolenceClickListener {
                vm.trackClick(
                    "edit",
                    "edit",
                    null
                )
                Analytics.track(EventConstants.EVENT_PROFILE_ADD_INFORMATION_CLICK)
                MetaRouter.Account.editProfile(this, null, null)
            }
        }
        binding.vBadgeBg.setOnAntiViolenceClickListener {
            vm.trackClick(
                "medal",
                "medal",
                "medal"
            )
            vm.oldState.userProfile()?.labelInfo?.let {
                UserLabelView.showDescDialog(this, UserLabelView.TYPE_MEDAL to it)
            }
        }
        binding.vLinkBg.setOnAntiViolenceClickListener {
            vm.trackClick(
                "link",
                "link",
                "link"
            )
            val s = vm.oldState
            val userProfile = s.userProfile() ?: return@setOnAntiViolenceClickListener
            if (userProfile.externalLinks.isEmpty()) return@setOnAntiViolenceClickListener
            if (userProfile.externalLinks.size == 1) {
                val item = userProfile.externalLinks[0]
                MetaRouterWrapper.ExternalLink.jump(
                    this,
                    item,
                    SOURCE_LOCAL
                )
                Analytics.track(
                    EventConstants.EVENT_LINK_CLICK,
                    mapOf("userid" to s.uuid, "link" to item.url.orEmpty())
                )
            } else {
                LinkSelectDialog.show(this, userProfile.externalLinks, s.uuid, s.isMe, s.isMineTab)
            }
        }
        binding.tvFollowBtn.setOnAntiViolenceClickListener {
            if (vm.isBlocked) {
                reportBlockObserver?.showUnBlockDialog()
            } else if (vm.oldState.isFollow) {
                vm.trackClick(
                    null,
                    null,
                    "follow_no"
                )
                vm.changeFollow()
            } else {
                vm.trackClick(
                    null,
                    null,
                    "follow"
                )
                vm.changeFollow()
            }
        }
        binding.tvTitleBarFollowBtn.setOnAntiViolenceClickListener {
            vm.trackClick(
                null,
                null,
                if (vm.oldState.isFollow) {
                    "follow_no"
                } else {
                    "follow"
                }
            )
            vm.changeFollow()
        }
        binding.tvAddFriendBtn.setOnAntiViolenceClickListener {
            vm.trackClick(
                null,
                null,
                "add_friends"
            )
            goAddFriend()
        }
        binding.tvChatBtn.setOnAntiViolenceClickListener {
            vm.trackClick(
                null,
                null,
                "message"
            )
            if (vm.oldState.userProfile()?.relation == FriendReleation.WEAREFRIENDS.key) {
                goChat()
            } else {
                goChatOfficial()
            }
        }
        binding.layerOtherGroup.setOnAntiViolenceClickListener {
            vm.trackClick(
                null,
                null,
                "group"
            )
            MetaRouter.IM.goHeGroupsListFragment(
                this,
                vm.oldState.uuid
            )
        }
        binding.vBannerBg.setOnAntiViolenceClickListener {
            vm.trackClick(
                "activity",
                "activity",
                null
            )
            vm.oldState.banner()?.let {
                UniJumpUtil.jump(
                    this,
                    it,
                    LinkData.SOURCE_PROFILE_BANNER,
                    CategoryId.OPERATION_POSITION_PROFILE_BANNER,
                    null
                )
            }
        }
        binding.icBannerCloseBtn.setOnAntiViolenceClickListener {
            vm.closeBanner()
        }
        vm.setupRefreshLoading(
            ProfileState::userProfile,
            binding.lv,
            null
        ) {
            vm.getProfile()
        }
        vm.onAsync(ProfileState::userProfile) {
            binding.ivShareBtn.visible()
            if (!args.isMe) {
                reportBlockObserver?.addUserProfileInfo(it)
                binding.ivMoreBtn.visible()
            }

            binding.tvId.setTextWithArgs(R.string.user_id, it.userNumber.orEmpty())
            val isOfficial = it.isOfficial()
            val isCreator = it.isCreator()
            binding.ivLabelOfficial.visible(isOfficial)
            binding.ivLabelCreator.visible(isCreator)
            binding.tvFriendNum.text = UnitUtil.formatKMCount(it.friendTotal)
            binding.tvFollowingNum.text = UnitUtil.formatKMCount(it.followCount)
            binding.tvFanNum.text = UnitUtil.formatKMCount(it.fansCount)
            binding.tvLikesNum.text = UnitUtil.formatKMCount(it.likeCount)
            if (it.labelInfo != null) {
                visibleList(
                    binding.vBadgeBg,
                    binding.ivBadge,
                    binding.tvBadge,
                    visible = true
                )
                UserLabelView.loadMedal(glide, it.labelInfo.icon, binding.ivBadge) {
                    (dp(18) * (it.minimumWidth / it.minimumHeight.toFloat())).toInt() + dp(8)
                }
                binding.tvBadge.text = it.labelInfo.name
            } else {
                visibleList(
                    binding.vBadgeBg,
                    binding.ivBadge,
                    binding.tvBadge,
                    visible = false
                )
            }
            if (vm.showTryOnTips && !tryOnTipsShown) {
                tryOnTipsShown = true
                binding.groupFigureTips.visible()
                if (args.isMe) {
                    binding.tvFigureTipsContent.setText(R.string.profile_try_on_tap_tips)
                } else {
                    binding.tvFigureTipsContent.setText(R.string.profile_try_on_tap_tips_others)
                }
                ValueAnimator.ofFloat(1.0f, 0.0f).apply {
                    addUpdateListener(viewLifecycleOwner) {
                        val alpha = it.animatedValue as Float
                        binding.ivFigureTipsDot.alpha = alpha
                        binding.ivFigureTipsArrow.alpha = alpha
                        binding.tvFigureTipsContent.alpha = alpha
                    }
                    addListener(viewLifecycleOwner, onEnd = {
                        binding.groupFigureTips.gone()
                    })
                    startDelay = 3_000L
                    start()
                }
            }

            if (args.isFromBottom) {
                drawerLayout?.findViewById<ImageView>(R.id.ivSidebarAvatar)
                    ?.let { ivSidebarAvatar ->
                        glide?.run {
                            load(it.portrait).placeholder(R.drawable.icon_default_avatar)
                                .circleCrop()
                                .into(ivSidebarAvatar)
                        }
                    }
                drawerLayout?.findViewById<TextView>(R.id.tvSidebarUsername)
                    ?.let { tvSidebarUsername ->
                        tvSidebarUsername.text = it.nickname
                    }
            }
        }
        vm.onEach(
            ProfileState::userProfile,
            ProfileState::fansCount
        ) { userProfile, fansCount ->
            userProfile()?.let {
                binding.tvFriendNum.text = UnitUtil.formatKMCount(it.friendTotal)
                binding.tvFollowingNum.text = UnitUtil.formatKMCount(it.followCount)
                binding.tvFanNum.text = UnitUtil.formatKMCount(fansCount)
                binding.tvLikesNum.text = UnitUtil.formatKMCount(it.likeCount)
            }
        }
        vm.onEach(ProfileState::portrait) {
            glide?.run {
                load(it).placeholder(R.drawable.icon_default_avatar)
                    .circleCrop()
                    .into(binding.ivAvatar)
                load(it).placeholder(R.drawable.icon_default_avatar)
                    .circleCrop()
                    .into(binding.ivTitleBarAvatar)
            }
        }
        vm.onEach(ProfileState::wholeBodyImage) {
            glide?.run {
                load(it).placeholder(R.drawable.placeholder_circle)
                    .placeholder(R.drawable.ic_default_avatar)
                    .fitCenter()
                    .transform(TransparentBoundCrop())
                    .listener(object : RequestListener<Drawable> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any?,
                            target: Target<Drawable>,
                            isFirstResource: Boolean
                        ): Boolean {
                            return false
                        }

                        override fun onResourceReady(
                            resource: Drawable,
                            model: Any,
                            target: Target<Drawable>?,
                            dataSource: DataSource,
                            isFirstResource: Boolean
                        ): Boolean {
                            if (isBindingAvailable()) {
                                binding.ivFigure.setHeightEx(((dp(103) / resource.minimumWidth.toFloat()) * resource.minimumHeight).toInt())
                            }
                            return false
                        }
                    })
                    .into(binding.ivFigure)
            }
        }
        vm.onEach(ProfileState::nickname) {
            binding.etUsername.setText(it)
        }
        vm.onEach(ProfileState::signature) {
            if (it.isNullOrEmpty()) {
                if (args.isMe) {
                    binding.tvSignature.text = SpannableHelper.Builder().centerImageSpan(
                        requireContext(),
                        R.drawable.ic_profile_signature_add,
                        marginRight = dp(4)
                    )
                        .text(getString(R.string.text_add_profile_information))
                        .build()
                } else {
                    binding.tvSignature.setText(R.string.profile_signature_default)
                }
            } else {
                if (args.isMe) {
                    binding.tvSignature.text = SpannableHelper.Builder()
                        .centerImageSpan(
                            requireContext(),
                            R.drawable.ic_profile_signature_add,
                            marginRight = dp(4)
                        )
                        .text(it)
                        .build()
                } else {
                    binding.tvSignature.text = it
                }
            }
        }
        vm.onEach(ProfileState::mediaLinks) {
            if (!it.isNullOrEmpty()) {
                visibleList(
                    binding.vLinkBg,
                    binding.ivLinkArrow,
                    visible = true
                )
                it.getOrNull(0)?.let {
                    binding.ivLink1.visible()
                    glide?.run {
                        load(it.icon).into(binding.ivLink1)
                    }
                }
                it.getOrNull(1)?.let {
                    binding.ivLink2.visible()
                    glide?.run {
                        load(it.icon).into(binding.ivLink2)
                    }
                }
                it.getOrNull(2)?.let {
                    binding.ivLink3.visible()
                    glide?.run {
                        load(it.icon).into(binding.ivLink3)
                    }
                }
            } else {
                visibleList(
                    binding.vLinkBg,
                    binding.ivLink1,
                    binding.ivLink2,
                    binding.ivLink3,
                    binding.ivLinkArrow,
                    visible = false
                )
            }
        }
        if (args.isMe) {
            vm.onEach(ProfileState::entrances) {
                if (it.isEmpty()) {
                    binding.bannerEntrance.gone()
                    binding.indicatorEntrance.gone()
                } else {
                    binding.bannerEntrance.visible()
                    binding.indicatorEntrance.visible()
                    val size = it.size
                    binding.indicatorEntrance.gone(size <= 1)
                    binding.indicatorEntrance
                        .setIndicatorStyle(IndicatorStyle.ROUND_RECT)
                        .setSlideMode(IndicatorSlideMode.NORMAL)
                        .setSliderWidth(dp(3).toFloat(), dp(8).toFloat())
                        .setSliderHeight(dp(3).toFloat())
                        .setSliderGap(dp(3).toFloat())
                        .setPageSize(size)
                        .notifyDataChanged()
                    bannerAdapter.updateData(it)
                    if (bannerPosition !in 1..size) {
                        bannerPosition = 1
                    }
                    if (bannerPosition in 1..size) {
                        binding.indicatorEntrance.setCurrentPosition(bannerPosition - 1)
                        binding.bannerEntrance.startPosition = bannerPosition
                    }
                    if (binding.bannerEntrance.adapter == null) {
                        binding.bannerEntrance.setAdapter(bannerAdapter)
                    }
                    binding.bannerEntrance
                        .isAutoLoop(false)
                        .addBannerLifecycleObserver(viewLifecycleOwner)
                        .removeIndicator()
                        .setOnBannerListener { data, _ ->
                            if (data !is ProfileEntrance) {
                                return@setOnBannerListener
                            }
                            onClickEntranceItem(data)
                        }
                        .addOnPageChangeListener(object : OnPageChangeListener {
                            override fun onPageScrolled(
                                position: Int,
                                positionOffset: Float,
                                positionOffsetPixels: Int
                            ) {
                                binding.indicatorEntrance.onPageScrolled(
                                    position,
                                    positionOffset,
                                    positionOffsetPixels
                                )
                            }

                            override fun onPageSelected(position: Int) {
                                bannerPosition = position + 1
                                binding.indicatorEntrance.onPageSelected(position)
                            }

                            override fun onPageScrollStateChanged(state: Int) {
                                binding.indicatorEntrance.onPageScrollStateChanged(state)
                            }
                        })
                }
            }
            vm.onEach(
                ProfileState::banner
            ) {
                val banner = it()
                if (banner != null) {
                    visibleList(
                        binding.vBannerBg,
                        binding.icBannerCloseBtn,
                        binding.tvBannerBtn,
                        visible = true
                    )
                    if (banner.iconUrl.isNullOrEmpty()) {
                        binding.ivBannerIcon.gone()
                    } else {
                        binding.ivBannerIcon.visible()
                        glide?.run {
                            load(banner.iconUrl).placeholder(R.drawable.placeholder)
                                .into(binding.ivBannerIcon)
                        }
                    }
                    binding.tvBannerTitle.goneIfValueEmpty(banner.title)
                    binding.tvBannerDesc.goneIfValueEmpty(banner.subTitle)
                } else {
                    visibleList(
                        binding.vBannerBg,
                        binding.ivBannerIcon,
                        binding.tvBannerTitle,
                        binding.tvBannerDesc,
                        binding.tvBannerBtn,
                        binding.icBannerCloseBtn,
                        visible = false
                    )
                }
            }
        } else {
            vm.onEach(
                ProfileState::userProfile,
                ProfileState::isFollow
            ) { userProfile, isFollow ->
                val profile = userProfile()
                if (profile == null) {
                    binding.tvFollowBtn.gone()
                    binding.tvAddFriendBtn.gone()
                    binding.tvChatBtn.gone()
                    return@onEach
                }
                val isBlock = vm.isBlocked(profile.blockRelation)
                val isFriend = profile.relation == FriendReleation.WEAREFRIENDS.key
                val iAmOfficial = vm.accountInteractor.isOfficial
                if (isBlock) {
                    binding.tvFollowBtn.visible()
                    binding.tvFollowBtn.setFontFamily(R.font.poppins_regular_400)
                    binding.tvFollowBtn.setText(R.string.unblock_user)
                    binding.tvFollowBtn.setTextColorByRes(R.color.white)
                    binding.tvFollowBtn.setBackgroundResource(R.drawable.bg_ff826c_round_100)
                    binding.tvAddFriendBtn.gone()
                    binding.tvChatBtn.visible(iAmOfficial)
                    if (iAmOfficial) {
                        binding.tvChatBtn.text = SpannableHelper.Builder()
                            .centerImageSpan(requireContext(), R.drawable.ic_profile_other_message)
                            .build()
                        binding.tvChatBtn.setPaddingEx(
                            top = dp(9),
                            bottom = dp(9)
                        )
                        binding.tvChatBtn.setWidthEx(WindowManager.LayoutParams.WRAP_CONTENT)
                    }
                } else if (isFollow) {
                    binding.tvFollowBtn.visible()
                    binding.tvFollowBtn.setFontFamily(R.font.poppins_regular_400)
                    binding.tvFollowBtn.setText(R.string.following_cap)
                    binding.tvFollowBtn.setTextColorByRes(R.color.color_BEA99E)
                    binding.tvFollowBtn.setBackgroundResource(R.drawable.bg_fffae9_round_100)
                    binding.tvAddFriendBtn.visible(!isFriend)
                    if (!isFriend) {
                        binding.tvAddFriendBtn.text = SpannableHelper.Builder()
                            .centerImageSpan(
                                requireContext(),
                                R.drawable.ic_profile_other_add_friend,
                                marginRight = dp(4)
                            )
                            .text(
                                getString(R.string.friend_singular)
                            )
                            .build()
                        binding.tvAddFriendBtn.setPaddingEx(
                            top = dp(7.5),
                            bottom = dp(7.5)
                        )
                        binding.tvAddFriendBtn.setWidthEx(0)
                    }
                    binding.tvChatBtn.visible(isFriend || iAmOfficial)
                    if (isFriend || iAmOfficial) {
                        binding.tvChatBtn.text = SpannableHelper.Builder()
                            .centerImageSpan(
                                requireContext(),
                                R.drawable.ic_profile_other_message,
                                marginRight = dp(4)
                            )
                            .text(
                                getString(R.string.message)
                            )
                            .build()
                        binding.tvChatBtn.setPaddingEx(
                            top = dp(7.5),
                            bottom = dp(7.5)
                        )
                        binding.tvChatBtn.setWidthEx(0)
                    }
                } else {
                    binding.tvFollowBtn.visible()
                    binding.tvFollowBtn.setFontFamily(R.font.poppins_medium_500)
                    binding.tvFollowBtn.setTextColorByRes(R.color.color_451A03)
                    binding.tvFollowBtn.setBackgroundResource(R.drawable.bg_ffda62_round_100)
                    binding.tvFollowBtn.text = SpannableHelper.Builder()
                        .centerImageSpan(
                            requireContext(),
                            R.drawable.ic_profile_follow_add_14,
                            marginRight = dp(4)
                        )
                        .text(
                            getString(R.string.follow)
                        )
                        .build()
                    binding.tvAddFriendBtn.visible(!isFriend)
                    if (!isFriend) {
                        binding.tvAddFriendBtn.text = SpannableHelper.Builder()
                            .centerImageSpan(
                                requireContext(),
                                R.drawable.ic_profile_other_add_friend
                            )
                            .build()
                        binding.tvAddFriendBtn.setPaddingEx(
                            top = dp(9),
                            bottom = dp(9)
                        )
                        binding.tvAddFriendBtn.setWidthEx(WindowManager.LayoutParams.WRAP_CONTENT)
                    }
                    binding.tvChatBtn.visible(isFriend || iAmOfficial)
                    if (isFriend || iAmOfficial) {
                        binding.tvChatBtn.text = SpannableHelper.Builder()
                            .centerImageSpan(
                                requireContext(),
                                R.drawable.ic_profile_other_message
                            )
                            .build()
                        binding.tvChatBtn.setPaddingEx(
                            top = dp(9),
                            bottom = dp(9)
                        )
                        binding.tvChatBtn.setWidthEx(WindowManager.LayoutParams.WRAP_CONTENT)
                    }
                }
                binding.tvTitleBarFollowBtn.visible(!isBlock)
                if (isFollow) {
                    binding.tvTitleBarFollowBtn.setText(R.string.following_cap)
                    binding.tvTitleBarFollowBtn.setTextColorByRes(R.color.color_CAA220)
                    binding.tvTitleBarFollowBtn.setBackgroundResource(R.drawable.sp_c100_caa220_s1)
                    binding.tvTitleBarFollowBtn.clearCompoundDrawables(left = true)
                } else {
                    binding.tvTitleBarFollowBtn.setText(R.string.follow)
                    binding.tvTitleBarFollowBtn.setTextColorByRes(R.color.color_1A1A1A)
                    binding.tvTitleBarFollowBtn.setBackgroundResource(R.drawable.sp_c100_1a1a1a_s1)
                    binding.tvTitleBarFollowBtn.compoundDrawables(left = R.drawable.ic_profile_follow_add_14)
                }
                binding.tlProfile.invisible(isBlock)
                binding.vp.invisible(isBlock)
                binding.nsvEmptyTip.visible(isBlock)
                binding.tvEmptyTipText.setTextWithArgs(
                    R.string.block_empty_message,
                    profile.nickname
                )
            }
            vm.onEach(
                ProfileState::groupCount
            ) {
                val groupCount = it?.createCount ?: 0
                if (groupCount == 0) {
                    binding.layerOtherGroup.gone()
                } else {
                    binding.layerOtherGroup.visible()
                    if (groupCount == 1) {
                        binding.tvOtherGroupDesc.setText(R.string.profile_other_one_group_desc)
                    } else {
                        binding.tvOtherGroupDesc.setTextWithArgs(
                            R.string.profile_other_group_desc,
                            groupCount
                        )
                    }
                }
            }
        }

        if (args.isMe) {
            vm.accountInteractor.badgeLiveData.observe(viewLifecycleOwner) {
                binding.vFanNumRedDot.visible(it?.newFollower?.hasNew == true)
            }
        }

        Analytics.track(EventConstants.EVENT_PROFILE_PAGE_SHOW) {
            put("type", if (args.isFromBottom) 1 else 2)
            put("from", if (args.isFromBottom) "tab" else args.from)
        }
        if (args.isMe) {
            if (PandoraToggle.isVipPlusOpen()) {
                Analytics.track(
                    EventConstants.EVENT_SUBSCRIBE_SHOW,
                    mapOf("entry_style" to if (vm.oldState.memberInfo?.isActive() == true) "1" else "0")
                )
            } else if (PandoraToggle.isVipStatusOpen()) {
                vm.onEach(
                    ProfileState::memberInfo
                ) {
                    if (it?.isActive() == true) {
                        Analytics.track(
                            EventConstants.EVENT_SUBSCRIBE_SHOW,
                            mapOf("entry_style" to if (vm.oldState.memberInfo?.isActive() == true) "1" else "0")
                        )
                    }
                }
            }
        }
    }

    private fun initTabs() {
        binding.tlProfile.addOnTabSelectedListener(
            viewLifecycleOwner,
            object : TabLayout.OnTabSelectedListener {
                private var first = true

                override fun onTabSelected(tab: TabLayout.Tab) {
                    if (first) {
                        first = false
                    } else {
                        when (tab.tag as? HomepageTab) {
                            HomepageTab.RECENT -> {
                                vm.trackClick(
                                    "tab_recent",
                                    "tab_recent",
                                    "tab_recent"
                                )
                            }

                            HomepageTab.POST -> {
                                vm.trackClick(
                                    "tab_post",
                                    "tab_post",
                                    "tab_post"
                                )
                            }

                            HomepageTab.CLOTHES -> {
                                vm.trackClick(
                                    "tab_clothes",
                                    "tab_clothes",
                                    "tab_clothes"
                                )
                            }

                            HomepageTab.UGC -> {
                                vm.trackClick(
                                    "tab_maps",
                                    "tab_maps",
                                    "tab_maps"
                                )
                            }

                            HomepageTab.ASSETS -> {
                                vm.trackClick(
                                    "tab_asset",
                                    "tab_asset",
                                    "tab_asset"
                                )
                            }

                            null -> {}
                        }
                    }
                    setTabSelectUI(tab, true)
                }

                override fun onTabUnselected(tab: TabLayout.Tab) {
                    setTabSelectUI(tab, false)
                }

                override fun onTabReselected(tab: TabLayout.Tab) {}
            })
        binding.dtlh.setOnEventListener(
            viewLifecycleOwner,
            object : DrawerTabLayoutHost.OnEventListener {
                override fun onEvent(ev: MotionEvent, tl: TabLayout, intercept: Boolean) {
                    when (ev.action) {
                        MotionEvent.ACTION_DOWN -> {
                            lockDrawerToClosed(tl.canScrollHorizontally(-1))
                        }

                        MotionEvent.ACTION_MOVE -> {
                            lockDrawerToClosed(intercept)
                        }

                        else -> {
                            lockDrawerToClosed(false)
                        }
                    }
                }
            }
        )
        binding.vp.registerOnPageChangeCallback(
            viewLifecycleOwner,
            object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    vm.changeSelectTab(position)
                    if ((binding.tlProfile.getTabAt(position)?.tag as? HomepageTab) == HomepageTab.ASSETS) {
                        Analytics.track(
                            EventConstants.MY_LIBRARY_TAB_CLICK
                        )
                    }
                }
            })
        binding.aslfl.setOnEventListener(
            viewLifecycleOwner,
            object : DrawerViewPager2Host.OnEventListener {
                override fun onEvent(
                    ev: MotionEvent,
                    vp2: ViewPager2,
                    adapter: RecyclerView.Adapter<*>,
                    intercept: Boolean
                ) {
                    when (ev.action) {
                        MotionEvent.ACTION_DOWN -> {
                            Timber.d("lockDrawerToClosed, action: ${ev.action}, closed: ${vp2.currentItem < adapter.itemCount - 1}")
                            lockDrawerToClosed(vp2.currentItem > 0)
                        }

                        MotionEvent.ACTION_MOVE -> {
                            lockDrawerToClosed(intercept)
                        }

                        else -> {
                            Timber.d("lockDrawerToClosed, action: ${ev.action}, closed: false")
                            lockDrawerToClosed(false)
                        }
                    }
                }
            }
        )
        binding.vp.offscreenPageLimit = 1
        vm.onEach(ProfileState::tabs, ProfileState::selectedTab) { tabs, selectedTab ->
            if (tabs.isNullOrEmpty() && binding.vp.adapterAllowStateLoss != null) {
                tabLayoutMediator?.detach()
                tabLayoutMediator = null
                binding.vp.adapterAllowStateLoss = null
                binding.tlProfile.removeAllTabs()
            } else if (!tabs.isNullOrEmpty() && binding.vp.adapterAllowStateLoss == null) {
                val tabTitles = tabs.map { it.first }
                val tabFactories = tabs.map { it.second }
                binding.vp.adapterAllowStateLoss =
                    CommonTabStateAdapter(
                        tabFactories,
                        childFragmentManager,
                        viewLifecycleOwner.lifecycle
                    )
                binding.vp.setCurrentItemIfValid(selectedTab)
                tabLayoutMediator =
                    TabLayoutMediator(binding.tlProfile, binding.vp) { tab, position ->
                        val tabBinding = TabIndicatorProfileV2Binding.inflate(layoutInflater)
                        val title = getString(tabTitles[position].title)

                        tabBinding.tvThin.text = title
                        tabBinding.tvBold.text = title

                        tab.customView = tabBinding.root
                        tab.tag = tabTitles[position]
                    }
                tabLayoutMediator?.attach()
                binding.tlProfile.addTabGapFirstLast(dp(4), dp(4))
            } else if (!tabs.isNullOrEmpty()) {
                binding.vp.setCurrentItemIfValid(selectedTab)
            }
        }
    }

    private fun initOthers() {
        if (args.isFromBottom) {
            vm.onEach(
                ProfileState::avatarPopup,
                deliveryMode = uniqueOnly()
            ) {
                it?.let {
                    AvatarPopupDialog.show(this, it)
                }
            }
        }
    }

    private fun setTabSelectUI(tab: TabLayout.Tab, select: Boolean) {
        val cv = tab.customView ?: return
        cv.findViewById<TextView>(R.id.tvThin)?.isInvisible = select
        cv.findViewById<TextView>(R.id.tvBold)?.isInvisible = !select
        if (select) {
            (tab.tag as? HomepageTab)?.event?.let {
                Analytics.track(it)
            }
        }
    }

    private fun share(userExtra: ShareRawData.UserExtra?) {
        val s = vm.oldState
        val user = s.userProfile()?.copy(
            nickname = s.nickname,
            signature = s.signature,
            portrait = s.portrait,
            wholeBodyImage = s.wholeBodyImage,
            fansCount = s.fansCount
        ) ?: return
        GlobalShareDialog.show(
            childFragmentManager, ShareRawData.user(user.copy(fansCount = s.fansCount), userExtra)
        )
    }

    private fun goScan() {
        MetaRouter.IM.goQRCodeScan(
            requireActivity(),
            this,
            QRCodeScanFragment.KEY_PROFILE_REQUEST_SCAN_QRCODE,
            ScanEntry.Profile
        )
    }

    private fun goWallet() {
        if (PayProvider.ENABLE_RECHARGE) {
            vm.checkPayConnect()
            MetaRouter.Pay.goBuyCoinsPage(
                requireContext(),
                this,
                "profile"
            )
        } else {
            toast(R.string.under_development)
        }
    }

    private fun goAddFriend() {
        val userProfile = vm.oldState.userProfile() ?: return
        Analytics.track(EventConstants.EVENT_IM_ADD_FRIEND_CLICK) { put("from", 1) }
        MetaRouter.IM.goFriendApply(
            fragment = this,
            uuid = userProfile.uid.orEmpty(),
            userNumber = userProfile.userNumber.orEmpty(),
            gamePackageName = "",
            tagIds = userProfile.tagIds,
            labelInfo = userProfile.labelInfo,
            userName = userProfile.nickname,
            avatar = userProfile.portrait
        )
    }

    private fun goChat() {
        val userProfile = vm.oldState.userProfile() ?: return
        Analytics.track(EventConstants.EVENT_IM_CHAT_CLICK) { put("from", 4) }
        MetaRouter.IM.gotoConversation(
            fragment = this,
            otherUid = userProfile.uid.orEmpty(),
            title = userProfile.nickname,
            tagIds = userProfile.tagIds,
            labelInfo = userProfile.labelInfo,
        )
    }

    private fun goChatOfficial() {
        val userProfile = vm.oldState.userProfile() ?: return
        Analytics.track(EventConstants.EVENT_IM_CHAT_CLICK) { put("from", 4) }
        MetaRouter.IM.gotoConversation(
            fragment = this,
            otherUid = userProfile.uid.orEmpty(),
            title = userProfile.nickname,
            tagIds = userProfile.tagIds,
            labelInfo = userProfile.labelInfo,
            autoConnect = true
        )
    }

    private fun onClickEntranceItem(item: ProfileEntrance) {
        when (item) {
            is ProfileEntrance.Premium -> {
                vm.trackClick(
                    "member",
                    "member",
                    null
                )
                val url = StringBuilder(vm.getH5PageUrl(H5PageConfigInteractor.VIP_STATUS))
                    .append("?source=personal")
                    .toString()
                MetaRouter.Web.navigate(
                    this,
                    null,
                    url,
                    showTitle = false,
                    showStatusBar = false
                )
                Analytics.track(
                    EventConstants.EVENT_USER_SUBSCRIBE_CLICK,
                    mapOf("entry_style" to if (vm.oldState.memberInfo?.isActive() == true) "1" else "0")
                )
            }

            is ProfileEntrance.Wallet -> {
                vm.trackClick(
                    "wallet",
                    "wallet",
                    null
                )
                goWallet()
            }

            is ProfileEntrance.MyGroup -> {
                vm.trackClick(
                    "group",
                    "group",
                    null
                )
                MetaRouter.IM.goMyGroupsListFragment(
                    this,
                    vm.oldState.uuid
                )
            }

            is ProfileEntrance.Build -> {
                vm.trackClick(
                    "build",
                    "build",
                    null
                )
                Analytics.track(EventConstants.PROFILE_UGC_CREATE)
                MetaRouter.MobileEditor.creation(this)
            }

            is ProfileEntrance.TryOn -> {
                vm.trackClick(
                    "clothes",
                    "clothes",
                    null
                )
                MetaRouter.MobileEditor.fullScreenRole(
                    requireContext(),
                    FullScreenEditorActivityArgs(
                        categoryId = CategoryId.JUMP_ROLE_GAME_FROM_ME_PROFILE
                    )
                )
                vm.showTryOnTips = false
            }
        }
    }

    //region drawer
    private fun lockDrawerToClosed(closed: Boolean) {
        val drawerLayout = drawerLayout ?: return
        val targetMode =
            if (closed) DrawerLayout.LOCK_MODE_LOCKED_CLOSED else DrawerLayout.LOCK_MODE_UNLOCKED
        if (drawerLayout.getDrawerLockMode(GravityCompat.START) == targetMode) return
        drawerLayout.setDrawerLockMode(targetMode, GravityCompat.START)
    }

    private fun initDrawerMenu() {
        if (!args.isFromBottom) return
        binding.ivDrawerBtn.setOnAntiViolenceClickListener {
            vm.trackClick(
                "side_bar",
                null,
                null
            )
            drawerLayout?.openDrawer(
                GravityCompat.START
            )
        }
        lockDrawerToClosed(true)
        val drawerLayout = drawerLayout ?: return
        for (child in drawerLayout.children) {
            if (child.id == R.id.clSidebar) {
                drawerLayout.removeView(child)
                break
            }
        }

        val moreBinding = ViewHomeDrawerBinding.inflate(layoutInflater, drawerLayout, true)
        moreBinding.rvSidebarEntrance.layoutManager = LinearLayoutManager(requireContext())
        moreBinding.rvSidebarEntrance.setController(drawerController)
        moreBinding.tvSidebarScan.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.EVENT_HOME_SIDE_BAR_CLICK,
                "click" to "scan"
            )
            goScan()
        }
        moreBinding.tvSidebarFeedback.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.EVENT_HOME_SIDE_BAR_CLICK,
                "click" to "feedback"
            )
            MetaRouter.Feedback.feedback(
                fragment = this,
                gameId = null,
                source = SubmitNewFeedbackRequest.SOURCE_APP_NUMBER,
                defaultSelectType = null,
                needBackRole = false,
                needBackGame = false,
                fromGameId = null
            )
        }
        moreBinding.tvSidebarSetting.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.EVENT_HOME_SIDE_BAR_CLICK,
                "click" to "setting"
            )
            Analytics.track(EventConstants.EVENT_PROFILE_SET_CLICK)
            Analytics.track(EventConstants.HOMEPAGE_SETTING_CLICK) {
                put("type", 0)
            }
            MetaRouter.AccountSetting.setting(this, LoginPageSource.Profile, null)
        }

        drawerLayout.setEdgeSize("Left", viewWidth = dp(304))
        drawerLayout.addDrawerListener(viewLifecycleOwner, object : DrawerLayout.DrawerListener {
            override fun onDrawerSlide(drawerView: View, slideOffset: Float) {}

            override fun onDrawerOpened(drawerView: View) {
                onBackPressedCallback?.isEnabled = true
                Analytics.track(
                    EventConstants.EVENT_HOME_SIDE_BAR_SHOW
                )
            }

            override fun onDrawerClosed(drawerView: View) {
                onBackPressedCallback?.isEnabled = false
            }

            override fun onDrawerStateChanged(newState: Int) {}
        })
    }

    private fun onClickDrawerItem(item: HomeDrawerEntrance) {
        when (item) {
            is HomeDrawerEntrance.AboutUs -> {
                Analytics.track(
                    EventConstants.EVENT_HOME_SIDE_BAR_CLICK,
                    "click" to "about"
                )
                MetaRouter.Control.navigate(this, R.id.about_us)
                Analytics.track(EventConstants.EVENT_ABOUT_US_CLICK)
            }

            is HomeDrawerEntrance.AccountSetting -> {
                Analytics.track(
                    EventConstants.EVENT_HOME_SIDE_BAR_CLICK,
                    "click" to "account"
                )
                MetaRouter.AccountSetting.navigate(this, LoginPageSource.Profile, null)
                Analytics.track(EventConstants.EVENT_MINE_ACCOUNT_CLICK)
            }

            is HomeDrawerEntrance.AddFriend -> {
                Analytics.track(
                    EventConstants.EVENT_HOME_SIDE_BAR_CLICK,
                    "click" to "friends"
                )
                MetaRouter.IM.goSearchFriend(this)
                Analytics.track(EventConstants.EVENT_ADD_FRIENDS_CLICK) {
                    put("source", "profile")
                }
            }

            is HomeDrawerEntrance.EditProfile -> {
                Analytics.track(
                    EventConstants.EVENT_HOME_SIDE_BAR_CLICK,
                    "click" to "edit"
                )
                MetaRouter.Account.editProfile(this, null, null)
                Analytics.track(EventConstants.EVENT_PROFILE_EDIT_PROFILE_CLICK)
            }

            is HomeDrawerEntrance.Membership -> {
                Analytics.track(
                    EventConstants.EVENT_HOME_SIDE_BAR_CLICK,
                    "click" to "premium"
                )
                val url = StringBuilder(vm.getH5PageUrl(H5PageConfigInteractor.VIP_STATUS))
                    .append("?source=personal")
                    .toString()
                MetaRouter.Web.navigate(
                    this,
                    null,
                    url,
                    showTitle = false,
                    showStatusBar = false
                )
                Analytics.track(
                    EventConstants.EVENT_USER_SUBSCRIBE_CLICK,
                    mapOf("entry_style" to if (vm.oldState.memberInfo?.isActive() == true) "1" else "0")
                )
            }

            is HomeDrawerEntrance.MyBalance -> {
                Analytics.track(
                    EventConstants.EVENT_HOME_SIDE_BAR_CLICK,
                    "click" to "wallet"
                )
                goWallet()
            }

            is HomeDrawerEntrance.PrivacySetting -> {
                Analytics.track(
                    EventConstants.EVENT_HOME_SIDE_BAR_CLICK,
                    "click" to "privacy"
                )
                MetaRouter.Control.navigate(this, R.id.privacySetting)
            }
        }
    }

    private fun buildDrawerController() = simpleController(vm, ProfileState::drawerItems) {
        it.forEachIndexed { index, item ->
            add(
                HomeDrawerItem(
                    item,
                    index,
                    it.size,
                    drawerItemListener
                ).id("HomeDrawer-${index}")
            )
        }
    }
    //endregion

    override fun onResume() {
        super.onResume()
        if (args.isMe) {
            vm.getProfile()
        }
        if (args.isFromBottom) {
            lockDrawerToClosed(false)
            vm.checkShowAvatarPopupDialog()
            onBackPressedCallback =
                requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, false) {
                    drawerLayout?.closeDrawer(
                        GravityCompat.START
                    )
                }
        }
    }

    override fun onPause() {
        super.onPause()
        if (args.isFromBottom) {
            lockDrawerToClosed(true)
            onBackPressedCallback?.remove()
            onBackPressedCallback = null
        }
    }

    override fun onDestroyView() {
        reportBlockObserver?.dismiss()
        reportBlockObserver = null
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        binding.vp.adapterAllowStateLoss = null
        super.onDestroyView()
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_NAME_PROFILETAB
}