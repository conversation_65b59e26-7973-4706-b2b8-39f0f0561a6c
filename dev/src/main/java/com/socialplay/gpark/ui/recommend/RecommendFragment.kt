package com.socialplay.gpark.ui.recommend

import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.paging.LoadState
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.meta.biz.ugc.model.EditorTemplate
import com.meta.lib.mwbiz.MWBiz
import com.meta.lib.mwbiz.MWLifeCallback
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.EditorInteractor
import com.socialplay.gpark.data.interactor.TokenChangedCallback
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.HomeCustomRecommend
import com.socialplay.gpark.data.model.HomeRecommendOperation
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardType
import com.socialplay.gpark.data.model.choice.ChoiceContentType
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.data.model.choice.IChoiceItem
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.data.model.room.House
import com.socialplay.gpark.data.model.videofeed.ChoiceHomeVideoItem
import com.socialplay.gpark.databinding.FragmentRecommendBinding
import com.socialplay.gpark.databinding.ViewChoiceContinueGameBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.editor.EditorUGCLaunchParams
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.maverick.mapRetained
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.cottage.detail.CottageRoomDetailDialog
import com.socialplay.gpark.ui.dialog.LoadingDialogFragment
import com.socialplay.gpark.ui.editor.BaseEditorFragmentMaverick
import com.socialplay.gpark.ui.editorschoice.RecommendVideoAnalytics
import com.socialplay.gpark.ui.editorschoice.header.friends.ChoiceHomeHeaderFriends
import com.socialplay.gpark.ui.gamepay.PayDialogStyle
import com.socialplay.gpark.ui.gamepay.PayResult
import com.socialplay.gpark.ui.gamepay.PayScene
import com.socialplay.gpark.ui.home.HomeViewModel
import com.socialplay.gpark.ui.home.adapter.GameItemPlayedAdapter
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.outfit.BuyUgcDesignState
import com.socialplay.gpark.ui.outfit.BuyUgcDesignViewModel
import com.socialplay.gpark.ui.post.feed.tag.TagCommunityFeedFragmentArgs
import com.socialplay.gpark.ui.recommend.choice.ChoiceTitleMoreItem
import com.socialplay.gpark.ui.recommend.choice.IChoiceListener
import com.socialplay.gpark.ui.recommend.choice.choiceCardItem
import com.socialplay.gpark.ui.room.HomeRoomAnalytics
import com.socialplay.gpark.ui.room.RoomDetailDialog
import com.socialplay.gpark.ui.suggestion.GameSuggestionViewModel
import com.socialplay.gpark.ui.view.BlurEffectManager
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.PerformanceMonitor
import com.socialplay.gpark.util.UniJumpUtil
import com.socialplay.gpark.util.extension.attachV2
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.unsetOnClick
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

class RecommendFragment : BaseEditorFragmentMaverick<FragmentRecommendBinding>(R.layout.fragment_recommend) {
    companion object {
        fun newInstance(args: TagCommunityFeedFragmentArgs): RecommendFragment {
            return RecommendFragment().apply {
                arguments = args.asMavericksArgs()
            }
        }
    }

    private val args by args<TagCommunityFeedFragmentArgs>()

    private val accountInteractor: AccountInteractor by inject()
    private val mainViewModel: MainViewModel by sharedViewModel()
    private val viewModel: RecommendViewModel by fragmentViewModel()
    private val buyViewModel: BuyUgcDesignViewModel by fragmentViewModel()
    private val homeViewModel by viewModel<HomeViewModel>()
    private val suggestionViewModel by sharedViewModel<GameSuggestionViewModel>()

    private val playedAdapter by lazy { GameItemPlayedAdapter(::glide) }

    private val editorInteractor: EditorInteractor by inject()

    private val metaKV: MetaKV by inject()
    private var timer: CountDownTimer? = null
    private var startTime = 0

    private val recommendController by lazy { buildRecommendController() }
    private val homeHeaderFriends by lazy { ChoiceHomeHeaderFriends(this) }

    private var isFirstResume = true
    private var headContinueGameBinding: ViewChoiceContinueGameBinding? = null

    private var loadingDialog: LoadingDialogFragment? = null

    // 模糊效果管理器
    private val blurEffectManager = BlurEffectManager.getInstance()

    private val accountCallback = object : TokenChangedCallback {
        override fun invoke(old: String?, new: String?) {
            if (old.isNullOrEmpty() && !new.isNullOrEmpty()) {
                accountInteractor.removeTokenChangedCallback(this)
                checkRefreshRoomList()
            }
        }
    }

    private val choiceListener = object : IChoiceListener {
        override fun onItemShow(
            cardPos: Int, card: ChoiceCardInfo, itemPos: Int, item: IChoiceItem, isBanner: Boolean
        ) {
            when (item) {
                is ChoiceGameInfo -> <EMAIL>(
                    cardPos + 1, card, itemPos + 1, item, if (isBanner) {
                        CategoryId.HOME_RECOMMEND_CATEGORY_BANNER
                    } else {
                        CategoryId.HOME_RECOMMEND_CATEGORY
                    }
                )

                is ChatRoomInfo -> HomeRoomAnalytics.trackRoomShow(
                    item, CategoryId.HOME_RECOMMEND_CATEGORY
                )

                is House -> Analytics.track(
                    EventConstants.EVENT_DSHOME_ENTRY_SHOW, mapOf("show_categoryid" to "2")
                )

                is ChoiceHomeVideoItem -> {// Recommend video
                    RecommendVideoAnalytics.trackVideoItemShow(
                        ResIdBean.newInstance().setCategoryID(CategoryId.HOME_RECOMMEND_NEWEST_GAME).setParam1(itemPos + 1).setReqId(item.reqId), item.postDetail
                    )
                }
            }
        }

        override fun onMoreClick(item: ChoiceCardInfo?) {
            if (ChoiceCardType.isRoomCardType(item?.cardType)) {
                HomeRoomAnalytics.trackHomeRoomCardSeeAllCLick()
                jumpToMoreRoomPage()
            } else if (ChoiceCardType.isHomeRoomCardType(item?.cardType)) {
                jumpToHomeRoomPage()
            } else if (ChoiceCardType.isUgcCreate(item?.cardType)) {
                if (isAdded && !isDetached) {
                    MetaRouter.MobileEditor.ugcAll(
                        this@RecommendFragment, item?.cardId.toString(), item?.cardName ?: ""
                    )
                }
            } else if (item?.cardType == ChoiceCardType.TEMPLATE) {
                // 处理模板卡片的更多点击
                if (isAdded && !isDetached) {
                    MetaRouter.Template.templateListDetail(
                        this@RecommendFragment, item
                    )
                }
            } else if (item?.cardType == ChoiceCardType.ASSETS) {
                // 处理资源卡片的更多点击
                if (isAdded && !isDetached) {
                    MetaRouter.Assets.assetList(
                        this@RecommendFragment, item
                    )
                }
            } else if (ChoiceCardType.isHomeVideoCardType(item?.cardType)) {
                val resId = ResIdBean.newInstance().setCategoryID(CategoryId.HOME_RECOMMEND_NEWEST_GAME).setReqId(item?.videoFeeds?.reqId ?: "")

                RecommendVideoAnalytics.trackVideoMoreClick(resId)
                if (isAdded && !isDetached) {
                    MetaRouter.Video.goRecommendVideoList(
                        this@RecommendFragment, resId, item?.videoFeeds
                    )
                }
            }
        }

        override fun onOtherClick(clickType: Int, position: Int, card: ChoiceCardInfo, item: ChoiceGameInfo) {
            if (!isAdded || isDetached) {
                return
            }
            if (card.cardType == ChoiceCardType.TEMPLATE) {
                if (clickType == 1) {
                    // 模板点击创建
                    if (isAdded && !isDetached) {
                        viewModel.getEditorTemplateInfo(item.contentId ?: "")
                    }
                } else if (clickType == 2) {
                    // 头像点击，跳转个人页
                    MetaRouter.Profile.other(
                        this@RecommendFragment, item.uid ?: "", "home_template"
                    )
                }
            } else if (card.cardType == ChoiceCardType.ASSETS) {
                val selfUuid = accountInteractor.getUserInfoFromCache()?.uuid
                if (item.assetShowState(selfUuid) == 3) {
                    loadingDialog?.dismissAllowingStateLoss()
                    loadingDialog = MetaRouter.Dialog.loading(childFragmentManager)
                    // 是模组 & 不是自己的模组 & 未购买 & 并且开启定价
                    buyViewModel.buy(viewLifecycleOwner, item.commodityId, onStartPay = { payInfo ->
                        loadingDialog?.dismissAllowingStateLoss()
                        loadingDialog = null
                        payInfo.copy(
                            productCode = item.commodityId ?: "",
                            productName = item.displayName ?: "",
                            productCount = 1,
                            sceneCode = PayScene.MODULE.value,
                            source = "party",
                            metrialidid = item.contentId,
                            payDialogStyle = PayDialogStyle.BOTTOM,
                            productImageUrl = item.iconUrl ?: "",
                            productSoldCount = item.purchaseCount ?: 0L,
                            creatorName = item.nickname ?: "",
                            creatorAvatarUrl = item.avatar ?: "",
                        )
                    }, onPayResult = { payResult ->
                        loadingDialog?.dismissAllowingStateLoss()
                        loadingDialog = null
                        var resultStr = "2"
                        // 支付完成回调
                        if (payResult.isSuccess) {
                            // 支付成功,处理支付成功逻辑,更新Item展示状态
                            viewModel.updateAssetStatus(item.feedId)
                            resultStr = "0"
                        } else if (payResult.resultCode == PayResult.RESULT_CODE_FAILED) {
                            // 支付失败,处理支付失败逻辑
                            toast(payResult.failedReason ?: getString(R.string.common_failed))
                            resultStr = "1"
                        } else {
                            // 用户取消
                            resultStr = "2"
                        }

                        Analytics.track(
                            EventConstants.LIBRARY_METARIAL_GET_CLICK,
                            "metrialidid" to (item.contentId ?: ""),
                            "show_categoryid" to CategoryId.HOME_RECOMMEND_CATEGORY,
                            "authorid" to item.uid.orEmpty(),
                            "result" to resultStr,
                            "type" to if (item.isModule()) 1 else 0,
                            "is_price" to (if (item.isPriced == true) "yes" else "no"),
                            "price" to (item.price ?: 0L)
                        )
                    })
                } else if (item.assetShowState(selfUuid) == 2) {
                    // 已经购买, 但是不在个人库中, 或者是服装，直接获取
                    viewLifecycleOwner.lifecycle.currentState
                    buyViewModel.get(
                        viewLifecycleOwner, item.feedId ?: "", onGetResult = { getResult ->
                            var resultStr = "2"
                            if (getResult is Success) {
                                viewModel.updateAssetStatus(item.feedId)
                                toast(R.string.ugc_module_get_tips)
                                resultStr = "0"
                            } else if (getResult is Fail) {
                                toast(getResult.invoke())
                                resultStr = "1"
                            }

                            Analytics.track(
                                EventConstants.LIBRARY_METARIAL_GET_CLICK,
                                "metrialidid" to (item.contentId ?: ""),
                                "show_categoryid" to CategoryId.HOME_RECOMMEND_CATEGORY,
                                "authorid" to item.uid.orEmpty(),
                                "result" to resultStr,
                                "type" to if (item.isModule()) 1 else 0,
                                "is_price" to (if (item.isPriced == true) "yes" else "no"),
                                "price" to (item.price ?: 0L)
                            )
                        })
                } else {
                    // 其他情况，跳详情
                    if (isAdded && !isDetached) {
                        MetaRouter.UgcDesign.detail(
                            this@RecommendFragment, item.feedId ?: "", CategoryId.HOME_RECOMMEND_CATEGORY, tabId = 0
                        )
                    }
                    Analytics.track(
                        EventConstants.LIBRARY_METARIAL_GET_CLICK, "metrialidid" to (item.contentId ?: ""), "show_categoryid" to CategoryId.HOME_RECOMMEND_CATEGORY, "authorid" to item.uid.orEmpty(), "result" to "3", "type" to if (item.isModule()) 1 else 0, "is_price" to (if (item.isPriced == true) "yes" else "no"), "price" to (item.price ?: 0L)
                    )
                }
            } else if (card.cardType == ChoiceCardType.BIG_CARD) {
                if (clickType == 2) {
                    // 头像点击，跳转个人页
                    MetaRouter.Profile.other(
                        this@RecommendFragment, item.uid ?: "", "home_big_card"
                    )
                }
            }
        }

        override fun onItemClick(
            cardPos: Int, card: ChoiceCardInfo, itemPos: Int, item: IChoiceItem, isBanner: Boolean
        ) {
            when (item) {
                is ChoiceGameInfo -> <EMAIL>(
                    cardPos + 1, card, itemPos + 1, item, if (isBanner) {
                        CategoryId.HOME_RECOMMEND_CATEGORY_BANNER
                    } else {
                        CategoryId.HOME_RECOMMEND_CATEGORY
                    }
                )

                is ChatRoomInfo -> showRoomDetail(item)

                is House -> {
                    Analytics.track(
                        EventConstants.EVENT_DSHOME_ENTRY_CLICK, mapOf(
                            "show_categoryid" to "2", "userid" to (item.ownerUuid ?: ""), "homename" to (item.description ?: ""), "onlinenumber" to (item.number ?: ""), "roomid" to (item.roomId ?: "")
                        )
                    )
                    item.roomId?.let {
                        CottageRoomDetailDialog.show(
                            this@RecommendFragment, it, ResIdBean().setGameId(item.gameId).setCategoryID(CategoryId.HOME_RECOMMEND_CATEGORY), "2"
                        )
                    }
                }

                is ChoiceHomeVideoItem -> {
                    val resId = ResIdBean.newInstance().setCategoryID(CategoryId.HOME_RECOMMEND_NEWEST_GAME).setParam1(itemPos + 1).setReqId(item.reqId)

                    RecommendVideoAnalytics.trackVideoItemClick(resId, item.postDetail)
                    if (isAdded && !isDetached) {
                        MetaRouter.Video.goRecommendVideoFeed(
                            this@RecommendFragment, resId, item.postDetail.postId
                        )
                    }
                }
            }
        }

        override fun onCardShow(cardPos: Int, card: ChoiceCardInfo) {
            if (ChoiceCardType.isRoomCardType(card.cardType)) {
                HomeRoomAnalytics.trackHomeRoomCardShow(card.roomList?.size ?: 0)
            }
            card.recommend?.let { item ->
                Analytics.track(
                    EventConstants.C_FEED_ITEM_SHOW, "gameid" to item.code.toString(), "packagename" to item.packageName.toString(), "reqid" to item.reqid.toString(), "icon_type" to item.gcMode.toString(), "type" to "1", "show_categoryid" to CategoryId.HOME_RECOMMEND_NEWEST_GAME, "show_param1" to cardPos
                )
            }
        }

        override fun bindPlayed(playedCount: Int, binding: ViewChoiceContinueGameBinding) {
            if (binding.rvRecentlyPlayed.layoutManager == null) {
                binding.rvRecentlyPlayed.layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
            }
            playedAdapter.setOnItemClickListener { _, position ->
                playedAdapter.peek(position)?.let {
                    val house = editorInteractor.userMainHouse.value?.data
                    if (house?.gameId.equals(it.id)) {
                        Analytics.track(EventConstants.EVENT_ITEM_CLICK) {
                            put("gameid", it.id)
                            put("game_type", if (it.isUgcGame) "UGC" else "PGC")
                            putAll(ResIdUtils.getAnalyticsMap(getResid()))
                        }
                        // 小屋
                        editorInteractor.launchCottageGame(
                            this@RecommendFragment, accountInteractor.curUuid, "8", CategoryId.COTTAGE_ROOM_LIST, "dsHomePlayedGame"
                        )
                    } else {
                        if (it.isUgcGame) {
                            Analytics.track(EventConstants.EVENT_ITEM_CLICK) {
                                put("gameid", it.id)
                                put("game_type", if (it.isUgcGame) "UGC" else "PGC")
                                putAll(ResIdUtils.getAnalyticsMap(getResid()))
                            }
                            if (isAdded && !isDetached) {
                                MetaRouter.MobileEditor.ugcDetail(
                                    this@RecommendFragment, it.id, null, requirePlayedGameResIdBean(it.id).setParam1(position + 1).setTsType(ResIdBean.TS_TYPE_UCG).setIconType(ResIdBean.ICON_TYPE_UGC)
                                )
                            }
                        } else {
                            playGame(
                                it.id, it.packageName ?: "", requirePlayedGameResIdBean(it.id).setParam1(position + 1).setTsType(ResIdBean.TS_TYPE_NORMAL).setIconType(ResIdBean.ICON_TYPE_PGC)
                            )
                        }
                    }
                }
            }
            if (binding.rvRecentlyPlayed.adapter != playedAdapter) {
                binding.rvRecentlyPlayed.adapter = playedAdapter
            }
            binding.rvRecentlyPlayed.setHasFixedSize(true)

            binding.tvPlayedManage.setOnClickListener {
                if (isAdded && !isDetached) { // 添加双重状态校验
                    MetaRouter.Played.manager(this@RecommendFragment)
                } else {
                    Timber.w("RecommendFragment not attached when click manage button")
                }
            }
            headContinueGameBinding = binding
        }

        override fun unbindPlayed(playedCount: Int, binding: ViewChoiceContinueGameBinding) {
            playedAdapter.setOnItemClickListener(null)
            binding.tvPlayedManage.unsetOnClick()
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): FragmentRecommendBinding? {
        return FragmentRecommendBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.recyclerView.itemAnimator = null
        initTitle()
        initRv()
        refreshMyGames()
        initData()
        homeViewModel.init()
        homeViewModel.cardListLiveData.observe(viewLifecycleOwner) {
            val oldSize = viewModel.oldState.choiceList.invoke()?.first?.size ?: 0
            val newSize = it.originalDataList?.size
            viewModel.updateChoiceData(it)
            PerformanceMonitor.logDataChange("ChoiceCardList", oldSize, newSize, "更新")
        }
        suggestionViewModel.insertPartyPageCallback.observe(viewLifecycleOwner) {
            homeViewModel.insertTrending()
        }
        observeRefreshRoomList()
        homeHeaderFriends.initialize()

        // 初始化性能监控
//        initPerformanceMonitoring()
    }

    private fun initRv() {

        viewModel.setupRefreshLoading(
            RecommendViewModelState::state, binding.loadingView, binding.refreshLayout
        ) {
            if (NetUtil.isNetworkAvailable()) {
                playedAdapter.refresh()
                viewModel.refresh()
                viewModel.featRecommendOperations()
                homeViewModel.refreshData()
            } else {
                binding.refreshLayout.isRefreshing = false
                binding.loadingView.showError()
            }
        }

        // 初始化Epoxy可见性追踪器
        val visibilityTracker = EpoxyVisibilityTracker().apply {
            partialImpressionThresholdPercentage = 100
            // 减少可见性检查频率
            attachV2(viewLifecycleOwner, binding.recyclerView)
        }

        // 强制设置GridLayoutManager，确保2列布局
        val gridLayoutManager = GridLayoutManager(requireContext(), 2, GridLayoutManager.VERTICAL, false)
        binding.recyclerView.layoutManager = gridLayoutManager

        // 添加调试日志
        Timber.tag("Performance").d("GridLayoutManager forced set - SpanCount: ${gridLayoutManager.spanCount}, Orientation: ${gridLayoutManager.orientation}")

        // 设置预取数量
//        gridLayoutManager.initialPrefetchItemCount = 10
        // 等待 refreshLayout 布局加载完成后，再给设置一个固定高度，避免后续高度频繁变化？待定。担心平板设备旋转问题
        refreshRecyclerViewAndRefreshViewHeight()
//        binding.refreshLayout.apply {
//            if(layoutParams == null){
//                layoutParams = ViewGroup.LayoutParams(
//                    ViewGroup.LayoutParams.MATCH_PARENT,
//                    2000
//                )
//            }
//            layoutParams = layoutParams.apply {
//                height = 2000
//            }
//        }

        // 彻底优化RecyclerView配置
        binding.recyclerView.apply {
            // 禁用动画，减少布局计算
            itemAnimator = null

            // 设置固定大小，减少measure调用
            setHasFixedSize(true)

            // 优化滚动性能
            overScrollMode = View.OVER_SCROLL_NEVER

            // 减少嵌套滚动冲突
            isNestedScrollingEnabled = false

//            // 设置固定高度，避免高度震荡
//            layoutParams = layoutParams.apply {
//                height = 2000
//            }

            // 禁用自动测量
            (layoutManager as? GridLayoutManager)?.isAutoMeasureEnabled = false

            setItemViewCacheSize(30)
        }

        // 添加更多调试日志
        Timber.tag("Performance").d("RecyclerView configuration completed - HasFixedSize: ${binding.recyclerView.hasFixedSize()}, ItemAnimator: ${binding.recyclerView.itemAnimator}, LayoutParams: ${binding.recyclerView.layoutParams}")

//        // 设置滚动监听器，优化滚动性能
//        binding.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
//            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
//                super.onScrollStateChanged(recyclerView, newState)
//                when (newState) {
//                    RecyclerView.SCROLL_STATE_IDLE -> {
//                        // 滚动停止时恢复图片加载
//                        Glide.with(recyclerView.context).resumeRequests()
//                    }
//
//                    RecyclerView.SCROLL_STATE_DRAGGING -> {
//                        // 滚动时暂停图片加载
//                        Glide.with(recyclerView.context).pauseRequests()
//                    }
//
//                    RecyclerView.SCROLL_STATE_SETTLING -> {
//                        // 惯性滚动时暂停图片加载
//                        Glide.with(recyclerView.context).pauseRequests()
//                    }
//                }
//            }
//        })

        // 注册模糊效果管理器
        blurEffectManager.registerRecyclerView(binding.recyclerView)

        binding.recyclerView.setController(recommendController)
//        binding.recyclerView.setDelayMsWhenRemovingAdapterOnDetach(1)

//        // 使用详细的性能监控
//        PerformanceMonitor.monitorRecyclerViewDetailed(binding.recyclerView)
//        PerformanceMonitor.monitorEpoxyControllerDetailed(recommendController)
//        PerformanceMonitor.monitorEpoxyVisibilityTracker(visibilityTracker)
//
//        // 记录初始化日志
//        Timber.tag("Performance").d("RecommendFragment RecyclerView initialization completed - Item count: ${binding.recyclerView.adapter?.itemCount ?: 0}")
//        PerformanceMonitor.logMemoryUsage()
//        Timber.tag("Performance").d("RecommendFragment performance monitoring initialized")
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
    }

    private fun refreshRecyclerViewAndRefreshViewHeight() {
        if (!isPad) {
            // 处理某些机型频繁变化列表高度问题（比如一加LE2100），pad理当不会，所以跳过处理，不然要再单独处理pad的横竖屏切换问题
            try {
                binding.refreshLayout?.post {
                    binding.refreshLayout.apply {
                        val mHeight = measuredHeight
                        layoutParams = layoutParams.apply {
                            width = ViewGroup.LayoutParams.MATCH_PARENT
                            height = mHeight
                        }
                    }
                }
                binding.recyclerView?.post {
                    binding.recyclerView.apply {
                        val mHeight = measuredHeight
                        layoutParams = layoutParams.apply {
                            width = ViewGroup.LayoutParams.MATCH_PARENT
                            height = mHeight
                        }
                    }
                }
            } catch (e: Exception) {

            }
        }
    }

    private fun checkRefreshRoomList() {
        if (accountInteractor.checkAccountInit() && MWBiz.isAvailable()) {
            homeViewModel.checkRefreshRoomList()
        }
    }

    /**
     * 初始化性能监控
     */
    private fun initPerformanceMonitoring() {
//        // 监控RecyclerView
//        PerformanceMonitor.monitorRecyclerView(binding.recyclerView)

        // 监控EpoxyController（包括滑动时的构建）
        PerformanceMonitor.monitorEpoxyControllerDuringScroll(recommendController)

//        // 监控EpoxyVisibilityTracker
//        val visibilityTracker = EpoxyVisibilityTracker()
//        PerformanceMonitor.monitorEpoxyVisibilityTracker(visibilityTracker)
//
//        // 监控内存使用
//        PerformanceMonitor.monitorMemoryUsage()
//
//        Timber.tag("Performance").d("RecommendFragment performance monitoring initialized")
    }

    private fun observeRefreshRoomList() {
        accountInteractor.addTokenChangedCallback(accountCallback)
        MWLifeCallback.available.observe(viewLifecycleOwner) {
            checkRefreshRoomList()
        }
    }

    private var lastDataHash = 0
    private fun buildRecommendController() = simpleController(
        viewModel,
        RecommendViewModelState::refresh,
        RecommendViewModelState::loadMore,
        RecommendViewModelState::playedCount,
        RecommendViewModelState::operationList,
        RecommendViewModelState::choiceList,
        deliveryMode = uniqueOnly()
    ) { refresh, loadMore, playedCount, operationList, choiceList ->
        val startTime = System.currentTimeMillis()

        // 记录构建开始
        Timber.tag("Performance").d("EpoxyController build started - Refresh: ${refresh.javaClass.simpleName}, LoadMore: ${loadMore.javaClass.simpleName}, PlayedCount: $playedCount")

        // 只在数据真正变化时构建
        val list = refresh.invoke() ?: emptyList()
        val choiceCards = choiceList()?.first ?: emptyList()

//        // 添加数据变化检测
//        val currentDataHash = (list.size + choiceCards.size).hashCode()
//        if (currentDataHash == lastDataHash && refresh !is Success) {
//            Timber.tag("Performance").d("LayoutDebug Data unchanged, skip rebuild")
//            return@simpleController
//        }
//        Timber.tag("Performance").d("EpoxyController build started list.size: ${list.size}, choiceCards.size: ${choiceCards.size}")
//        lastDataHash = currentDataHash

        // 暂时注释掉数据变化检测，避免影响正常功能
        // val currentDataHash = (list.size + choiceCards.size).hashCode()
        // if (currentDataHash == lastDataHash) {
        //     Timber.tag("Performance").d("EpoxyController build skipped - No data changes detected")
        //     return@simpleController
        // }
        // lastDataHash = currentDataHash

        Timber.tag("Performance").d("Recommend list size: ${list.size}, Choice cards: ${choiceCards.size}")
        val addHeaderStartTimestamp = System.currentTimeMillis()
        // 添加header
        addHeader(playedCount, operationList)
        val addHeaderTime = System.currentTimeMillis() - addHeaderStartTimestamp

        val addChoiceCardsStartTimestamp = System.currentTimeMillis()
        // 添加choice cards
        choiceCards.forEachIndexed { cardPosition, card ->
            val itemStartTime = System.currentTimeMillis()
            Timber.tag("Performance").d("Adding choice card at position: $cardPosition")
            choiceCardItem(viewLifecycleOwner, card, cardPosition, 2, choiceListener, accountInteractor.getUserInfoFromCache()?.uuid)
            Timber.tag("Performance").d("Choice card added at position: $cardPosition ${System.currentTimeMillis() - itemStartTime}ms")
        }
        val addChoiceCardsTime = System.currentTimeMillis() - addChoiceCardsStartTimestamp

        val addRecommendStartTimestamp = System.currentTimeMillis()
        // 添加recommend items
        addRecommend(list)
        val addRecommendTime = System.currentTimeMillis() - addRecommendStartTimestamp

        val addFooterStartTimestamp = System.currentTimeMillis()
        // 添加footer
        addFooter(refresh, list, loadMore)
        val addFooterTime = System.currentTimeMillis() - addFooterStartTimestamp

        val buildTime = System.currentTimeMillis() - startTime
        Timber.tag("Performance").d("EpoxyController build completed - Time: ${buildTime}ms, Data count: ${list.size}, Choice cards: ${choiceCards.size}, Add header time: ${addHeaderTime}ms, Add choice cards time: ${addChoiceCardsTime}ms, Add recommend time: ${addRecommendTime}ms, Add footer time: ${addFooterTime}ms")

        // 如果构建时间过长，记录警告
        if (buildTime > 100) {
            Timber.tag("Performance").w("Slow EpoxyController build detected - ${buildTime}ms, Add header time: ${addHeaderTime}ms, Add choice cards time: ${addChoiceCardsTime}ms, Add recommend time: ${addRecommendTime}ms, Add footer time: ${addFooterTime}ms")
        }
    }

    private fun MetaEpoxyController.addRecommend(list: List<HomeCustomRecommend.RecommendList>) {
        val startTime = System.currentTimeMillis()
        Timber.tag("Performance").d("start add recommend, Recommend list size: ${list.size}")
        add(ChoiceTitleMoreItem(null, 2, null, needMore = false, title = getString(R.string.recommend_for_you)).id("addRecommend-header").spanSizeOverride { _, _, _ -> 2 })
        list.forEachIndexed { index, it ->
            val itemStartTime = System.currentTimeMillis()
//            Timber.tag("Performance").d("Adding game item - Index: $index, GameId: ${it.gameId}")
            addGame(it, index)
            Timber.tag("Performance").d("Add game item time: ${System.currentTimeMillis() - itemStartTime}ms")
        }
        Timber.tag("Performance").d("end add recommend, Time: ${System.currentTimeMillis() - startTime}ms")
    }

    private fun MetaEpoxyController.addFooter(
        refresh: Async<List<HomeCustomRecommend.RecommendList>>, list: List<HomeCustomRecommend.RecommendList>, loadMore: Async<LoadMoreState>
    ) {
        if (list.isNotEmpty()) {
            loadMoreFooter(loadMore, spanSize = 2) {
                // 移除对isRefreshing的检查，因为refreshLayout现在是FrameLayout
                viewModel.loadMore()
            }
        } else if (refresh is Fail && loadMore.shouldLoad) {
            loadMoreFooter(refresh.mapRetained(loadMore), spanSize = 2) {
                viewModel.refresh()
            }
        } else if (refresh is Loading) {
            loadMoreFooter(refresh.mapRetained(loadMore), spanSize = 2) {

            }
        }
    }

    private fun MetaEpoxyController.addGame(
        it: HomeCustomRecommend.RecommendList, index: Int
    ) {
        Timber.tag("Performance").d("Adding game item - Index: $index, GameId: ${it.gameId}")
        addGameItem(index, it, {
            Analytics.track(
                EventConstants.C_FEED_ITEM_SHOW, "gameid" to it.gameId, "gamename" to it.gameName.toString(), "packagename" to it.packageName.toString(), "reqid" to it.reqid.toString(), "type" to "${it.gameType}", "icon_type" to if (it.isUGCGame()) "UGC" else "PGC", "show_categoryid" to CategoryId.HOME_RECOMMEND, "show_param1" to index + 1
            )
        }, {
            Analytics.track(
                EventConstants.C_FEED_ITEM_CLICK, "gameid" to it.gameId, "gamename" to it.gameName.toString(), "packagename" to it.packageName.toString(), "reqid" to it.reqid.toString(), "type" to "${it.gameType}", "icon_type" to if (it.isUGCGame()) "UGC" else "PGC", "show_categoryid" to CategoryId.HOME_RECOMMEND, "show_param1" to index + 1
            )
            goGameDetail(it)
        }, {
            // 头像点击，跳转个人页
            MetaRouter.Profile.other(
                this@RecommendFragment, it.userUuid ?: "", "home_template"
            )
        }, ::glide)
    }

    private fun MetaEpoxyController.addHeader(playedCount: Int, operationList: Async<List<HomeRecommendOperation>>) {
        // 写死，没有首页跟房，需求来自 -> 宋津瑶
        if (PandoraToggle.openHomeFlowRoom) {
            addFriendHeader(homeHeaderFriends)
        }

        addContinueHeader(playedCount, choiceListener)
    }

    private fun onRecommendOperationClick(operation: HomeRecommendOperation) {
        Analytics.track(EventConstants.HOME_BANNER_CLICK) {
            put("link", operation.param1 ?: "")
            put("source", "1")
            put(EventParamConstants.KEY_EVENT_ID, operation.id)
        }
        val jumpConfig = operation.toUniJumpConfig()
        val handled = UniJumpUtil.jump(
            this, jumpConfig, LinkData.SOURCE_CHOICE_OPERATION, CategoryId.HOME_RECOMMEND_CATEGORY_BANNER, null
        )
        if (!handled) {
            toast(R.string.not_currently_support)
        }
    }

    private fun goGameDetail(game: HomeCustomRecommend.RecommendList) {
        viewModel.tempGameItem = game
        if (game.isUGCGame()) {
            if (isAdded && !isDetached) {
                MetaRouter.MobileEditor.ugcDetail(
                    this@RecommendFragment, game.gameId, null, getResid(game)
                )
            }
        } else {
            playGame(game.gameId, game.packageName ?: "", getResid(game))
        }
    }

    private fun getResid(item: HomeCustomRecommend.RecommendList? = null) = ResIdBean().setCategoryID(CategoryId.HOME_RECOMMEND).setReqId(item?.reqid).setGameId(item?.gameId).setIconType(if (item?.isUGCGame() == true) "UGC" else "PGC").setTsType(if (item?.isUGCGame() == true) ResIdBean.TS_TYPE_UCG else ResIdBean.TS_TYPE_NORMAL)

    // 请求最近玩过列表数据
    private fun refreshMyGames() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.playedGames.collectLatest {
                playedAdapter.submitData(it)
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            playedAdapter.loadStateFlow.collect { loadStates ->
                viewModel.updatePlayedCount(playedAdapter.itemCount)
                // 当数据刷新完成时
                if (loadStates.refresh is LoadState.NotLoading && playedAdapter.itemCount > 0 && headContinueGameBinding?.rvRecentlyPlayed?.layoutManager != null) {
                    // 将列表滚动到最前面（如果首次还没绑定ViewM，不执行的话，不影响最终效果）
                    headContinueGameBinding?.rvRecentlyPlayed?.scrollToPosition(0)
                }
            }
        }
    }

    private fun initTitle() {
    }

    private var isLoadFinishTrack = false
    private var isLoadSuccess = false

    private fun initData() {
        viewModel.onAsync(RecommendViewModelState::template, deliveryMode = uniqueOnly(), onSuccess = { it: EditorTemplate ->
            loadingDialog?.dismissAllowingStateLoss()
            loadingDialog = null
            val resid = ResIdBean().setCategoryID(CategoryId.HOME_RECOMMEND_CATEGORY)
            editorGameLaunchHelper?.startTemplateGame(
                this@RecommendFragment, it, resid
            )
        }, onLoading = {
            loadingDialog?.dismissAllowingStateLoss()
            loadingDialog = MetaRouter.Dialog.loading(childFragmentManager)
        }, onFail = { it, _ ->
            loadingDialog?.dismissAllowingStateLoss()
            loadingDialog = null
            toast(it?.message)
        })
        buyViewModel.registerAsyncErrorToast(BuyUgcDesignState::getResult)

        viewModel.onAsync(
            RecommendViewModelState::state,
            onSuccess = {
                isLoadSuccess = true
                if (!isLoadFinishTrack && isResumed) {
                    isLoadFinishTrack = true
                    Analytics.track(EventConstants.EVENT_HOMEPAGE_MENU_SHOW) {
                        put("menu_name", args.blockName)
                    }
                }
            }
        )

//        setFragmentResultListenerByHostFragment(
//            UgcDesignDetailFragment.TAG,
//            viewLifecycleOwner
//        ) { _, bundle ->
//            val result = UgcDesignDetailFragment.getResult(bundle)
//            viewModel.updateAssetStatus(result?.feedId, result)
//        }
    }

    private fun playGame(id: String, packageName: String, resIdBean: ResIdBean) {
        if (isAdded && !isDetached) {
            MetaRouter.GameDetail.navigate(this@RecommendFragment, id, resIdBean, packageName, type = "ts")
        }
    }

    private fun requirePlayedGameResIdBean(gameId: String): ResIdBean {
        val cacheBean = metaKV.analytic.getLaunchResIdBeanWithId(gameId)
        return ResIdBean().setCategoryID(CategoryId.RECOMMEND_MY_GAMES).apply {
            cacheBean?.also {
                setTsType(it.getTsType())
            }
        }
    }

    override fun getPageName(): String {
        return PageNameConstants.FRAGMENT_MAPS_TAB_MAPS
    }

    private fun jumpToWeb(fragment: Fragment, title: String? = null, url: String) {
        if (url.startsWith("http://", ignoreCase = true) or url.startsWith("https", true)) {
            if (isAdded && !isDetached) {
                MetaRouter.Web.navigate(fragment, title, url)
            }
        }
    }

    private fun jump(subInfo: ChoiceGameInfo, resIdBean: ResIdBean) {
        when (subInfo.type) {
            ChoiceContentType.GAME -> {
                Analytics.track(EventConstants.EVENT_ITEM_CLICK) {
                    put("gameid", subInfo.code.toString())
                    put("game_type", subInfo.gcMode.toString())
                    putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                }
                viewModel.tempItem = subInfo
                if (subInfo.gcMode == "UGC") {
                    if (PandoraToggle.enableUgcDetail) {
                        if (isAdded && !isDetached) {
                            MetaRouter.MobileEditor.ugcDetail(this, subInfo.code.orEmpty(), null, resIdBean)
                        }
                    } else {
                        val params = EditorUGCLaunchParams(subInfo.code ?: "", subInfo.packageName, subInfo.displayName ?: "", subInfo.iconUrl ?: "", "")
                        editorGameLaunchHelper?.startUgcGame(this, params, resIdBean.setTsType(ResIdBean.TS_TYPE_UCG))
                    }
                } else {
                    playGame(subInfo.code ?: "", subInfo.packageName ?: "", resIdBean.setTsType(ResIdBean.TS_TYPE_NORMAL))
                }
            }

            ChoiceContentType.LINK -> {
                // 内部、外部的web链接，外部scheme
                subInfo.router?.let { router ->
                    Analytics.track(EventConstants.HOME_BANNER_CLICK) {
                        put("link", router)
                        put("source", "2")
                    }
                    if (subInfo.jumpOperation()) {
                    } else if (subInfo.jumpOutside()) {
                        // 外部scheme/http url
                        kotlin.runCatching {
                            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(router))
                            startActivity(intent)
                        }.getOrElse {
                            jumpToWeb(this, subInfo.title, router)
                        }
                    } else {
                        // 内部http url
                        jumpToWeb(this, subInfo.title, router)
                    }
                }
            }

            ChoiceContentType.OPERATION -> {
                // 内部scheme
                Analytics.track(EventConstants.HOME_BANNER_CLICK) {
                    put("link", subInfo.operatingPosition?.param1.orEmpty())
                    put("source", "2")
                    put(EventParamConstants.KEY_EVENT_ID, subInfo.operatingPosition?.id.orEmpty())
                }
                subInfo.operatingPosition?.let {
                    val handled = UniJumpUtil.jump(
                        this, it, LinkData.SOURCE_CHOICE_OPERATION, CategoryId.HOME_RECOMMEND_CATEGORY_BANNER, mainViewModel, null
                    )
                    if (!handled) {
                        toast(R.string.not_currently_support)
                    }
                } ?: run {
                    toast(R.string.not_currently_support)
                }
            }

            ChoiceContentType.TEMPLATE -> {
                // 模板
                Analytics.track(EventConstants.EVENT_ITEM_CLICK) {
                    put("gameid", subInfo.contentId.toString())
                    put("game_type", if (subInfo.isTemplate()) "template" else "ugc")
                    putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                }
                viewModel.getEditorTemplateInfo(subInfo.contentId ?: "")
                viewModel.tempItem = subInfo
            }

            ChoiceContentType.MODULE, ChoiceContentType.CLOTH -> {
                // 模组
                Analytics.track(EventConstants.LIBRARY_ITEM_CLICK) {
                    put("metrialidid", (subInfo.contentId ?: ""))
                    put("type", if (subInfo.isModule()) 1 else 0)
                    put("authorid", subInfo.uid.orEmpty())
                    put("tab_id", 0)
                    put("is_recreate", "no")
                    put("is_price", (if (subInfo.isPriced == true) "yes" else "no"))
                    putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                }
                viewModel.tempItem = subInfo
                MetaRouter.UgcDesign.detail(
                    this@RecommendFragment, subInfo.feedId ?: "", CategoryId.HOME_RECOMMEND_CATEGORY, tabId = 0, needResult = true
                )
            }

            else -> {

            }
        }
    }

    private fun jumpToHomeRoomPage() {
        if (isAdded && !isDetached) {
            MetaRouter.HomeRoom.goAllHomeRoom(this)
        }
    }

    private fun jumpToMoreRoomPage() {
        if (isAdded && !isDetached) {
            MetaRouter.HomeRoom.goAllRoom(this)
        }
    }

    /**
     * 点击展示房间详情页
     */
    private fun showRoomDetail(roomInfo: ChatRoomInfo) {
        HomeRoomAnalytics.trackRoomClick(roomInfo, CategoryId.HOME_RECOMMEND_CATEGORY)
        RoomDetailDialog.show(
            this, roomInfo, ResIdBean().setGameId(roomInfo.platformGameId).setCategoryID(CategoryId.HOME_RECOMMEND_CATEGORY)
        )
    }

    private fun refreshRoom() {
        homeViewModel.refreshRoomList()
    }

    private fun onItemShow(
        cardPos: Int, card: ChoiceCardInfo?, itemPos: Int, item: ChoiceGameInfo, categoryId: Int = CategoryId.HOME_RECOMMEND_CATEGORY
    ) {
        Timber.tag("RecommendFragment").d("itemShow cardPos:$cardPos card:[${card?.cardId}, ${card?.cardName}, ${card?.gameListNoNull?.size}] itemPos:$itemPos item:[${item.code}, ${item.displayName}, ${item.packageName}]")
        when (item.type) {
            ChoiceContentType.LINK -> {
                Analytics.track(EventConstants.HOME_BANNER_SHOW, "link" to item.router.toString(), "source" to "2")
            }

            ChoiceContentType.OPERATION -> {
                Analytics.track(
                    EventConstants.HOME_BANNER_SHOW, "link" to item.operatingPosition?.param1.orEmpty(), "source" to "2", EventParamConstants.KEY_EVENT_ID to item.operatingPosition?.id.orEmpty()
                )
            }

            ChoiceContentType.GAME -> {
                lifecycleScope.launch(Dispatchers.Default) {
                    Analytics.track(EventConstants.EVENT_ITEM_SHOW) {
                        put("gameid", item.code ?: "")
                        put("packagename", item.packageName ?: "")
                        put("game_type", item.typeToString())
                        putAll(
                            ResIdUtils.getAnalyticsMap(
                                ResIdBean().setGameId(item.code ?: "").setCategoryID(categoryId).setParam1(cardPos).setParam2(itemPos).setParamExtra(card?.cardName)
                            )
                        )
                    }
                }
            }

            ChoiceContentType.TEMPLATE -> {
                lifecycleScope.launch(Dispatchers.Default) {
                    Analytics.track(EventConstants.EVENT_ITEM_SHOW) {
                        put("gameid", item.code ?: "")
                        put("packagename", item.packageName ?: "")
                        put("game_type", if (item.isTemplate()) "template" else "ugc")
                        putAll(
                            ResIdUtils.getAnalyticsMap(
                                ResIdBean().setGameId(item.code ?: "").setCategoryID(categoryId).setParam1(cardPos).setParam2(itemPos).setParamExtra(card?.cardName)
                            )
                        )
                    }
                }
            }

            ChoiceContentType.MODULE, ChoiceContentType.CLOTH -> {
                lifecycleScope.launch(Dispatchers.Default) {
                    Analytics.track(EventConstants.LIBRARY_ITEM_SHOW) {
                        put("metrialidid", (item.contentId ?: ""))
                        put("type", if (item.isModule()) 1 else 0)
                        put("authorid", item.uid.orEmpty())
                        put("tab_id", 0)
                        put("is_recreate", "no")
                        put("is_price", (if (item.isPriced == true) "yes" else "no"))
                        putAll(
                            ResIdUtils.getAnalyticsMap(
                                ResIdBean().setGameId(item.code ?: "").setCategoryID(categoryId).setParam1(cardPos).setParam2(itemPos).setParamExtra(card?.cardName)
                            )
                        )
                    }
                }
            }
        }
    }

    private fun onItemClick(
        cardPos: Int,
        card: ChoiceCardInfo?,
        itemPos: Int,
        item: ChoiceGameInfo,
        categoryId: Int = CategoryId.HOME_RECOMMEND_CATEGORY,
    ) {
        Timber.tag("RecommendFragment").e("itemClick cardPos:$cardPos card:[${card?.cardId}, ${card?.cardName}, ${card?.gameListNoNull?.size}] itemPos:$itemPos item:[${item.code}, ${item.displayName}, ${item.packageName}]")
        val resIdBean = ResIdBean().setGameId(item.code ?: "").setCategoryID(categoryId).setParam1(cardPos).setParam2(itemPos).setParamExtra(card?.cardName).setReqId(item.reqid.toString()).setIconType(item.gcMode)
        jump(item, resIdBean)
    }

    override fun invalidate() {}

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.apiMonitor(
            this, RecommendViewModelState::refresh
        )
        recommendController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        recommendController.onSaveInstanceState(outState)
    }

    override fun onResume() {
        super.onResume()
        viewModel.tempItem = null
        if (!isFirstResume) {
            refreshRoom()
        } else {
            isFirstResume = false
        }

        // 如果之前请求过了，并且有列表是空的，则再请求一次
        val s = viewModel.oldState
        if (s.choiceList.invoke()?.first == null || s.refresh.invoke().isNullOrEmpty()) {
            viewModel.refresh()
        }

        if (isLoadSuccess) {
            Analytics.track(EventConstants.EVENT_HOMEPAGE_MENU_SHOW) {
                put("menu_name", args.blockName)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        timer?.cancel()
        timer = null
        homeHeaderFriends?.destroy()
        loadingDialog = null

        // 清理模糊效果管理器资源
        blurEffectManager.clear()
    }

    /**
     * 获取性能监控报告
     */
    fun getPerformanceReport(): String {
        return PerformanceMonitor.getDetailedPerformanceReport()
    }

    /**
     * 重置性能监控
     */
    fun resetPerformanceMonitoring() {
        PerformanceMonitor.resetCounters()
    }
}