package com.socialplay.gpark.ui.videofeed

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.annotation.StringRes
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.viewpager2.widget.ViewPager2
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.airbnb.mvrx.withState
import com.bumptech.glide.Glide
import com.google.android.exoplayer2.DefaultLoadControl
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.PlaybackException
import com.google.android.exoplayer2.Player
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.ApiInvokeException
import com.socialplay.gpark.data.interactor.PublishPostInteractor
import com.socialplay.gpark.data.model.choice.CommunityTabTargetType
import com.socialplay.gpark.data.model.post.CommunityBlockType
import com.socialplay.gpark.data.model.post.PostPublishStatus
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.data.model.videofeed.GameStatus
import com.socialplay.gpark.data.model.videofeed.PlayerState
import com.socialplay.gpark.data.model.videofeed.VideoFeedArgs
import com.socialplay.gpark.data.model.videofeed.VideoPlayStatus
import com.socialplay.gpark.data.model.videofeed.WrappedVideoFeedItem
import com.socialplay.gpark.databinding.FragmentVideoFeedBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.exoplayer.VideoPlayerCacheInteractor
import com.socialplay.gpark.function.mw.launch.ui.TSLaunchFailedWrapper
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.IGlobalShareCallback
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.statusbar.StatusBarStateProvider
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.main.MainBottomNavigationItem
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.post.tab.CommunityFeedTabModelState
import com.socialplay.gpark.ui.post.tab.CommunityFeedTabViewModel
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.videofeed.comment.VideoFeedCommentDialogFragment
import com.socialplay.gpark.ui.view.LoadingView
import com.socialplay.gpark.ui.view.refresh.VideoFeedSwipeRefreshLayout
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.extension.collectIn
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.keepScreenOn
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.smoothScrollTo
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import timber.log.Timber
import java.io.IOException
import kotlin.math.min
import kotlin.reflect.KProperty1

/**
 * author : wei.zhu
 * e-mail : <EMAIL>
 * time   : 2023/11/13
 * desc   :
 */
class VideoFeedFragment :
    BaseFragment<FragmentVideoFeedBinding>(R.layout.fragment_video_feed),
    StatusBarStateProvider, VideoFeedCoordinateHelper, IGlobalShareCallback {

    override fun isStatusBarDarkText() = false

    override fun getPageName(): String = PageNameConstants.FRAGMENT_VIDEO_FEED

    override fun isEnableTrackPageExposure() = false

    private val viewModel by fragmentViewModel(VideoFeedViewModel::class)
    private val mainViewModel by sharedViewModel<MainViewModel>()
    private val feedViewModel: CommunityFeedTabViewModel by parentFragmentViewModel()

    private val videoCacheInteractor by inject<VideoPlayerCacheInteractor>()
    private val publishPostInteractor by inject<PublishPostInteractor>()

    private val adapter: VideoFeedAdapter by lazy {
        VideoFeedAdapter(Glide.with(this), viewModel.oldState.args.style)
    }

    private lateinit var player: ExoPlayer
    private lateinit var videoFirstFrameRenderedDetector: VideoFirstFrameRenderedDetector

    private val playerListener = object : Player.Listener {

        override fun onPlaybackStateChanged(playbackState: Int) {
            super.onPlaybackStateChanged(playbackState)
            val itemState = player.currentMediaItem.getVideoFeedItemState() ?: return
            Timber.d("onPlaybackStateChanged state:${playbackState} vid:${itemState.videoFeedItem.videoId} url${itemState.videoFeedItem.videoUrl}")

            when (playbackState) {
                Player.STATE_IDLE -> {}
                Player.STATE_BUFFERING -> {
                    viewModel.setVideoDataReadyStatus(itemState.videoFeedItem.videoId, false)
                    viewModel.trackBufferingStart(itemState.videoFeedItem.videoId)
                }

                Player.STATE_READY -> {
                    Timber.d("onVideoReady vid:${itemState.videoFeedItem.videoId} url:${itemState.videoFeedItem.videoUrl} cover:${itemState.videoFeedItem.videoCover}")
                    viewModel.setVideoDataReadyStatus(itemState.videoFeedItem.videoId, true)
                    viewModel.trackBufferingEnd(itemState.videoFeedItem.videoId)
                }

                Player.STATE_ENDED -> {}
            }
        }

        override fun onPlayWhenReadyChanged(playWhenReady: Boolean, reason: Int) {
            super.onPlayWhenReadyChanged(playWhenReady, reason)
            Timber.d("onPlaybackStateChanged $playWhenReady reason:${reason}")
            val itemState = player.currentMediaItem.getVideoFeedItemState() ?: return
            if (playWhenReady) {
                viewModel.setPlayStatus(itemState.videoFeedItem.videoId, VideoPlayStatus.Playing, false)
            }
        }

        override fun onPlayerError(error: PlaybackException) {
            super.onPlayerError(error)
            Timber.d(error, "onPlayerError")

            if (error.cause is IOException) {
                toast(getString(R.string.api_error_net))
            }

            Analytics.track(
                EventConstants.EVENT_ON_SLIDE_OR_PLAY_VIDEO_FAILED, mapOf(
                    "reason" to 3,
                    "message" to error.errorCodeName
                )
            )

            // 播放失败的话，将Item设置为暂停状态，方便用户手动重试。
            val itemState = player.currentMediaItem.getVideoFeedItemState() ?: return
            viewModel.setPlayStatus(itemState.videoFeedItem.videoId, VideoPlayStatus.Paused, false)
        }
    }

    private val adapterPageChangeCallback = object : OnPageChangeCallback() {

        override fun onPageSelected(position: Int) {

            mayUpdateActiveItem()

            Timber.d("ScrollDbg onPageSelected position:${position}")

            val remaining = adapter.itemCount - position + 1
            if (remaining < 3) {
                viewModel.loadMoreVideoFeed()
            }

            val item = adapter.getItemOrNull(position) ?: return
            item.videoFeedItem.game?.let {
                Analytics.track(
                    EventConstants.EVENT_GAME_FEED_VIDEO_SHOW,
                    mapOf(
                        "postid" to item.videoFeedItem.videoId,
                        "gameid" to it.id,
                        "type" to "pgc",
                        "video_pkg" to it.packageName,
                        "video_gameid" to it.id,
                        "video_id" to item.videoFeedItem.videoId,
                        "reqid" to (item.reqId ?: ""),
                        "show_categoryid" to viewModel.oldState.args.resId.getCategoryID(),
                    ),
                )
            }
        }

        override fun onPageScrolled(
            position: Int,
            positionOffset: Float,
            positionOffsetPixels: Int
        ) {
            val currentItem = binding.vpVideoList.currentItem
            Timber.d("ScrollDbg onPageScrolled current:$currentItem dstPos:${position} positionOffset:${positionOffset} positionOffsetPixels:${positionOffsetPixels}")
            if (position < currentItem) {
                //Scroll to prev item
                binding.playerView.translationY = (binding.playerView.height * (1 - positionOffset))
            } else {
                //Scroll to next item
                binding.playerView.translationY = -(binding.playerView.height * positionOffset)
            }
        }

        override fun onPageScrollStateChanged(state: Int) {
            super.onPageScrollStateChanged(state)
            Timber.d("ScrollDbg onPageScrollStateChanged position:${binding.vpVideoList.currentItem} state:${state}")

            if (state == ViewPager2.SCROLL_STATE_IDLE) {
                binding.playerView.translationY = 0.toFloat()
                mayUpdateActiveItem()
            }
        }
    }

    override val currentVideoFeedItem: WrappedVideoFeedItem?
        get() {
            return adapter.getItemOrNull(currentPosition)
        }

    override val currentPosition: Int
        get() {
            return if (isBindingAvailable()) {
                binding.vpVideoList.currentItem
            } else {
                -1
            }
        }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentVideoFeedBinding? {
        return FragmentVideoFeedBinding.inflate(inflater, container, false)
    }

    private fun initPlayer() {
        player = ExoPlayer.Builder(requireContext().applicationContext)
            .setLoadControl(
                DefaultLoadControl.Builder()
                    .setBufferDurationsMs(
                        5_000,
                        5_000,
                        DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_MS,
                        DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS,
                    )
                    .build()
            )
            .setMediaSourceFactory(videoCacheInteractor.mediaSourceFactory)
            .build()

        videoFirstFrameRenderedDetector = VideoFirstFrameRenderedDetector(player, this::onVideoFirstFrameRendered)

        player.playWhenReady = false
        player.repeatMode = Player.REPEAT_MODE_ONE

        binding.playerView.player = player

        player.addListener(playerListener)
    }

    private fun releasePlayer() {
        player.playWhenReady = false
        player.stop()
        player.release()
        player.removeListener(playerListener)
    }

    private fun collectPlayPosition() {
        viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            while (true) {
                val state = viewModel.awaitState()
                val videoInfo = state.watchingVideoInfo

                if (videoInfo != null) {
                    val currentPlayingItem = player.currentMediaItem?.getVideoFeedItemState()
                    if (currentPlayingItem != null && currentPlayingItem.videoFeedItem.videoId == videoInfo.wrapped.videoFeedItem.videoId) {

                        val currentPosition = player.currentPosition
                        val videoTotalDuration = player.duration

                        viewModel.setWatchingVideoInfo(
                            currentPosition,
                            videoTotalDuration
                        )

                        binding.fplProgressBar.apply {
                            if (!isDragging()) {
                                setMax(videoTotalDuration)
                                setProgress(currentPosition)
                            }
                        }
                    }
                }
                delay(33)
            }
        }
    }

    override fun invalidate() {}

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initPlayer()

        configUIStyle()

        SmoothPagerSnapHelper().attachToViewPager(binding.vpVideoList)

        binding.vpVideoList.adapter = adapter

        binding.vpVideoList.registerOnPageChangeCallback(adapterPageChangeCallback)

        // 放在viewModel之前，初始化赋值更早
        feedViewModel.onEach(CommunityFeedTabModelState::jumpTabInfo) { bundle ->
            Timber.tag(VideoFeedViewModel.DATA_RELAY_TAG).d("videoTab receive bundle: ${bundle}")
            if (bundle?.tab?.target == CommunityTabTargetType.VIDEO.name) {
                if (!VideoFeedViewModel.pinPostId.isNullOrEmpty()) {
                    scrollToTop()
                    viewModel.refreshVideoFeed()
                }
                feedViewModel.clearRelay()
            }
        }

        viewModel.registerToast(VideoFeedViewModelState::toastMsg)

        viewModel.setupRefreshLoading(
            VideoFeedViewModelState::refresh,
            binding.loading,
            binding.slRefreshLayout
        ) {
            viewModel.refreshVideoFeed()
        }

        val offset = StatusBarUtil.getStatusBarHeight(requireContext()) + 44.dp
        binding.slRefreshLayout.setProgressViewOffset(false, offset, offset + 24.dp)

        adapter.setItemExpandListener { bindingAdapterPosition, isExpanded ->
            val item = adapter.getItemOrNull(bindingAdapterPosition) ?: return@setItemExpandListener
            viewModel.setItemExpandStatus(item.videoFeedItem.videoId, isExpanded)
        }

        adapter.setItemLikeListener(object : ItemLikeListener {
            override fun onLikeClicked(bindingAdapterPosition: Int, isLiked: Boolean) {
                val item = adapter.getItemOrNull(bindingAdapterPosition) ?: return
                viewModel.setItemLikeCnt(item.videoFeedItem.videoId, isLiked)
            }

            override fun onLikeAnimationComplete(bindingAdapterPosition: Int, isLiked: Boolean) {
                val item = adapter.getItemOrNull(bindingAdapterPosition) ?: return
                viewModel.setItemLikeStatus(item.videoFeedItem.videoId, isLiked)
                if (isLiked) {
                    DialogShowManager.triggerLike(this@VideoFeedFragment)
                }
            }
        })

        adapter.setItemClickListener(object : ItemClickListener {

            override fun onSingleTapConfirmed(bindingAdapterPosition: Int, event: MotionEvent) {
                val itemState = adapter.getItemOrNull(bindingAdapterPosition) ?: return

                if (player.isPlaying) {
                    // Manually paused
                    player.pause()
                    viewModel.setPlayStatus(
                        itemState.videoFeedItem.videoId,
                        VideoPlayStatus.Paused,
                        true
                    )
                } else {
                    // 如果是播放错误后直接调用play，不会再触发资源加载逻辑，也就不会再触发PlayError了
                    // 所以在这里加个prepare用于处理重试的情况
                    player.prepare()
                    player.play()
                    viewModel.setPlayStatus(
                        itemState.videoFeedItem.videoId,
                        VideoPlayStatus.Playing,
                        true
                    )
                }
            }

            override fun onDoubleTapConfirmed(bindingAdapterPosition: Int, event: MotionEvent) {
                val itemState = adapter.getItemOrNull(bindingAdapterPosition) ?: return
                viewModel.setItemLikeCnt(itemState.videoFeedItem.videoId, true)
                viewModel.setItemLikeStatus(itemState.videoFeedItem.videoId, true)

                binding.likeView.startAnimation(event.x.toInt(), event.y.toInt())
            }
        })

        viewModel.onAsync(
            VideoFeedViewModelState::refresh,
            onFail = {},
            onSuccess = {
            adapter.setNewInstance(it.toMutableList())
        })

        viewModel.onEach(VideoFeedViewModelState::items) {
            Timber.d("RefreshDebug submitData${it.map { it.videoPlayStatus }}")
            adapter.submitData(viewLifecycleOwner.lifecycle, it.toMutableList()) {}
        }


        viewModel.onEach(VideoFeedViewModelState::latestGameStatus, deliveryMode = uniqueOnly()) {
            if (it is GameStatus.LaunchFailed) {
                if (it.params != null) {
                    TSLaunchFailedWrapper.show(this@VideoFeedFragment, it.params, it.ex)
                } else {
                    toast(R.string.launch_failed_click_to_retry)
                }
            }
        }

        adapter.addChildClickViewIds(
            R.id.dpn_play_game,
            R.id.cl_game_info,
            R.id.siv_author_avatar
        )

        adapter.setOnItemChildClickListener { _, childView, position ->
            val item = adapter.getItemOrNull(position) ?: return@setOnItemChildClickListener
            val gameInfo = item.videoFeedItem.game ?: return@setOnItemChildClickListener

            when (childView.id) {
                R.id.dpn_play_game -> {
                    viewModel.handleDownloadButtonClick(item)
                }

                R.id.cl_game_info -> {
                    val viewModelState = viewModel.oldState

                    Analytics.track(
                        EventConstants.EVENT_GAME_FEED_VIDEO_CLICK,
                        mapOf(
                            "postid" to item.videoFeedItem.videoId,
                            "gameid" to gameInfo.id,
                            "type" to "pgc",
                            "video_pkg" to gameInfo.packageName,
                            "video_gameid" to gameInfo.id,
                            "video_id" to item.videoFeedItem.videoId,
                            "reqid" to (item.reqId ?: ""),
                            "show_categoryid" to viewModel.oldState.args.resId.getCategoryID(),
                        ),
                    )

                    val resId = ResIdBean(viewModelState.args.resId).apply {
                        this.setReqId(item.reqId ?: "")
                        this.setExtras(
                            mapOf(
                                "source_video" to "1",
                                "video_id" to item.videoFeedItem.videoId,
                            )
                        )
                    }


                    MetaRouter.GameDetail.navigate(
                        fragment = this@VideoFeedFragment,
                        gameId = gameInfo.id,
                        resIdBean = resId,
                        packageName = gameInfo.packageName
                    )
                }

                R.id.siv_author_avatar -> {
                    // Ignored
                }
            }
        }

        adapter.setItemAttachListener {
            Timber.d("OnItemAttached pos:${it}")
            binding.vpVideoList.post { mayUpdateActiveItem() }
        }

        adapter.setItemAvatarClickListener { bindingAdapterPosition ->
            val item = adapter.getItemOrNull(bindingAdapterPosition) ?: return@setItemAvatarClickListener

            Analytics.track(
                EventConstants.EVENT_ON_CLICK_VIDEO_AUTHOR_AVATAR, mapOf(
                    "postid" to item.videoFeedItem.videoId,
                )
            )
            MetaRouter.Profile.other(this@VideoFeedFragment, item.videoFeedItem.author.uuid, "video_feed")
        }

        adapter.setItemFollowClickListener { bindingAdapterPosition ->
            val item = adapter.getItemOrNull(bindingAdapterPosition) ?: return@setItemFollowClickListener
            val followStatus = !item.videoFeedItem.author.isFollow
            // 在这里不能取消关注
            if (!followStatus) return@setItemFollowClickListener

            viewModel.followUser(
                videoId = item.videoFeedItem.videoId,
                uid = item.videoFeedItem.author.uuid,
                followOrNot = followStatus
            )
        }

        adapter.setItemShareClickListener { bindingAdapterPosition ->
            val item = adapter.getItemOrNull(bindingAdapterPosition) ?: return@setItemShareClickListener
            GlobalShareDialog.show(
                childFragmentManager,
                ShareRawData.video(item.videoFeedItem, item.reqId),
                requestKey = viewModel.requestKey
            )
        }

        adapter.setItemCommentClickListener { bindingAdapterPosition ->
            val item = adapter.getItemOrNull(bindingAdapterPosition) ?: return@setItemCommentClickListener

            Analytics.track(
                EventConstants.EVENT_CLICK_VIDEO_COMMENT, mapOf(
                    "postid" to item.videoFeedItem.videoId
                )
            )

            withState(viewModel) {
                VideoFeedCommentDialogFragment.show(
                    childFragmentManager,
                    item.reqId ?: "",
                    item.videoFeedItem.videoId,
                    item.videoFeedItem.videoCommentCount,
                    it.args.resId
                )
            }
        }

        binding.fplProgressBar.setDragListener(object : FloatingProgressLayout.DragListener {
            override fun onStartDragging() {

                viewLifecycleOwner.lifecycleScope.launch {
                    val state = viewModel.awaitState()
                    Analytics.track(
                        EventConstants.EVENT_CLICK_VIDEO_PROGRESS, mapOf(
                            "postid" to (state.active?.videoFeedItem?.videoId ?: "")
                        )
                    )
                }

                viewModel.setActiveItemDecorationsVisible(false)

                binding.fplProgressBar.apply {
                    setMax(player.duration)
                    setProgress(player.currentPosition)
                }
            }

            override fun onFinishDragging() {
                viewModel.setActiveItemDecorationsVisible(true)
                player.seekTo(binding.fplProgressBar.getProgress())
                player.play()
            }

            override fun onProgressChanged(progress: Long, max: Long) {
            }
        })


        binding.vflLoadmore.setOnLoadMoreListener {
            withState(viewModel) { state ->
                if (state.loadMore is Success) {
                    if (state.loadMore.invoke().data.isNullOrEmpty()) {
                        toast(R.string.video_feed_no_more_data)
                        binding.vflLoadmore.isLoadMoreShowing = false
                    } else {
                        viewModel.loadMoreVideoFeed(VideoFeedViewModel.LOAD_MORE_TRIGGER_SOURCE_OVERSCROLL)
                    }
                } else if (state.loadMore is Fail || state.loadMore is Uninitialized) {
                    viewModel.loadMoreVideoFeed(VideoFeedViewModel.LOAD_MORE_TRIGGER_SOURCE_OVERSCROLL)
                }
            }
        }

        viewModel.onAsync(
            VideoFeedViewModelState::loadMore,
            onSuccess = {
                binding.vflLoadmore.isLoadMoreShowing = false
            }, onFail = { ex, data ->
                binding.vflLoadmore.isLoadMoreShowing = false

                if (data != null && data.triggerSource == VideoFeedViewModel.LOAD_MORE_TRIGGER_SOURCE_OVERSCROLL) {
                    val message = ApiInvokeException.getToastByError(requireContext(), ex)
                    toast(message)
                }
            }
        )


        viewModel.stateFlow.map { it.active }
            .filterNotNull()
            .distinctUntilChangedBy { it.videoFeedItem.videoId }
            .collectIn(viewLifecycleOwner.lifecycleScope) {

                Timber.d("ActiveItemChanged vid:${it.videoFeedItem.videoId} videoUrl:${it.videoFeedItem.videoUrl} coverUrl:${it.videoFeedItem.videoCover}")

                val state = viewModel.awaitState()
                val playerState = state.playerState

                val restorePlayback = playerState != null && it.videoFeedItem.videoId == playerState.videoId

                if (restorePlayback) {
                    viewModel.resetWatchingItemBufferRecords(it.videoFeedItem.videoId)
                    viewModel.setVideoFirstFrameRendered(it.videoFeedItem.videoId, false)
                    viewModel.setVideoDataReadyStatus(it.videoFeedItem.videoId, false)
                }

                val mediaItem = MediaItem.Builder()
                    .setTag(it)
                    .setUri(it.videoFeedItem.videoUrl)
                    .build()
                player.setMediaItem(mediaItem)
                viewModel.setPlayStatus(it.videoFeedItem.videoId, VideoPlayStatus.Playing, false)
                player.prepare()

                // Restore player playback position
                if (restorePlayback) {
                    Timber.d("Restore player playback position to:${playerState?.position}")
                    player.seekTo(playerState?.position ?: 0)

                    viewModel.setPlayerState(null)
                }

                if (isResumed) {
                    player.play()
                }

                // 防止视频高度变化在前，视频设置在后，定位丢失的问题
                positionVideoAtTop(state.positionVideoAtTop)
            }

        viewModel.onEach(VideoFeedViewModelState::positionVideoAtTop) { bottomOnScreen ->
            positionVideoAtTop(bottomOnScreen)
        }

        mainViewModel.tabReSelectedEvent
            .filter { it.item.itemId == MainBottomNavigationItem.FEED.itemId }
            .collectIn(viewLifecycleOwner.lifecycleScope) {
                scrollToTop()
            }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.RESUMED) {
                publishPostInteractor.publishPostSharedFlow.collect {

                    if (it.isVideoPublish()) {
                        Timber.d("VideoPostPublishStatus changed:${it}")
                        if (it.status == PostPublishStatus.STATUS_SUCCEEDED) {
                            toast(R.string.video_publish_publish_success)
                        } else if (it.status == PostPublishStatus.STATUS_FAILED) {
                            toast(it.errorMessage)
                        }
                    }
                }
            }
        }

        collectPlayPosition()
    }

    private fun scrollToTop() {
        binding.vpVideoList.smoothScrollTo(0, 80)
    }

    private fun configUIStyle() {
        when (viewModel.oldState.args.style) {
            VideoFeedArgs.STYLE_RECOMMEND -> {
                binding.fplProgressBar.setMargin(bottom = 16.dp)
            }

            else -> {

            }
        }
    }

    private fun onVideoFirstFrameRendered(item: WrappedVideoFeedItem) {
        Timber.d(
            "onVideoFirstFrameRendered" +
                    " vid:${item.videoFeedItem.videoId}" +
                    " url:${item.videoFeedItem.videoUrl}" +
                    " cover:${item.videoFeedItem.videoCover}" +
                    " isDataReady:${item.isDataReady}"
        )
        viewModel.setVideoFirstFrameRendered(item.videoFeedItem.videoId, true)
    }

    private fun mayUpdateActiveItem() {
        if (!isBindingAvailable()) return

        val position = binding.vpVideoList.currentItem

        val data = adapter.getItemOrNull(position) ?: return

        val playingItem = player.currentMediaItem?.getVideoFeedItemState()

        if (playingItem != null && playingItem.videoFeedItem.videoId == data.videoFeedItem.videoId) {
            Timber.d("Playing item is not changed videoId:${data.videoFeedItem.videoId} ${data.videoFeedItem.videoUrl}")
            return
        }

        viewModel.setCurrentItem(data.videoFeedItem.videoId)
        Timber.d("Prepare to play videoId:${data.videoFeedItem.videoId} videoUrl:${data.videoFeedItem.videoUrl} cover:${data.videoFeedItem.videoCover}")
    }


    // 把视频渲染盒子放到给定的这个位置之上，防止视频被盖住
    private fun positionVideoAtTop(maxBottomOnScreen: Int) {
        Timber.d("positionVideoAtTop maxBottomOnScreen:${maxBottomOnScreen} active:${player.currentMediaItem?.getVideoFeedItemState()}")

        val playerContainerLocationOnScreen = intArrayOf(0, 0)
        binding.flParent.getLocationOnScreen(playerContainerLocationOnScreen)
        val playerContainerTop = playerContainerLocationOnScreen[1]
        val playerContainerBottom = playerContainerTop + binding.flParent.height

        if (maxBottomOnScreen <= 0
            || maxBottomOnScreen >= playerContainerBottom/*防止给定的bottom比容器的bottom还要低，造成视频往下移*/
            || binding.flParent.height <= 0/*兜底，防止布局乱掉*/) {
            binding.playerView.translationY = 0F
            binding.playerView.scaleX = 1F
            binding.playerView.scaleY = 1F
            return
        }

        val playingItem = player.currentMediaItem?.getVideoFeedItemState() ?: return

        val viewportWidth = binding.flParent.width
        val viewportHeight = maxBottomOnScreen - playerContainerTop

        val viewWidth = binding.playerView.width
        val viewHeight = binding.playerView.height

        val videoWidth = playingItem.videoFeedItem.videoWidth
        val videoHeight = playingItem.videoFeedItem.videoHeight

        val intrinsicWidthScaleFactor = viewWidth.toFloat() / videoWidth
        val intrinsicHeightScaleFactor = viewHeight.toFloat() / videoHeight
        val renderScaleFactor = min(intrinsicWidthScaleFactor, intrinsicHeightScaleFactor)

        // 视频渲染到画面上的视频大小
        val renderVideoWidth = videoWidth * renderScaleFactor
        val renderVideoHeight = videoHeight * renderScaleFactor

        val wsf = viewportWidth.toFloat() / renderVideoWidth
        val hsf = viewportHeight.toFloat() / renderVideoHeight

        val sf = min(wsf, hsf).let {
            if (it.isNaN() || it.isInfinite()) 1F else it
        }

        binding.playerView.scaleY = sf
        binding.playerView.scaleX = sf

        val translateY = (viewHeight - viewportHeight) / 2F
        binding.playerView.translationY = -translateY

        Timber.d(
            """positionVideoAtTop 
            | viewportHeight:$viewportHeight
            | translationY:${-translateY} sf:$sf 
            | intrinsicWidthScaleFactor:$intrinsicWidthScaleFactor 
            | intrinsicHeightScaleFactor:$intrinsicHeightScaleFactor"""
        )
    }

    override fun onShareCountIncrease(data: ShareRawData) {
        if (data.scene == ShareHelper.SCENE_VIDEO_FEED && data.videoFeed != null) {
            viewModel.shareCountIncrement(data.videoFeed!!.videoId)
        }
    }

    override fun onResume() {
        super.onResume()
        StatusBarUtil.setDarkText(this, false)
        requireActivity().keepScreenOn(true)
        player.play()

        viewModel.resetWatchingTime()
    }

    override fun onPause() {
        super.onPause()
        requireActivity().keepScreenOn(false)
        player.pause()
        val videoId = player.currentMediaItem.getVideoFeedItemState()?.videoFeedItem?.videoId
        videoId?.let { viewModel.setPlayerState(PlayerState(videoId, player.currentPosition)) }
        viewModel.sendFeedItemShowEventImmediately()
    }

    override fun onDestroyView() {
        binding.vpVideoList.unregisterOnPageChangeCallback(adapterPageChangeCallback)
        binding.vpVideoList.adapter = null
        binding.playerView.player = null
        releasePlayer()
        super.onDestroyView()
    }

    private fun MediaItem?.getVideoFeedItemState(): WrappedVideoFeedItem? {
        val mediaItem = this ?: return null
        return mediaItem.localConfiguration?.tag as WrappedVideoFeedItem? ?: return null
    }

    private fun VideoFeedViewModel.setupRefreshLoading(
        asyncProp: KProperty1<VideoFeedViewModelState, Async<List<WrappedVideoFeedItem>>>,
        loadingView: LoadingView,
        refreshLayout: VideoFeedSwipeRefreshLayout,
        @StringRes emptyMsgId: Int = R.string.no_data,
        onRefresh: () -> Unit
    ) {
        refreshLayout.setOnRefreshListener {
            onRefresh()
        }
        loadingView.setRetry {
            onRefresh()
        }
        onAsync(
            asyncProp,
            onLoading = {
                if (it.isNullOrEmpty()) {
                    loadingView.showLoading()
                } else {
                    refreshLayout.isRefreshing = true
                }
            },
            onFail = { _, items ->
                refreshLayout.isRefreshing = false
                if (items.isNullOrEmpty()) {
                    loadingView.showError()
                }
            },
            onSuccess = {
                refreshLayout.isRefreshing = false
                if (it.isEmpty()) {
                    loadingView.showEmpty(loadingView.resources.getString(emptyMsgId))
                } else {
                    loadingView.hide()
                }
            }
        )
    }


    /**
     * 视频首帧渲染探测器
     * ExoPlayer原本的拖动进度条/缓冲之后都会触发一次onRenderedFirstFrame
     * 这个只有在视频第一次播放的时候触发，在不切换视频的情况下只触发一次
     */
    private inner class VideoFirstFrameRenderedDetector(
        private val player: Player,
        private val firstFrameRendered: (WrappedVideoFeedItem) -> Unit
    ) {

        private var lastFiredVideoItem: WrappedVideoFeedItem? = null

        private val playListener = object : Player.Listener {
            override fun onRenderedFirstFrame() {
                val item = player.currentMediaItem.getVideoFeedItemState()
                if (lastFiredVideoItem?.videoFeedItem?.videoId != item?.videoFeedItem?.videoId) {
                    item?.let { firstFrameRendered(it) }
                    lastFiredVideoItem = item
                }
            }
        }

        fun resetLastFiredFirstFrameRenderedItem() {
            lastFiredVideoItem = null
        }

        init {
            player.addListener(playListener)
        }

        fun release() {
            player.removeListener(playListener)
        }
    }
}