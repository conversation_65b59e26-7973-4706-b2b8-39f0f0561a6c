package com.socialplay.gpark.ui.view

import android.graphics.Bitmap
import android.util.LruCache
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.socialplay.gpark.util.BitmapRecycleUtil
import java.util.concurrent.ConcurrentHashMap

/**
 * 一个全局的、与生命周期绑定的模糊位图缓存管理器。
 *
 * 该管理器使用单例模式，为每个 LifecycleOwner (如 Activity 或 Fragment) 提供一个 LruCache。
 * 当 LifecycleOwner 被销毁时，其对应的缓存会自动被清理，防止内存泄漏。
 * 
 * 重要：使用Glide的BitmapPool来回收Bitmap，避免与Glide的内存管理冲突。
 */
object BlurCacheManager {

    // 使用 ConcurrentHashMap 保证线程安全
    private val caches = ConcurrentHashMap<Lifecycle, LruCache<String, Bitmap>>()

    /**
     * 使用Glide的BitmapPool安全回收Bitmap
     */
    fun safeRecycleBitmap(bitmap: Bitmap?) {
        BitmapRecycleUtil.safeRecycle(bitmap)
    }

    /**
     * 监听 LifecycleOwner 的销毁事件，以便清理对应的缓存。
     */
    private class CacheLifecycleObserver(private val lifecycle: Lifecycle) : DefaultLifecycleObserver {
        override fun onDestroy(owner: LifecycleOwner) {
            // 当生命周期结束时，移除并清空缓存
            caches.remove(lifecycle)?.evictAll()
            // 移除监听，避免内存泄漏
            lifecycle.removeObserver(this)
        }
    }

    /**
     * 获取与指定 Lifecycle 关联的 LruCache 实例。
     * 如果缓存尚不存在，则会创建一个新的。
     *
     * @param lifecycle 目标 Lifecycle。
     * @return LruCache<String, Bitmap> 实例。
     */
    fun getCache(lifecycle: Lifecycle): LruCache<String, Bitmap> {
        // 首先检查缓存是否存在，避免不必要的同步开销
        var cache = caches[lifecycle]
        if (cache == null) {
            // 如果缓存不存在，则进入同步块
            synchronized(caches) {
                // 再次检查，防止在等待锁的过程中其他线程已经创建了缓存
                cache = caches[lifecycle]
                if (cache == null) {
                    // 创建新的 LruCache 实例
                    val newCache = createNewCache()
                    // 存入 Map
                    caches[lifecycle] = newCache
                    // 为新的缓存添加生命周期观察者
                    lifecycle.addObserver(CacheLifecycleObserver(lifecycle))
                    cache = newCache
                }
            }
        }
        return cache!!
    }

    private fun createNewCache(): LruCache<String, Bitmap> {
        // 设置缓存大小为可用内存的 1/10
        val maxMemory = (Runtime.getRuntime().maxMemory() / 1024).toInt()
        val cacheSize = maxMemory / 10 // 单位 KB

        return object : LruCache<String, Bitmap>(cacheSize) {
            override fun sizeOf(key: String, value: Bitmap): Int {
                // 如果Bitmap因为意外被回收，返回0以防止崩溃。但这预示着一个潜在的逻辑错误。
                if (value.isRecycled) {
                    return 0
                }
                // 返回位图的大小（KB），并确保至少为1，避免大小为0的条目。
                return (value.byteCount / 1024).coerceAtLeast(1)
            }

            override fun entryRemoved(
                evicted: Boolean,
                key: String,
                oldValue: Bitmap,
                newValue: Bitmap?
            ) {
                // 使用Glide的BitmapPool来回收Bitmap，避免冲突
                if (oldValue !== newValue && !oldValue.isRecycled) {
                    safeRecycleBitmap(oldValue)
                }
            }
        }
    }
} 