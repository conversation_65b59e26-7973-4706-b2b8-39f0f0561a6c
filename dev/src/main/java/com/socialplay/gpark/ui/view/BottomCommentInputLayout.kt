package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.withStyledAttributes
import com.airbnb.lottie.LottieAnimationView
import com.socialplay.gpark.R
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.cancelAnimationIfAnimating
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible

class BottomCommentInputLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    val tvReplyHint: MetaTextView
    val ivImageBtn: ImageView
    val ivEmojiBtn: ImageView
    val ivLike: ImageView
    val tvLike: MetaTextView
    val layerLike: View
    val lavLikeAnim: LottieAnimationView
    val ivLightUp: ImageView
    val tvLightUp: MetaTextView
    val layerLightUp: View
    val ivComment: ImageView
    val tvComment: MetaTextView
    val layerComment: View
    val ivSendFlowers: ImageView
    val tvSendFlowers: MetaTextView
    val layerSendFlowers: View

    private var likeEnabled: Boolean = true
    private var lightUpEnabled: Boolean = false
    private var commentEnabled: Boolean = true
    private var sendFlowerEnabled: Boolean = false

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_bottom_comment_input, this, true)
        tvReplyHint = findViewById(R.id.tvReplyHint)
        ivImageBtn = findViewById(R.id.ivImageBtn)
        ivEmojiBtn = findViewById(R.id.ivEmojiBtn)
        ivLike = findViewById(R.id.ivLike)
        tvLike = findViewById(R.id.tvLike)
        layerLike = findViewById(R.id.layerLike)
        lavLikeAnim = findViewById(R.id.lavLikeAnim)
        ivLightUp = findViewById(R.id.ivLightUp)
        tvLightUp = findViewById(R.id.tvLightUp)
        layerLightUp = findViewById(R.id.layerLightUp)
        ivComment = findViewById(R.id.ivComment)
        tvComment = findViewById(R.id.tvComment)
        layerComment = findViewById(R.id.layerComment)
        ivSendFlowers = findViewById(R.id.ivSendFlowers)
        tvSendFlowers = findViewById(R.id.tvSendFlowers)
        layerSendFlowers = findViewById(R.id.layerSendFlowers)

        attrs?.let {
            context.withStyledAttributes(it, R.styleable.BottomCommentInputLayout) {
                likeEnabled = getBoolean(R.styleable.BottomCommentInputLayout_enableLike, true)
                lightUpEnabled =
                    getBoolean(R.styleable.BottomCommentInputLayout_enableLightUp, true)
                commentEnabled =
                    getBoolean(R.styleable.BottomCommentInputLayout_enableComment, true)
                sendFlowerEnabled =
                    getBoolean(R.styleable.BottomCommentInputLayout_enableSendFlower, true)
            }
        }
        lavLikeAnim.setMinAndMaxProgress(0.0f, 1.0f)
        setLikeEnabled(likeEnabled)
        setLightUpEnabled(lightUpEnabled)
        setCommentEnabled(commentEnabled)
        setSendFlowerEnabled(sendFlowerEnabled)
    }

    fun updateLike(isLike: Boolean, likeCount: Long, anim: Boolean = true) {
        if (isLike) {
            tvLike.setTextColor(getColorByRes(R.color.color_FF5F42))
            ivLike.invisible()
            lavLikeAnim.visible()
            if (anim) {
                lavLikeAnim.progress = 0.0f
                lavLikeAnim.playAnimation()
            } else {
                lavLikeAnim.progress = 1.0f
            }
        } else {
            tvLike.setTextColor(getColorByRes(R.color.color_1A1A1A))
            ivLike.visible()
            lavLikeAnim.cancelAnimationIfAnimating()
            lavLikeAnim.gone()
        }
        val likeCountStr = UnitUtil.formatNumberWithUnit(
            num = likeCount,
            decimal = 1,
            kCaps = true,
            mCaps = true,
            bCaps = true,
            showRawNumberBelow10W = false
        )
        tvLike.text = likeCountStr
    }

    /**
     * 设置like组件可用/不可用，不可用时隐藏
     */
    fun setLikeEnabled(enabled: Boolean) {
        likeEnabled = enabled
        val visibility = if (enabled) View.VISIBLE else View.GONE
        ivLike.visibility = visibility
        tvLike.visibility = visibility
        layerLike.visibility = visibility
        lavLikeAnim.visibility = visibility
    }

    /**
     * 设置lightUp组件可用/不可用，不可用时隐藏
     */
    fun setLightUpEnabled(enabled: Boolean) {
        lightUpEnabled = enabled
        val visibility = if (enabled) View.VISIBLE else View.GONE
        ivLightUp.visibility = visibility
        tvLightUp.visibility = visibility
        layerLightUp.visibility = visibility
    }

    /**
     * 设置comment组件可用/不可用，不可用时隐藏
     */
    fun setCommentEnabled(enabled: Boolean) {
        commentEnabled = enabled
        val visibility = if (enabled) View.VISIBLE else View.GONE
        ivComment.visibility = visibility
        tvComment.visibility = visibility
        layerComment.visibility = visibility
    }

    /**
     * 设置sendflower组件可用/不可用，不可用时隐藏
     */
    fun setSendFlowerEnabled(enabled: Boolean) {
        sendFlowerEnabled = enabled
        val visibility = if (enabled) View.VISIBLE else View.GONE
        ivSendFlowers.visibility = visibility
        tvSendFlowers.visibility = visibility
        layerSendFlowers.visibility = visibility
    }

    fun setOnReplyClickListener(listener: (View) -> Unit) {
        tvReplyHint.setOnAntiViolenceClickListener(listener)
    }

    fun setOnLikeClickListener(listener: (View) -> Unit) {
        layerLike.setOnAntiViolenceClickListener(listener)
    }

    fun setOnLightUpClickListener(listener: (View) -> Unit) {
        layerLightUp.setOnAntiViolenceClickListener(listener)
    }

    fun setOnCommentClickListener(listener: (View) -> Unit) {
        layerComment.setOnAntiViolenceClickListener(listener)
    }

    fun setOnSendFlowerClickListener(listener: (View) -> Unit) {
        layerSendFlowers.setOnAntiViolenceClickListener(listener)
    }
}