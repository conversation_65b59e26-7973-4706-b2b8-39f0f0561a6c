package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.widget.ImageView
import android.widget.ImageView.ScaleType
import androidx.core.content.withStyledAttributes
import androidx.core.view.marginEnd
import com.socialplay.gpark.R
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.getDrawableByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.visible

class FollowView(
    context: Context,
    attrs: AttributeSet? = null,
) : CustomLayout(context, attrs) {
    enum class Status {
        UNFOLLOW,
        FOLLOWING,
    }

    enum class Style(val value: Int) {
        /**
         * 实心
         */
        SOLID(value = 1),

        /**
         * 空心
         */
        HOLLOW(value = 2);

        companion object {
            fun from(value: Int): Style {
                return Style.entries.toTypedArray().firstOrNull { style ->
                    style.value == value
                } ?: SOLID
            }
        }
    }

    var style = Style.HOLLOW
        set(value) {
            if (field != value) {
                field = value
                updateViewByStatus(status)
            }
        }

    var status = Status.UNFOLLOW
        set(value) {
            if (field != value) {
                field = value
                updateViewByStatus(value)
            }
        }
    private var unfollowHollowColor: Int = getColorByRes(R.color.color_FBD24E)
    private var unfollowSolidColor: Int = getColorByRes(R.color.color_FFDC1C)
    private var unfollowTextColor: Int = getColorByRes(R.color.color_FABE25)
    private var followingHollowColor: Int = getColorByRes(R.color.color_D9D9D9)
    private var followingSolidColor: Int = getColorByRes(R.color.color_D9D9D9)
    private var followingTextColor: Int = getColorByRes(R.color.color_666666)

    private val ivAdd = ImageView(context).apply {
        layoutParams = LayoutParams(12.dp, 12.dp).also {
            it.marginEnd = 4.dp
        }
        setImageResource(R.drawable.icon_add_fabe25_size_12)
        scaleType = ScaleType.CENTER_CROP
    }.addTo(this)

    private val tvFollow = MetaTextView(context).apply {
        layoutParams = LayoutParams(wrapContent, wrapContent)
        setTextAppearance(R.style.MetaTextView_S12_PoppinsSemiBold600)
        setTextColorByRes(R.color.color_FABE25)
        setText(R.string.follow)
    }.addTo(this)

    init {
        attrs?.let {
            context.withStyledAttributes(it, R.styleable.FollowView) {
                style = Style.from(getInt(R.styleable.FollowView_viewStyle, 1))
                unfollowHollowColor = getColor(
                    R.styleable.FollowView_unfollowHollowColor,
                    getColorByRes(R.color.color_FBD24E)
                )
                unfollowSolidColor = getColor(
                    R.styleable.FollowView_unfollowSolidColor,
                    getColorByRes(R.color.color_FFDC1C)
                )
                unfollowTextColor = getColor(
                    R.styleable.FollowView_unfollowTextColor,
                    getColorByRes(R.color.color_FABE25)
                )
                followingHollowColor = getColor(
                    R.styleable.FollowView_followingHollowColor,
                    getColorByRes(R.color.color_D9D9D9)
                )
                followingSolidColor = getColor(
                    R.styleable.FollowView_followingSolidColor,
                    getColorByRes(R.color.color_D9D9D9)
                )
                followingTextColor = getColor(
                    R.styleable.FollowView_followingTextColor,
                    getColorByRes(R.color.color_B3B3B3)
                )
            }
        }
        updateViewByStatus(status)
    }

    private fun updateViewByStatus(status: Status) {
        background = GradientDrawable().apply {
            shape = GradientDrawable.RECTANGLE
            cornerRadius = 100.dp.toFloat()
            if (style == Style.HOLLOW) {
                setStroke(
                    dp(0.5), when (status) {
                        Status.UNFOLLOW -> unfollowHollowColor
                        Status.FOLLOWING -> followingHollowColor
                    }
                )
            }
            if (style == Style.SOLID) {
                setColor(
                    when (status) {
                        Status.UNFOLLOW -> unfollowSolidColor
                        Status.FOLLOWING -> followingSolidColor
                    }
                )
            }
        }
        when (status) {
            Status.UNFOLLOW -> {
                ivAdd.visible()
                ivAdd.setImageDrawable(getDrawableByRes(R.drawable.icon_add_fabe25_size_12)?.apply {
                    mutate().setTint(unfollowTextColor)
                })
                tvFollow.setTextAppearance(R.style.MetaTextView_S12_PoppinsSemiBold600)
                tvFollow.setTextColor(unfollowTextColor)
                tvFollow.setText(R.string.follow)
            }

            Status.FOLLOWING -> {
                ivAdd.gone()
                tvFollow.setTextAppearance(R.style.MetaTextView_S12_PoppinsRegular400)
                tvFollow.setTextColor(followingTextColor)
                tvFollow.setText(R.string.following_cap)
            }
        }
    }

    override fun onMeasureChildren(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val maxWidth = MeasureSpec.getSize(widthMeasureSpec)
        var contentWidth = 0
        var contentHeight = 0
        if (status == Status.UNFOLLOW) {
            ivAdd.autoMeasure()

            contentWidth += ivAdd.measuredWidth + ivAdd.marginEnd
            contentHeight = ivAdd.measuredHeight

            tvFollow.measure(
                (maxWidth - contentWidth).toAtMostMeasureSpec(),
                heightMeasureSpec.size.toAtMostMeasureSpec()
            )

            contentWidth += tvFollow.measuredWidth
            contentHeight = contentHeight.coerceAtLeast(tvFollow.measuredHeight)
        } else {
            tvFollow.measure(
                maxWidth.toAtMostMeasureSpec(),
                heightMeasureSpec.size.toAtMostMeasureSpec()
            )
        }
        setMeasuredDimension(
            widthMeasureSpec,
            heightMeasureSpec,
            contentWidth,
            contentHeight
        )
    }

    override fun onLayout(
        changed: Boolean,
        l: Int,
        t: Int,
        r: Int,
        b: Int
    ) {
        if (status == Status.UNFOLLOW) {
            val contentWidth = ivAdd.measuredWidthWithMargins + tvFollow.measuredWidthWithMargins
            ivAdd.layout(
                x = (measuredWidth - contentWidth) / 2,
                y = (measuredHeight - ivAdd.measuredHeightWithMargins) / 2
            )
            tvFollow.layout(
                x = ivAdd.right + ivAdd.marginEnd,
                // 由于MetaTextView显示的文本顶部的空白区域比底部的空白区域小, 为了让文本看起来是垂直方向居中的, 就将tvFollow再向下偏移15%
                y = (measuredHeight - tvFollow.measuredHeightWithMargins) / 2 + (tvFollow.lineHeight * 0.15).toInt()
            )
        } else {
            tvFollow.layout(
                x = (measuredWidth - tvFollow.measuredWidthWithMargins) / 2,
                // 由于MetaTextView显示的文本顶部的空白区域比底部的空白区域小, 为了让文本看起来是垂直方向居中的, 就将tvFollow再向下偏移15%
                y = (measuredHeight - tvFollow.measuredHeightWithMargins) / 2 + (tvFollow.lineHeight * 0.15).toInt()
            )
        }
    }
}