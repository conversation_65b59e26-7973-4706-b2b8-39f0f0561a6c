package com.socialplay.gpark.ui.view

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.graphics.Bitmap
import android.graphics.BlurMaskFilter
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.graphics.Shader
import android.util.AttributeSet
import android.view.View
import android.view.ViewTreeObserver
import androidx.core.graphics.ColorUtils
import androidx.core.graphics.scale
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.findViewTreeLifecycleOwner
import com.socialplay.gpark.R
import com.socialplay.gpark.util.blur.RenderScriptBlur
import timber.log.Timber
import java.lang.ref.WeakReference
import kotlin.math.cos
import kotlin.math.max
import kotlin.math.sin
import kotlin.math.sqrt

/**
 * 金属边框自定义View，支持光照效果
 *
 * 特性：
 * - 可调节光照角度
 * - 可调节边框厚度
 * - 透明中心区域
 * - 增强的高光和阴影效果
 * - 支持圆角矩形
 * - 支持背景模糊和颜色叠加
 */
class MetallicBorderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 绘制相关
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val glowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val clearPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val overlayPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    // 路径和矩形
    private val borderPath = Path()
    private val innerPath = Path()
    private val glowPath = Path()
    private val borderRect = RectF()
    private val innerRect = RectF()
    private val glowRect = RectF()
    private val framePath = Path() // 用于仅绘制边框

    // 属性
    private var borderThickness: Float = 0f
    private var lightAngle: Float = 0f
    private var cornerRadius: Float = 0f
    private var highlightColor: Int = Color.WHITE
    private var shadowColor: Int = Color.BLACK
    private var metallicBaseColor: Int = Color.parseColor("#C0C0C0")
    private var highlightIntensity: Float = 0.9f
    private var shadowIntensity: Float = 0.6f
    private var highlightWidth: Float = 0.5f // 高光宽度，0.0 to 1.0, a percentage of the gradient band
    private var debugMode: Boolean = false // 调试模式，不挖空中心

    // 外发光属性
    private var glowIntensity: Float = 0.5f // 将作为不透明度
    private var glowColor: Int = Color.parseColor("#0080FF")
    private var glowRadius: Float = 0f

    // 背景模糊相关属性
    private var blurEnabled: Boolean = false
    private var blurRadius: Float = 25f
    private var blurOverlayColor: Int = Color.argb(128, 0, 0, 0) // 半透明黑色
    private var blurIntensity: Float = 1.0f // 模糊强度（透明度）

    // 内部使用的模糊处理变量
    private var blurRenderScript: RenderScriptBlur? = null
    private var blurredBitmap: Bitmap? = null
    private var isBlurDirty = true

    private var isStart = false
    private var blurSourceViewRef: WeakReference<View>? = null
    private var blurCacheKey: String? = null

    private var preCacheKey: String? = null // 上一个源的key
    private var isBitmapFromCache: Boolean = false

    private var isChangedSource: Boolean = false    // 是否切换了源
    private var lifecycle: Lifecycle? = null

    private val preDrawListener = ViewTreeObserver.OnPreDrawListener {
        if (blurEnabled && isBlurDirty && isAttachedToWindow && isStart) {
            isStart = false
            updateBlurBackground()
        }
        true
    }

    companion object {
        private const val MAX_BLUR_RADIUS_DIRECT = 25f
        private const val DOWNSCALE_FACTOR = 4f
        private const val TAG = "MetallicBorderView"
    }

    init {
        // 设置软件渲染以支持阴影效果, 但在启用模糊时，硬件加速可能更优
        // setLayerType(LAYER_TYPE_SOFTWARE, null)

        initAttributes(context, attrs)
        initPaints()
    }

    private fun initAttributes(context: Context, attrs: AttributeSet?) {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.MetallicBorderView)

        borderThickness = typedArray.getDimension(
            R.styleable.MetallicBorderView_borderThickness,
            context.resources.displayMetrics.density * 1 // Default 1dp
        )

        lightAngle = typedArray.getFloat(R.styleable.MetallicBorderView_lightAngle, 10f) // Default 10 degrees

        cornerRadius = typedArray.getDimension(R.styleable.MetallicBorderView_cornerRadius, 0f)

        highlightColor = typedArray.getColor(
            R.styleable.MetallicBorderView_highlightColor,
            Color.WHITE
        )

        shadowColor = typedArray.getColor(
            R.styleable.MetallicBorderView_shadowColor,
            Color.BLACK
        )

        metallicBaseColor = typedArray.getColor(
            R.styleable.MetallicBorderView_metallicBaseColor,
            ColorUtils.setAlphaComponent(Color.WHITE, (0.2f * 255).toInt()) // Default white 20%
        )

        highlightIntensity = typedArray.getFloat(
            R.styleable.MetallicBorderView_highlightIntensity,
            1.0f // Default 100%
        ).coerceIn(0f, 1f)

        shadowIntensity = typedArray.getFloat(
            R.styleable.MetallicBorderView_shadowIntensity,
            0.6f
        ).coerceIn(0f, 1f)

        highlightWidth = typedArray.getFloat(
            R.styleable.MetallicBorderView_highlightWidth,
            0.15f // Default 15%
        ).coerceIn(0f, 1f)

        glowIntensity = typedArray.getFloat(
            R.styleable.MetallicBorderView_glowIntensity, 0.15f // Default 15%
        )

        glowRadius = typedArray.getDimension(
            R.styleable.MetallicBorderView_glowRadius,
            context.resources.displayMetrics.density * 3 // Default 3dp
        )

        glowColor = typedArray.getColor(
            R.styleable.MetallicBorderView_glowColor, Color.parseColor("#FFFFFF")   // Default white
        )

        // 背景模糊属性
        blurEnabled = typedArray.getBoolean(R.styleable.MetallicBorderView_blurEnabled, true) // Default on
//        blurRadius = typedArray.getFloat(R.styleable.MetallicBorderView_blurRadius, 40f) // Default 40
        blurOverlayColor = typedArray.getColor(
            R.styleable.MetallicBorderView_blurOverlayColor,
            ColorUtils.setAlphaComponent(Color.GRAY, (0.3f * 255).toInt()) // Default gray 30%
        )

        typedArray.recycle()

        if (blurEnabled && !isInEditMode) {
            setupBlur()
        }
    }

    private fun initPaints() {
        // 基础边框画笔
        borderPaint.apply {
            style = Paint.Style.FILL
            color = metallicBaseColor
        }

        // 清除画笔（用于挖空中心）
        clearPaint.apply {
            xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
            color = Color.TRANSPARENT
        }

        // 外发光画笔
        glowPaint.apply {
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 叠加层画笔
        overlayPaint.apply {
            style = Paint.Style.FILL
            color = blurOverlayColor
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        viewTreeObserver.addOnPreDrawListener(preDrawListener)
        lifecycle = findViewTreeLifecycleOwner()?.lifecycle
        // Timber.tag(TAG).d("ID[${this.hashCode()}] onAttachedToWindow:${blurCacheKey.hashCode()} blurCacheKey:$blurCacheKey")
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        viewTreeObserver.removeOnPreDrawListener(preDrawListener)
        try {
            blurRenderScript?.destroy()
        } catch (e: Exception) {
            // Timber.tag(TAG).e(e, "Error destroying blurRenderScript in onDetachedFromWindow")
        }
        blurRenderScript = null
        // 不再由View负责回收Bitmap，交由BlurCacheManager全权处理
        // try {
        // blurredBitmap?.recycle()
        // } catch (e: Exception) {
        // // Timber.tag(TAG).e(e, "Error recycling blurredBitmap in onDetachedFromWindow")
        // }
        // 离开屏幕需要清空，但是不能在onDetachedFromWindow中回收，因为可能会被复用，某些情况是不会走onBind和unBind的，不能这里直接清空
        // 如果在这里清空了，会导致缓慢滑动时，往回滑动的时候没有模糊Bitmap
//        blurredBitmap = null
        lifecycle = null
        // Timber.tag(TAG).d("ID[${this.hashCode()}] onDetachedFromWindow:${blurCacheKey.hashCode()} blurCacheKey:$blurCacheKey")
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        updatePaths()
        // 尺寸变化后，模糊效果需要重新生成
        isBlurDirty = true
    }

    private fun updatePaths() {
        val width = width.toFloat()
        val height = height.toFloat()

        if (width <= 0 || height <= 0) return

        // 外边框矩形（在padding内部）
        borderRect.set(
            paddingLeft.toFloat(),
            paddingTop.toFloat(),
            width - paddingRight.toFloat(),
            height - paddingBottom.toFloat()
        )

        // 内部透明区域矩形
        innerRect.set(
            borderRect.left + borderThickness,
            borderRect.top + borderThickness,
            borderRect.right - borderThickness,
            borderRect.bottom - borderThickness
        )

        // 创建外边框路径
        borderPath.reset()
        borderPath.addRoundRect(borderRect, cornerRadius, cornerRadius, Path.Direction.CW)

        // 创建内部路径
        innerPath.reset()
        val innerCornerRadius = maxOf(0f, cornerRadius - borderThickness)
        innerPath.addRoundRect(innerRect, innerCornerRadius, innerCornerRadius, Path.Direction.CW)

        // 更新光照效果
        updateLightingPaths()
    }

    private fun updateLightingPaths() {
        val width = borderRect.width()
        val height = borderRect.height()

        if (width <= 0 || height <= 0) return

        // 使用与BorderFocusedMetallicView相同的线性渐变方法
        updateBorderShader()
    }

    private fun updateBorderShader() {
        val width = borderRect.width()
        val height = borderRect.height()

        if (width <= 0 || height <= 0) return

        // 计算光照方向
        val angleRad = Math.toRadians(lightAngle.toDouble())
        val lightDx = cos(angleRad).toFloat()
        val lightDy = sin(angleRad).toFloat()

        // 创建跨越整个边框的线性渐变
        val centerX = borderRect.centerX()
        val centerY = borderRect.centerY()

        // 核心修正：使用对角线长度作为渐变场的跨度，以减少角落的失真
        val gradientSpan = sqrt(width * width + height * height)
        val positionRadius = max(width, height) / 2f // 高光中心点仍使用半径定位

        // The highlight should be on the edge of the border, in the direction of the light.
        val highlightCenterX = centerX + lightDx * positionRadius
        val highlightCenterY = centerY + lightDy * positionRadius

        // The gradient line is perpendicular to the light direction and uses the large diagonal span
        val gradStartX = highlightCenterX - lightDy * gradientSpan
        val gradStartY = highlightCenterY + lightDx * gradientSpan
        val gradEndX = highlightCenterX + lightDy * gradientSpan
        val gradEndY = highlightCenterY - lightDx * gradientSpan

        // 创建通过混合颜色的光晕效果
        val finalHighlightColor = blendColors(metallicBaseColor, highlightColor, highlightIntensity)
        val finalShadowColor = blendColors(metallicBaseColor, shadowColor, shadowIntensity)

        // 根据高光宽度属性计算光带的宽度。
        val highlightSpread = highlightWidth / 2f
        val positions = floatArrayOf(
            0f,
            (0.5f - highlightSpread).coerceAtLeast(0f),
            0.5f,
            (0.5f + highlightSpread).coerceAtMost(1f),
            1f
        )

        val gradient = LinearGradient(
            gradStartX, gradStartY, gradEndX, gradEndY,
            intArrayOf(
                finalShadowColor,        // 阴影面：暗
                metallicBaseColor,  // 中间：基础色
                finalHighlightColor,     // 高光面：亮
                metallicBaseColor,  // 中间：基础色
                finalShadowColor         // 阴影面：暗
            ),
            positions,
            Shader.TileMode.CLAMP
        )

        borderPaint.shader = gradient
        borderPaint.style = Paint.Style.FILL
    }

    private fun blendColors(color1: Int, color2: Int, ratio: Float): Int {
        val inverseRatio = 1f - ratio.coerceIn(0f, 1f)
        val r = Color.red(color1) * inverseRatio + Color.red(color2) * ratio
        val g = Color.green(color1) * inverseRatio + Color.green(color2) * ratio
        val b = Color.blue(color1) * inverseRatio + Color.blue(color2) * ratio
        val a = Color.alpha(color1) * inverseRatio + Color.alpha(color2) * ratio
        return Color.argb(a.toInt(), r.toInt(), g.toInt(), b.toInt())
    }

//    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
//        val widthMode = MeasureSpec.getMode(widthMeasureSpec)
//        val widthSize = MeasureSpec.getSize(widthMeasureSpec)
//        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
//        val heightSize = MeasureSpec.getSize(heightMeasureSpec)
//
//        // 计算边框和发光效果需要的额外空间
//        val extraSpace = (borderThickness + glowRadius).toInt()
//
//        // 计算期望的尺寸
//        var desiredWidth = suggestedMinimumWidth + extraSpace * 2
//        var desiredHeight = suggestedMinimumHeight + extraSpace * 2
//
//        // 处理不同的测量模式
//        val width = when (widthMode) {
//            MeasureSpec.EXACTLY -> widthSize
//            MeasureSpec.AT_MOST -> minOf(desiredWidth, widthSize)
//            else -> desiredWidth
//        }
//
//        val height = when (heightMode) {
//            MeasureSpec.EXACTLY -> heightSize
//            MeasureSpec.AT_MOST -> minOf(desiredHeight, heightSize)
//            else -> desiredHeight
//        }
//
//        setMeasuredDimension(width, height)
////        setMeasuredDimension(0, 0)
////        setMeasuredDimension(200, 80)
//    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (width <= 0 || height <= 0) return

//        // 1. 绘制外发光 (optional)
//        if (glowIntensity > 0f && glowRadius > 0f) {
//            drawOuterGlow(canvas)
//        }

        // 2. 绘制 "内容"区域 (模糊和/或颜色叠加)
        // 这部分绘制在边框的 "下方"
        if (blurEnabled && blurredBitmap != null && !blurredBitmap!!.isRecycled) {
            canvas.save()
            canvas.clipPath(innerPath) // 只在内部区域绘制
            backgroundPaint.alpha = (255 * blurIntensity).toInt()
            // 使用0,0作为绘制起点，因为裁剪是基于View坐标系的
            canvas.drawBitmap(blurredBitmap!!, 0f, 0f, backgroundPaint)
            backgroundPaint.alpha = 255
            canvas.restore()
        }
        if (Color.alpha(blurOverlayColor) > 0) {
            overlayPaint.color = blurOverlayColor
            canvas.drawPath(innerPath, overlayPaint) // 只在内部区域绘制
        }
//
//        // 3. 绘制边框本身，使用 saveLayer 和 clearPaint 来创建镂空效果
//        // 这样可以确保边框的半透明效果正确叠加在内容之上
//        val saveCount = canvas.saveLayer(null, null)
//        // 绘制完整的边框（带光照）
//        canvas.drawPath(borderPath, borderPaint)
//        // 在边框上挖出内部区域，显示出下方已绘制的内容
//        if (!debugMode) {
//            canvas.drawPath(innerPath, clearPaint)
//        }
//        canvas.restoreToCount(saveCount)
    }

    private fun drawOuterGlow(canvas: Canvas) {
        // Bugfix: 先将辉光绘制出来，然后把边框的区域从中挖掉，
        // 这样可以避免辉光颜色渗透到半透明的边框中。
        val glowLayerId = canvas.saveLayer(0f, 0f, width.toFloat(), height.toFloat(), null)

        // 在新图层上绘制模糊的辉光
        val originalMaskFilter = glowPaint.maskFilter
        glowPaint.apply {
            color = glowColor
            alpha = (255 * glowIntensity).toInt()
            maskFilter = BlurMaskFilter(glowRadius, BlurMaskFilter.Blur.NORMAL)
        }
        canvas.drawPath(borderPath, glowPaint)
        glowPaint.maskFilter = originalMaskFilter // 重置

        // 使用 clearPaint (Xfermode.CLEAR) 挖掉边框本身占据的区域
        canvas.drawPath(borderPath, clearPaint)

        // 将处理过的、仅存在于外部的辉光绘制回主图层
        canvas.restoreToCount(glowLayerId)
    }

    // --- 背景模糊核心逻辑 ---

    private fun setupBlur() {
        if (isInEditMode) return
        try {
            // 统一使用RenderScript进行位图模糊，不再对View本身使用RenderEffect
            if (blurRenderScript == null) {
                blurRenderScript = RenderScriptBlur(context)
            }
        } catch (e: Exception) {
            // Timber.tag(TAG).e(e, "Error initializing RenderScriptBlur in setupBlur")
            blurRenderScript = null // Ensure it's null if initialization failed
        }
        overlayPaint.color = blurOverlayColor
    }

    private fun updateBlurBackground() {
        if (!isAttachedToWindow || width <= 0 || height <= 0 || isInEditMode) {
            return
        }
        updateBlurFromSource()
    }

    private fun buildCacheKey(): String? {
        if (blurCacheKey == null) return null
        // Key should also depend on view dimensions and blur radius to avoid returning incorrect blur image
        return "${blurCacheKey}_${width}x${height}_r${blurRadius}"
    }

    private fun updateBlurFromSource() {
        val sourceView = blurSourceViewRef?.get()
        val generateKey = blurCacheKey
        if (sourceView == null || sourceView.width <= 0 || sourceView.height <= 0) {
            // Timber.tag(TAG).w("updateBlurFromSource: SourceView is null or invalid (width/height <= 0).")
            isBlurDirty = false // 没有有效的源，不再尝试
            invalidate()
            return
        }

        val cache = lifecycle?.let { BlurCacheManager.getCache(it) }
        if (cache == null) {
            // Timber.tag(TAG).w("updateBlurFromSource: BlurCacheManager.getCache returned null for lifecycle: $lifecycle")
        }
        val effectiveCacheKey = buildCacheKey()

        // 1. 尝试从缓存获取
        if (effectiveCacheKey != null && cache != null) {
            try {
                val cachedBitmap = cache.get(effectiveCacheKey)
                if (cachedBitmap != null && !cachedBitmap.isRecycled) {
                    // Timber.tag(TAG).d("updateBlurFromSource: Cache hit for key: $effectiveCacheKey")
                    blurredBitmap = cachedBitmap.copy(cachedBitmap.config ?: Bitmap.Config.ARGB_8888, true)
                    isBitmapFromCache = true
                    isBlurDirty = false
                    invalidate()
                    return
                } else {
                    // Timber.tag(TAG).d("updateBlurFromSource: Cache miss or bitmap recycled for key: $effectiveCacheKey")
                }
            } catch (e: Exception) {
                // Timber.tag(TAG).e(e, "Error getting bitmap from cache for key: $effectiveCacheKey")
            }
        } else {
            // Timber.tag(TAG).d("updateBlurFromSource: No effectiveCacheKey or cache is null, skipping cache check.")
        }


        // 2. 缓存未命中，生成新的Bitmap
        val newBlurredBitmap = try {
            generateBlurredBitmap(sourceView)
        } catch (e: Exception) {
            // Timber.tag(TAG).e(e, "Error generating blurred bitmap.")
            null
        }

        if (newBlurredBitmap == null) {
            // Timber.tag(TAG).w("updateBlurFromSource: generateBlurredBitmap returned null.")
            isBlurDirty = false
            return
        }

        // 3. 分配新Bitmap并更新状态
        blurredBitmap = newBlurredBitmap
        isBitmapFromCache = false // Generated, not from cache
        isBlurDirty = false

        // 4. 将新Bitmap放入缓存
        if (effectiveCacheKey != null && cache != null && generateKey == blurCacheKey) {
            try {
                // Timber.tag(TAG).d("updateBlurFromSource: Putting new bitmap into cache with key: $effectiveCacheKey")
                cache.put(effectiveCacheKey, blurredBitmap!!) // blurredBitmap is checked for null above
            } catch (e: Exception) {
                // Timber.tag(TAG).e(e, "Error putting bitmap into cache for key: $effectiveCacheKey")
            }
        } else {
            // Timber.tag(TAG).d("updateBlurFromSource: Key changed, Put new bitmap into cache with key: $effectiveCacheKey failed")
        }
        invalidate()
    }

    /**
     * 从源View生成一个新的模糊Bitmap。
     * @param sourceView 用于生成模糊背景的源视图。
     * @return 返回一个新的、经过模糊处理的Bitmap，如果失败则返回null。
     */
    private fun generateBlurredBitmap(sourceView: View): Bitmap? {
        try {
            // Timber.tag(TAG).d("generateBlurredBitmap: Starting generation for sourceView: $sourceView")
            // 1. 获取源View的位图
            val sourceBitmap: Bitmap = try {
                Bitmap.createBitmap(sourceView.width, sourceView.height, Bitmap.Config.ARGB_8888)
            } catch (e: OutOfMemoryError) {
                // Timber.tag(TAG).e(e, "OutOfMemoryError creating sourceBitmap.")
                return null
            } catch (e: Exception) {
                // Timber.tag(TAG).e(e, "Error creating sourceBitmap.")
                return null
            }
            val canvas = Canvas(sourceBitmap)
            sourceView.draw(canvas)
             Timber.tag(TAG).d("generateBlurredBitmap: $blurRadius Source bitmap created ${sourceBitmap.width}x${sourceBitmap.height} isAttachedToWindow:${sourceView.isAttachedToWindow}  isVisible:${sourceView.isVisible} isDrawingCacheEnabled:${sourceView.isDrawingCacheEnabled}")

            // 2. 计算相对位置并裁剪 - 增强平板适配
            val myLocation = IntArray(2)
            val sourceLocation = IntArray(2)
            
            // 使用更可靠的坐标获取方法
            try {
                getLocationInWindow(myLocation)
                sourceView.getLocationInWindow(sourceLocation)
            } catch (e: Exception) {
                // 如果getLocationInWindow失败，尝试使用OnScreen方法
                getLocationOnScreen(myLocation)
                sourceView.getLocationOnScreen(sourceLocation)
            }
            
            // 计算相对位置，加入更多的边界检查
            var cropX = (myLocation[0] - sourceLocation[0]).coerceAtLeast(0)
            var cropY = (myLocation[1] - sourceLocation[1]).coerceAtLeast(0)
            
            // 针对平板设备的特殊处理
            val isPad = com.socialplay.gpark.util.ScreenUtil.isPad(context)
            if (isPad) {
                // 平板设备可能有更大的窗口偏移，需要更宽松的边界检查
                val displayMetrics = context.resources.displayMetrics
                val tolerance = (displayMetrics.density * 2).toInt() // 2dp的容错
                
                // 如果坐标计算结果明显异常，尝试使用中心对齐
                if (cropX > sourceBitmap.width || cropY > sourceBitmap.height) {
                    cropX = maxOf(0, (sourceBitmap.width - width) / 2)
                    cropY = maxOf(0, (sourceBitmap.height - height) / 2)
                     Timber.tag(TAG).d("generateBlurredBitmap: Tablet fallback to center alignment")
                }
            }
            
            // 确保裁剪区域不超过源视图边界
            val maxCropX = maxOf(0, sourceBitmap.width - width)
            val maxCropY = maxOf(0, sourceBitmap.height - height)
            cropX = cropX.coerceIn(0, maxCropX)
            cropY = cropY.coerceIn(0, maxCropY)

            // 添加调试日志
            // Timber.tag(TAG).d(
//                """
//            generateBlurredBitmap:
//            isPad=$isPad
//            myLocation=${myLocation.joinToString()}
//            sourceLocation=${sourceLocation.joinToString()}
//            adjustedCropX=$cropX, adjustedCropY=$cropY
//            sourceBitmapSize=${sourceBitmap.width}x${sourceBitmap.height}
//            viewSize=${width}x${height}
//        """.trimIndent()
//            )

            if (width <= 0 || height <= 0) {
                 Timber.tag(TAG).w("Invalid view dimensions")
                com.socialplay.gpark.util.BitmapRecycleUtil.safeRecycle(sourceBitmap)
                return null
            }
            
            // 更宽松的边界检查，特别适用于平板设备
            val isValidCrop = cropX >= 0 && cropY >= 0 && 
                             cropX + width <= sourceBitmap.width && 
                             cropY + height <= sourceBitmap.height
                             
            if (!isValidCrop) {
                // 如果裁剪区域仍然无效，尝试调整到最接近的有效区域
                if (cropX + width > sourceBitmap.width) {
                    cropX = maxOf(0, sourceBitmap.width - width)
                }
                if (cropY + height > sourceBitmap.height) {
                    cropY = maxOf(0, sourceBitmap.height - height)
                }
                
                // 最后检查，如果仍然无效则返回null
                if (cropX < 0 || cropY < 0 || cropX + width > sourceBitmap.width || cropY + height > sourceBitmap.height) {
                     Timber.tag(TAG).w("generateBlurredBitmap: Invalid crop area after adjustment. cropX=$cropX, cropY=$cropY, width=$width, height=$height, sourceWidth=${sourceBitmap.width}, sourceHeight=${sourceBitmap.height}")
                    sourceBitmap.recycle()
                    return null
                }
            }

            val croppedBitmap: Bitmap = try {
                Bitmap.createBitmap(sourceBitmap, cropX, cropY, width, height)
            } catch (e: OutOfMemoryError) {
                // Timber.tag(TAG).e(e, "OutOfMemoryError creating croppedBitmap.")
                com.socialplay.gpark.util.BitmapRecycleUtil.safeRecycle(sourceBitmap)
                return null
            } catch (e: Exception) {
                // Timber.tag(TAG).e(e, "Error creating croppedBitmap.")
                com.socialplay.gpark.util.BitmapRecycleUtil.safeRecycle(sourceBitmap)
                return null
            }
            com.socialplay.gpark.util.BitmapRecycleUtil.safeRecycle(sourceBitmap) // 源位图副本不再需要
            // Timber.tag(TAG).d("generateBlurredBitmap: Cropped bitmap created ${croppedBitmap.width}x${croppedBitmap.height}")


            // 3. 缩放与模糊
            val useDownscaling = blurRadius > MAX_BLUR_RADIUS_DIRECT
            val toBlurBitmap: Bitmap
            val effectiveRadius: Float

            if (useDownscaling) {
                val scaledWidth = (croppedBitmap.width / DOWNSCALE_FACTOR).toInt()
                val scaledHeight = (croppedBitmap.height / DOWNSCALE_FACTOR).toInt()
                if (scaledWidth == 0 || scaledHeight == 0) {
                    // Timber.tag(TAG).w("generateBlurredBitmap: Scaled dimensions are zero. scaledWidth=$scaledWidth, scaledHeight=$scaledHeight")
                    com.socialplay.gpark.util.BitmapRecycleUtil.safeRecycle(croppedBitmap)
                    return null
                }
                toBlurBitmap = try {
                    croppedBitmap.scale(scaledWidth, scaledHeight)
                } catch (e: OutOfMemoryError) {
                    // Timber.tag(TAG).e(e, "OutOfMemoryError creating scaled toBlurBitmap.")
                    com.socialplay.gpark.util.BitmapRecycleUtil.safeRecycle(croppedBitmap)
                    return null
                } catch (e: Exception) {
                    // Timber.tag(TAG).e(e, "Error creating scaled toBlurBitmap.")
                    com.socialplay.gpark.util.BitmapRecycleUtil.safeRecycle(croppedBitmap)
                    return null
                }
                // Timber.tag(TAG).d("generateBlurredBitmap: Downscaled bitmap created ${toBlurBitmap.width}x${toBlurBitmap.height}")
                com.socialplay.gpark.util.BitmapRecycleUtil.safeRecycle(croppedBitmap)
                effectiveRadius = (blurRadius / DOWNSCALE_FACTOR).coerceIn(1f, MAX_BLUR_RADIUS_DIRECT)
            } else {
                toBlurBitmap = croppedBitmap // No recycle here, it's the same bitmap
                effectiveRadius = blurRadius
                // Timber.tag(TAG).d("generateBlurredBitmap: Using original cropped bitmap for blur.")
            }

            if (blurRenderScript == null) {
                // Timber.tag(TAG).w("generateBlurredBitmap: blurRenderScript is null, attempting to reinitialize.")
                setupBlur() // Attempt to reinitialize if null
                if (blurRenderScript == null) {
                    // Timber.tag(TAG).e("generateBlurredBitmap: blurRenderScript still null after reinitialization attempt.")
                    com.socialplay.gpark.util.BitmapRecycleUtil.safeRecycle(toBlurBitmap)
                    return null
                }
            }
            var blurred: Bitmap? = try {
                // Timber.tag(TAG).d("generateBlurredBitmap: Applying blur with radius: $effectiveRadius")
                blurRenderScript?.blur(toBlurBitmap, effectiveRadius)
            } catch (e: Exception) {
                // Timber.tag(TAG).e(e, "Error applying blur using blurRenderScript.")
                null
            } finally {
                // blurRenderScript.blur 不会回收 toBlurBitmap, 我们需要手动回收
                com.socialplay.gpark.util.BitmapRecycleUtil.safeRecycle(toBlurBitmap)
                // Timber.tag(TAG).d("generateBlurredBitmap: Recycled toBlurBitmap.")
            }


            if (blurred == null) {
                // Timber.tag(TAG).w("generateBlurredBitmap: blurRenderScript?.blur returned null.")
                return null
            }
            // Timber.tag(TAG).d("generateBlurredBitmap: Blurred bitmap created ${blurred.width}x${blurred.height}")


            // 4. 如果是降采样，需要将模糊后的小图放大回原始尺寸
            if (useDownscaling) { // 'blurred' is not null here due to the check above
                if (width > 0 && height > 0) {
                    val upscaled: Bitmap? = try {
                        Bitmap.createScaledBitmap(blurred, width, height, true)
                    } catch (e: OutOfMemoryError) {
                        // Timber.tag(TAG).e(e, "OutOfMemoryError creating upscaled bitmap.")
                        null // Keep original blurred to be recycled
                    } catch (e: Exception) {
                        // Timber.tag(TAG).e(e, "Error creating upscaled bitmap.")
                        null // Keep original blurred to be recycled
                    }

                    if (upscaled != null) {
                        // Timber.tag(TAG).d("generateBlurredBitmap: Upscaled bitmap created ${upscaled.width}x${upscaled.height}")
                        com.socialplay.gpark.util.BitmapRecycleUtil.safeRecycle(blurred)
                        blurred = upscaled
                    } else {
                        // Timber.tag(TAG).w("generateBlurredBitmap: Upscaling failed, returning non-upscaled (but blurred) bitmap.")
                        // If upscaling fails, we might return the smaller blurred bitmap or null.
                        // For now, let's assume we must return null if upscaling fails, to avoid size mismatch.
                        // However, the original code returned the smaller one.
                                    // To be safe and avoid further issues, if upscaled is null, recycle current blurred and return null
            com.socialplay.gpark.util.BitmapRecycleUtil.safeRecycle(blurred)
                        return null
                    }
                } else {
                    // Timber.tag(TAG).w("generateBlurredBitmap: Target width or height is zero for upscaling. width=$width, height=$height")
                    com.socialplay.gpark.util.BitmapRecycleUtil.safeRecycle(blurred) // Clean up before returning null
                    return null
                }
            }
            // Timber.tag(TAG).d("generateBlurredBitmap: Finished successfully.")
            return blurred
        } catch (e: Exception) {
            // Timber.tag(TAG).e(e, "Unexpected error in generateBlurredBitmap.")
            return null
        }
    }

    /**
     * 启用或禁用背景模糊效果
     * @param enabled 是否启用
     * @param radius 模糊半径 (像素), 建议值 1-100
     * @param overlayColor 叠加层的颜色, e.g., Color.argb(128, 0, 0, 0)
     */
    fun setBackgroundBlur(enabled: Boolean, radius: Float = 25f, overlayColor: Int = Color.argb(128, 0, 0, 0), cacheKey: String? = null) {
        if (blurEnabled == enabled && blurRadius == radius && blurOverlayColor == overlayColor) {
            return
        }
        // Timber.tag(TAG).d("ID[${this.hashCode()}] setBackgroundBlur:${cacheKey.hashCode()} equals:${cacheKey == blurCacheKey} enabled=$enabled, radius=$radius, overlayColor=$overlayColor cacheKey: $cacheKey  blurCacheKey:$blurCacheKey")
        blurEnabled = enabled
        blurRadius = radius.coerceIn(1f, 100f) // 半径上限提高到100
        blurOverlayColor = overlayColor

        isBlurDirty = true

        if (enabled && !isInEditMode) {
            setupBlur()
        } else if (!enabled && !isInEditMode) {
            // Timber.tag(TAG).d("setBackgroundBlur: Disabling blur, cleaning up resources.")
//            try {
//                blurredBitmap?.recycle()
//                // Timber.tag(TAG).d("setBackgroundBlur: Recycled existing blurredBitmap.")
//            } catch (e: Exception) {
//                // Timber.tag(TAG).e(e, "Error recycling blurredBitmap in setBackgroundBlur.")
//            }
//            blurredBitmap = null

//            try {
//                blurRenderScript?.destroy()
//                // Timber.tag(TAG).d("setBackgroundBlur: Destroyed blurRenderScript.")
//            } catch (e: Exception) {
//                // Timber.tag(TAG).e(e, "Error destroying blurRenderScript in setBackgroundBlur.")
//            }
//            blurRenderScript = null
        }

//        invalidate()
    }



    /**
     * 设置用于生成模糊背景的源视图。
     * @param source 任何你希望从中截取背景的View。
     */
    fun setBlurSource(source: View?) {
        setBlurSource(source, null)
    }

    /**
     * 设置用于生成模糊背景的源视图。
     * @param source 任何你希望从中截取背景的View。
     * @param cacheKey 用于缓存的唯一键，例如图片URL。如果为null，则不使用缓存。
     */
    fun setBlurSource(source: View?, cacheKey: String?) {
        // Timber.tag(TAG).d("ID[${this.hashCode()}] setBlurSource:${cacheKey.hashCode()} equals:${cacheKey == blurCacheKey}, cacheKey: $cacheKey  blurCacheKey:$blurCacheKey")
        blurSourceViewRef = if (source != null) WeakReference(source) else null
        blurCacheKey = cacheKey
        isBlurDirty = true
        isStart = false

        // 在这里置空bitmap，是为了处理只执行attach和detach的情况下能有缓存Bitmap用，所以不在detach的函数里面置空bitmap
        blurredBitmap = null
        invalidate()
    }

    fun startBlur(source: View?, cacheKey: String?) {
        // Timber.tag(TAG).d("ID[${this.hashCode()}] startBlur:${cacheKey.hashCode()} equals:${cacheKey == blurCacheKey}, cacheKey: $cacheKey  blurCacheKey:$blurCacheKey")
        if (blurCacheKey != null && blurCacheKey != cacheKey) {
            // 换源了，需要让生成图片的地方判断源不一样的话就别保存，绘制也别继续绘制
            isChangedSource = true
            preCacheKey = blurCacheKey
            // 跟设置的源不一样了，不管了，还会等下一次的
            return
        } else {
            isChangedSource = false
            preCacheKey = null
        }
        blurCacheKey = cacheKey
        isStart = true
        invalidate()
    }

    // 公共方法用于动态调整属性

    /**
     * 设置光照角度
     * @param angle 角度（0-360度）
     */
    fun setLightAngle(angle: Float) {
        lightAngle = angle % 360f
        updateBorderShader()
        invalidate()
    }

    /**
     * 设置边框厚度
     * @param thickness 厚度（像素）
     */
    fun setBorderThickness(thickness: Float) {
        borderThickness = thickness
        updatePaths()
        invalidate()
    }

    /**
     * 设置圆角半径
     * @param radius 半径（像素）
     */
    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        updatePaths()
        invalidate()
    }

    /**
     * 设置高光强度
     * @param intensity 强度（0.0-1.0）
     */
    fun setHighlightIntensity(intensity: Float) {
        highlightIntensity = intensity.coerceIn(0f, 1f)
        updateBorderShader()
        invalidate()
    }

    /**
     * 设置高光宽度
     * @param width 宽度比例 (0.0-1.0)
     */
    fun setHighlightWidth(width: Float) {
        highlightWidth = width.coerceIn(0f, 1f)
        updateBorderShader()
        invalidate()
    }

    /**
     * 设置阴影强度
     * @param intensity 强度（0.0-1.0）
     */
    fun setShadowIntensity(intensity: Float) {
        shadowIntensity = intensity.coerceIn(0f, 1f)
        updateBorderShader()
        invalidate()
    }

    /**
     * 设置金属基础颜色
     * @param color 颜色值
     */
    fun setMetallicBaseColor(color: Int) {
        metallicBaseColor = color
        updateBorderShader()
        invalidate()
    }

    /**
     * 动画旋转光照角度
     * @param targetAngle 目标角度
     * @param duration 动画时长（毫秒）
     */
    fun animateLightAngle(targetAngle: Float, duration: Long = 1000) {
        val startAngle = lightAngle
        val endAngle = targetAngle % 360f

        val animator = android.animation.ValueAnimator.ofFloat(startAngle, endAngle).apply {
            this.duration = duration
            addUpdateListener { animation ->
                setLightAngle(animation.animatedValue as Float)
            }
        }
        animator.start()
    }

    /**
     * 开始连续旋转动画
     * @param duration 一圈的时长（毫秒）
     */
    fun startContinuousRotation(duration: Long = 5000) {
        val animator = android.animation.ValueAnimator.ofFloat(0f, 360f).apply {
            this.duration = duration
            repeatCount = android.animation.ValueAnimator.INFINITE
            addUpdateListener { animation ->
                setLightAngle(animation.animatedValue as Float)
            }
        }
        animator.start()
        tag = animator // 保存动画引用以便停止
    }

    /**
     * 停止连续旋转动画
     */
    fun stopContinuousRotation() {
        (tag as? android.animation.ValueAnimator)?.cancel()
        tag = null
    }

    /**
     * 获取当前光照角度
     */
    fun getLightAngle(): Float = lightAngle

    /**
     * 获取当前边框厚度
     */
    fun getBorderThickness(): Float = borderThickness

    /**
     * 获取当前圆角半径
     */
    fun getCornerRadius(): Float = cornerRadius

    /**
     * 设置调试模式（不挖空中心，便于观察高光效果）
     */
    fun setDebugMode(debug: Boolean) {
        debugMode = debug
        invalidate()
    }

    /**
     * 设置外发光强度（不透明度）
     * @param intensity 强度（0.0-1.0）
     */
    fun setGlowIntensity(intensity: Float) {
        glowIntensity = intensity.coerceIn(0f, 1f)
        invalidate()
    }

    /**
     * 设置外发光颜色
     * @param color 颜色值
     */
    fun setGlowColor(color: Int) {
        glowColor = color
        invalidate()
    }

    /**
     * 获取外发光强度
     */
    fun getGlowIntensity(): Float = glowIntensity

    /**
     * 获取外发光颜色
     */
    fun getGlowColor(): Int = glowColor

    /**
     * 设置外发光半径
     * @param radius 半径（像素）
     */
    fun setGlowRadius(radius: Float) {
        glowRadius = radius
        invalidate()
    }

    /**
     * 获取外发光半径
     */
    fun getGlowRadius(): Float = glowRadius

    /**
     * 设置模糊强度（模糊后位图的透明度）
     * @param intensity 强度 (0.0 - 1.0)
     */
    fun setBlurIntensity(intensity: Float) {
        this.blurIntensity = intensity.coerceIn(0f, 1f)
        invalidate() // 只需要重绘，不需要重新生成模糊
    }

    /**
     * 从当前视图的Context中获取宿主Activity。
     */
    private fun getActivity(): Activity? {
        var ctx = context
        while (ctx is ContextWrapper) {
            if (ctx is Activity) {
                return ctx
            }
            ctx = ctx.baseContext
        }
        return null
    }


}
