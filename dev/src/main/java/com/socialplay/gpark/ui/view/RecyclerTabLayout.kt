package com.socialplay.gpark.ui.view

import android.animation.Animator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.os.Parcelable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.socialplay.gpark.R

class RecyclerTabLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

    private val recyclerView: RecyclerView
    private val indicatorPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val indicatorRect = RectF()
    private var indicatorColor: Int = 0
    private var indicatorHeight: Float = 6f
    private var indicatorRadius: Float = 3f
    private var indicatorWidth: Float = 0f

    private var tabAdapter: TabAdapter? = null
    private var selectedIndex = 0

    // indicator动画相关
    private var indicatorLeft = 0f
    private var indicatorRight = 0f
    private var targetLeft = 0f
    private var targetRight = 0f
    private var indicatorAnimator: ValueAnimator? = null
    private var isIndicatorAnimating = false

    // 字体和间距相关属性
    private var selectedTextSizeSp: Float = 14f
    private var unselectedTextSizeSp: Float = 14f
    private var selectedTextColor: Int = 0
    private var unselectedTextColor: Int = 0
    private var selectedTextAppearance: Int = R.style.MetaTextView_S14_PoppinsMedium500
    private var unselectedTextAppearance: Int = R.style.MetaTextView_S14_PoppinsRegular400
    private var tabHeightPx: Int = 0
    private var indicatorMarginTop: Float = 0f

    var onTabSelected: ((Int) -> Unit)? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.view_recycler_tab_layout, this, true)
        recyclerView = findViewById(R.id.recyclerTab)
        recyclerView.layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
        indicatorColor = context.getColor(R.color.color_FFDD70)
        indicatorPaint.color = indicatorColor
        indicatorHeight = resources.getDimension(R.dimen.dp_4)
        indicatorRadius = indicatorHeight / 2
        indicatorWidth = resources.getDimension(R.dimen.dp_12)
        selectedTextColor = context.getColor(R.color.color_1A1A1A)
        unselectedTextColor = context.getColor(R.color.color_666666)
        tabHeightPx = resources.getDimensionPixelSize(R.dimen.dp_40)
        indicatorMarginTop = 0f
        setWillNotDraw(false)

        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(rv: RecyclerView, dx: Int, dy: Int) {
                if (!isIndicatorAnimating) {
                    updateIndicatorOnScroll()
                }
            }
        })
    }

    private fun getTabTextCenter(itemView: View): Float {
        val tv = itemView.findViewById<TextView>(R.id.tvTab)
        val left = tv.left + tv.paddingStart
        val right = tv.right - tv.paddingEnd
        return (left + right) / 2f
    }

    private fun updateIndicatorOnScroll() {
        val holder = recyclerView.findViewHolderForAdapterPosition(selectedIndex)
        if (holder == null) {
            // 还是需要重绘一下, 当猛的左划右划时, indicator还会残留在屏幕内
            invalidate()
            return
        }
        val itemView = holder.itemView
        val center = getTabTextCenter(itemView)
        indicatorLeft = center - indicatorWidth / 2f
        indicatorRight = center + indicatorWidth / 2f
        invalidate()
    }

    fun setTabs(tabs: List<String>, defaultIndex: Int = 0) {
        tabAdapter = TabAdapter(tabs) { index ->
            selectTab(index, true)
            onTabSelected?.invoke(index)
        }
        recyclerView.adapter = tabAdapter
        post {
            selectTab(defaultIndex, false)
        }
    }

    fun getTabs(): List<String>? {
        return tabAdapter?.tabs
    }

    fun getSelectedIndex(): Int {
        return selectedIndex
    }

    // 判断item是否完全可见
    private fun isItemCompletelyVisible(index: Int): Boolean {
        val layoutManager = recyclerView.layoutManager as? LinearLayoutManager ?: return false
        val first = layoutManager.findFirstCompletelyVisibleItemPosition()
        val last = layoutManager.findLastCompletelyVisibleItemPosition()
        return index in first..last
    }

    fun selectTab(index: Int, animate: Boolean) {
        val itemCount = tabAdapter?.getItemCount() ?: return
        if (index < 0 || index >= itemCount) {
            return
        }
        if (index == selectedIndex) return
        val oldIndex = selectedIndex
        selectedIndex = index
        tabAdapter?.setSelected(index)
        if (!isItemCompletelyVisible(index)) {
            // item不完全可见
            moveIndicatorTo(index, animate)
            recyclerView.smoothScrollToPosition(index)
        } else {
            // item完全可见，立即动画
            moveIndicatorTo(index, animate)
//            recyclerView.smoothScrollToPosition(index)
        }
    }

    private fun moveIndicatorTo(index: Int, animate: Boolean) {
        val holder = recyclerView.findViewHolderForAdapterPosition(index) ?: return
        val itemView = holder.itemView
        val center = getTabTextCenter(itemView)
        targetLeft = center - indicatorWidth / 2f
        targetRight = center + indicatorWidth / 2f
        if (!animate) {
            indicatorLeft = targetLeft
            indicatorRight = targetRight
            invalidate()
            return
        }
        indicatorAnimator?.cancel()
        val startLeft = indicatorLeft
        val startRight = indicatorRight
        isIndicatorAnimating = true
        indicatorAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = 200
            addUpdateListener {
                val fraction = it.animatedFraction
                indicatorLeft = startLeft + (targetLeft - startLeft) * fraction
                indicatorRight = startRight + (targetRight - startRight) * fraction
                invalidate()
            }
            addListener(object:Animator.AnimatorListener{
                override fun onAnimationStart(animation: Animator) {}
                override fun onAnimationEnd(animation: Animator) {
                    isIndicatorAnimating = false
                    updateIndicatorOnScroll()
                }
                override fun onAnimationCancel(animation: Animator) {}
                override fun onAnimationRepeat(animation: Animator) {}
            })
            start()
        }
    }

    // 暴露设置方法
    fun setTabTextStyle(
        selectedSizeSp: Float,
        unselectedSizeSp: Float,
        selectedColor: Int,
        unselectedColor: Int,
        selectedAppearance: Int,
        unselectedAppearance: Int
    ) {
        selectedTextSizeSp = selectedSizeSp
        unselectedTextSizeSp = unselectedSizeSp
        selectedTextColor = selectedColor
        unselectedTextColor = unselectedColor
        selectedTextAppearance = selectedAppearance
        unselectedTextAppearance = unselectedAppearance
        tabAdapter?.notifyDataSetChanged()
    }

    fun setTabHeight(heightPx: Int) {
        tabHeightPx = heightPx
        tabAdapter?.notifyDataSetChanged()
    }

    fun setIndicatorMarginTop(marginTopPx: Float) {
        indicatorMarginTop = marginTopPx
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 只有tab数量大于1时才绘制indicator
        if ((tabAdapter?.itemCount ?: 0) <= 1) return
        val holder = recyclerView.findViewHolderForAdapterPosition(selectedIndex)
        if (holder == null) return
        if (indicatorRight > indicatorLeft) {
            val top = height - indicatorHeight - indicatorMarginTop
            indicatorRect.set(indicatorLeft, top, indicatorRight, top + indicatorHeight)
            canvas.drawRoundRect(indicatorRect, indicatorRadius, indicatorRadius, indicatorPaint)
        }
    }

    fun restoreInstanceState(state: Parcelable) {
        recyclerView.layoutManager?.onRestoreInstanceState(state)
    }

    fun saveInstanceState(): Parcelable? {
        return recyclerView.layoutManager?.onSaveInstanceState()
    }


    private inner class TabAdapter(
        val tabs: List<String>,
        private val onClick: (Int) -> Unit
    ) : RecyclerView.Adapter<TabViewHolder>() {
        private var selected = 0
        fun setSelected(index: Int) {
            val old = selected
            selected = index
            notifyItemChanged(old)
            notifyItemChanged(selected)
        }
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_recycler_tab, parent, false)
            // 设置item高度
            view.layoutParams = RecyclerView.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, tabHeightPx)
            return TabViewHolder(view)
        }
        override fun getItemCount() = tabs.size
        override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
            holder.bind(tabs[position], position == selected)
            holder.itemView.setOnClickListener { onClick(position) }
        }
    }

    private inner class TabViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        fun bind(text: String, selected: Boolean) {
            val tv = itemView.findViewById<TextView>(R.id.tvTab)
            tv.text = text
            tv.isSelected = selected
            tv.textSize = if (selected) selectedTextSizeSp else unselectedTextSizeSp
            tv.setTextColor(if (selected) selectedTextColor else unselectedTextColor)
            tv.setTextAppearance(if (selected) selectedTextAppearance else unselectedTextAppearance)
        }
    }
} 