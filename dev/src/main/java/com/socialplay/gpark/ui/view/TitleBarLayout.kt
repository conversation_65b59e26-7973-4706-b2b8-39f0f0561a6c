package com.socialplay.gpark.ui.view

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.widget.ImageViewCompat
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ViewTitleBarBinding
import com.socialplay.gpark.util.extension.setFontWeight700
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2021/06/11
 *     desc   :
 */
class TitleBarLayout : FrameLayout {

    private var binding: ViewTitleBarBinding = ViewTitleBarBinding.inflate(LayoutInflater.from(context), this, true)

    val titleView: TextView get() = binding.tvTitle


    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        if (attrs != null) {
            val typedArray = context.obtainStyledAttributes(attrs, R.styleable.TitleBarLayout)
            setTitle(typedArray.getString(R.styleable.TitleBarLayout_title_text))
            setTitleTextColor(typedArray.getColor(R.styleable.TitleBarLayout_title_text_color, ContextCompat.getColor(context, R.color.colorAccent)))
            setDividerVisibility(typedArray.getBoolean(R.styleable.TitleBarLayout_isDividerVisible, true))
            setRightIconVisibility(typedArray.getBoolean(R.styleable.TitleBarLayout_isRightIconVisible, false))
            typedArray.getDrawable(R.styleable.TitleBarLayout_rightIcon)?.let(::setRightIconDrawable)
            if (typedArray.hasValue(R.styleable.TitleBarLayout_background_color)) {
                setBarBackgroundDrawable(typedArray.getDrawable(R.styleable.TitleBarLayout_background_color))
            }

            if (typedArray.hasValue(R.styleable.TitleBarLayout_back_icon)) {
                setBackIcon(typedArray.getDrawable(R.styleable.TitleBarLayout_back_icon))
            }

            if (typedArray.hasValue(R.styleable.TitleBarLayout_back_icon_tint)) {
                setBackIconTint(typedArray.getColor(R.styleable.TitleBarLayout_back_icon_tint, -1))
            }

            if(typedArray.hasValue(R.styleable.TitleBarLayout_title_divider_color)){
                setDividerColor(typedArray.getColor(R.styleable.TitleBarLayout_title_divider_color, -1))
            }

            if(typedArray.hasValue(R.styleable.TitleBarLayout_showRoleDress)){
                setShowRoleDress(typedArray.getBoolean(R.styleable.TitleBarLayout_showRoleDress, false))
            }

            if (typedArray.hasValue(R.styleable.TitleBarLayout_roleDressText)) {
                setRoleDressText(typedArray.getString(R.styleable.TitleBarLayout_roleDressText))
            }
            if(typedArray.hasValue(R.styleable.TitleBarLayout_rightText)){
                setRightText(typedArray.getString(R.styleable.TitleBarLayout_rightText))
            }
            if(typedArray.hasValue(R.styleable.TitleBarLayout_showRightText)){
                setShowRightText(typedArray.getBoolean(R.styleable.TitleBarLayout_showRightText, false))
            }
            if(typedArray.hasValue(R.styleable.TitleBarLayout_showBackIcon)){
                showBackIcon(typedArray.getBoolean(R.styleable.TitleBarLayout_showBackIcon, true))
            }
            typedArray.recycle()
        }
        initView()
    }

    private fun showBackIcon(isShow: Boolean) {
        binding.ibBack.isInvisible = !isShow
    }

    fun setShowRightText(visible: Boolean) {
        binding.tvSkip.visible(visible)
    }

    private fun setRightText(des: String?) {
        binding.tvSkip.text = des
    }

    private fun initView() {
        binding.tvTitle.setFontWeight700()
    }


    fun setBackIconTint(tintColor: Int) {
        ImageViewCompat.setImageTintList(binding.ibBack, ColorStateList.valueOf(tintColor))
    }

    fun setBackIcon(icon: Drawable?) {
        binding.ibBack.setImageDrawable(icon)
    }

    fun setTitleTextColor(color: Int) {
        binding.tvTitle.setTextColor(color)
    }

    fun setDividerVisibility(visible: Boolean) {
        binding.viewTitleDivider.visible(visible)
    }


    fun setDividerColor(dividerColor: Int) {
        binding.viewTitleDivider.setBackgroundColor(dividerColor)
    }

    fun setTitle(title: String?) {
        binding.tvTitle.text = title
    }

    fun setBarBackgroundDrawable(color: Drawable?) {
        binding.root.background = color ?: ContextCompat.getDrawable(context, R.color.default_background_color)
    }

    fun setBarBackgroundColor(color: Int) {
        binding.root.setBackgroundColor(color)
    }


    fun setOnBackClickedListener(listener: (View) -> Unit) {
        binding.ibBack.setOnAntiViolenceClickListener(listener)
    }

//    fun setOnBackClickedListener(listener: OnClickListener) {
//        binding.ibBack.setOnClickListener(listener)
//    }

    fun setOnBackAntiViolenceClickedListener(listener: (View) -> Unit) {
        binding.ibBack.setOnAntiViolenceClickListener(listener)
    }

    fun setSkipClickedListener(listener: OnClickListener) {
        binding.tvSkip.setOnClickListener(listener)
    }

    fun setBackIconVisible(visible: Boolean) {
        binding.ibBack.isInvisible = !visible
    }

    fun setShowRoleDress(show: Boolean) {
        binding.tvRoleDress.isVisible = show
    }

    fun setRoleDressText(text: String?) {
        binding.tvRoleDress.text = text ?: ""
    }

    fun setRightIconDrawable(icon: Drawable?) {
        binding.ibRightIcon.setImageDrawable(icon)
    }

    fun setRightIconVisibility(visible: Boolean) {
        binding.ibRightIcon.visible(visible)
    }

    fun setOnRightIconClickedListener(antiViolence: Boolean = false, listener: (View) -> Unit) {
        binding.ibRightIcon.setOnAntiViolenceClickListener({ antiViolence }, listener)
    }

    fun setOnRightIconClickedListener(listener: OnClickListener?) {
        binding.ibRightIcon.setOnClickListener(listener)
    }

    fun setTintColor(textColor: Int) {
        binding.tvTitle.setTextColor(textColor)
        ImageViewCompat.setImageTintList(binding.ibBack, ColorStateList.valueOf(textColor))
        ImageViewCompat.setImageTintList(binding.ibRightIcon, ColorStateList.valueOf(textColor))
    }
}