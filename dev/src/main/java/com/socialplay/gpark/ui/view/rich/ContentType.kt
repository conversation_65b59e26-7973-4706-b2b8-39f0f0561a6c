package com.socialplay.gpark.ui.view.rich

object ContentType {
    //文字类型
    const val CONTENT_TEXT = "block_normal_text"
    //游戏卡片类型
    const val CONTENT_GAME = "game"
    // 游戏卡片类型
    const val CONTENT_UGC_GAME = "ugcGame"
    //文章链接
    const val CONTENT_LINK = "link"
    //视频
    const val CONTENT_VIDEO = "video"
    //图片
    const val CONTENT_IMG = "img"
    // 穿搭卡片
    const val CONTENT_OUTFIT = "outfit"
    // 拍剧卡片
    const val CONTENT_MOMENT = "moment"
    //头部内容部分
    const val COMMENT = "comment"
    //文字颜色，大小，是否加粗，是否有下划线，是否有中划线
    const val TEXT_DP_SIZE = "dpSize"
    const val TEXT_BOLD = "bold"
    const val TEXT_ITALIC = "italic"
    const val TEXT_CORLOR = "color"
    const val TEXT_UNDLINE = "underline"
    const val TEXT_DELETELINE = "strike_through"
    /**
     * 可以响应点击操作的类型
     */
    //链接
    const val TEXT_LINKE = "link"
    //帖子
    const val TEXT_POST = "post"
    //下载链接
    const val TEXT_DOWNLOAD_LINK ="downloadLink"
    //话题
    const val TEXT_TOPIC = "topic"
    //无序列表
    const val CONTENT_BULLET_LIST ="bulletList"
    //有序列表
    const val CONTENT_ORDERED_LIST ="orderedList"
    //列表的ITEM
    const val CONTENT_LIST_ITEM = "listItem"
    //at好友
    const val TEXT_AT_FRIEND = "at"
    //标题
    const val CONTENT_HEAD = "block_head_text"
}
