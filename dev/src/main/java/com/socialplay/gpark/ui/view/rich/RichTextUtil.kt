package com.socialplay.gpark.ui.view.rich

import android.content.Context
import android.graphics.Paint
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.util.SpannableHelper

/**
 * 富文本解析工具类
 */
class RichTextUtil {

    private fun handleTextInlineStyle(
        context: Context,
        linkClickListener: Link<PERSON>lickListenerWrapper,
        tvContent: TextView,
        contentText: String?,
        inlineStyleEntities: List<InlineStyleEntitiesBean>
    ) {
        var sb = SpannableHelper.valueOf(contentText)
        inlineStyleEntities.forEach {
            when (it.inlineType) {
                ContentType.TEXT_DP_SIZE -> sb =
                    TextConvertUtil.getContentTextSize(it, sb)

                ContentType.TEXT_CORLOR -> sb =
                    TextConvertUtil.getContentTextColor(it, sb)

                ContentType.TEXT_BOLD -> sb =
                    TextConvertUtil.getContentTextBold(it, sb)

                ContentType.TEXT_ITALIC -> sb =
                    TextConvertUtil.getContentTextItalic(it, sb)

                ContentType.TEXT_UNDLINE -> tvContent.paint.flags = Paint.UNDERLINE_TEXT_FLAG

                ContentType.TEXT_DELETELINE -> tvContent.paint.flags = Paint.STRIKE_THRU_TEXT_FLAG

                ContentType.TEXT_LINKE, ContentType.TEXT_DOWNLOAD_LINK, ContentType.TEXT_TOPIC, ContentType.TEXT_AT_FRIEND -> {
                    sb = TextConvertUtil.getTextLink(
                        context,
                        it, SpannableHelper.valueOf(sb), linkClickListener
                    )
                }

                ContentType.TEXT_POST -> {
                    sb = TextConvertUtil.getTextLink(
                        context,
                        it, SpannableHelper.valueOf(sb), linkClickListener
                    )
                }
            }
            tvContent.text = sb
        }
    }

}

class LinkClickListenerWrapper(val fragment: Fragment) : LinkClickListener {
    override fun onClick(inlineStyleEntitiesBean: InlineStyleEntitiesBean) {
        when (inlineStyleEntitiesBean.inlineType) {
            ContentType.TEXT_TOPIC -> {
                // 跳转话题
//                    CommunityRouter.openTopicDetailPage(this@ArticleDetailFragment) {
//                        tagId = inlineStyleEntitiesBean.tagId ?: 0
//                        tagName = inlineStyleEntitiesBean.tagName ?: ""
//                        source = SOURCE_TOPIC_ARTICLE_DETAIL
//                        resId = args.resId
//                    }
            }

            ContentType.TEXT_POST -> {
                inlineStyleEntitiesBean.resId?.let {
                    // 跳转帖子
//                        CommunityRouter.openArticleDetailPage(this@ArticleDetailFragment) {
//                            resId = it
//                            categoryId = CommunityCategoryId.ARTICLE_SOURCE_ARTICLE_LINK
//                            source = GameCircleConstants.CLICK_ARTICLE_SOURCE_ARTICLE_LINK
//                        }
                }
            }

            ContentType.TEXT_LINKE -> {
                inlineStyleEntitiesBean.href?.let {
                    MetaRouter.Web.navigate(fragment, url = it)
                    // 打开web
//                        CommunityRouter.openWebPage(this@ArticleDetailFragment, url = it)
                }
            }

            ContentType.TEXT_AT_FRIEND -> {
                val atFriendUid = inlineStyleEntitiesBean.uid
                if (atFriendUid.isNullOrEmpty()) {
                    return
                }
                MetaRouter.Profile.other(fragment, atFriendUid, "at_friend")
                // 打开个人页面
//                    CommunityRouter.openUserPage(
//                        this@ArticleDetailFragment,
//                        "atFriend",
//                        atFriendUid
//                    )
            }

            else -> {

            }
        }
    }
}