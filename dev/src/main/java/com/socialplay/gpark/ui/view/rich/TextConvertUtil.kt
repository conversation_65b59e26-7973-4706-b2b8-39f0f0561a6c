package com.socialplay.gpark.ui.view.rich

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.Gravity
import com.socialplay.gpark.util.SpannableHelper

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2022/04/14
 *     desc   :
 *
 */
object TextConvertUtil {

//    /**
//     * 设置评论数
//     */
//    fun setCommentCount(tv: TextView, commentCount: Long) {
//        tv.text = tv.context.getString(R.string.community_article_comment_count_most, if (commentCount == 0L) "" else UnitUtil.formatAmount(commentCount))
//    }

//    fun setDetailCommentCount(tv: TextView, commentCount: Long) {
//        tv.text = if (commentCount == 0L) "" else UnitUtil.formatAmount(commentCount)
//    }

    /**
     * 设置字体
     */
    fun getContentTextSize(it: InlineStyleEntitiesBean, content: SpannableStringBuilder?): SpannableStringBuilder {
        val spannableString = SpannableHelper.valueOf(content)
        try {
            spannableString.setSpan(it.dpSize?.toInt()?.let { it1 -> AbsoluteSizeSpan(it1, true) }, it.offset, it.offset + it.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return spannableString
    }

    /**
     * 设置字体颜色
     */
    fun getContentTextColor(it: InlineStyleEntitiesBean, content: SpannableStringBuilder?): SpannableStringBuilder {
        val spannableString = SpannableHelper.valueOf(content)
        try {
            spannableString.setSpan(ForegroundColorSpan(Color.parseColor(it.color?.trim())), it.offset, it.offset + it.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return spannableString
    }

    /**
     * 设置字体加粗
     */
    fun getContentTextBold(it: InlineStyleEntitiesBean, content: SpannableStringBuilder?): SpannableStringBuilder {
        val spannableString = SpannableHelper.valueOf(content)
        val span = StyleSpan(Typeface.BOLD)
        try {
            spannableString.setSpan(span, it.offset, it.offset + it.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return spannableString
    }

    /**
     * 设置斜体
     */
    fun getContentTextItalic(it: InlineStyleEntitiesBean, content: SpannableStringBuilder?): SpannableStringBuilder {
        val spannableString = SpannableHelper.valueOf(content)
        val span = StyleSpan(Typeface.ITALIC)
        try {
            spannableString.setSpan(span, it.offset, it.offset + it.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        } catch (e: Exception) {
            e.toString()
        }
        return spannableString
    }


    /**
     * 设置字体链接
     */
    fun getTextLink(
        context: Context,
        inlineStyleEntitie: InlineStyleEntitiesBean,
        sb: SpannableStringBuilder,
        linkClickListener: LinkClickListener?
    ): SpannableStringBuilder {
        val stringBuilder = SpannableHelper.valueOf(sb)
        try {
            val start = inlineStyleEntitie.offset
            var end = start + inlineStyleEntitie.length
            //步骤顺序不能变
            //第一步文字点击
            stringBuilder.setSpan(
                TextLinkClickable(
                    context,
                    inlineStyleEntitie,
                    linkClickListener
                ), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )

        } catch (e: Exception) {
            e.printStackTrace()
        }
        return stringBuilder
    }

    //返回各级标题对应的字号
    fun getHeadStyleSize(level: Int?): Float {
        return when (level) {
            1 -> {
                24f
            }

            2 -> {
                20f
            }

            3 -> {
                16f
            }

            else -> {
                16f
            }
        }
    }

    //返回对齐方式
    fun getTextGravity(align: String?): Int {
        return when (align) {
            "left" -> {
                Gravity.LEFT
            }

            "right" -> {
                Gravity.RIGHT
            }

            "center" -> {
                Gravity.CENTER
            }

            else -> {
                Gravity.LEFT
            }
        }
    }

}