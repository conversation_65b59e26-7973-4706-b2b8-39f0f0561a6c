package com.socialplay.gpark.ui.view.rich

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.View
import com.socialplay.gpark.R
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.getColorByRes

/**
 * 文字链接点击处理
 */
class TextLinkClickable(var context: Context?, private val inlineStyleEntitie: InlineStyleEntitiesBean, val linkClickListener: LinkClickListener?) : ClickableSpan() {
    override fun onClick(widget: View) {
        when (inlineStyleEntitie.inlineType) {
            ContentType.TEXT_DOWNLOAD_LINK -> {
                openBrowser()
            }

            else -> {
                linkClickListener?.onClick(inlineStyleEntitie)
            }
        }
    }

    /**
     * 文字下载链接跳转
     * 调用第三方浏览器打开
     */
    private fun openBrowser() {
        val intent = Intent()
        intent.action = Intent.ACTION_VIEW
        intent.data = Uri.parse(inlineStyleEntitie.href)
        // 注意此处的判断intent.resolveActivity()可以返回显示该Intent的Activity对应的组件名
        context?.let {
            if (intent.resolveActivity(it.packageManager) != null) {
                context?.startActivity(Intent.createChooser(intent, it.getString(R.string.community_please_choose_browser)))
            } else {
                ToastUtil.showLong(it.getString(R.string.community_please_download_browser))
            }
        }
    }

    override fun updateDrawState(ds: TextPaint) {
        super.updateDrawState(ds)

        if (!inlineStyleEntitie.color.isNullOrBlank()) {
            ds.color = Color.parseColor(inlineStyleEntitie.color) // 字体颜色（前景色）
            ds.bgColor = Color.TRANSPARENT  // 背景颜色
            ds.linkColor = Color.parseColor(inlineStyleEntitie.color)  // 链接颜色
            ds.isUnderlineText = false
        } else {
            context?.let {
                ds.color = it.getColorByRes(R.color.color_4AB4FF)
                ds.isUnderlineText = true
            }
        }

    }

}

public interface LinkClickListener {
    fun onClick(inlineStyleEntitiesBean: InlineStyleEntitiesBean)
}