package com.socialplay.gpark.ui.web

import androidx.fragment.app.Fragment
import com.meta.web.WebRouter
import com.meta.web.model.EmbeddedWebArgsBuilder
import com.meta.web.model.EmbeddedWebFragmentArgs
import com.socialplay.gpark.contract.web.extra
import com.socialplay.gpark.contract.web.fromTab
import com.socialplay.gpark.contract.web.gameId
import com.socialplay.gpark.contract.web.gamePackageName
import com.socialplay.gpark.contract.web.isCommunity
import com.socialplay.gpark.contract.web.isShowMetaAppShare
import com.socialplay.gpark.function.analytics.resid.ResIdBean

object WebPageFactory {

    fun createEmbedded(
        url: String,
        gamePackageName: String? = null,
        isCommunity: Boolean = false,
        isMetaAppShare: Boolean = false,
        from: String? = "inner",
        needWebLifecycle: Boolean = true,
        textZoom: Int = -1,
        resIdBean: ResIdBean? = null,
        gameId: String? = null,
        extra: String? = null,
        fromTab: Boolean = false,
        preUnique: String? = null,
        backToClose: Boolean = false,
        reload: Boolean = true,
        backToRelease: Boolean = true,
    ): Fragment {
        return WebRouter.createEmbeddedWebFragment(
            url = url, resIdBean = resIdBean ?: ResIdBean()
        ) {
            this.gamePackageName = gamePackageName
            this.isCommunity = isCommunity
            this.from = from
            this.fromTab = fromTab
            this.textZoom = textZoom
            this.gameId = gameId
            this.extra = extra
            this.isShowMetaAppShare = isMetaAppShare
        }
    }

    fun buildEmbeddedWebFragmentArgs(
        url: String, resIdBean: ResIdBean? = null, builder: EmbeddedWebArgsBuilder.() -> Unit = {}
    ): EmbeddedWebFragmentArgs {

        val paramBuilder = EmbeddedWebArgsBuilder()
        return builder(paramBuilder).let { paramBuilder.build(url, resIdBean ?: ResIdBean()) }
    }
}