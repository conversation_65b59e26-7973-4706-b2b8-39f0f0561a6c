package com.socialplay.gpark.ui.web.jsinterfaces

import android.webkit.JavascriptInterface
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.asFlow
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.Product
import com.socialplay.gpark.data.model.event.PayResult
import com.socialplay.gpark.data.model.share.DirectShareData
import com.socialplay.gpark.data.model.share.DirectSharePlatforms
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.function.download.DownloadFileProvider
import com.socialplay.gpark.function.mw.launch.TSLaunch
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.share.MetaShare
import com.socialplay.gpark.ui.web.jsinterfaces.ext.allowRecharge
import com.socialplay.gpark.ui.web.jsinterfaces.ext.closeActivity
import com.socialplay.gpark.ui.web.jsinterfaces.ext.closeWebView
import com.socialplay.gpark.ui.web.jsinterfaces.ext.downloadGame
import com.socialplay.gpark.ui.web.jsinterfaces.ext.downloadStop
import com.socialplay.gpark.ui.web.jsinterfaces.ext.getAppChannel
import com.socialplay.gpark.ui.web.jsinterfaces.ext.getAppChannelName
import com.socialplay.gpark.ui.web.jsinterfaces.ext.getAppPackageName
import com.socialplay.gpark.ui.web.jsinterfaces.ext.getAppVersionCode
import com.socialplay.gpark.ui.web.jsinterfaces.ext.getAppVersionName
import com.socialplay.gpark.ui.web.jsinterfaces.ext.getBaseParams
import com.socialplay.gpark.ui.web.jsinterfaces.ext.getNativeAbValue
import com.socialplay.gpark.ui.web.jsinterfaces.ext.getProductList
import com.socialplay.gpark.ui.web.jsinterfaces.ext.getString
import com.socialplay.gpark.ui.web.jsinterfaces.ext.getSubscribeProductList
import com.socialplay.gpark.ui.web.jsinterfaces.ext.getSystemVersion
import com.socialplay.gpark.ui.web.jsinterfaces.ext.getUserInfo
import com.socialplay.gpark.ui.web.jsinterfaces.ext.getUserUUID
import com.socialplay.gpark.ui.web.jsinterfaces.ext.goBack
import com.socialplay.gpark.ui.web.jsinterfaces.ext.gotoLogin
import com.socialplay.gpark.ui.web.jsinterfaces.ext.hasHomeCategory
import com.socialplay.gpark.ui.web.jsinterfaces.ext.installApp
import com.socialplay.gpark.ui.web.jsinterfaces.ext.isGameInstall
import com.socialplay.gpark.ui.web.jsinterfaces.ext.isGuestRecharge
import com.socialplay.gpark.ui.web.jsinterfaces.ext.isInstalled
import com.socialplay.gpark.ui.web.jsinterfaces.ext.isInstalledAliPay
import com.socialplay.gpark.ui.web.jsinterfaces.ext.isInstalledQQ
import com.socialplay.gpark.ui.web.jsinterfaces.ext.isInstalledWX
import com.socialplay.gpark.ui.web.jsinterfaces.ext.isNativeTitleShow
import com.socialplay.gpark.ui.web.jsinterfaces.ext.launchApp
import com.socialplay.gpark.ui.web.jsinterfaces.ext.nativeAnalytics
import com.socialplay.gpark.ui.web.jsinterfaces.ext.openFeedbackPage
import com.socialplay.gpark.ui.web.jsinterfaces.ext.openNewWeb
import com.socialplay.gpark.ui.web.jsinterfaces.ext.openOutside
import com.socialplay.gpark.ui.web.jsinterfaces.ext.playGame
import com.socialplay.gpark.ui.web.jsinterfaces.ext.saveString
import com.socialplay.gpark.ui.web.jsinterfaces.ext.setNativeTitleShow
import com.socialplay.gpark.ui.web.jsinterfaces.ext.setStatusBarBgColor
import com.socialplay.gpark.ui.web.jsinterfaces.ext.setStatusBarShow
import com.socialplay.gpark.ui.web.jsinterfaces.ext.setStatusBarTextColor
import com.socialplay.gpark.ui.web.jsinterfaces.ext.startPay
import com.socialplay.gpark.ui.web.jsinterfaces.ext.toast
import com.socialplay.gpark.ui.web.jsinterfaces.ext.webTransToMw
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.ImageUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.json.JSONArray
import org.json.JSONObject
import org.koin.core.context.GlobalContext
import timber.log.Timber
import toJson

/**me
 * 对web提供的js桥接接口
 * <AUTHOR>
 * @date 2021/05/20
 */
class JsBridgeApi(val helper: JsBridgeHelper) : LifecycleObserver {

    val payInteractor: IPayInteractor by GlobalContext.get().inject()
    val tsLaunch: TSLaunch by lazy { TSLaunch() }

    init {
        initProductsCallback()
        initPayResultCallback()
    }

    private fun initPayResultCallback() {
        EventBus.getDefault().register(this)
        helper.contract.lifecycle.addObserver(this)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        EventBus.getDefault().unregister(this)
    }

    @Subscribe
    fun payResult(payResult: PayResult) {
        helper.lifecycleScope.launch {
            helper.onPayResultToGame(payResult)
            val data = GsonUtil.gson.toJson(payResult)
            helper.loadJs(JsBridgeHelper.JS_METHOD_PAY_RESULT, data)
        }
    }

    private fun initProductsCallback() {
        // 使用fragment的lifecycle而不是viewLifecycleOwner，避免在PreloadFragment中出现生命周期问题
        val lifecycleOwner = helper.contract.lifecycleOwner
        payInteractor.products.asFlow().collectWithLifecycleOwner(lifecycleOwner, Lifecycle.State.CREATED) {
            productsCallback(it)
        }
        payInteractor.subsProducts.asFlow().collectWithLifecycleOwner(lifecycleOwner, Lifecycle.State.CREATED) {
            subsProductsCallback(it)
        }
    }

    suspend fun getData(data: MutableList<Product>?) = flow {
        emit(data)
    }

    private fun productsCallback(products: MutableList<Product>) {
        helper.contract.lifecycleOwner.lifecycleScope.launchWhenResumed {
            if (products.isNullOrEmpty()) {
                helper.loadJs(
                    JsBridgeHelper.JS_METHOD_PRODUCT_LIST,
                    GsonUtil.gson.toJson(mutableListOf<Product>())
                )
            } else {
                helper.loadJs(JsBridgeHelper.JS_METHOD_PRODUCT_LIST, GsonUtil.gson.toJson(products))
            }
        }
    }

    private fun subsProductsCallback(products: MutableList<Product>) {
        helper.contract.lifecycleOwner.lifecycleScope.launchWhenResumed {
            if (products.isNullOrEmpty()) {
                helper.loadJs(
                    JsBridgeHelper.JS_METHOD_SUBS_PRODUCT_LIST,
                    GsonUtil.gson.toJson(mutableListOf<Product>())
                )
            } else {
                helper.loadJs(JsBridgeHelper.JS_METHOD_SUBS_PRODUCT_LIST, GsonUtil.gson.toJson(products))
            }
        }
    }

//    fun getLaunchGameInteractor(isTsGame: Boolean) =
//        if (isTsGame) mwLaunchGameInteractor else launchGameInteractor

    /**
     * 对js提供的接口映射
     */
    private val jsMethodMap: Map<String, (suspend (param: JSONArray) -> String?)> = mapOf(
        "getBaseParams" to { getBaseParams() },
        "getUserInfo" to { getUserInfo() },
        "getUserUUID" to { getUserUUID() },
        "getAppVersionCode" to { getAppVersionCode() },
        "getAppVersionName" to { getAppVersionName() },
        "getAppChannelName" to { getAppChannelName() },
        "getAppChannel" to { getAppChannel() },
        "getAppPackageName" to { getAppPackageName() },
        "isGameInstall" to { param -> isGameInstall(param) },
        "isNativeTitleShow" to { isNativeTitleShow() },
        "setNativeTitleShow" to { param -> setNativeTitleShow(param) },
        "goBack" to { goBack() },
        "closeActivity" to { closeActivity() },
        "saveString" to { param -> saveString(param) },
        "getString" to { param -> getString(param) },
        "nativeAnalytics" to { param -> nativeAnalytics(param) },
        "setStatusColor" to { param -> setStatusBarBgColor(param) },
        "setStatusMode" to { param -> setStatusBarTextColor(param) },
        "downloadGame" to { param -> downloadGame(param) },
        "downloadStop" to { param -> downloadStop(param) },
        "playGame" to { param -> playGame(param) },
        "isInstalledWX" to { isInstalledWX() },
        "isInstalledAliPay" to { isInstalledAliPay() },
        "isInstalledQQ" to { isInstalledQQ() },
        "startPay" to { param -> startPay(param) },
        "gotoLogin" to { param -> gotoLogin(param) },
        "getProductList" to { param -> getProductList(param) },
        "isGuestRecharge" to { isGuestRecharge() },
        "toast" to { param -> toast(param) },
        "allowRecharge" to { allowRecharge() },
        "setStatusShow" to { param -> setStatusBarShow(param) },
        "openNewWeb" to { param -> openNewWeb(param) },
        "getSubscribeProductList" to { param -> getSubscribeProductList(param) },
        "getNativeAbValue" to { param -> getNativeAbValue(param) },
        "openFeedbackPage" to { param -> openFeedbackPage(param) },
        "isInstalled" to { param -> isInstalled(param) },
        "launchApp" to { param -> launchApp(param) },
        "installApp" to { param -> installApp(param) },
        "openOutside" to { param -> openOutside(param) },
        "copyTextToClipboard" to { param -> copyTextToClipboard(param) },
        "getSystemVersion" to { getSystemVersion() },
        "saveImageToGalleryByUrl" to { param -> saveImageToGalleryByUrl(param) },
        "getSharePlatforms" to { getSharePlatforms() },
        "directShare" to { param -> directShare(param) },
        "saveImageToLocal" to { param -> saveImageToLocal(param) },
        "closeWebView" to { param -> closeWebView(param) },
        "webTransToMw" to { param -> webTransToMw(param) },
        "hasHomeCategory" to { param -> hasHomeCategory(param) },
    )


    /**
     * 执行json参数
     */
    @JavascriptInterface
    fun exec(json: String?): String {

        Timber.d("JsBridgeApi.exec.json=$json")

        if (json.isNullOrBlank()) {
            return createErrorResult(501, "current parameter is empty")
        }

        //解析json转换
        val jsonObject = JSONObject(json)
        val method = jsonObject.optString("method")
        val asyncCallback = jsonObject.optString("asyncCallback")
        val params = jsonObject.optJSONArray("param") ?: JSONArray()

        if (asyncCallback.isNotBlank()) {
            Timber.d("JsBridgeApi.exec.async=start")
            //todo 生命周期考虑下，待测试
            helper.lifecycleScope.launch(Dispatchers.IO) {
                val result = execTargetMethod(method, params)
                //执行js
                helper.loadJs(asyncCallback, result)
            }

            return createSuccessResult(302, "async")
        }

        //同步返回
        return runBlocking(helper.lifecycleScope.coroutineContext + Dispatchers.IO) {
            execTargetMethod(method, params)
        }
    }

    /**
     * 调用目标方法
     */
    private suspend fun execTargetMethod(methodName: String, params: JSONArray): String =
        if (jsMethodMap.containsKey(methodName)) {
            jsMethodMap.getValue(methodName)(params).toString()
        } else {
            Timber.e("unknow '$methodName' method, please check it whether  defined.")
            createErrorResult(404, "method '$methodName' not found")
        }

    /**
     * 错误结果创建
     */
    internal fun createErrorResult(
        code: Int = 502,
        msg: String = "error",
        data: Any? = null
    ): String = createJsonResult(code, msg, data)

    /**
     * 正确结果创建
     */
    internal fun createSuccessResult(
        code: Int = 200,
        msg: String = "success",
        data: Any? = null
    ): String = createJsonResult(code, msg, data)

    /**
     * 复制到剪贴板
     */
    suspend fun JsBridgeApi.copyTextToClipboard(paramArray: JSONArray): String {
        val message = paramArray.optString(0)
        withContext(Dispatchers.Main) {
            ClipBoardUtil.setClipBoardContent(message, helper.contract.requireContext())
        }
        return createSuccessResult()
    }

    private fun JsBridgeApi.saveImageToGalleryByUrl(paramArray: JSONArray): String {
        val url = paramArray.optString(0)
        helper.contract.context?.let {
            ImageUtil.saveImageToGalleryByUrl(it, url) { isSuccess ->
                if (isSuccess) {
                    ToastUtil.showShort(R.string.saved)
                } else {
                    ToastUtil.showShort(R.string.save_failed)
                }
            }
        }
        return createSuccessResult()
    }

    private fun JsBridgeApi.directShare(paramArray: JSONArray): String {
        val shareJson = paramArray.optString(0)
        if (shareJson.isNullOrEmpty()) {
            return createErrorResult(msg = "function share() params shareJson isBlank")
        }
        val webShareParam =
            GsonUtil.gsonSafeParse<DirectShareData>(shareJson) ?: return createErrorResult(msg = "function share() params isNull")
        MetaShare.share(helper.contract.requireActivity(), webShareParam.toShareData())
        return createSuccessResult()
    }

    /**
     * 获取支持的分享渠道列表
     */
    private fun JsBridgeApi.getSharePlatforms(): String {
        val list = DirectSharePlatforms()
        list.multiImages.add(SharePlatform.PLATFORM_SYSTEM)
        list.singleImage.add(SharePlatform.PLATFORM_SYSTEM)
        val data = GsonUtil.toJson(list)
        return createSuccessResult(data = data)
    }

    /**
     * 存储到本地相册
     */
    private fun JsBridgeApi.saveImageToLocal(paramArray: JSONArray): String {
        val url = paramArray.optString(0)
        val context = helper.contract.context ?: return createErrorResult()
        val path = "${DownloadFileProvider.shareImgCacheDir}/webShareLocal.png"
        ImageUtil.saveImageToLocalByUrl(context, url, path) {
            if (it) {
                helper.updatePath(path)
            }
        }
        return createSuccessResult(data = path)
    }

    /**
     * json格式返回
     */
    internal fun createJsonResult(
        code: Int,
        msg: String,
        data: Any? = null
    ): String {
        val jsonObject = JSONObject()
        jsonObject.put("code", code)
        jsonObject.put("msg", msg)
        jsonObject.put("data", data)
        val result = jsonObject.toString()
        Timber.d("JsBridgeApi.result=$result")
        return result
    }
}