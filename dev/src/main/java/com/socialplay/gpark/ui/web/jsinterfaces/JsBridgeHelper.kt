package com.socialplay.gpark.ui.web.jsinterfaces

import android.webkit.WebView
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.*
import com.bin.cpbus.CpEventBus
import com.google.gson.GsonBuilder
import com.meta.biz.ugc.model.GameCommonFeature
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.DeviceInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.WebKV
import com.socialplay.gpark.data.model.event.MWTransWebEvent
import com.socialplay.gpark.data.model.event.PayResult
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.util.InstallUtil
import com.socialplay.gpark.util.ProcessUtil
import com.socialplay.gpark.util.WebUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import timber.log.Timber

/**
 * js桥接帮助类，耦合fragment,实际JsBridge不与fragment耦合
 * <AUTHOR>
 * @date 2021/05/20
 */
class JsBridgeHelper constructor(var contract: IJsBridgeContract, private val webView: WebView) : KoinComponent, LifecycleOwner {

    companion object {


        const val JS_BRIDGE_ALIAS = "MetaX"

        // goBack的结果
        const val JS_METHOD_GO_BACK_RESULT = "goBackResult"

        // 登录成功，通知web更新用户信息
        const val JS_METHOD_UPDATE_USER_INFO = "nativeLogin"

        // 用户离开，home键
        const val JS_METHOD_HOME = "callSystemHome"
        //支付结果回调
        const val JS_METHOD_PAY_RESULT = "payResult"
        //商品列表更新
        const val JS_METHOD_PRODUCT_LIST = "updateProductList"
        //商品列表更新
        const val JS_METHOD_SUBS_PRODUCT_LIST = "updateSubscribeProductList"
        // 刷新原因：todo 后期使用多的话可以给web提供注册方法
        const val WEB_UPDATE_REASON_FOLLOW_CHANGED = "follow_changed"
        const val JS_METHOD_REFRESH_UI = "refreshUI"
        const val JS_METHOD_PAGE_STATUS = "handlePageStatus"
        const val JS_METHOD_SAVE_PATH = "updateSavePath"
        //MW发消息给WebView
        const val JS_METHOD_MW_TRANS_TO_WEB = "mwTransToWeb"

        // 仅兼容代码，无实际用途
        const val JS_METHOD_BACK_TO_WEB = "backToWeb"
    }

    private val accountInteractor: AccountInteractor by inject()
    internal val deviceInteractor: DeviceInteractor by inject()
    private val metaKV: MetaKV by inject()
    //注册生命周期相关
    override val lifecycle = LifecycleRegistry(this)
    private val lifecycleObserver = LifecycleEventObserver { _, event ->
        Timber.d("anxin_aaaaa $event")
        lifecycle.handleLifecycleEvent(event)
    }
    //处理数据交互的时候，多加一层转义，防止数据传输到web层转义出错
    private val prettyGson by lazy { GsonBuilder().setPrettyPrinting().create() }

    init {
        //这里要使用fragment的生命周期，viewLifecycleOwner,否则fragment调用onDestroyView时候js会失效
        contract.lifecycle.apply {
            removeObserver(lifecycleObserver)
            addObserver(lifecycleObserver)
            addObserver(object :DefaultLifecycleObserver{

                override fun onPause(owner: LifecycleOwner) {
                    contract.lifecycleScope.launch {
                        WebUtil.loadJs(webView, JS_METHOD_PAGE_STATUS, "onPause")
                    }
                }

                override fun onResume(owner: LifecycleOwner) {
                    contract.lifecycleScope.launch {
                        WebUtil.loadJs(webView, JS_METHOD_PAGE_STATUS, "onResume")
                    }
                }
                override fun onDestroy(owner: LifecycleOwner) {
                    super.onDestroy(owner)
                }
            })
        }
        //监听账号变化
        accountInteractor.accountLiveData.observe(contract.lifecycleOwner, Observer {
            contract.lifecycleScope.launch { refreshUserInfo() }
        })
        EventBus.getDefault().register(this)
    }

    fun onDestroy() {
        Timber.d("web_activity_onDestroy")
        EventBus.getDefault().unregister(this)
    }

    /**
     * 获取用户信息
     */
    internal fun getMetaUserInfo(): MetaUserInfo? = accountInteractor.accountLiveData.value

    /**
     * 获取web kv
     */
    internal fun getWebKv(): WebKV = metaKV.web


    /**
     * 返回上一个页面
     */
    suspend fun goBack(): Boolean {
        withContext(Dispatchers.Main) {
            if (webView.canGoBack()) {
                webView.goBack()
                loadJs(JS_METHOD_GO_BACK_RESULT, "success")
            } else {
                val result = contract.goBack()
                loadJs(JS_METHOD_GO_BACK_RESULT, if (result) "success" else "failed")
            }

        }
        return true
    }

    fun getActivity(): FragmentActivity {
        return contract.requireActivity()
    }

    /**
     * 检查toolbar是否显示
     */
    suspend fun isToolbarVisible(): Boolean {
        return withContext(Dispatchers.Main) {
            contract.isToolbarVisible() ?: false
        }
    }

    /**
     * 设置toolbar是否可以显示
     */
    suspend fun setToolbarVisible(isVisible: Boolean) {
        withContext(Dispatchers.Main) {
            contract.setToolbarVisible(isVisible)
        }
    }

    suspend fun setStatusBarBgColor(colorStr: String): Boolean {
        return withContext(Dispatchers.Main) {
            contract.setStatusBarColor(colorStr)?:false
        }
    }

    suspend fun setStatusBarTextColor(isDark: Boolean) {
        withContext(Dispatchers.Main) {
            contract.setStatusBarTextColor(isDark)
        }
    }

    fun isRealLogin():Boolean{
        return accountInteractor.isRealLogin()
    }

    /**
     * 判断是否绑定账号信息
     */
    fun isBindAccount(): Boolean {
        return accountInteractor.isBindAccount()
    }

    /**
     * 跳转登录页面
     */
    suspend fun navigateLogin(source: String) {
        withContext(Dispatchers.Main) {
            goLogin(source)
        }
    }

    /**
     * 后退
     */
    suspend fun navigateBack() {
        withContext(Dispatchers.Main) {
            //返回上一页
            kotlin.runCatching {
                contract.goBack()
            }.getOrElse {
                contract.requireActivity().finish()
            }
        }
    }

    /**
     * 后退
     */
    suspend fun closeWebView(remove: Boolean) {
        withContext(Dispatchers.Main) {
            //返回上一页
            kotlin.runCatching {
                contract.closeWebView(remove)
            }.getOrElse {
                contract.requireActivity().finish()
            }
        }
    }

    /**
     * 通知登录结果
     */
    suspend fun refreshUserInfo() {
        //通知web更新用户信息
        loadJs(JS_METHOD_UPDATE_USER_INFO)
    }

    /**
     * 加载js方法
     */
    suspend fun loadJs(jsMethod: String, vararg params: Any?) {
        WebUtil.loadJs(webView, jsMethod, *params)
    }

    fun isInstalledQQ(): Boolean {
        return InstallUtil.isInstalledQQ(contract.requireContext())
    }

    fun isInstalledWX(): Boolean {
        return InstallUtil.isInstalledWX(contract.requireContext())
    }

    fun isInstalledAliPay(): Boolean {
        return InstallUtil.isInstalledAliPay(contract.requireContext())
    }

    fun isGuestRecharge(): Boolean {
        return false
    }

    suspend fun isFuncLimitByU13(): Boolean {
        return false
    }

    fun getRechargeTip(type: String): String {
        return when (type) {
            "visitor" -> {
                contract.context?.getString(R.string.visitor_recharge_tip) ?: "Payment failed, visitor cannot recharge"
            }
            "u13"     -> {
                contract.context?.getString(R.string.u13_recharge_tip) ?: "Payment failed, users under the age of 13 cannot recharge"
            }
            else      -> {
                ""
            }
        }
    }

    /**
     * @param 与web商定
     * [埋点文档] https://meta.feishu.cn/wiki/L4vFwdeNQiK46DkJ4DRcnE0nnLg
     */
    private fun goLogin(source: String){
        if (ProcessUtil.isMainProcess(contract.requireContext()) && contract.activity is MainActivity){
            contract.routerToLogin(source)
        }else{
            contract.gameToLogin(source)
        }
    }
    /**
     * 设置toolbar是否可以显示
     */
    suspend fun setStatusBarVisible(isVisible: Boolean) {
        withContext(Dispatchers.Main) {
            contract.setStatusBarVisible(isVisible)
        }
    }
    fun openNewWeb(title: String? = null, url: String, showStatusBar: Boolean, showTitle: Boolean) {
        if (contract.hasFragment()) {
            MetaRouter.Web.navigate(
                contract.requireFragment(),
                title = title,
                url = url,
                showStatusBar = showStatusBar,
                showTitle = showTitle
            )
        }
    }

    fun onPayResultToGame(payResult: PayResult) {
        contract.onPayResultToGame(payResult)
    }

    fun updatePath(path: String) {
        contract.lifecycleScope.launch(Dispatchers.IO) {
            WebUtil.loadJs(webView, JS_METHOD_SAVE_PATH, path)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMWGameFeatureWithWebEvent(event: MWTransWebEvent) {
        val request = event.feature
        when (request.feature) {
            GameCommonFeature.FEATURE_MW_TRANS_WEB -> {
                val data = request.params?.get("data")?.toString() ?: ""
                Timber.d("MWTransWebEventJs.loadJs1：$data")
                val params = prettyGson.toJson(data)
                Timber.d("MWTransWebEventJs.loadJs2：$params")
                contract.lifecycleScope.launch(Dispatchers.IO) {
                    WebUtil.loadJs(webView, JS_METHOD_MW_TRANS_TO_WEB, params)
                }
            }
        }
    }
}