package com.socialplay.gpark.ui.web.jsinterfaces.contract

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.meta.biz.ugc.model.RechargeResultMgs
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.event.PayResult
import com.socialplay.gpark.data.model.pay.IAPConstants
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.web.WebViewDialog
import com.socialplay.gpark.ui.gamepay.GamePayLifecycle
import com.socialplay.gpark.ui.web.GameWebDialog
import com.socialplay.gpark.ui.web.WebFragment
import com.socialplay.gpark.ui.web.jsinterfaces.IJsBridgeContract
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.toast

class FragmentJsBridgeContract(private val fragment: Fragment) : IJsBridgeContract {
    override val context: Context?
        get() = fragment.context
    override val viewLifecycleOwner: LifecycleOwner
        get() = fragment.viewLifecycleOwner
    override val lifecycle: Lifecycle
        get() = fragment.lifecycle
    override val lifecycleScope: LifecycleCoroutineScope
        get() = fragment.lifecycleScope
    override val lifecycleOwner: LifecycleOwner
        get() = fragment
    override val activity: Activity?
        get() = fragment.activity

    override fun requireContext(): Context {
        return fragment.requireContext()
    }

    override fun requireActivity(): FragmentActivity {
        return fragment.requireActivity()
    }

    override fun isFromGame(): Boolean {
        return fragment is GameWebDialog
    }

    override fun isToolbarVisible(): Boolean? {
        return (fragment as? WebFragment)?.isToolbarVisible()
    }

    override fun setStatusBarVisible(visible: Boolean) {
        (fragment as? WebFragment)?.setStatusBarVisible(visible)
    }

    override fun setToolbarVisible(visible: Boolean) {
        (fragment as? WebFragment)?.setToolbarVisible(visible)
    }

    override fun setStatusBarColor(colorStr: String): Boolean? {
        return (fragment as? WebFragment)?.setStatusBarColor(colorStr)
    }

    override fun setStatusBarTextColor(dark: Boolean) {
        (fragment as? WebFragment)?.setStatusBarTextColor(dark)
    }

    override fun routerToLogin(source: String) {
        MetaRouter.Login.login(fragment, source)
    }

    override fun gameToLogin(source: String) {
        MetaRouter.Main.gameToLogin(fragment.requireContext(), LoginSource.Web(source))
    }

    override fun hasFragment(): Boolean {
        return true
    }

    override fun isWebFragment(): Boolean {
        return fragment is WebFragment
    }

    override fun goBack(): Boolean {
        return when (fragment) {
            is WebFragment -> {
                fragment.goBack()
                true
            }

            is WebViewDialog -> {
                fragment.dismissAllowingStateLoss()
                true
            }

            is GameWebDialog -> {
                fragment.dismissAllowingStateLoss()
                true
            }

            else -> {
                false
            }
        }
    }

    override fun closeWebView(removeWebView: Boolean) {
        when (fragment) {
            is GameWebDialog -> {
                fragment.navigateToPreviousPage(removeWebView)
            }
            else -> {}
        }
    }


    override fun requireFragment(): Fragment {
        return fragment
    }

    override fun startActivity(createChooser: Intent) {
        fragment.startActivity(createChooser)
    }

    override fun isWebViewDialog(): Boolean {
        return fragment is WebViewDialog
    }

    override fun toast(res: Int) {
        fragment.toast(res)
    }

    override fun fragmentManager(): FragmentManager {
        return fragment.childFragmentManager
    }


    override fun onPayResultToGame(payResult: PayResult) {
        when (fragment) {
            is WebFragment -> {
                fragment.getExtra()?.let { onResultToGame(payResult, it) }
            }

            is WebViewDialog -> {
                fragment.getExtra()?.let { onResultToGame(payResult, it) }
            }
        }
    }

    private fun onResultToGame(payResult: PayResult, extra: String) {
        when (payResult.iapScene) {
            IAPConstants.IAP_SCENE_VIP_PLUS, IAPConstants.IAP_SCENE_VIP_PLUS_RENEW, IAPConstants.IAP_SCENE_PG_COIN -> {
                val rechargeResultMgs = GsonUtil.gsonSafeParseCollection<RechargeResultMgs>(extra)
                rechargeResultMgs?.let {
                    it.code = payResult.code ?: 0
                    it.message = payResult.reason
                    it.amount = payResult.amount
                    GamePayLifecycle.sendRechargeResult2UE(it)
                }
            }
        }
    }

    override fun fromTab(): Boolean {
        return false
    }

    override fun webTs(): Long? {
        return null
    }

    override fun gamePackageName(): String? {
        return null
    }

    override fun gameId(): String? {
        return null
    }

    override fun source(): String? {
        return null
    }

    override fun style(): String? {
        return null
    }

    override fun onPaySuccess() {

    }

    override fun onPayFailed() {

    }

    override fun notifyBackToWebFromWeb() {

    }

    override fun onResumeGame() {

    }

    override fun routerToLogin(popUpId: Int) {

    }

    override fun routerToAuthList(hasPasswordItem: Boolean) {

    }

    override fun routerToLoginBind(hasPasswordItem: Boolean) {

    }

    override fun routerToAccountSetting(source: com.socialplay.gpark.ui.web.jsinterfaces.LoginSource) {

    }

    override fun closeAll(removeWebView: Boolean) {

    }

    override fun getResId(): ResIdBean? {
        return null
    }

    override fun send2Ue(amount: Int, code: Int, errorMessage: String?) {

    }

    override fun closeLoading() {

    }

    override fun setPageSource(source: String) {

    }

    override fun addRefreshFragmentListenerByReason(reason: String) {

    }

    override fun preOpenNativePayPage(data: String?) {

    }
}