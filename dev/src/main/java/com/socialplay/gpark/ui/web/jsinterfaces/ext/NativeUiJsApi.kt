package com.socialplay.gpark.ui.web.jsinterfaces.ext

import android.content.Intent
import android.net.Uri
import androidx.lifecycle.lifecycleScope
import com.meta.biz.ugc.model.GameCommonFeature
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.socialplay.gpark.data.interactor.TabConfigInteractor
import com.socialplay.gpark.data.model.web.WebParams
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.download.DownloadFileProvider
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeApi
import com.socialplay.gpark.ui.web.jsinterfaces.model.JSTransToData
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.InstallUtil
import com.socialplay.gpark.util.PackageUtil
import com.socialplay.gpark.util.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.io.File

/**
 * UI操作相关的js接口
 * <AUTHOR>
 * @date 2021/06/09
 */

suspend fun JsBridgeApi.hasHomeCategory(param: JSONArray): String {
    val id = param.getInt(0)
    val hasCategory = GlobalContext.get().get<TabConfigInteractor>().getHomeConfigTab()?.find { it.id == id } != null
    return createSuccessResult(data = hasCategory)
}

/**
 * 原生顶栏是否显示
 */
suspend fun JsBridgeApi.isNativeTitleShow(): String = createSuccessResult(data = helper.isToolbarVisible())

/**
 * 显示原生标题栏
 */
suspend fun JsBridgeApi.setNativeTitleShow(paramArray: JSONArray): String {
    //是否显示标题
    val isShow = paramArray.optBoolean(0, false)
    helper.setToolbarVisible(isShow)
    return createSuccessResult()
}

/**
 * 返回上一页
 */
suspend fun JsBridgeApi.goBack(): String = createSuccessResult(data = helper.goBack())

/**
 * 关闭页面
 */
suspend fun JsBridgeApi.closeActivity(): String {
    helper.navigateBack()
    return createSuccessResult()
}

suspend fun JsBridgeApi.closeWebView(paramArray: JSONArray): String {
    val remove = paramArray.optBoolean(0, false)
    helper.closeWebView(remove)
    return createSuccessResult()
}

suspend fun JsBridgeApi.setStatusBarBgColor(paramArray: JSONArray): String {
    val colorStr = paramArray.optString(0)
    return if (colorStr.isNullOrEmpty() || !helper.setStatusBarBgColor(colorStr)) {
        createErrorResult(msg = "color string is empty or error")
    } else {
        createSuccessResult()
    }

}

suspend fun JsBridgeApi.setStatusBarTextColor(paramArray: JSONArray): String {
    val isDark = paramArray.optBoolean(0, false)
    helper.setStatusBarTextColor(isDark)
    return createSuccessResult()
}

/**
 * 检测微信是否安装
 */
fun JsBridgeApi.isInstalledWX(): String {
    val result = helper.isInstalledWX()
    return createSuccessResult(data = result)
}

/**
 * 检测支付宝是否安装
 */
fun JsBridgeApi.isInstalledAliPay(): String {
    val result = helper.isInstalledAliPay()
    return createSuccessResult(data = result)
}

/**
 * 检测支付宝是否安装
 */
fun JsBridgeApi.isInstalledQQ(): String {
    val result = helper.isInstalledQQ()
    return createSuccessResult(data = result)
}

/**
 * js调用toast弹窗
 */
fun JsBridgeApi.toast(paramArray: JSONArray): String {
    Timber.d("toast %s", paramArray)
    val message = paramArray.optString(0)
    if (!message.isNullOrEmpty()) {
        helper.lifecycleScope.launch(Dispatchers.Main) {
            ToastUtil.showShort(message)
        }
    }
    return createSuccessResult()
}

/**
 * 帐密体系游客登录状态是否可以充值
 */
fun JsBridgeApi.isGuestRecharge(): String {
    val result = helper.isGuestRecharge()
    return createSuccessResult(data = result)
}

/**
 * 显示状态栏
 */
suspend fun JsBridgeApi.setStatusBarShow(paramArray: JSONArray): String {
    //是否显示状态栏
    val isShow = paramArray.optBoolean(0, true)
    helper.setStatusBarVisible(isShow)
    return createSuccessResult()
}

suspend fun JsBridgeApi.openNewWeb(param: JSONArray): String {
    val data = param.optString(0)
    val webParams = GsonUtil.gsonSafeParse<WebParams>(data)
    val url = webParams?.url
    //true代表使用web的,false代表使用Android的
    val isTranTop = webParams?.isTranTop ?: false
    //true代表使用Android的,false代表不用Android的title
    val showTitle = webParams?.showTitle ?: false
    if (url.isNullOrEmpty()) {
        return createErrorResult(msg = "url is empty")
    }
    withContext(Dispatchers.Main) {
        helper.openNewWeb(webParams.title, url, !isTranTop, showTitle)
    }
    return createSuccessResult()
}

suspend fun JsBridgeApi.openFeedbackPage(param: JSONArray): String {
    return param.optJSONObject(0)?.let {
        val source = it.optString("source")
        val defaultSelectType = it.optString("defaultSelectType")
        val feedbackGameId = it.optString("feedbackGameId")
        if (helper.contract.activity == null) {
            createErrorResult(500, "there is a problem with the current page status")
        } else if (source.isNullOrEmpty()) {
            createErrorResult(400, "miss params source")
        } else {
            helper.contract.activity?.let {
                withContext(Dispatchers.Main) {
                    if (helper.contract.hasFragment()) {
                        MetaRouter.Main.anyToFeedback(
                            it, helper.contract.requireFragment(), feedbackGameId, source, "web", defaultSelectType
                        )
                    }
                }
            }
            createSuccessResult()
        }
    } ?: createErrorResult(400, "miss params json")
}

fun JsBridgeApi.isInstalled(param: JSONArray): String {
    val pkg = param.optString(0)
    return createSuccessResult(data = helper.contract.context?.let { InstallUtil.isAppInstalled(it, pkg) })
}

fun JsBridgeApi.launchApp(param: JSONArray): String {
    val pkg = param.optString(0)
    val adId = param.optString(1)// 广告id, 统计用可空, 空则不发埋点
    if (adId != null) {
        Analytics.track(EventConstants.AD_DETAIL_WEB_LAUNCH, "id" to adId, "packagename" to pkg)
    }
    InstallUtil.launchAppByPkgName(helper.getActivity(), pkg)
    return createSuccessResult()
}

fun JsBridgeApi.installApp(param: JSONArray): String {
    val name = param.optString(0)
    val adId = param.optString(1)// 广告id, 统计用可空, 空则不发埋点
    val pkg = param.optString(2)// app包名, 统计用可空, 空则不发埋点
    val activity = helper.getActivity()
    helper.contract.viewLifecycleOwner.lifecycleScope.launchWhenResumed {
        PackageUtil.installApp(activity, File(DownloadFileProvider.webDownloadRoot, "$name.apk"))
        PackageUtil.startWatchInstallThenStart(activity.lifecycleScope, activity.applicationContext, pkg, adId)
    }
    return createSuccessResult()
}

fun JsBridgeApi.openOutside(param: JSONArray): String {
    val url = param.optString(0)

    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    helper.contract.startActivity(intent)
    return createSuccessResult()
}

/**
 * webview 发消息给 Mw
 */
suspend fun JsBridgeApi.webTransToMw(param: JSONArray): String {
    val transToData = param.optJSONObject(0)?.optString("data") ?: ""
    val jsData = JSTransToData(GameCommonFeature.FEATURE_WEB_TRANS_MW, "", mapOf("data" to transToData))
    UGCProtocolSender.sendProtocol(
        ProtocolSendConstant.PROTOCOL_CLIENT_GAME_FEATURE, 0, jsData
    )
    return createSuccessResult()
}
