package com.socialplay.gpark.util

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.os.Build
import android.provider.Settings
import android.text.TextUtils
import android.util.DisplayMetrics
import android.view.*
import android.view.WindowManager
import org.koin.core.context.GlobalContext
import kotlin.math.pow
import kotlin.math.sqrt

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/05/11
 * desc   :
 * </pre>
 */


object ScreenUtil {
    @Deprecated(message = "Stop using this due to flip phone compatibility issue, check ResourcesExt.kt out")
    val screenWidth by lazy { getScreenWidth(GlobalContext.get().get()) }

    @Deprecated(message = "Stop using this due to flip phone compatibility issue, check ResourcesExt.kt out")
    val screenHeight by lazy { getScreenHeight(GlobalContext.get().get()) }

    /**
     * 获取屏幕宽度
     */
    fun getScreenWidth(context: Context): Int {
        val displayMetrics: DisplayMetrics = getDisplayMetrics(context)
        return displayMetrics.widthPixels
    }

    fun dp2px(context: Context, dp: Float): Int {
        val scale: Float = getDisplayMetrics(context).density
        return (dp * scale + 0.5f).toInt()
    }

    fun sp2px(context: Context, sp: Int): Float {
        val fontScale: Float = getDisplayMetrics(context).scaledDensity
        return (sp * fontScale + 0.5f)
    }

    fun sp2px(context: Context, sp: Float): Int {
        val fontScale: Float = getDisplayMetrics(context).scaledDensity
        return (sp * fontScale + 0.5f).toInt()
    }

    fun getScreenDensity(context: Context): Float {
        return getDisplayMetrics(context).density
    }

    fun getScreenDensityDpi(context: Context): Int {
        return getDisplayMetrics(context).densityDpi
    }

    fun isSpLarger(context: Context): Boolean {
        val dm = getDisplayMetrics(context)
        return dm.scaledDensity > dm.density
    }

    /**
     * 获取屏幕高度
     *
     */
    fun getScreenHeight(context: Context): Int {
        val displayMetrics = getDisplayMetrics(context)
        return displayMetrics.heightPixels
    }

    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        return context.resources.displayMetrics
    }

    /**
     * 是否是横屏
     *
     * @param context 上下文
     * @return return true if is horizontal screen
     */
    fun isHorizontalScreen(context: Context): Boolean {
        val screen: IntArray = getScreenSize(context) ?: return false
        return screen[0] > screen[1]
    }

    fun isLandscape(context: Context): Boolean {
        val display = (context.getSystemService(Context.WINDOW_SERVICE) as WindowManager).defaultDisplay
        val rotation = display.rotation
        return rotation == Surface.ROTATION_90 || rotation == Surface.ROTATION_270
    }

    /**
     * 获取屏幕的宽高
     */
    fun getScreenSize(context: Context): IntArray? {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics: DisplayMetrics = getRealMetricsSafely(windowManager.defaultDisplay) ?: return null
        val size = IntArray(2)
        size[0] = displayMetrics.widthPixels
        size[1] = displayMetrics.heightPixels
        return size
    }

    private fun getRealMetricsSafely(display: Display?): DisplayMetrics? {
        if (display == null) {
            return null
        }
        try {
            val realDisplayMetrics = DisplayMetrics()
            display.getRealMetrics(realDisplayMetrics)
            return realDisplayMetrics
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    fun getNavBarHeight(context: Context?): Int {
        context ?: return 0
        val resources = context.resources
        val id = resources.getIdentifier("navigation_bar_height", "dimen", "android")
        var height = 0
        if (id > 0) {
            height = resources.getDimensionPixelSize(id)
        }
        return height
    }

    fun isNavBarExist(activity: Activity): Boolean {
        val vp = activity.window.decorView as? ViewGroup
        if (vp != null) {
            for (i in 0 until vp.childCount) {
                val id = vp.getChildAt(i).id
                if (id != View.NO_ID && "navigationBarBackground" == activity.resources.getResourceEntryName(id)) {
                    return true
                }
            }
        }
        return false
    }

    fun isFullScreenMode(activity: Activity): Boolean {
        val window = activity.window
        val flags = window.attributes.flags
        val fullScreen = flags and WindowManager.LayoutParams.FLAG_FULLSCREEN != 0
        val forceNotFullScreen = flags and WindowManager.LayoutParams.FLAG_FORCE_NOT_FULLSCREEN != 0
        val actionbarVisible: Boolean = activity.actionBar?.isShowing ?: false
        return fullScreen && !forceNotFullScreen && !actionbarVisible
    }

    fun hideNavigationBar(activity: Activity) {
        activity.window.run {
            attributes.systemUiVisibility = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            attributes = attributes
        }
    }

    /**
     * 获取虚拟按键的高度
     * 1. 全面屏下
     * 1.1 开启全面屏开关-返回0
     * 1.2 关闭全面屏开关-执行非全面屏下处理方式
     * 2. 非全面屏下
     * 2.1 没有虚拟键-返回0
     * 2.1 虚拟键隐藏-返回0
     * 2.2 虚拟键存在且未隐藏-返回虚拟键实际高度
     */
    fun getNavigationBarHeightIfRoom(activity: Activity): Int {
        return if (navigationGestureEnabled(activity)) {
            if (Build.BRAND.equals("XIAOMI", ignoreCase = true)) {
                getNavigationBarHeight(activity)
            } else {
                0
            }
        } else getCurrentNavigationBarHeight(activity)
    }

    /**
     * 全面屏（是否开启全面屏开关 0 关闭  1 开启）
     *
     * @param context
     * @return
     */
    fun navigationGestureEnabled(context: Context): Boolean {
        val flag = Settings.Global.getInt(context.contentResolver, getDeviceInfo(), 0)
        return flag != 0
    }

    /**
     * 获取设备信息（目前支持几大主流的全面屏手机，亲测华为、小米、oppo、魅族、vivo都可以）
     *
     * @return
     */
    fun getDeviceInfo(): String {
        val brand: String = Build.BRAND
        if (TextUtils.isEmpty(brand)) return "navigationbar_is_min"
        return if (brand.equals("HUAWEI", ignoreCase = true)) {
            "navigationbar_is_min"
        } else if (brand.equals("XIAOMI", ignoreCase = true)) {
            "force_fsg_nav_bar"
        } else if (brand.equals("VIVO", ignoreCase = true)) {
            "navigation_gesture_on"
        } else if (brand.equals("OPPO", ignoreCase = true)) {
            "navigation_gesture_on"
        } else {
            "navigationbar_is_min"
        }
    }

    /**
     * 非全面屏下 虚拟键实际高度(隐藏后高度为0)
     * @param activity
     * @return
     */
    fun getCurrentNavigationBarHeight(activity: Activity): Int {
        return if (isNavigationBarShown(activity)) {
            getNavigationBarHeight(activity)
        } else {
            0
        }
    }


    /**
     * 非全面屏下 虚拟按键是否打开
     * @param activity
     * @return
     */
    fun isNavigationBarShown(activity: Activity): Boolean {
        //虚拟键的view,为空或者不可见时是隐藏状态
        val view: View = activity.findViewById(android.R.id.navigationBarBackground) ?: return false
        return view.visibility == View.VISIBLE
    }

    /**
     * 非全面屏下 虚拟键高度(无论是否隐藏)
     * @param context
     * @return
     */
    fun getNavigationBarHeight(context: Context): Int {
        var result = 0
        val resourceId = context.resources.getIdentifier("navigation_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = context.resources.getDimensionPixelSize(resourceId)
        }
        return result
    }

    /**
     * 判断是否为平板
     */
    fun isPad(context: Context): Boolean {
        var result = false
        val wm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = wm.defaultDisplay
        val dm = DisplayMetrics()
        display.getMetrics(dm)
        val x = (dm.widthPixels / dm.xdpi).pow(2)
        val y = (dm.heightPixels / dm.ydpi).pow(2)
        // 屏幕尺寸
        val screenInches = sqrt(x + y)
        // 大于7尺寸则为Pad
        if (screenInches >= 7.0) {
            result = true
        }
        return result
    }

}