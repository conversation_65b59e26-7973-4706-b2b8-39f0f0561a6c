package com.socialplay.gpark.util

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.BackgroundColorSpan
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.ImageSpan
import android.text.style.StyleSpan
import android.text.style.TextAppearanceSpan
import android.text.style.UnderlineSpan
import android.view.View
import androidx.annotation.DimenRes
import androidx.annotation.DrawableRes
import androidx.annotation.StyleRes
import androidx.core.content.ContextCompat
import com.socialplay.gpark.ui.view.MarginCenterImageSpan
import com.socialplay.gpark.util.span.RadiusBackgroundSpan
import org.koin.core.context.GlobalContext


/**
 * 创建Spannable的快捷辅助类
 * 代码实例:
 * <pre>`
 * new SpannableHelper.Builder()
 * .text("AAA").color(Color.WHITE).size(mContext, R.dimen.dp_100).bold(true)
 * .text("BBB").color("#FF0000").size(120).bold(false)
 * .build()
`</pre> *
 * 每个文字样式需要以text()方法开头，之后再使用属性方法:color()、size()、bold(),属性方法不分顺序
 */
class SpannableHelper private constructor(builder: Builder) {
    private val mSpannableStringBuilder: SpannableStringBuilder = builder.mSpannableStringBuilder
    private val spannableStringBuilder: SpannableStringBuilder
        get() = mSpannableStringBuilder

    class Builder {
        private var index = 0
        private var textLength = 0
        val mSpannableStringBuilder: SpannableStringBuilder = SpannableStringBuilder()

        fun image(context: Context, @DrawableRes drawable: Int): Builder {
            val span = ImageSpan(context, drawable)
            text("rp")
            mSpannableStringBuilder.setSpan(span, index, index + textLength, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
            return this
        }

        fun centerImageSpan(
            context: Context,
            @DrawableRes drawable: Int,
            marginLeft: Int = 0,
            marginTop: Int = 0,
            marginRight: Int = 0,
            marginBottom: Int = 0
        ): Builder {
            val span = MarginCenterImageSpan(
                context,
                drawable,
                marginLeft,
                marginTop,
                marginRight,
                marginBottom
            )
            text("rp")
            mSpannableStringBuilder.setSpan(
                span,
                index,
                index + textLength,
                Spannable.SPAN_INCLUSIVE_EXCLUSIVE
            )
            return this
        }

        fun image(drawable: Drawable): Builder {
            val span = ImageSpan(drawable)
            text("rp")
            mSpannableStringBuilder.setSpan(span, index, index + textLength, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
            return this
        }

        fun text(text: CharSequence?): Builder {
            if (TextUtils.isEmpty(text)) {
                throw NullPointerException("SpannableHelper.Builder#text(CharSequence text) params can not be empty!")
            }
            index = mSpannableStringBuilder.length
            textLength = text?.length ?: 0
            mSpannableStringBuilder.append(text)
            return this
        }

        @Deprecated("use colorRes(color: Int)")
        fun color(color: Int): Builder {
            val colorSpan = ForegroundColorSpan(color)
            mSpannableStringBuilder.setSpan(colorSpan, index, index + textLength, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            return this
        }

        fun colorRes(color: Int): Builder {
            val colorSpan = ForegroundColorSpan(ContextCompat.getColor(GlobalContext.get().get(), color))
            mSpannableStringBuilder.setSpan(colorSpan, index, index + textLength, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            return this
        }

        fun bgColor(color: Int): Builder {
            val colorSpan = BackgroundColorSpan(color)
            mSpannableStringBuilder.setSpan(colorSpan, index, index + textLength, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            return this
        }

        fun bgRadiusColor(color: Int, textColor: Int, radius: Int): Builder {
            val radiusBackgroundSpan = RadiusBackgroundSpan(color, radius.toFloat())
            mSpannableStringBuilder.setSpan(
                radiusBackgroundSpan,
                index,
                index + textLength,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            return this
        }

        fun color(color: String?): Builder {
            return color(Color.parseColor(color))
        }

        fun size(size: Int): Builder {
            val absoluteSizeSpan = AbsoluteSizeSpan(size)
            mSpannableStringBuilder.setSpan(
                absoluteSizeSpan,
                index,
                index + textLength,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            return this
        }

        fun size(context: Context, @DimenRes dimenRes: Int): Builder {
            return size(context.resources.getDimension(dimenRes).toInt())
        }

        fun bold(bold: Boolean): Builder {
            if (bold) {
                val styleSpan = StyleSpan(Typeface.BOLD) //粗体
                mSpannableStringBuilder.setSpan(
                    styleSpan,
                    index,
                    index + textLength,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            return this
        }

        fun textAppearance(context: Context?, @StyleRes styleRes: Int): Builder {
            val tas = TextAppearanceSpan(context, styleRes)
            mSpannableStringBuilder.setSpan(tas, index, index + textLength, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            return this
        }

        /**
         * 下划线
         */
        fun underline(): Builder {
            val span = UnderlineSpan()
            mSpannableStringBuilder.setSpan(span, index, index + textLength, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            return this
        }

        /**
         * 点击
         *
         * @apiNote 使用这个属性需要设置 [android.widget.TextView.setMovementMethod] [android.text.method.LinkMovementMethod]
         * @apiNote 同时设置颜色 [.color] 需要放在此方法之后执行
         */
        fun click(onClickListener: View.OnClickListener?): Builder {
            val span: ClickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    onClickListener?.onClick(widget)
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false
                }
            }
            mSpannableStringBuilder.setSpan(span, index, index + textLength, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            return this
        }

        /**
         * 点击（直接设置ClickableSpan）
         *
         * @apiNote 使用这个属性需要设置 [android.widget.TextView.setMovementMethod] [android.text.method.LinkMovementMethod]
         * @apiNote 同时设置颜色 [.color] 需要放在此方法之后执行
         */
        fun click(span: ClickableSpan?): Builder {
            mSpannableStringBuilder.setSpan(span, index, index + textLength, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            return this
        }

        /**
         * 点击（直接设置ClickableSpan）
         */
        fun click(span: ClickableSpan?, start: Int, end: Int): Builder {
            mSpannableStringBuilder.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            return this
        }

        fun build(): SpannableStringBuilder {
            return SpannableHelper(this).spannableStringBuilder
        }

    }

    companion object {
        fun valueOf(text: CharSequence?): SpannableStringBuilder {
            return SpannableStringBuilder.valueOf(text ?: "")
        }
    }

}

class SpanClick(val call: () -> Unit) : ClickableSpan() {
    override fun onClick(widget: View) {
        call.invoke()
    }

    override fun updateDrawState(ds: TextPaint) {
        ds.isUnderlineText = false
    }
}