package com.socialplay.gpark.util.extension

/**
 *
 * <AUTHOR>
 * @date 2021/12/08
 */


inline fun <reified T : Enum<T>> safeEnumValueOf(type: String): T? {
    return try {
        java.lang.Enum.valueOf(T::class.java, type)
    } catch (e: IllegalArgumentException) {
        null
    }
}

inline fun <reified T : Enum<T>> safeEnumValueOf(type: String, default: T): T = safeEnumValueOf<T>(type) ?: default

/**
 * Returns `true` if enum T contains an entry with the specified name.
 */
inline fun <reified T : Enum<T>> enumContains(name: String): Bo<PERSON>an {
    return enumValues<T>().any { it.name == name }
}

inline fun <reified T : Enum<T>> enumContainsIgnoreCase(name: String?): Bo<PERSON>an {
    return enumValues<T>().any { it.name.equals(name, true) }
}