package com.socialplay.gpark.util.extension

import android.annotation.SuppressLint
import android.graphics.Color
import androidx.core.graphics.toColorInt
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.util.ifNullOrEmpty
import java.util.Scanner
import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2022/02/16
 *     desc   :
 */

fun String.camelToUnderscore(): String {
    return buildString {
        val chars = toCharArray()
        for (char in chars) {
            if (char.isUpperCase()) {
                append("_")
            }
            append(char.lowercase())
        }
    }
}

@SuppressLint("ChineseStringLiteral")
fun String.isHasChineseChar(): Boolean {
    val p: Pattern = Pattern.compile("[\u4e00-\u9fa5]")
    val m: Matcher = p.matcher(this)
    return m.find()
}

fun String?.replaceChinese(replace: String): String {
    if (EnvConfig.isParty()) {
        return this ?: replace
    }
    return if (this.isNullOrEmpty() || this.isHasChineseChar()) replace else this
}

fun Array<String>.getEnvValue(env: String = BuildConfig.ENV_TYPE): String {
    val indexOfCurrentEnv = BuildConfig.ENV_SCOPE.indexOf(env)
    check(indexOfCurrentEnv >= 0) { "Env:$env not found in ${BuildConfig.ENV_SCOPE}" }
    return this[indexOfCurrentEnv]
}

fun String.unescape(): String {

    // Create a Scanner to read the string character by character
    return Scanner(this).use {
        it.useDelimiter("")

        // Create a StringBuilder to store the unescaped string
        val unescapedString = StringBuilder()

        while (it.hasNext()) {
            val c: Char = it.next()[0]
            if (c == '\\') {
                // If the character is a backslash, read the next character and add it to the string
                unescapedString.append(it.next()[0])
            } else {
                // If the character is not a backslash, add it to the string
                unescapedString.append(c)
            }
        }

        return@use unescapedString.toString()
    }
}

inline fun <C : CharSequence> C?.ifNullOrBlank(defaultValue: () -> C): C =
    if (isNullOrBlank()) defaultValue() else this

/**
 * Safely parse a color string to color int, with fallback for empty/invalid strings
 * @param defaultColor The default color to use if parsing fails (default: Color.GRAY)
 * @return The parsed color int or the default color
 */
fun String?.toColorIntSafe(defaultColor: Int = Color.GRAY): Int {
    return if (isNullOrEmpty()) {
        defaultColor
    } else {
        runCatching { toColorInt() }.getOrDefault(defaultColor)
    }
}