package com.socialplay.gpark.util.extension

import android.graphics.Point
import android.graphics.Rect
import android.os.Build
import android.os.Looper
import android.text.InputFilter
import android.view.MotionEvent
import android.view.View
import android.view.View.MeasureSpec
import android.view.ViewGroup
import android.widget.CompoundButton
import android.widget.HorizontalScrollView
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.constraintlayout.widget.ConstraintHelper
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.ViewCompat
import androidx.core.view.children
import androidx.core.view.doOnNextLayout
import androidx.core.view.doOnPreDraw
import androidx.core.view.isVisible
import androidx.customview.widget.ViewDragHelper
import androidx.drawerlayout.widget.DrawerLayout
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.transition.Fade
import androidx.transition.Transition
import androidx.transition.TransitionManager
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.lottie.LottieAnimationView
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.R
import com.socialplay.gpark.util.ScreenUtil
import timber.log.Timber
import kotlin.math.max

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/05/12
 * desc   :
 * </pre>
 */

private class OnAntiViolenceClickListener(
    val listener: (View) -> Unit,
    val interval: Int = MIN_CLICK_INTERVAL_TIME,
    val needAntiViolence: () -> Boolean = { true }
) : View.OnClickListener {
    companion object {
        internal const val MIN_CLICK_INTERVAL_TIME = 234

        // 点击时间记录
        var time: Long = -12345678910
    }

    override fun onClick(view: View) {
        if (!needAntiViolence()) {
            listener(view)
            return
        }
        val current = System.currentTimeMillis()
        when {
            time == -12345678910                            -> {
                time = current
                listener(view)
            }
            current - time < 0 || current - time > interval -> {
                time = System.currentTimeMillis()
                listener(view)
            }
        }
    }

}

fun View.setOnAntiViolenceClickListener(listener: (View) -> Unit) {
    setOnClickListener(OnAntiViolenceClickListener(listener))
}

fun View.setOnAntiViolenceClickListener(
    interval: Int = OnAntiViolenceClickListener.MIN_CLICK_INTERVAL_TIME,
    listener: (View) -> Unit
) {
    setOnClickListener(OnAntiViolenceClickListener(listener, interval))
}

fun View.setOnAntiViolenceClickListener(
    needAntiViolence: () -> Boolean,
    listener: (View) -> Unit
) {
    setOnClickListener(OnAntiViolenceClickListener(listener, needAntiViolence = needAntiViolence))
}

fun View.visible(visible: Boolean = true, transition: Boolean = false, animDuration: Long = 200) {
    if (transition) {
        val transitionAnim: Transition = Fade()
        transitionAnim.duration = animDuration
        transitionAnim.addTarget(this)
        TransitionManager.beginDelayedTransition(parent as ViewGroup, transitionAnim)
    }

    if (visible) {
        this.visibility = View.VISIBLE
    } else {
        this.visibility = View.GONE
    }
}

fun visibleList(vararg views: View, visible: Boolean = true) {
    views.forEach {
        it.visible(visible, false)
    }
}

fun View.invisible(invisible: Boolean = true, transition: Boolean = false) {
    if (transition) {
        val transitionAnim: Transition = Fade()
        transitionAnim.duration = 200
        transitionAnim.addTarget(this)
        TransitionManager.beginDelayedTransition(parent as ViewGroup, transitionAnim)
    }

    if (invisible) {
        this.visibility = View.INVISIBLE
    } else {
        this.visibility = View.VISIBLE
    }
}

fun View.gone(gone: Boolean = true) {
    visibility = if (gone) View.GONE else View.VISIBLE
}

fun View.setWidth(width: Int) {
    if (layoutParams.width == width) return
    layoutParams.width = width
    layoutParams = layoutParams
}

fun View.setWidthEx(width: Int) {
    if (layoutParams.width == width) return
    layoutParams.width = width
    layoutParams = layoutParams
}

fun View.setHeight(height: Int) {
    if (layoutParams.height == height) return
    layoutParams.height = height
    layoutParams = layoutParams
}

fun View.setHeightEx(height: Int) {
    if (layoutParams.height == height) return
    layoutParams.height = height
    layoutParams = layoutParams
}

fun View.setSize(width: Int, height: Int) {
    if (layoutParams.width == width && layoutParams.height == height) return
    layoutParams.width = width
    layoutParams.height = height
    layoutParams = layoutParams
}

fun View.setEndMargin(end: Int) {
    val lp = layoutParams as? ViewGroup.MarginLayoutParams ?: return
    var changed = false
    if (end != lp.marginEnd) {
        lp.marginEnd = end
        changed = true
    }
    if (changed) {
        layoutParams = lp
    }
}

fun View.setMargin(left: Int? = null, top: Int? = null, right: Int? = null, bottom: Int? = null) {
    val lp = layoutParams as? ViewGroup.MarginLayoutParams ?: return

    var changed = false
    if (left != null && left != lp.leftMargin) {
        lp.leftMargin = left
        changed = true
    }
    if (top != null && top != lp.topMargin) {
        lp.topMargin = top
        changed = true
    }
    if (right != null && right != lp.rightMargin) {
        lp.rightMargin = right
        changed = true
    }
    if (bottom != null && bottom != lp.bottomMargin) {
        lp.bottomMargin = bottom
        changed = true
    }
    if (changed) {
        layoutParams = lp
    }
}

fun View.setPaddingEx(left: Int? = null, top: Int? = null, right: Int? = null, bottom: Int? = null) {
    var changed = false
    if (left != null && left != paddingLeft) {
        changed = true
    }
    if (top != null && top != paddingTop) {
        changed = true
    }
    if (right != null && right != paddingRight) {
        changed = true
    }
    if (bottom != null && bottom != paddingBottom) {
        changed = true
    }
    if (changed) {
        setPadding(left ?: paddingLeft, top ?: paddingTop, right ?: paddingRight, bottom ?: paddingBottom)
    }
}

fun View.setPaddingEnd(end: Int) {
    var changed = false
    if (end != paddingRight) {
        changed = true
    }
    if (changed) {
        setPaddingRelative(paddingStart, paddingTop, end, paddingBottom)
    }
}

fun ConstraintHelper.removeViewReference(reference: View) {
    this.referencedIds = this.referencedIds.filter { it != reference.id }.toIntArray()
}

fun ConstraintHelper.addViewReference(reference: View) {
    if (this.referencedIds.contains(reference.id)) return
    this.referencedIds = intArrayOf(reference.id, *this.referencedIds)
}


fun View.getLocationInWindow(): Point {
    val locationArray = intArrayOf(0, 0)
    getLocationInWindow(locationArray)
    return Point(locationArray[0], locationArray[1])
}

fun View.getLocationOnScreenEx(): Point {
    val locationArray = intArrayOf(0, 0)
    getLocationOnScreen(locationArray)
    return Point(locationArray[0], locationArray[1])
}

fun View.translationY(to: Number, duration: Number = 240) {
    this.animate().translationY(to.toFloat()).setDuration(duration.toLong()).start()
}

fun TextView.setFontWeight300() {
    typeface = ResourcesCompat.getFont(context, R.font.poppins_light_300)
}

fun TextView.setFontWeight400() {
    typeface = ResourcesCompat.getFont(context, R.font.poppins_regular_400)
}

fun TextView.setFontWeight500() {
    typeface = ResourcesCompat.getFont(context, R.font.poppins_medium_500)
}

fun TextView.setFontWeight600() {
    typeface = ResourcesCompat.getFont(context, R.font.poppins_semi_bold_600)
}

fun TextView.setFontWeight700() {
    typeface = ResourcesCompat.getFont(context, R.font.poppins_bold_700)
}

fun TextView.setFontFamily(font: Int) {
    typeface = ResourcesCompat.getFont(context, font)
}

fun TextView.goneIfValueEmpty(value: String?) {
    text = value
    isVisible = !value.isNullOrEmpty()
}

/**
 * 获取TextView声明的MaxLength
 */
fun TextView.getMaxLength(): Int {
    var maxLen = 0

    for (filter in filters) {
        if (filter is InputFilter.LengthFilter) {
            maxLen = filter.max
        }
    }

    return maxLen
}

fun View?.enableParentClip2TabLayout() {
    enableParentClip2<TabLayout>()
}

inline fun <reified T: ViewGroup> View?.enableParentClip2() {
    this ?: return
    if (this is ViewGroup) {
        clipToPadding = false
        clipChildren = false
    }
    var curParent = parent as? ViewGroup
    while (curParent != null) {
        curParent.clipToPadding = false
        curParent.clipChildren = false
        if (curParent is T) return
        curParent = curParent.parent as? ViewGroup
    }
}

fun TabLayout.addTabGap(
    left: Int = 0,
    top: Int = 0,
    right: Int = 0,
    bottom: Int = 0,
    includeFirstTab: Boolean = false
) {
    doOnNextLayout {
        val tabCount = tabCount
        val startIndex = if (includeFirstTab) 0 else 1
        for (i in startIndex until tabCount) {
            val tab = (getChildAt(0) as? ViewGroup)?.getChildAt(i) ?: continue
            val lp = tab.layoutParams as? ViewGroup.MarginLayoutParams ?: continue
            lp.setMargins(left, top, right, bottom)
            tab.requestLayout()
        }
    }
}

fun TabLayout.addTabGapFirstLast(
    left: Int = 0,
    right: Int = 0,
) {
    doOnNextLayout {
        val vg = getChildAt(0) as? ViewGroup ?: return@doOnNextLayout
        val tabFirst = vg.getChildAt(0) ?: return@doOnNextLayout
        val tabLast = vg.getChildAt(tabCount - 1) ?: return@doOnNextLayout
        val tabFirstLp =
            tabFirst.layoutParams as? ViewGroup.MarginLayoutParams ?: return@doOnNextLayout
        val tabLastLp =
            tabLast.layoutParams as? ViewGroup.MarginLayoutParams ?: return@doOnNextLayout
        tabFirstLp.setMargins(left, 0, 0, 0)
        tabLastLp.setMargins(0, 0, right, 0)
        vg.requestLayout()
    }
}

inline fun View.doOnLayoutSized(crossinline action: (view: View) -> Unit) {
    if (ViewCompat.isLaidOut(this) && !isLayoutRequested && width > 0 && height > 0) {
        action(this)
    } else {
        addOnLayoutChangeListener(object : View.OnLayoutChangeListener {
            override fun onLayoutChange(
                view: View,
                left: Int,
                top: Int,
                right: Int,
                bottom: Int,
                oldLeft: Int,
                oldTop: Int,
                oldRight: Int,
                oldBottom: Int
            ) {
                if ((bottom - top) > 0 && (right - left) > 0) {
                    view.removeOnLayoutChangeListener(this)
                    action(view)
                }
            }
        })
    }
}

fun View.doOnLayoutChanged(
    owner: LifecycleOwner,
    action: (view: View) -> Unit
): View.OnLayoutChangeListener {
    val listener = View.OnLayoutChangeListener { view, _, _, _, _, _, _, _, _ ->
        action(view)
    }
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            addOnLayoutChangeListener(listener)
        },
        unregister = {
            removeOnLayoutChangeListener(listener)
        }
    )
    return listener
}

fun View.visiblePercent(): Float {
    if (!isVisible) return 0F
    val rect = Rect()
    val vis = getGlobalVisibleRect(rect)
    return if (vis) {
        (rect.width().toFloat() * rect.height()) / (width * height) * 100
    } else {
        0F
    }
}

fun View.isVisibleByPercent(percent: Int): Boolean {
    return visiblePercent() >= percent
}

fun View.enableWithAlpha(enable: Boolean, alpha: Float = 0.5F) {
    this.isEnabled = enable
    this.alpha = if (enable) 1F else alpha
}

fun View.enableAllWithAlpha(enable: Boolean, alpha: Float = 0.5F) {
    this.enableWithAlpha(enable, alpha)
    if (this is ViewGroup) {
        children.forEach { child ->
            if (child is ViewGroup) {
                child.enableAllWithAlpha(enable, alpha)
            } else {
                child.enableWithAlpha(enable, 1F)
            }
        }
    }
}

fun View.enableAllWithAlphaOpt(enable: Boolean, alpha: Float = 0.5F) {
    val enableRecord = this.getTag(R.id.enable_all_with_alpha) as Boolean?
    if (enableRecord == null || enableRecord != enable) {
        this.setTag(R.id.enable_all_with_alpha, enable)
        this.enableAllWithAlpha(enable)
    }
}

fun View.setAllAlpha(alpha: Float = 0.5F) {
    this.alpha = alpha
    if (this is ViewGroup) {
        children.forEach { child ->
            if (child is ViewGroup) {
                child.setAllAlpha(alpha)
            } else {
                child.alpha = alpha
            }
        }
    }
}

fun View.setAllAlphaOpt(alpha: Float = 0.5F) {
    val alphaRecord = this.getTag(R.id.set_all_alpha) as Float?
    if (alphaRecord == null || alphaRecord != alpha) {
        this.setTag(R.id.set_all_alpha, alpha)
        this.setAllAlpha(alpha)
    }
}

fun View.unsetOnTouch() {
    setOnTouchListener(null)
}

fun View.unsetOnClick() {
    setOnClickListener(null)
}

fun View.unsetOnClickAndClickable() {
    setOnClickListener(null)
    isClickable = false
}

fun View.unsetOnLongClick() {
    setOnLongClickListener(null)
}

fun View.unsetOnLongClickAndLongClickable() {
    setOnLongClickListener(null)
    isLongClickable = false
}

fun View.setBackgroundColorByRes(@ColorRes resId: Int) {
    setBackgroundColor(getColorByRes(resId))
}

fun View.backgroundTintListByRes(resId: Int) {
    backgroundTintList = getCslByRes(resId)
}

fun View.backgroundTintListByColor(color: Int) {
    backgroundTintList = getCslByColor(color)
}

@Throws
fun View.backgroundTintListByColorStr(colorStr: String) {
    backgroundTintList = getCslByColorStr(colorStr)
}

fun ImageView.imageTintListByRes(resId: Int) {
    imageTintList = getCslByRes(resId)
}

fun ImageView.imageTintListByColor(color: Int) {
    imageTintList = getCslByColor(color)
}

fun LottieAnimationView.cancelAnimationIfAnimating() {
    if (isAnimating) {
        cancelAnimation()
    }
}

fun LifecycleOwner.runOnMainThreadWhenNotDestroyed(block: () -> Unit) {
    if (Looper.getMainLooper().thread != Thread.currentThread()) {
        throw IllegalStateException("Must invoke on main thread")
    }
    if (lifecycle.currentState == Lifecycle.State.DESTROYED) {
        return
    }
    block()
}

fun LifecycleOwner.observeOnMainThreadWhenNotDestroyed(
    register: () -> Unit,
    unregister: () -> Unit
) {
    runOnMainThreadWhenNotDestroyed {
        register()
        runWhenDestroyed(unregister)
    }
}

fun View.doOnFocusChange(owner: LifecycleOwner, action: View.OnFocusChangeListener) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            onFocusChangeListener = action
        }, unregister = {
            onFocusChangeListener = null
        }
    )
}

fun View.onTouch(owner: LifecycleOwner, action: View.OnTouchListener) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            setOnTouchListener(action)
        }, unregister = {
            setOnTouchListener(null)
        }
    )
}

fun BottomSheetBehavior<*>.addBottomSheetCallback(
    owner: LifecycleOwner,
    callback: BottomSheetBehavior.BottomSheetCallback
) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            addBottomSheetCallback(callback)
        }, unregister = {
            removeBottomSheetCallback(callback)
        }
    )
}

fun TabLayoutMediator.attach(owner: LifecycleOwner) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            attach()
        }, unregister = {
            detach()
        }
    )
}
fun ViewPager2.registerOnPageChangeCallback(
    owner: LifecycleOwner,
    callback: ViewPager2.OnPageChangeCallback
) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            registerOnPageChangeCallback(callback)
        }, unregister = {
            unregisterOnPageChangeCallback(callback)
        }
    )
}

fun TabLayout.addOnTabSelectedListener(
    owner: LifecycleOwner,
    listener: TabLayout.OnTabSelectedListener
) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            addOnTabSelectedListener(listener)
        }, unregister = {
            removeOnTabSelectedListener(listener)
        }
    )
}

fun AppBarLayout.addOnOffsetChangedListener(
    owner: LifecycleOwner,
    callback: AppBarLayout.OnOffsetChangedListener
) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            addOnOffsetChangedListener(callback)
        }, unregister = {
            removeOnOffsetChangedListener(callback)
        }
    )
}

fun AppBarLayout.setDraggableForever(owner: LifecycleOwner) {
    setDragCallback(owner) { true }
}

fun AppBarLayout.setDragCallback(owner: LifecycleOwner, canDrag: () -> Boolean) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            currentBehavior?.setDragCallback(object : AppBarLayout.Behavior.DragCallback () {
                override fun canDrag(appBarLayout: AppBarLayout): Boolean {
                    return canDrag()
                }
            })
        },
        unregister = {
            currentBehavior?.setDragCallback(null)
        }
    )
}

val AppBarLayout.currentBehavior: AppBarLayout.Behavior?
    get() {
        return (layoutParams as? CoordinatorLayout.LayoutParams)?.behavior as? AppBarLayout.Behavior
    }

fun CompoundButton.setOnCheckedChangeListener(
    owner: LifecycleOwner,
    listener: CompoundButton.OnCheckedChangeListener
) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            setOnCheckedChangeListener(listener)
        }, unregister = {
            setOnCheckedChangeListener(null)
        }
    )
}

fun MotionEvent.getXYByPointerIndex(): Pair<Float, Float> {
    val pointerIndex = actionIndex
    return getX(pointerIndex) to getY(pointerIndex)
}

fun MotionEvent.getXYByPointerIndexInInt(): Pair<Int, Int> {
    val pointerIndex = actionIndex
    return getX(pointerIndex).toInt() to getY(pointerIndex).toInt()
}

fun View.measure(
    width: Int,
    height: Int,
    widthMeasureMode: Int = MeasureSpec.AT_MOST,
    heightMeasureMode: Int = MeasureSpec.AT_MOST
) {

    this.measure(
        MeasureSpec.makeMeasureSpec(width, widthMeasureMode),
        MeasureSpec.makeMeasureSpec(height, heightMeasureMode)
    )
}

fun View.syncHeightWith(
    another: View,
    oneshot: Boolean = false
) {
    another.doOnPreDraw {
        val height = another.height
        Timber.d("View.syncHeight height:$height")

        if (this.height != height) {
            this.setHeight(height)
        }

        if (!oneshot) {
            another.doOnNextLayout { syncHeightWith(another, false) }
        }
    }
}

fun HorizontalScrollView.getContentWidth(): Int {
    val count: Int = childCount
    val contentWidth: Int = width - paddingLeft - paddingRight
    if (count == 0) {
        return contentWidth
    }
    return getChildAt(0).width
}

fun TabLayout.selectedTab() = getTabAt(selectedTabPosition)

fun View.addOnLayoutChangeListener(
    owner: LifecycleOwner,
    listener: View.OnLayoutChangeListener
) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            addOnLayoutChangeListener(listener)
        }, unregister = {
            removeOnLayoutChangeListener(listener)
        }
    )
}

val View.validHeight get() = height.coerceAtLeast(measuredHeight)

/**
 * 设置侧边菜单栏触发范围
 *
 * @param orientation Left | Right
 * @param percent 屏幕宽度的百分比
 * @param viewWidth 抽屉视图的宽度
 */
fun DrawerLayout.setEdgeSize(orientation: String, percent: Float? = null, viewWidth: Int? = null) {
    try {
        val screenWidth = ScreenUtil.getScreenWidth(context)
        val targetEdgeSize = if (percent != null) {
            (screenWidth * percent).toInt()
        } else if (viewWidth != null) {
            screenWidth - viewWidth
        } else {
            return
        }

        var actualClass: Class<in DrawerLayout> = javaClass
        while (!actualClass.isAssignableFrom(DrawerLayout::class.java)) {
            actualClass = actualClass.superclass
        }

        val draggerField = actualClass.getDeclaredField("m${orientation}Dragger")
        draggerField.isAccessible = true
        val dragger = draggerField[this] as ViewDragHelper

        val edgeSizeField = dragger.javaClass.getDeclaredField("mEdgeSize")
        edgeSizeField.isAccessible = true
        val edgeSize = edgeSizeField.getInt(dragger)
        edgeSizeField.setInt(dragger, max(edgeSize, targetEdgeSize))

        val callbackField = actualClass.getDeclaredField("m${orientation}Callback")
        callbackField.isAccessible = true
        val callback = callbackField[this] as ViewDragHelper.Callback

        val peekRunnableField = callback.javaClass.getDeclaredField("mPeekRunnable")
        peekRunnableField.isAccessible = true
        peekRunnableField[callback] = Runnable {}

        if (Build.VERSION.SDK_INT >= 29) {
            val defaultEdgeSizeField = dragger.javaClass.getDeclaredField("mDefaultEdgeSize")
            defaultEdgeSizeField.isAccessible = true
            val defaultEdgeSize = defaultEdgeSizeField.getInt(dragger)
            defaultEdgeSizeField.setInt(dragger, max(defaultEdgeSize, targetEdgeSize))
        }
    } catch (_: Exception) {
    }
}

fun DrawerLayout.addDrawerListener(owner: LifecycleOwner, listener: DrawerLayout.DrawerListener) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            addDrawerListener(listener)
        },
        unregister = {
            removeDrawerListener(listener)
        }
    )
}

val View.lifecycleOwner: LifecycleOwner get() {
    val self = this
    var owner = self.getTag(R.id.view_lifecycle_owner) as? LifecycleOwner
    if(owner == null) {
        val lifecycleOwner = object : LifecycleOwner {
            private val registry = LifecycleRegistry(this)
            override val lifecycle: LifecycleRegistry = registry
        }
        self.setTag(R.id.view_lifecycle_owner, lifecycleOwner)
        val viewLifecycle = lifecycleOwner.lifecycle
        owner = lifecycleOwner
        self.addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(v: View) {
                viewLifecycle.currentState = Lifecycle.State.STARTED
                viewLifecycle.handleLifecycleEvent(Lifecycle.Event.ON_START)
                viewLifecycle.handleLifecycleEvent(Lifecycle.Event.ON_RESUME)
            }

            override fun onViewDetachedFromWindow(v: View) {
                viewLifecycle.handleLifecycleEvent(Lifecycle.Event.ON_PAUSE)
                viewLifecycle.handleLifecycleEvent(Lifecycle.Event.ON_STOP)
                viewLifecycle.currentState = Lifecycle.State.DESTROYED
                self.setTag(R.id.view_lifecycle_owner, null)
                self.removeOnAttachStateChangeListener(this)
            }
        })
    }
    return owner
}