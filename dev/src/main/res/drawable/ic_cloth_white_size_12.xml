<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="13dp"
    android:height="13dp"
    android:viewportWidth="13"
    android:viewportHeight="13">
  <group>
    <clip-path
        android:pathData="M0.75,0.406h11.958v11.958h-11.958z"/>
    <path
        android:pathData="M4.782,1.962C5.045,1.962 5.248,2.179 5.308,2.434C5.473,3.137 6.03,3.401 6.729,3.401C7.429,3.401 7.986,3.137 8.151,2.434C8.211,2.179 8.414,1.962 8.676,1.962H9.285C9.809,1.962 10.291,2.25 10.54,2.712L11.555,4.596C11.991,5.404 11.689,6.413 10.88,6.849L10.32,7.15L10.373,9.613C10.39,10.412 9.747,11.068 8.948,11.068H4.511C3.712,11.068 3.069,10.412 3.086,9.613L3.138,7.15L2.579,6.849C1.77,6.413 1.467,5.404 1.903,4.596L2.918,2.712C3.167,2.25 3.649,1.962 4.173,1.962H4.782Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="2.678"
            android:startY="2.507"
            android:endX="4.947"
            android:endY="12.284"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#B2FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
