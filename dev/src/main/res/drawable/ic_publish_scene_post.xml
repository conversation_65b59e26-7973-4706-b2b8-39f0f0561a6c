<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="36dp"
    android:height="36dp"
    android:viewportWidth="36"
    android:viewportHeight="36">
  <path
      android:pathData="M23.587,4.667H12.413C7.56,4.667 4.667,7.56 4.667,12.414V23.574C4.667,28.44 7.56,31.334 12.413,31.334H23.573C28.427,31.334 31.32,28.44 31.32,23.587V12.414C31.333,7.56 28.44,4.667 23.587,4.667Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M23.587,4.667H12.413C7.56,4.667 4.667,7.56 4.667,12.414V23.574C4.667,28.44 7.56,31.334 12.413,31.334H23.573C28.427,31.334 31.32,28.44 31.32,23.587V12.414C31.333,7.56 28.44,4.667 23.587,4.667Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="1.557"
          android:startY="4.667"
          android:endX="17.611"
          android:endY="31.562"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF9C00"/>
        <item android:offset="1" android:color="#FFFFC160"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M12.435,23.334H23.565C24.022,23.334 24.4,22.988 24.4,22.572C24.4,22.155 24.022,21.81 23.565,21.81H12.435C11.979,21.81 11.6,22.155 11.6,22.572C11.6,22.988 11.979,23.334 12.435,23.334Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="22.452"
          android:startY="12.311"
          android:endX="21.675"
          android:endY="34.258"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#33FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M12.435,18.762H23.565C24.022,18.762 24.4,18.417 24.4,18C24.4,17.584 24.022,17.238 23.565,17.238H12.435C11.979,17.238 11.6,17.584 11.6,18C11.6,18.417 11.979,18.762 12.435,18.762Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="22.452"
          android:startY="12.311"
          android:endX="21.675"
          android:endY="34.258"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#33FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M12.435,14.191H23.565C24.022,14.191 24.4,13.845 24.4,13.429C24.4,13.012 24.022,12.667 23.565,12.667H12.435C11.979,12.667 11.6,13.012 11.6,13.429C11.6,13.845 11.979,14.191 12.435,14.191Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="22.452"
          android:startY="12.311"
          android:endX="21.675"
          android:endY="34.258"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#33FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
