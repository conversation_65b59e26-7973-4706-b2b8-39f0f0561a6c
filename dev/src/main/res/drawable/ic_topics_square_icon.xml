<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
  <path
      android:pathData="M12,0L36,0A12,12 0,0 1,48 12L48,36A12,12 0,0 1,36 48L12,48A12,12 0,0 1,0 36L0,12A12,12 0,0 1,12 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="47.455"
          android:startY="20.5"
          android:endX="-0.001"
          android:endY="20.574"
          android:type="linear">
        <item android:offset="0" android:color="#26FFAFA1"/>
        <item android:offset="1" android:color="#26EDA4FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M12,0L36,0A12,12 0,0 1,48 12L48,36A12,12 0,0 1,36 48L12,48A12,12 0,0 1,0 36L0,12A12,12 0,0 1,12 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="23.182"
          android:startY="48"
          android:endX="23.182"
          android:endY="-0"
          android:type="linear">
        <item android:offset="0" android:color="#FFEDF3FF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M9,9h30v30h-30z"/>
    <path
        android:pathData="M10.633,27.415C12.641,34.764 20.227,39.094 27.576,37.085C29.559,36.543 31.323,35.595 32.801,34.355L37.5,34.467C38.071,34.481 38.442,33.87 38.167,33.369L36.282,29.938C37.746,27.008 38.178,23.549 37.246,20.141C35.237,12.792 27.651,8.463 20.302,10.472C12.953,12.48 8.624,20.066 10.633,27.415Z"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="3.891"
            android:startY="14.957"
            android:endX="27.698"
            android:endY="37.529"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFDC1C"/>
          <item android:offset="1" android:color="#FFFFC160"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M10.633,27.415C12.641,34.764 20.227,39.094 27.576,37.085C29.559,36.543 31.323,35.595 32.801,34.355L37.5,34.467C38.071,34.481 38.442,33.87 38.167,33.369L36.282,29.938C37.746,27.008 38.178,23.549 37.246,20.141C35.237,12.792 27.651,8.463 20.302,10.472C12.953,12.48 8.624,20.066 10.633,27.415Z"
        android:fillAlpha="0.6">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="20.302"
            android:startY="10.472"
            android:endX="27.698"
            android:endY="37.529"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFAA21"/>
          <item android:offset="1" android:color="#FFFF6C6C"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M26.335,15.455C27.097,15.451 27.717,16.066 27.721,16.828L27.729,18.517L29.563,18.017C30.297,17.816 31.056,18.249 31.257,18.983C31.458,19.718 31.024,20.477 30.289,20.678L27.743,21.373L27.757,24.229L30.13,23.582C30.865,23.381 31.623,23.814 31.824,24.549C32.025,25.284 31.592,26.042 30.858,26.243L27.772,27.086L27.785,29.703C27.789,30.465 27.175,31.086 26.413,31.09C25.651,31.094 25.03,30.479 25.027,29.717L25.017,27.839L22.449,28.541L22.463,31.158C22.467,31.92 21.852,32.541 21.09,32.545C20.328,32.549 19.708,31.934 19.704,31.172L19.694,29.294L18.438,29.638C17.703,29.838 16.944,29.406 16.743,28.671C16.542,27.936 16.975,27.178 17.71,26.977L19.68,26.438L19.666,23.581L17.87,24.072C17.135,24.273 16.377,23.84 16.176,23.105C15.975,22.371 16.408,21.612 17.143,21.411L19.652,20.725L19.64,18.297C19.636,17.535 20.25,16.914 21.012,16.91C21.774,16.906 22.395,17.521 22.399,18.283L22.406,19.972L24.974,19.27L24.962,16.842C24.958,16.08 25.573,15.459 26.335,15.455ZM22.421,22.828L22.435,25.685L25.002,24.982L24.988,22.126L22.421,22.828Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="26.277"
            android:startY="16.945"
            android:endX="26.379"
            android:endY="37.418"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#33FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
