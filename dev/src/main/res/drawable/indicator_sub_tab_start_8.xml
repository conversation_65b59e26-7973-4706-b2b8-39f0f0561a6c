<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 二级tab的通用的indicator -->
    <!-- 当TabLayout的tabPaddingStart=8dp而tabPaddingEnd=16dp时, indicator没居中, 所以需要indicator start偏移8dp -->
    <item
        android:width="@dimen/dp_12"
        android:height="@dimen/dp_4"
        android:gravity="center_horizontal"
        android:start="@dimen/dp_8">
        <shape android:shape="rectangle">
            <solid android:color="@color/color_FFDD70" />
            <corners android:radius="@dimen/dp_2" />
        </shape>
    </item>
</layer-list>