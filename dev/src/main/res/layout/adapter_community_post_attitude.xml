<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layerLike"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivLike"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_marginVertical="@dimen/dp_16"
            android:layout_marginStart="@dimen/dp_16"
            android:src="@drawable/ic_heart_666666_size_20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvLike"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lavLikeAnim"
            android:layout_width="@dimen/dp_33"
            android:layout_height="@dimen/dp_33"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/ivLike"
            app:layout_constraintEnd_toEndOf="@id/ivLike"
            app:layout_constraintStart_toStartOf="@id/ivLike"
            app:layout_constraintTop_toTopOf="@id/ivLike"
            app:lottie_autoPlay="false"
            app:lottie_fileName="community_like.zip"
            app:lottie_progress="1"
            tools:visibility="visible" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvLike"
            style="@style/MetaTextView.S13.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_16"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@color/color_666666"
            app:layout_constraintBottom_toBottomOf="@id/ivLike"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/ivLike"
            app:layout_constraintTop_toTopOf="@id/ivLike"
            tools:text="1000.0K" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layerComment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivComment"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_16"
            android:src="@drawable/ic_comment_666666_size_20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvComment"
            style="@style/MetaTextView.S13.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_16"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@color/color_666666"
            app:layout_constraintBottom_toBottomOf="@id/ivComment"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/ivComment"
            app:layout_constraintTop_toTopOf="@id/ivComment"
            tools:text="1000.0K" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layerShare"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivShare"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_16"
            android:src="@drawable/ic_share_666666_size_20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvShare"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvShare"
            style="@style/MetaTextView.S13.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_16"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@color/color_666666"
            app:layout_constraintBottom_toBottomOf="@id/ivShare"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/ivShare"
            app:layout_constraintTop_toTopOf="@id/ivShare"
            tools:text="1000.0K" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>