<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/vBg"
        android:layout_width="@dimen/dp_108"
        android:layout_height="@dimen/dp_150"
        android:layout_marginEnd="@dimen/dp_9"
        android:background="@drawable/bg_recommend_creator_colorful"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lav"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/vBg"
        app:layout_constraintEnd_toEndOf="@id/vBg"
        app:layout_constraintStart_toStartOf="@id/vBg"
        app:layout_constraintTop_toTopOf="@id/vBg"
        app:lottie_fileName="bg_creator_card.zip" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:padding="@dimen/dp_05"
        android:layout_marginTop="@dimen/dp_15"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="@id/vBg"
        app:layout_constraintStart_toStartOf="@id/vBg"
        app:layout_constraintTop_toTopOf="@id/vBg"
        app:shapeAppearance="@style/circleStyle"
        app:strokeColor="@color/white_80"
        app:strokeWidth="@dimen/dp_1"
        tools:src="@drawable/icon_default_creator_yellow_size88" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvUserName"
        style="@style/MetaTextView.S13.PoppinsSemiBold600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_3"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        app:uiLineHeight="@dimen/dp_20"
        android:textColor="@color/neutral_color_1"
        app:layout_constraintEnd_toEndOf="@id/vBg"
        app:layout_constraintStart_toStartOf="@id/vBg"
        app:layout_constraintTop_toBottomOf="@id/iv"
        tools:text="User NameName" />

    <com.socialplay.gpark.ui.view.FollowView
        android:id="@+id/followView"
        android:layout_width="@dimen/dp_88"
        android:layout_height="@dimen/dp_28"
        android:layout_marginTop="@dimen/dp_12"
        app:followingSolidColor="@color/white_60"
        app:followingTextColor="@color/color_B3B3B3"
        app:layout_constraintEnd_toEndOf="@id/vBg"
        app:layout_constraintStart_toStartOf="@id/vBg"
        app:layout_constraintTop_toBottomOf="@id/tvUserName"
        app:unfollowTextColor="@color/color_1A1A1A"
        app:viewStyle="solid" />

</androidx.constraintlayout.widget.ConstraintLayout>