<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <!--！！如果要修改id或替换掉ConstraintLayout，请注意kotlin动态引用也需要修改！！-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="@dimen/dp_164"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="164:190"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_room_item_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/placeholder_corner_12"
            app:shapeAppearance="@style/round_corner_12dp" />

        <LinearLayout
            android:id="@+id/ll_parent_tag"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_22"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_12"
            android:background="@drawable/bg_white_20_corner_4"
            android:maxWidth="@dimen/dp_140"
            android:orientation="horizontal"
            android:paddingStart="@dimen/dp_4"
            android:paddingEnd="@dimen/dp_4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/iv_room_mark"
                android:layout_width="@dimen/dp_17"
                android:layout_height="@dimen/dp_18"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/dp_6"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                app:lottie_fileName="party.zip" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_room_tag"
                style="@style/MetaTextView.S12.PoppinsMedium500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:ellipsize="end"
                android:gravity="start|top"
                android:singleLine="true"
                android:textColor="@color/white"
                tools:text="chatting" />

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rl_parent_member"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_24"
            android:layout_marginStart="@dimen/dp_6"
            android:layout_marginBottom="@dimen/dp_12"
            android:paddingStart="@dimen/dp_6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/room_member_3"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:layout_marginStart="@dimen/dp_34"
                android:padding="@dimen/dp_1"
                android:visibility="gone"
                app:shapeAppearance="@style/circleStyle"
                app:strokeColor="@color/white"
                app:strokeWidth="@dimen/dp_1"
                tools:src="@drawable/icon_default_avatar"
                tools:visibility="visible" />

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/room_member_2"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:layout_marginStart="@dimen/dp_17"
                android:padding="@dimen/dp_1"
                android:visibility="gone"
                app:shapeAppearance="@style/circleStyle"
                app:strokeColor="@color/white"
                app:strokeWidth="@dimen/dp_1"
                tools:src="@drawable/icon_default_avatar"
                tools:visibility="visible" />

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/room_member_1"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:padding="@dimen/dp_1"
                android:visibility="gone"
                app:shapeAppearance="@style/circleStyle"
                app:strokeColor="@color/white"
                app:strokeWidth="@dimen/dp_1"
                tools:src="@drawable/icon_default_avatar"
                tools:visibility="visible" />
        </RelativeLayout>


        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_player_count"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_6"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="@id/rl_parent_member"
            app:layout_constraintStart_toEndOf="@id/rl_parent_member"
            app:layout_constraintTop_toTopOf="@id/rl_parent_member"
            tools:text="12 player" />

        <RelativeLayout
            android:id="@+id/rv_parent_style"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:paddingEnd="@dimen/dp_12"
            app:layout_constraintStart_toStartOf="@id/ll_parent_tag"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_parent_tag">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_room_style"
                style="@style/MetaTextView.S10.PoppinsSemiBold600"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_20"
                android:background="@drawable/shape_white_corner"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/dp_6"
                android:singleLine="true"
                android:textColor="@color/color_B944BF"
                tools:text="club" />
        </RelativeLayout>


        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_room_name"
            style="@style/MetaTextView.S16.PoppinsSemiBold600"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp_6"
            android:layout_marginEnd="@dimen/dp_14"
            android:layout_marginBottom="@dimen/dp_2"
            android:ellipsize="end"
            android:textColor="@color/white"
            app:layout_constraintBottom_toTopOf="@id/rl_parent_member"
            app:layout_constraintStart_toStartOf="@id/ll_parent_tag"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/rv_parent_style"
            tools:text="let's talk about lifelet's talk about lifelet's talk about life" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>