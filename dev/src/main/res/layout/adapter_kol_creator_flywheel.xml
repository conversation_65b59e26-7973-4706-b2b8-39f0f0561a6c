<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivIcon"
        android:layout_width="@dimen/dp_52"
        android:layout_height="@dimen/dp_52"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_16"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/circleStyle"
        tools:src="@drawable/placeholder_icon" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvName"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="top|center_horizontal"
        android:lines="2"
        android:maxLines="2"
        android:minLines="2"
        android:textColor="@color/color_333333"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/ivIcon"
        app:layout_constraintStart_toStartOf="@id/ivIcon"
        app:layout_constraintTop_toBottomOf="@id/ivIcon"
        tools:text="Yangetuos" />

    <View
        android:id="@+id/vUnread"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_1"
        android:layout_marginEnd="@dimen/dp_1"
        android:background="@drawable/red_point_white_stroke"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/ivIcon"
        app:layout_constraintTop_toTopOf="@id/ivIcon"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/tvTips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_daily_task_tips"
        android:translationX="@dimen/dp_9"
        android:translationY="-3dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/ivIcon"
        app:layout_constraintTop_toTopOf="@id/ivIcon"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>