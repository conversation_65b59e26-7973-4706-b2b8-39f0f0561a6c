<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <Space
        android:id="@+id/spaceTop"
        android:layout_width="@dimen/dp_1"
        android:layout_height="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivIcon"
        android:layout_width="164dp"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder"
        app:layout_constraintDimensionRatio="164:123"
        app:layout_constraintEnd_toStartOf="@id/spaceEnd"
        app:layout_constraintStart_toEndOf="@id/spaceStart"
        app:layout_constraintTop_toBottomOf="@id/spaceTop"
        app:shapeAppearance="@style/round_corner_12dp" />

    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/mlvLike"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/ivIcon"
        app:layout_constraintStart_toStartOf="@id/ivIcon"
        app:likeText="@string/x_players"
        app:showIcon="true" />

    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/mlvPlayers"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="-4dp"
        app:layout_constraintBottom_toBottomOf="@id/ivIcon"
        app:layout_constraintStart_toEndOf="@id/mlvLike"
        app:likeIcon="@drawable/icon_item_player"
        app:likeText="@string/x_players"
        app:showIcon="true" />

    <TextView
        android:id="@+id/tvName"
        style="@style/MetaTextView.S13.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/neutral_color_1"
        app:layout_constraintEnd_toEndOf="@id/ivIcon"
        app:layout_constraintStart_toStartOf="@id/ivIcon"
        app:layout_constraintTop_toBottomOf="@id/ivIcon"
        tools:text="Leslie Alexander\n范德萨" />

    <!--  new: @drawable/bg_followed_work_new  @string/community_followed_work_tag_new-->
    <!--  hot: @drawable/bg_followed_work_hot  @string/community_followed_work_tag_hot-->
    <!--  recommend: @drawable/bg_followed_work_recommend  @string/community_followed_work_tag_recommend-->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTag"
        style="@style/MetaTextView.S10.PoppinsBlack900"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_17"
        android:background="@drawable/bg_followed_work_new"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_10"
        android:text="@string/community_followed_work_tag_new"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="@id/ivIcon"
        app:layout_constraintTop_toTopOf="@id/ivIcon"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.TagContainerView
        android:id="@+id/tags"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="@id/ivIcon"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/ivIcon"
        app:layout_constraintTop_toBottomOf="@id/tvName"
        tools:background="@color/color_999999"
        tools:layout_height="@dimen/dp_22"
        tools:layout_width="@dimen/dp_84"
        tools:visibility="visible" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAuthorAvatar"
        android:layout_width="@dimen/dp_18"
        android:layout_height="@dimen/dp_18"
        android:layout_marginTop="@dimen/dp_8"
        app:layout_constraintStart_toStartOf="@id/ivIcon"
        app:layout_constraintTop_toBottomOf="@id/tags"
        app:shapeAppearance="@style/circleStyle"
        app:strokeColor="@color/white_60"
        app:strokeWidth="@dimen/dp_1"
        tools:src="@drawable/placeholder" />

    <TextView
        android:id="@+id/tvAuthorName"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_757575"
        app:layout_constraintBottom_toBottomOf="@id/ivAuthorAvatar"
        app:layout_constraintEnd_toEndOf="@id/ivIcon"
        app:layout_constraintStart_toEndOf="@+id/ivAuthorAvatar"
        app:layout_constraintTop_toTopOf="@id/ivAuthorAvatar"
        tools:text="Leslie Alexander Alexander" />

    <Space
        android:id="@+id/spaceBottom"
        android:layout_width="@dimen/dp_1"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivAuthorAvatar" />

    <Space
        android:id="@+id/spaceStart"
        android:layout_width="0dp"
        android:layout_height="1dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:id="@+id/spaceEnd"
        android:layout_width="0dp"
        android:layout_height="1dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>