<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivBg"
        app:shapeAppearance="@style/RoundedStyle24"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="12dp"
        android:background="#4AB4FF"
        app:layout_constraintDimensionRatio="343:215"
        app:layout_constraintTop_toTopOf="parent" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/ivLive"
        android:layout_width="18dp"
        android:layout_height="20dp"
        android:layout_margin="16dp"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_fileName="party.zip"
        app:layout_constraintLeft_toLeftOf="@id/ivBg"
        app:layout_constraintTop_toTopOf="@id/ivBg"/>

    <com.socialplay.gpark.ui.view.MetaTextView
        app:layout_constraintTop_toTopOf="@id/ivLive"
        app:layout_constraintBottom_toBottomOf="@id/ivLive"
        app:layout_constraintLeft_toRightOf="@id/ivLive"
        android:text="@string/party_cap"
        android:layout_marginLeft="6dp"
        android:textColor="#fff"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <ImageView
        android:id="@+id/ivMore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="16dp"
        android:paddingVertical="12dp"
        android:src="@drawable/party_icon_more"
        app:layout_constraintRight_toRightOf="@id/ivBg"
        app:layout_constraintTop_toTopOf="@id/ivBg" />

    <LinearLayout
        android:id="@+id/llPlayer"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:layout_marginLeft="16dp"
        android:layout_marginBottom="19dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/ivBg"
        app:layout_constraintLeft_toLeftOf="@id/ivBg" />

    <com.socialplay.gpark.ui.view.MetaTextView
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:id="@+id/tvPlayerNum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="6dp"
        android:textColor="#D1FFFFFF"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@id/llPlayer"
        app:layout_constraintLeft_toRightOf="@id/llPlayer"
        app:layout_constraintTop_toTopOf="@id/llPlayer"
        tools:text="13 players" />

    <com.flyjingfish.hollowtextviewlib.HollowTextView
        android:id="@+id/tvJoin3"
        style="@style/MetaTextView.S14.PoppinsSemiBold600"
        android:layout_width="75dp"
        android:layout_height="29dp"
        android:layout_margin="16dp"
        android:background="@drawable/shape_white_corner"
        android:gravity="center"
        android:text="@string/join_all_cap"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/ivBg"
        app:layout_constraintRight_toRightOf="@id/ivBg" />

    <com.socialplay.gpark.ui.view.MetaTextView
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:id="@+id/tvDes"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="#fff"
        android:textSize="12sp"
        android:singleLine="true"
        android:layout_marginBottom="15dp"
        app:layout_constraintBottom_toTopOf="@id/llPlayer"
        app:layout_constraintLeft_toLeftOf="@id/llPlayer"
        app:layout_constraintRight_toRightOf="@id/ivBg"
        tools:text="asfihioashfoaiuhsfiouhasfihioashfoaiuhsfiouhasfihioashfoaiuhsfiouhasfihioashfoaiuhsfiouhasfihioashfoaiuhsfiouhasfihioashfoaiuhsfiouh" />

    <com.socialplay.gpark.ui.view.MetaTextView
        style="@style/MetaTextView.S18.PoppinsExtraBold800"
        android:id="@+id/tvGameName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="#fff"
        android:textSize="18sp"
        android:singleLine="true"
        app:layout_constraintBottom_toTopOf="@id/tvDes"
        app:layout_constraintLeft_toLeftOf="@id/llPlayer"
        app:layout_constraintRight_toRightOf="@id/ivBg"
        tools:text="asfihioashfoaiuhsfiouhasfihioashfoaiuhsfiouhasfihioashfoaiuhsfiouhasfihioashfoaiuhsfiouhasfihioashfoaiuhsfiouh" />


</androidx.constraintlayout.widget.ConstraintLayout>