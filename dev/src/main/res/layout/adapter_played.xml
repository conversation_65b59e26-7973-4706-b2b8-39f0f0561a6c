<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_56"
    android:layout_height="wrap_content"
    android:layout_marginRight="@dimen/dp_8">

    <ImageView
        android:id="@+id/iv"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/placeholder_corner_12" />

    <com.socialplay.gpark.ui.view.GameLoadingView
        android:id="@+id/loading"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:loadRoundRadius="@dimen/dp_12"
        app:radius="@dimen/dp_12"
        app:strokeWidth="@dimen/dp_2" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_name"
        style="@style/MetaTextView.S10.PoppinsMedium500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:gravity="left"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constraintLeft_toLeftOf="@+id/iv"
        app:layout_constraintRight_toRightOf="@+id/iv"
        app:layout_constraintTop_toBottomOf="@+id/iv"
        tools:text="Explore" />
</androidx.constraintlayout.widget.ConstraintLayout>