<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/vBg"
        android:layout_width="@dimen/dp_108"
        android:layout_height="@dimen/dp_150"
        android:layout_marginEnd="@dimen/dp_9"
        android:background="@drawable/bg_round_12_gradient_26a7a8a9_to_white_stroke_05"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:layout_marginTop="@dimen/dp_6"
        android:padding="1dp"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="@id/vBg"
        app:layout_constraintStart_toStartOf="@id/vBg"
        app:layout_constraintTop_toTopOf="@id/vBg"
        app:shapeAppearance="@style/circleStyle"
        tools:src="@drawable/icon_default_creator_yellow_size88" />

    <View
        android:id="@+id/labelViewBg"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:layout_marginStart="@dimen/dp_3"
        android:layout_marginEnd="@dimen/dp_3"
        android:background="@drawable/bg_white_90_corner_100"
        app:layout_constraintBottom_toBottomOf="@id/labelView"
        app:layout_constraintEnd_toEndOf="@id/labelView"
        app:layout_constraintStart_toStartOf="@id/labelView"
        app:layout_constraintTop_toTopOf="@id/labelView" />

    <com.socialplay.gpark.ui.view.UserLabelView
        android:id="@+id/labelView"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_4"
        app:layout_constraintBottom_toBottomOf="@id/iv"
        app:layout_constraintEnd_toEndOf="@id/iv"
        app:layout_constraintStart_toStartOf="@id/iv"
        app:layout_constraintTop_toBottomOf="@id/iv" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvUserName"
        style="@style/MetaTextView.S13.PoppinsSemiBold600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_7"
        android:layout_marginTop="@dimen/dp_10"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/neutral_color_1"
        app:layout_constraintEnd_toEndOf="@id/vBg"
        app:layout_constraintStart_toStartOf="@id/vBg"
        app:layout_constraintTop_toBottomOf="@id/iv"
        app:uiLineHeight="@dimen/dp_20"
        tools:text="User NameName" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSignature"
        style="@style/MetaTextView.S11.PoppinsRegular400"
        android:textColor="@color/color_999999"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_7"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="@id/vBg"
        app:layout_constraintStart_toStartOf="@id/vBg"
        app:layout_constraintTop_toBottomOf="@id/tvUserName"
        app:uiLineHeight="@dimen/dp_17"
        tools:text="hihihi~~" />

    <View
        android:id="@+id/vUserClick"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="@id/tvUserName"
        app:layout_constraintEnd_toEndOf="@id/vBg"
        app:layout_constraintStart_toStartOf="@id/vBg"
        app:layout_constraintTop_toTopOf="@id/vBg" />

    <com.socialplay.gpark.ui.view.FollowView
        android:id="@+id/followView"
        android:layout_width="@dimen/dp_88"
        android:layout_height="@dimen/dp_28"
        android:layout_marginTop="@dimen/dp_2"
        app:layout_constraintEnd_toEndOf="@id/vBg"
        app:layout_constraintStart_toStartOf="@id/vBg"
        app:layout_constraintTop_toBottomOf="@id/tvSignature"
        app:unfollowTextColor="@color/color_1A1A1A"
        app:viewStyle="solid" />

    <View
        android:id="@+id/vFollowClick"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="@id/vBg"
        app:layout_constraintEnd_toEndOf="@id/vBg"
        app:layout_constraintStart_toStartOf="@id/vBg"
        app:layout_constraintTop_toTopOf="@id/followView" />

</androidx.constraintlayout.widget.ConstraintLayout>