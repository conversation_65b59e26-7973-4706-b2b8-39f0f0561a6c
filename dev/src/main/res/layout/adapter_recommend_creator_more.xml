<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/vBg"
        android:layout_width="@dimen/dp_108"
        android:layout_height="@dimen/dp_150"
        android:layout_marginEnd="@dimen/dp_10"
        android:background="@drawable/bg_round_12_gradient_26a7a8a9_to_white_stroke_05"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv1"
        android:layout_width="@dimen/dp_42"
        android:layout_height="@dimen/dp_42"
        android:layout_marginStart="@dimen/dp_26"
        android:layout_marginTop="@dimen/dp_6"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_default_creator_blue_size66"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintStart_toStartOf="@id/vBg"
        app:layout_constraintTop_toTopOf="@id/vBg"
        app:shapeAppearance="@style/circleStyle" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv2"
        android:layout_width="@dimen/dp_42"
        android:layout_height="@dimen/dp_42"
        android:layout_marginStart="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_28"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_default_creator_yellow_size88"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintStart_toStartOf="@id/vBg"
        app:layout_constraintTop_toTopOf="@id/vBg"
        app:shapeAppearance="@style/circleStyle"
        app:strokeColor="@color/white"
        app:strokeWidth="@dimen/dp_1" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTitle"
        style="@style/MetaTextView.S9.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_6"
        android:layout_marginTop="@dimen/dp_10"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="2"
        android:text="@string/community_recommend_creator_more_desc"
        android:textColor="@color/neutral_color_1"
        app:layout_constraintEnd_toEndOf="@id/vBg"
        app:layout_constraintStart_toStartOf="@id/vBg"
        app:layout_constraintTop_toBottomOf="@id/iv2" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvMore"
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        android:layout_width="@dimen/dp_88"
        android:layout_height="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_6"
        android:background="@drawable/bg_ffdc1c_round_100"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="@string/community_recommend_creator_more_btn"
        android:textColor="@color/neutral_color_1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/vBg"
        app:layout_constraintStart_toStartOf="@id/vBg" />

</androidx.constraintlayout.widget.ConstraintLayout>