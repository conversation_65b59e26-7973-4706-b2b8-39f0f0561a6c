<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_40"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:id="@+id/load_more_loading_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="horizontal">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/ivProgress"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_fileName="circle_loading.zip"
            android:layout_gravity="center_vertical"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginRight="@dimen/dp_4" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/loading_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_4"
            android:text="@string/loading"
            style="@style/MetaTextView.S15.PoppinsRegular400"
            android:textColor="#1a1a1a" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/load_more_load_fail_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">


        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_prompt"
            android:layout_width="wrap_content"
            android:textColor="@color/color_loading_view"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/footer_load_failed" />

    </FrameLayout>

    <FrameLayout
        android:id="@+id/load_more_load_complete_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/footer_load_complete"
            android:textColor="@color/color_loading_view" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/load_more_load_end_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/footer_load_end"
            android:textColor="@color/color_loading_view" />
    </FrameLayout>
</FrameLayout>