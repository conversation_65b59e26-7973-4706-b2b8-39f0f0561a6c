<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/white"
        android:clickable="true">

        <ImageView
            android:id="@+id/iv_dialog_holder"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_4"
            android:layout_marginTop="@dimen/dp_12"
            android:src="@drawable/shape_dialog_holder"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_avatar"
            android:layout_width="@dimen/dp_74"
            android:layout_height="@dimen/dp_74"
            android:layout_marginTop="@dimen/dp_36"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_dialog_holder"
            tools:src="@drawable/home_friends_add" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_name"
            style="@style/MetaTextView.S16.PoppinsRegular400"
            android:layout_width="0dp"
            android:layout_marginHorizontal="16dp"
            android:layout_height="@dimen/dp_32"
            android:layout_marginTop="@dimen/dp_4"
            android:gravity="center"
            android:singleLine="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_avatar"
            tools:text="friends_name" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_join"
            style="@style/MetaTextView.S18.PoppinsBlack900"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_48"
            android:layout_marginHorizontal="@dimen/dp_61"
            android:layout_marginTop="@dimen/dp_4"
            android:background="@drawable/shape_home_friend_join_bg"
            android:gravity="center"
            android:text="@string/home_friend_join"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_name"
            tools:visibility="visible" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/pb_loading"
            android:layout_width="@dimen/dp_50"
            android:layout_height="@dimen/dp_50"
            android:layout_gravity="center"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/tv_join"
            app:layout_constraintEnd_toEndOf="@id/tv_join"
            app:layout_constraintStart_toStartOf="@id/tv_join"
            app:layout_constraintTop_toTopOf="@id/tv_join"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_fileName="circle_loading.zip" />

        <ImageView
            android:id="@+id/iv_online"
            android:layout_width="@dimen/dp_10"
            android:layout_height="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_4"
            android:src="@drawable/shape_online_status_point"
            app:layout_constraintBottom_toBottomOf="@id/tv_online"
            app:layout_constraintEnd_toStartOf="@id/tv_online"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="@id/iv_avatar"
            app:layout_constraintTop_toTopOf="@id/tv_online" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_online"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_20"
            android:gravity="center"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="#17191C"
            app:layout_constraintEnd_toEndOf="@id/iv_avatar"
            app:layout_constraintStart_toEndOf="@id/iv_online"
            app:layout_constraintTop_toBottomOf="@id/tv_name"
            tools:text="Online" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_not_join_hit"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            android:textColor="@color/color_FF5F42"
            android:layout_marginHorizontal="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_join"
            tools:text="@string/room_version_not_match_error" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_actions"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_12"
            android:orientation="horizontal"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_not_join_hit"
            app:layout_goneMarginTop="@dimen/dp_38"
            tools:itemCount="3"
            tools:listitem="@layout/adapter_item_friends_action"
            tools:orientation="vertical" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>