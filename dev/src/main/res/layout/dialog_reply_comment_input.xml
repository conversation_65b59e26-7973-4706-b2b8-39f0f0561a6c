<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_white_top_round_16">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingVertical="@dimen/dp_5">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/et_mgs_message"
            style="@style/MetaTextView.S14"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginVertical="@dimen/dp_12"
            android:background="@drawable/bg_f0f0f0_corner_20"
            android:gravity="center_vertical"
            android:hint="@string/post_reply"
            android:inputType="textMultiLine"
            android:lineSpacingMultiplier="1.2"
            android:maxHeight="@dimen/dp_186"
            android:maxLength="300"
            android:paddingVertical="@dimen/dp_8"
            android:paddingStart="@dimen/dp_12"
            android:paddingEnd="@dimen/dp_55"
            android:textColorHint="@color/color_666666"
            android:textCursorDrawable="@drawable/bg_cursor"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/btn_send_message"
            style="@style/MetaTextView.S14.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_22"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_7"
            android:gravity="center"
            android:text="@string/reply_cap"
            android:textColor="@color/color_4AB4FF"
            app:layout_constraintBottom_toBottomOf="@id/et_mgs_message"
            app:layout_constraintEnd_toEndOf="@id/et_mgs_message" />

        <com.socialplay.gpark.ui.view.LoadingView
            android:id="@+id/loading"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:background="@color/black_60"
            android:visibility="gone"
            app:loadingProgressSize="@dimen/dp_60"
            tools:visibility="gone" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
