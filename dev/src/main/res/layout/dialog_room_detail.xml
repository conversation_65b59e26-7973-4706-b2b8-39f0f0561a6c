<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/iv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@drawable/bg_black_80_s8"
        app:layout_constraintDimensionRatio="375:210"
        app:layout_constraintTop_toTopOf="parent" />
    <View
        android:id="@+id/v_drag_bar"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@drawable/bg_white_18"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/imgMore"
        android:layout_width="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/ivLive"
        app:layout_constraintBottom_toBottomOf="@+id/ivLive"
        app:layout_constraintRight_toRightOf="parent"
        android:padding="@dimen/dp_16"
        android:src="@drawable/icon_more_white_pro"
        android:layout_height="wrap_content"/>
    <View
        android:id="@+id/view_achor"
        android:layout_width="0.1dp"
        app:layout_constraintBottom_toBottomOf="@+id/imgMore"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginRight="@dimen/dp_16"
        android:layout_height="0.1dp"/>

    <View
        android:background="#fff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv"

        android:layout_width="match_parent"
        android:layout_height="0dp"/>

    <ImageView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginLeft="-5dp"
        android:layout_marginTop="-3dp"
        android:layout_marginRight="-5dp"
        android:layout_marginBottom="-3dp"
        android:src="@drawable/placeholder_corner_4"
        app:layout_constraintBottom_toBottomOf="@id/ivLive"
        app:layout_constraintLeft_toLeftOf="@id/ivLive"
        app:layout_constraintRight_toRightOf="@id/tvLive"
        app:layout_constraintTop_toTopOf="@id/ivLive"
        app:tint="#30FFFFFF" />


    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/ivLive"
        android:layout_width="18dp"
        android:layout_height="20dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="@dimen/dp_28"
        android:layout_marginBottom="@dimen/dp_16"
        android:layout_marginRight="@dimen/dp_16"
        app:layout_constraintLeft_toLeftOf="@id/iv"
        app:layout_constraintTop_toTopOf="@id/iv"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_fileName="party.zip" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvLive"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="6dp"
        android:text="@string/default_room_tag"
        android:textColor="#fff"
        app:layout_constraintBottom_toBottomOf="@id/ivLive"
        app:layout_constraintLeft_toRightOf="@id/ivLive"
        app:layout_constraintTop_toTopOf="@id/ivLive" />



    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvDialogTitle"
        style="@style/MetaTextView.S18.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_16"
        android:textColor="#fff"
        android:textSize="19sp"
        app:layout_constraintBottom_toBottomOf="@id/iv"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_room_style"
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        app:layout_constraintLeft_toLeftOf="@id/tvDialogTitle"
        app:layout_constraintBottom_toTopOf="@id/tvDialogTitle"
        android:layout_marginBottom="@dimen/dp_10"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_24"
        android:background="@drawable/shape_white_corner"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_6"
        android:singleLine="true"
        android:textColor="@color/color_B944BF"
        tools:text="club" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvHost"
        style="@style/MetaTextView.S14.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="22dp"
        android:text="@string/hosted_by"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv" />

    <ImageView
        android:id="@+id/ivHost"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="12dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvHost"
       />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_margin="16dp"
        android:background="#D9D9D9"
        app:layout_constraintTop_toBottomOf="@id/ivHost" />
    <androidx.constraintlayout.widget.Group
        android:id="@+id/ly_occupancy"
        android:layout_width="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvOccupancy,rv,line"
        android:layout_height="wrap_content"/>


    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvOccupancy"
        style="@style/MetaTextView.S14.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="22dp"
        android:text="@string/occupancy"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="12dp"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toBottomOf="@id/tvOccupancy" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvJoin"
        style="@style/Button.S18.PoppinsBlack900"
        android:layout_width="0dp"
        app:layout_constraintRight_toLeftOf="@id/tvCreate"
        android:layout_height="48dp"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="32dp"
        android:background="@drawable/placeholder_corner_360"
        android:backgroundTint="#8C3FF5"
        android:gravity="center"
        android:text="@string/join_cap"
        android:textColor="#fff"
        app:layout_constraintTop_toBottomOf="@id/rv" />

    <TextView
        android:id="@+id/tvCreate"
        android:text="@string/recommend_create"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvJoin"
        android:drawableTop="@drawable/icon_create"
        android:layout_width="wrap_content"
        android:visibility="gone"
        android:layout_marginRight="16dp"
        android:layout_height="wrap_content"/>

    <Space
        android:layout_width="match_parent"
        android:layout_height="35dp"
        app:layout_constraintTop_toBottomOf="@id/tvJoin" />

</androidx.constraintlayout.widget.ConstraintLayout>