<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sv_content_area"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:fillViewport="true"
        android:isScrollContainer="true"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp_32">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_tips"
                style="@style/MetaTextView.S14"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_16"
                android:gravity="left"
                android:text="@string/account_complete_post_desc"
                android:textColor="@color/neutral_color_2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/input_account"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:layout_marginTop="@dimen/dp_16"
                android:background="@drawable/selector_bg_sign_up_focus"
                android:hint="@string/enter_account"
                android:textColorHint="@color/black_40"
                android:visibility="gone"
                app:boxStrokeWidth="0dp"
                app:boxStrokeWidthFocused="0dp"
                app:hintAnimationEnabled="true"
                app:hintTextAppearance="@style/hintAppearence"
                app:hintTextColor="@color/black_40"
                app:layout_constraintTop_toBottomOf="@id/tv_tips">

                <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
                    android:id="@+id/et_account"
                    style="@style/MetaTextView.S14.PoppinsMedium500"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/transparent"
                    android:digits="@string/signup_account_input_digits"
                    android:inputType="textNoSuggestions"
                    android:maxLength="20"
                    android:singleLine="true"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_account_format_err_tip"
                style="@style/MetaTextView.S12"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:text="@string/signup_account_input_error"
                android:textColor="@color/neutral_color_3"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/input_account"
                app:layout_constraintStart_toStartOf="@id/input_account"
                app:layout_constraintTop_toBottomOf="@id/input_account" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/input_password"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:layout_marginTop="@dimen/dp_8"
                android:background="@drawable/selector_bg_sign_up_focus"
                android:hint="@string/enter_password"
                android:textColorHint="@color/black_40"
                app:boxStrokeWidth="0dp"
                app:boxStrokeWidthFocused="0dp"
                app:hintAnimationEnabled="true"
                app:hintTextAppearance="@style/hintAppearence"
                app:hintTextColor="@color/black_40"
                app:layout_constraintTop_toBottomOf="@id/tv_account_format_err_tip"
                app:layout_goneMarginTop="@dimen/dp_12">

                <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
                    android:id="@+id/et_password"
                    style="@style/MetaTextView.S14.PoppinsMedium500"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/transparent"
                    android:digits="@string/password_input_digits"
                    android:inputType="textPassword|textNoSuggestions"
                    android:maxLength="16"
                    android:singleLine="true"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/input_password2"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:layout_marginTop="@dimen/dp_12"
                android:background="@drawable/selector_bg_sign_up_focus"
                android:hint="@string/account_enter_password_again"
                android:textColorHint="@color/black_40"
                app:boxStrokeWidth="0dp"
                app:boxStrokeWidthFocused="0dp"
                app:hintAnimationEnabled="true"
                app:hintTextAppearance="@style/hintAppearence"
                app:hintTextColor="@color/black_40"
                app:layout_constraintTop_toBottomOf="@id/input_password">

                <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
                    android:id="@+id/et_password2"
                    style="@style/MetaTextView.S14.PoppinsMedium500"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/transparent"
                    android:digits="@string/password_input_digits"
                    android:inputType="textPassword|textNoSuggestions"
                    android:maxLength="16"
                    android:singleLine="true"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14" />

            </com.google.android.material.textfield.TextInputLayout>

            <ImageView
                android:id="@+id/iv_password_visibility"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp_16"
                android:src="@drawable/icon_login_visible_password"
                app:layout_constraintBottom_toBottomOf="@id/input_password"
                app:layout_constraintEnd_toEndOf="@id/input_password"
                app:layout_constraintTop_toTopOf="@id/input_password" />

            <ImageView
                android:id="@+id/iv_password_visibility2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp_16"
                android:src="@drawable/icon_login_visible_password"
                app:layout_constraintBottom_toBottomOf="@id/input_password2"
                app:layout_constraintEnd_toEndOf="@id/input_password2"
                app:layout_constraintTop_toTopOf="@id/input_password2" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_password_format_err_tip"
                style="@style/MetaTextView.S12"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:text="@string/signup_password_input_error"
                android:textColor="@color/neutral_color_3"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/input_password2"
                app:layout_constraintStart_toStartOf="@id/input_password2"
                app:layout_constraintTop_toBottomOf="@id/input_password2" />

            <LinearLayout
                android:id="@+id/llAccountNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp_32"
                app:layout_constraintBottom_toTopOf="@id/tvSure"
                app:layout_constraintLeft_toLeftOf="@id/tvSure"
                app:layout_constraintRight_toRightOf="@id/tvSure">

                <TextView
                    android:id="@+id/tvAccountNumberDesc"
                    style="@style/MetaTextView.S16.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/account_number_desc"
                    android:textColor="@color/Gray_800" />

                <TextView
                    android:id="@+id/tvAccountNumber"
                    style="@style/MetaTextView.S16.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/Gray_1000"
                    tools:text="6278212" />

                <ImageView
                    android:id="@+id/ivCopyAccountNumber"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dp_16"
                    android:scaleType="center"
                    android:src="@drawable/ic_module_project_copy"
                    app:tint="@color/color_4AB4FF" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvSure"
                style="@style/Button.S18.PoppinsBlack900"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_40"
                android:layout_marginTop="@dimen/dp_124"
                android:minHeight="@dimen/dp_56"
                android:text="@string/text_confirm_uppercase"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_password_format_err_tip" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon="@drawable/ic_close"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>