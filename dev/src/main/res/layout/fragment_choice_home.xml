<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/tab_layout_height"
    android:clipChildren="false"
    android:orientation="vertical"
    app:speed_monitor="true">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/holder"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        app:title_text="@string/selected"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        android:background="#fff"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivSearch"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_msg"
            android:layout_marginEnd="@dimen/dp_12"
            android:src="@drawable/icon_search"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <ImageView
            android:id="@+id/iv_msg"
            android:layout_width="@dimen/dp_32"
            android:layout_height="@dimen/dp_32"
            android:layout_marginEnd="@dimen/dp_16"
            android:src="@drawable/tab_icon_msg_selected"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/v_msg_red_dot"
            android:layout_width="@dimen/dp_8"
            android:layout_height="@dimen/dp_8"
            android:layout_margin="@dimen/dp_2"
            android:background="@drawable/sp_red_dot"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/iv_msg"
            app:layout_constraintTop_toTopOf="@id/iv_msg" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_party_title"
            style="@style/MetaTextView.S18.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/home_tab_create_maps"
            android:textColor="#1A1A1A"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        <View
            android:id="@+id/vLine"
            android:layout_width="match_parent"
            android:background="@color/color_d9d9d9"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_height="0.5dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>


    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/color_FFDC1C">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:background="#fff"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:orientation="vertical"
                android:paddingHorizontal="11dp"
                android:overScrollMode="never"
                android:paddingTop="@dimen/dp_10"
                android:paddingBottom="80dp"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="3"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white" />

</LinearLayout>