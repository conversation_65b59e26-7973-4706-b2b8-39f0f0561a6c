<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:paddingBottom="@dimen/tab_layout_height"
    app:speed_monitor="true">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MinWidthTabLayout
        android:id="@+id/tlFeed"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_44"
        android:background="@null"
        android:clipToPadding="false"
        android:paddingHorizontal="@dimen/dp_6"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/topDivider"
        app:layout_constraintEnd_toStartOf="@id/iv_msg_entrance"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusBar"
        app:tabBackground="@null"
        app:tabGravity="start"
        app:tabIndicator="@drawable/indicator_community"
        app:tabIndicatorColor="@color/color_FFDE70"
        app:tabIndicatorFullWidth="false"
        app:tabIndicatorHeight="@dimen/dp_4"
        app:tabMode="scrollable"
        app:tabPaddingEnd="@dimen/dp_10"
        app:tabPaddingStart="@dimen/dp_10"
        app:tabRippleColor="@color/transparent" />

    <View
        android:id="@+id/gradientEnd"
        android:layout_width="@dimen/dp_3"
        android:layout_height="@dimen/dp_0"
        android:background="@drawable/bg_linear_transparent_white"
        app:layout_constraintBottom_toTopOf="@id/topDivider"
        app:layout_constraintEnd_toStartOf="@id/iv_msg_entrance"
        app:layout_constraintTop_toBottomOf="@id/statusBar" />

    <ImageView
        android:id="@+id/iv_msg_entrance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_8"
        android:padding="@dimen/dp_8"
        android:src="@drawable/ic_message_black"
        app:layout_constraintBottom_toTopOf="@id/topDivider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusBar" />

    <View
        android:id="@+id/vMsgRedDot"
        android:layout_width="@dimen/dp_7"
        android:layout_height="@dimen/dp_7"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_8"
        android:background="@drawable/sp_red_dot"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/iv_msg_entrance"
        app:layout_constraintTop_toTopOf="@id/iv_msg_entrance"
        tools:visibility="visible" />

    <View
        android:id="@+id/topDivider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_05"
        android:layout_marginTop="@dimen/dp_44"
        android:background="@color/color_F0F0F0"
        app:layout_constraintTop_toBottomOf="@id/statusBar" />

    <com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topDivider">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vpFeed"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout>

    <include
        android:id="@+id/includeChoosePublishScene"
        layout="@layout/include_community_publish_entrance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ProgressBar
        android:id="@+id/pbPublish"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_3"
        android:max="100"
        android:progress="0"
        android:progressBackgroundTint="@color/transparent"
        android:progressTint="@color/color_4AB4FF"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:progress="50" />

</androidx.constraintlayout.widget.ConstraintLayout>