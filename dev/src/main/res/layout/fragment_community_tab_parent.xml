<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:speed_monitor="true">

    <View
        android:id="@+id/view_top_bg_gradient"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        tools:visibility="gone" />

    <android.widget.Space
        android:id="@+id/space_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_110"
        tools:visibility="gone" />

    <FrameLayout
        android:id="@+id/fl_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/space_top" />

    <RelativeLayout
        android:id="@+id/rl_h5_top_place_holder"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_112"
        android:visibility="gone" />

</RelativeLayout>