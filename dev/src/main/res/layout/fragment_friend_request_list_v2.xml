<?xml version="1.0" encoding="utf-8"?>
<com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/sl_refresh_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#fff">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <!--        <androidx.constraintlayout.widget.ConstraintLayout-->
        <!--            android:id="@+id/cl_empty_layout"-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="match_parent"-->
        <!--            android:background="@color/white"-->
        <!--            android:clickable="true"-->
        <!--            android:focusable="true">-->

        <!--            <ImageView-->
        <!--                android:id="@+id/iv_no_friend_tip_img"-->
        <!--                android:layout_width="wrap_content"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:src="@drawable/common_no_item"-->
        <!--                app:layout_constraintBottom_toTopOf="@+id/tv_no_friend_tip_text"-->
        <!--                app:layout_constraintEnd_toEndOf="parent"-->
        <!--                app:layout_constraintStart_toStartOf="parent"-->
        <!--                app:layout_constraintTop_toTopOf="parent"-->
        <!--                app:layout_constraintVertical_chainStyle="packed" />-->

        <!--            <TextView-->
        <!--                android:id="@+id/tv_no_friend_tip_text"-->
        <!--                android:layout_width="wrap_content"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_marginTop="@dimen/dp_12"-->
        <!--                android:text="@string/no_request_friends"-->
        <!--                android:textColor="@color/textColorSecondary"-->
        <!--                android:textSize="@dimen/sp_15"-->
        <!--                app:layout_constraintBottom_toBottomOf="parent"-->
        <!--                app:layout_constraintEnd_toEndOf="parent"-->
        <!--                app:layout_constraintStart_toStartOf="parent"-->
        <!--                app:layout_constraintTop_toBottomOf="@+id/iv_no_friend_tip_img" />-->

        <!--        </androidx.constraintlayout.widget.ConstraintLayout>-->

        <com.socialplay.gpark.ui.view.LoadingView
            android:id="@+id/lv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

    </FrameLayout>

</com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>