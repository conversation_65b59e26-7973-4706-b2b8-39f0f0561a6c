<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/placeHolderView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon="@drawable/icon_back_array_bold_black"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@+id/placeHolderView"
        app:title_text="@string/all_room"
        app:title_text_color="@color/textColorPrimary" />

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/color_FFDC1C"
        app:layout_constraintBottom_toTopOf="@id/rl_parent_create"
        app:layout_constraintTop_toBottomOf="@id/titleBar">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            android:background="@color/white"
            android:scrollbars="none"
            tools:listitem="@layout/adapter_item_room" />

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

    <RelativeLayout
        android:id="@+id/rl_parent_create"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dp_6"
        android:paddingBottom="@dimen/dp_35"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_create"
            android:layout_marginLeft="@dimen/dp_16"
            android:layout_marginRight="@dimen/dp_16"
            style="@style/Button.S18.PoppinsBlack900"
            android:background="@drawable/shape_8c3ff5_round"
            android:text="@string/create_room"
            android:textColor="@color/white"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48" />

    </RelativeLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleBar" />

</androidx.constraintlayout.widget.ConstraintLayout>