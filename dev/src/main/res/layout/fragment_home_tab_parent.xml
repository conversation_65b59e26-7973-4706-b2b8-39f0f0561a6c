<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:speed_monitor="true">

    <LinearLayout
        android:id="@+id/ll_top_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_312"
        android:orientation="vertical">

        <View
            android:id="@+id/view_top_bg"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_136" />

        <View
            android:id="@+id/view_top_bg_gradient"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

    </LinearLayout>

    <FrameLayout
        android:id="@+id/fl_rec_top_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <View
            android:id="@+id/v_rec_top_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:background="#6A88BE" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white" />
    </FrameLayout>


    <android.widget.Space
        android:id="@+id/space_top"
        android:layout_width="match_parent"
        android:layout_height="0dp" />

    <FrameLayout
        android:id="@+id/fl_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/space_top" />

    <RelativeLayout
        android:id="@+id/rl_h5_top_place_holder"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="visible" />

</RelativeLayout>