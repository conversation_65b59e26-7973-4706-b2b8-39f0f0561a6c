<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_height="@dimen/dp_0"
        android:layout_width="match_parent"
        android:background="@color/color_F6F6F6"
        app:layout_constraintTop_toBottomOf="@id/statusBar"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@id/statusBar" />

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rvMoreUgc"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:orientation="vertical"
                android:paddingTop="@dimen/dp_16"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="2" />

            <com.socialplay.gpark.ui.view.LoadingView
                android:id="@+id/loadingMoreUgc"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />
        </FrameLayout>
    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>
</androidx.constraintlayout.widget.ConstraintLayout>