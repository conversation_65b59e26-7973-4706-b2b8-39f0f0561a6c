<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:paddingBottom="@dimen/tab_layout_height"
    android:layout_height="match_parent">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/holder"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        android:paddingHorizontal="@dimen/dp_16"
        app:layout_constraintTop_toBottomOf="@id/holder">

        <com.socialplay.gpark.ui.view.MetaTextView
            style="@style/MetaTextView.S18.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/home_tab_create_maps"
            android:textColor="#1A1A1A"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/color_FFDC1C"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_title_bar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_played_games"
            android:layout_width="match_parent"
            android:background="#fff"
            android:layout_height="match_parent"
            android:visibility="visible">

            <TextView
                android:id="@+id/tv_played_title"
                style="@style/MetaTextView.S18.PoppinsBold700"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="@dimen/dp_12"
                android:text="@string/played_title"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_recently_played"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_12"
                android:orientation="horizontal"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_played_title"
                tools:listitem="@layout/adapter_played" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:clipToPadding="false"
                android:orientation="vertical"
                android:paddingTop="8dp"
                android:paddingBottom="90dp"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rv_recently_played"
                tools:listitem="@layout/adapter_party" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="@id/refresh" />

</androidx.constraintlayout.widget.ConstraintLayout>