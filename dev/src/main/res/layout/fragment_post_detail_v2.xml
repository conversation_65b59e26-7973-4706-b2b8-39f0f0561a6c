<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    app:speed_monitor="true">

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toTopOf="parent"
        app:title_text="@string/community_post_detail_title" />

    <ImageView
        android:id="@+id/iv_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_8"
        android:src="@drawable/ic_share_black"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tbl"
        tools:visibility="visible" />

    <View
        android:id="@+id/v_red_dot_more"
        android:layout_width="@dimen/dp_10"
        android:layout_height="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/sp_red_dot"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/iv_more"
        app:layout_constraintTop_toTopOf="@id/iv_more"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.refresh.MetaVerticalCoordinatorRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/bottomCommentInput"
        app:layout_constraintTop_toBottomOf="@id/tbl">

        <com.socialplay.gpark.ui.view.CallbackCoordinatorLayout
            android:id="@+id/cdl"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/abl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                app:layout_behavior="com.google.android.material.appbar.CustomAppBarBehavior"
                tools:expanded="true">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_post_detail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/iv_author_avatar"
                        android:layout_width="@dimen/dp_47"
                        android:layout_height="@dimen/dp_47"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_24"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:shapeAppearance="@style/circleStyle"
                        tools:src="@drawable/placeholder" />

                    <ImageView
                        android:id="@+id/iv_state"
                        android:layout_width="@dimen/dp_10"
                        android:layout_height="@dimen/dp_10"
                        android:layout_marginEnd="@dimen/dp_1"
                        android:layout_marginBottom="@dimen/dp_1"
                        android:src="@drawable/shape_green_point_white_stroke"
                        android:visibility="gone"
                        app:layout_constraintTop_toTopOf="@id/iv_author_avatar"
                        app:layout_constraintEnd_toEndOf="@id/iv_author_avatar"
                        tools:visibility="visible" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_author_name"
                        style="@style/MetaTextView.S14.PoppinsSemiBold600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_7"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:singleLine="true"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintBottom_toTopOf="@id/tv_time"
                        app:layout_constraintEnd_toStartOf="@id/iv_official"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toEndOf="@id/iv_author_avatar"
                        app:layout_constraintTop_toTopOf="@id/iv_author_avatar"
                        app:layout_constraintVertical_chainStyle="packed"
                        tools:text="aaa" />

                    <com.socialplay.gpark.ui.view.UserLabelView
                        android:id="@+id/iv_official"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/tv_author_name"
                        app:layout_constraintEnd_toStartOf="@id/space_follow"
                        app:layout_constraintStart_toEndOf="@id/tv_author_name"
                        app:layout_constraintTop_toTopOf="@id/tv_author_name"
                        tools:visibility="visible" />

                    <View
                        android:id="@+id/v_author_click"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                        app:layout_constraintEnd_toEndOf="@id/tv_author_name"
                        app:layout_constraintStart_toStartOf="@id/iv_author_avatar"
                        app:layout_constraintTop_toTopOf="@id/iv_author_avatar" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_time"
                        style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:singleLine="true"
                        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                        app:layout_constraintStart_toStartOf="@id/tv_author_name"
                        app:layout_constraintTop_toBottomOf="@id/tv_author_name"
                        tools:text="Nov 12, 2022 14:12 AM" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_review_status_label"
                        style="@style/MetaTextView.S10"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_4"
                        android:background="@drawable/bg_white_round"
                        android:drawableStart="@drawable/ic_label_under_review"
                        android:drawablePadding="@dimen/dp_4"
                        android:gravity="center"
                        android:paddingStart="@dimen/dp_2"
                        android:paddingEnd="@dimen/dp_6"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/tv_time"
                        app:layout_constraintHeight_min="@dimen/dp_16"
                        app:layout_constraintStart_toEndOf="@id/tv_time"
                        app:layout_constraintTop_toTopOf="@id/tv_time"
                        tools:backgroundTint="@color/color_EBF6FF"
                        tools:text="Under Review"
                        tools:textColor="@color/color_4AB4FF"
                        tools:visibility="visible" />

                    <Space
                        android:id="@+id/space_follow"
                        android:layout_width="@dimen/dp_16"
                        android:layout_height="@dimen/dp_16"
                        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                        app:layout_constraintEnd_toStartOf="@id/tvFollowBtn"
                        app:layout_constraintTop_toTopOf="@id/iv_author_avatar"
                        tools:visibility="gone" />

                    <com.socialplay.gpark.ui.view.FollowView
                        android:id="@+id/tvFollowBtn"
                        android:layout_width="@dimen/dp_88"
                        android:layout_height="@dimen/dp_30"
                        app:viewStyle="hollow"
                        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                        app:layout_constraintEnd_toEndOf="@id/guide_right"
                        app:layout_constraintTop_toTopOf="@id/iv_author_avatar"
                        />

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/guide_right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintGuide_end="@dimen/dp_16" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_content"
                        style="@style/MetaTextView.S14"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_8"
                        android:lineSpacingMultiplier="1.2"
                        app:layout_constraintTop_toBottomOf="@id/iv_author_avatar"
                        tools:text="Infinite Borders puts players in the ever captivating Three Kingdoms period of Ancient China, and offers a blend of strategy, RPG city-building, and gacha mechanics. Having spent considerable time in both its native East Asian markets —previously known as Reign of Warlords or ROW: Tam Quốc. — It is now entering a global release during its Closed Beta Test phase. However, the English localization is still a bit spotty." />

                    <com.socialplay.gpark.ui.view.video.VideoPageListView
                        android:id="@+id/vplv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_4"
                        app:layout_constraintTop_toBottomOf="@id/tv_content"
                        app:layout_goneMarginTop="@dimen/dp_8" />

                    <com.airbnb.epoxy.EpoxyRecyclerView
                        android:id="@+id/rv_img"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:nestedScrollingEnabled="false"
                        android:overScrollMode="never"
                        app:layout_constraintTop_toBottomOf="@id/vplv"
                        tools:visibility="gone" />

                    <com.airbnb.epoxy.EpoxyRecyclerView
                        android:id="@+id/rv_game"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:nestedScrollingEnabled="false"
                        android:overScrollMode="never"
                        app:layout_constraintTop_toBottomOf="@id/rv_img"
                        tools:itemCount="1"
                        tools:layout_marginTop="@dimen/dp_8"
                        tools:listitem="@layout/item_community_assets_card_full_width"
                        tools:visibility="visible" />

                    <!--话题需求后不用了这个rv了-->
                    <com.airbnb.epoxy.EpoxyRecyclerView
                        android:id="@+id/rv_topic"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_2"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:nestedScrollingEnabled="false"
                        android:overScrollMode="never"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/rv_game"
                        tools:itemCount="1"
                        tools:layout_marginStart="@dimen/dp_16"
                        tools:layout_marginTop="@dimen/dp_14"
                        tools:listitem="@layout/adapter_publish_post_tag" />

                    <include
                        android:id="@+id/includeAttitude"
                        layout="@layout/adapter_community_post_attitude"
                        android:layout_width="@dimen/dp_0"
                        android:layout_height="@dimen/dp_30"
                        android:clipChildren="false"
                        android:layout_marginTop="@dimen/dp_16"
                        app:layout_constraintTop_toBottomOf="@id/rv_topic"
                        app:layout_constraintStart_toStartOf="@id/rv_game"
                        app:layout_constraintEnd_toEndOf="@id/rv_game"
                        android:layout_marginHorizontal="@dimen/dp_23" />

                    <View
                        android:id="@+id/vDividerComment"
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/color_F0F0F0"
                        android:layout_marginTop="@dimen/dp_16"
                        app:layout_constraintTop_toBottomOf="@id/includeAttitude" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_comment_count_hang"
                        style="@style/MetaTextView.S13.PoppinsSemiBold600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_16"
                        android:paddingVertical="@dimen/dp_16"
                        tools:text="26 Comments" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_comment_sort_hang"
                        style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical|end"
                        android:drawableStart="@drawable/ic_post_sort_arrow"
                        android:drawablePadding="@dimen/dp_2"
                        android:paddingHorizontal="@dimen/dp_16"
                        android:paddingVertical="@dimen/dp_12"
                        android:visibility="gone" />

                </FrameLayout>

            </com.google.android.material.appbar.AppBarLayout>

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rv_comment"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />

        </com.socialplay.gpark.ui.view.CallbackCoordinatorLayout>
    </com.socialplay.gpark.ui.view.refresh.MetaVerticalCoordinatorRefreshLayout>

    <com.socialplay.gpark.ui.view.BottomCommentInputLayout
        android:id="@+id/bottomCommentInput"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_54"
        app:layout_constraintBottom_toBottomOf="parent"
        app:enableLike="true"
        app:enableLightUp="false"
        app:enableComment="true"
        app:enableSendFlower="false"/>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>