<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <!--顶栏-->
    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBarPlaceholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl_title_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@id/statusBarPlaceholder"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:title_text="@string/recommend_video_list_title" />


    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/sl_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl_title_bar">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:paddingHorizontal="@dimen/dp_16"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            android:layout_width="match_parent"
            app:spanCount="2"
            android:layout_height="match_parent" />

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        app:layout_constraintTop_toBottomOf="@id/tbl_title_bar"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="0dp" />

</androidx.constraintlayout.widget.ConstraintLayout>