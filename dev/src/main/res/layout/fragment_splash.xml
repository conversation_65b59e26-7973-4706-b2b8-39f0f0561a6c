<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_0F0A21"
    app:speed_monitor="true">

    <ImageView
        android:id="@+id/iv_bg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/bg_splash_src"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

<!--    <ImageView-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginTop="@dimen/dp_65"-->
<!--        android:src="@drawable/splash_top_icon"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintRight_toRightOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent" />-->

<!--    <TextView-->
<!--        android:id="@+id/tv_desc"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:gravity="center"-->
<!--        android:paddingHorizontal="@dimen/dp_30"-->
<!--        android:visibility="invisible"-->
<!--        android:paddingBottom="@dimen/dp_45"-->
<!--        android:text="抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。\n适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。-->
<!--\n\n著作权人：北京龙威互动科技有限公司  出版单位：北京联合出-->
<!--版有限责任公司 审批文号：国新出审【2024】372号-->
<!--\n出版物号：ISBN 978-7-498-13245-1"-->
<!--        android:textColor="@color/white_60"-->
<!--        android:textSize="@dimen/sp_10"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintRight_toRightOf="parent" />-->

<!--    <TextView-->
<!--        android:id="@+id/tv_title"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:gravity="center"-->
<!--        android:visibility="invisible"-->
<!--        android:paddingHorizontal="@dimen/dp_45"-->
<!--        android:paddingBottom="@dimen/dp_7"-->
<!--        android:text="@string/use_time_notice"-->
<!--        android:textColor="@color/white_60"-->
<!--        android:textFontWeight="600"-->
<!--        android:textSize="@dimen/sp_12"-->
<!--        app:layout_constraintBottom_toTopOf="@id/tv_desc"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintRight_toRightOf="parent" />-->

<!--    <ImageView-->
<!--        android:layout_width="@dimen/dp_151"-->
<!--        android:layout_height="@dimen/dp_42"-->
<!--        android:layout_marginBottom="@dimen/dp_50"-->
<!--        android:src="@drawable/icon_logo"-->
<!--        app:layout_constraintBottom_toTopOf="@id/tv_title"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintRight_toRightOf="parent" />-->


    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@+id/sbphv_placeholder" />

    <ImageView
        android:id="@+id/iv_age_restriction"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dp_20"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintRight_toRightOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl" />


    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>