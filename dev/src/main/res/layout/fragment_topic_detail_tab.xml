<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBarTopic"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/titleTopic"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/statusBarTopic" />

    <ImageView
        android:id="@+id/ivShareTopic"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:paddingHorizontal="@dimen/dp_16"
        android:src="@drawable/ic_share_black"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/titleTopic"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/titleTopic" />

    <com.socialplay.gpark.ui.view.VerticalCoordinatorLayout
        android:id="@+id/clTopic"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleTopic">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/ablTopic"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            app:layout_behavior="com.google.android.material.appbar.CustomAppBarBehavior">

            <include
                android:id="@+id/includeTopicHeader"
                layout="@layout/header_topic_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_scrollFlags="scroll" />

            <View
                android:layout_height="@dimen/dp_8"
                android:layout_width="match_parent"
                android:background="@color/color_F6F6F6"
                />

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tlTopicDetail"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_36"
                android:layout_marginTop="@dimen/dp_12"
                android:background="@color/white"
                app:tabContentStart="@dimen/dp_16"
                app:tabGravity="start"
                app:tabIndicator="@drawable/indicator_ffdd70_width_12_height_4_round_2"
                app:tabIndicatorColor="@color/color_FFDD70"
                app:tabIndicatorFullWidth="false"
                app:tabIndicatorHeight="@dimen/dp_4"
                app:tabMinWidth="@dimen/dp_10"
                app:tabMode="scrollable"
                app:tabPaddingEnd="@dimen/dp_8"
                app:tabPaddingStart="@dimen/dp_16"
                app:tabRippleColor="@null" />

        </com.google.android.material.appbar.AppBarLayout>

        <com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout
            android:id="@+id/aslfl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:clipToPadding="false"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/vp"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout>

    </com.socialplay.gpark.ui.view.VerticalCoordinatorLayout>

    <include
        android:id="@+id/includeTopicPublish"
        layout="@layout/include_community_publish_entrance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ProgressBar
        android:id="@+id/pbPublish"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_3"
        android:max="100"
        android:progress="0"
        android:progressBackgroundTint="@color/transparent"
        android:progressTint="@color/color_4AB4FF"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:progress="50" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/parentLoading"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleTopic"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>