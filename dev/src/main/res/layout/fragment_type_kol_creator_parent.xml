<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@id/statusBar" />

    <com.socialplay.gpark.ui.view.MinWidthTabLayout
        android:id="@+id/tlMoreCreator"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_50"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/title"
        app:layout_constraintTop_toTopOf="@id/title"
        app:tabContentStart="@dimen/dp_16"
        app:tabGravity="center"
        app:tabIconTint="@color/color_FFDC1C"
        app:tabIndicator="@drawable/indicator_kol_creator_parent"
        app:tabIndicatorColor="@color/color_FFDE70"
        app:tabIndicatorFullWidth="false"
        app:tabIndicatorHeight="@dimen/dp_4"
        app:tabMinWidth="0dp"
        app:tabMode="fixed"
        app:tabPaddingEnd="@dimen/dp_12"
        app:tabPaddingStart="@dimen/dp_12"
        app:tabRippleColor="@null" />

    <View
        android:id="@+id/topDivider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_05"
        android:background="@color/color_F0F0F0"
        app:layout_constraintTop_toBottomOf="@id/tlMoreCreator" />

    <com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topDivider">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vpMoreCreator"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white" />

    </com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>