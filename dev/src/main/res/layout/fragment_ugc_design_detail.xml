<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    app:speed_monitor="true">

    <com.socialplay.gpark.ui.view.VerticalCoordinatorLayout
        android:id="@+id/cdl"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/bottomCommentInput"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/abl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@null"
            app:layout_behavior="com.google.android.material.appbar.CustomAppBarBehavior">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_scrollable"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_ugc_design_detail"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guide_outfit_left"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_begin="@dimen/dp_16" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guide_outfit_right"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_end="@dimen/dp_16" />

                <View
                    android:id="@+id/v_bg_top_gradient"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginBottom="-52dp"
                    android:background="@drawable/bg_gradient_ugc_detail_top"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/iv_outfit"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"
                    tools:layout_editor_absoluteX="0dp"
                    tools:visibility="visible" />

                <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
                    android:id="@+id/sbphv_outfit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/iv_outfit"
                    android:layout_width="@dimen/dp_172"
                    android:layout_height="@dimen/dp_172"
                    android:layout_marginTop="@dimen/dp_76"
                    android:background="@drawable/bg_d0d9f0_corner_12"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/sbphv_outfit"
                    tools:src="@drawable/placeholder_corner_12" />

                <TextView
                    android:id="@+id/tvOutfitUnpublish"
                    style="@style/MetaTextView.S18.PoppinsSemiBold600"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/bg_white_80_round_12"
                    android:gravity="center"
                    android:text="@string/asset_unPublished"
                    android:textColor="@color/color_8792B2"
                    app:layout_constraintBottom_toBottomOf="@id/iv_outfit"
                    app:layout_constraintEnd_toEndOf="@id/iv_outfit"
                    app:layout_constraintStart_toStartOf="@id/iv_outfit"
                    app:layout_constraintTop_toTopOf="@id/iv_outfit" />

                <Space
                    android:id="@+id/spaceOutfitBottom"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_8"
                    app:layout_constraintTop_toBottomOf="@id/iv_outfit" />

                <com.socialplay.gpark.ui.view.TagContainerView
                    android:id="@+id/tagsView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/spaceOutfitBottom"
                    app:maxTagCount="3" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tv_outfit_title"
                    style="@style/MetaTextView.S20.PoppinsSemiBold600"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_24"
                    app:layout_constraintEnd_toEndOf="@id/guide_outfit_right"
                    app:layout_constraintStart_toStartOf="@id/guide_outfit_left"
                    app:layout_constraintTop_toBottomOf="@id/tagsView"
                    tools:text="title" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvSales"
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/color_999999"
                    app:layout_constraintBottom_toBottomOf="@id/tvPrice"
                    app:layout_constraintEnd_toEndOf="@id/guide_outfit_right"
                    app:layout_constraintTop_toTopOf="@id/tvPrice"
                    tools:text="@string/sales" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvPrice"
                    style="@style/MetaTextView.S20.PoppinsSemiBold600"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:drawableStart="@drawable/ic_coin_24"
                    android:drawablePadding="@dimen/dp_6"
                    android:gravity="center_vertical"
                    android:textColor="@color/Gray_1000"
                    app:layout_constraintEnd_toEndOf="@id/guide_outfit_right"
                    app:layout_constraintStart_toStartOf="@id/guide_outfit_left"
                    app:layout_constraintTop_toBottomOf="@id/tv_outfit_title"
                    tools:text="@string/cloth_price_free" />

                <com.socialplay.gpark.ui.view.ExpandableTextView
                    android:id="@+id/etv_outfit_desc"
                    style="@style/MetaTextView.S14"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:lineSpacingMultiplier="1.2"
                    app:etv_EnableToggleClick="true"
                    app:etv_MaxLinesOnShrink="6"
                    app:etv_ToExpandHint="@string/more_cap"
                    app:etv_ToExpandHintBold="true"
                    app:etv_ToExpandHintColor="@color/color_BDBDBD"
                    app:etv_ToShrinkHint="@string/collapse_cap"
                    app:etv_ToShrinkHintColor="@color/color_BDBDBD"
                    app:etv_ToShrinkHintShow="false"
                    app:layout_constraintEnd_toEndOf="@id/guide_outfit_right"
                    app:layout_constraintStart_toStartOf="@id/guide_outfit_left"
                    app:layout_constraintTop_toBottomOf="@id/tvPrice"
                    tools:text="Some description" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/group_remix_allowed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="v_bg_remix_allowed, tv_remix_allowed"
                    tools:visibility="visible" />

                <View
                    android:id="@+id/v_bg_remix_allowed"
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:layout_marginTop="@dimen/dp_8"
                    android:background="@color/color_E4F4FF"
                    app:layout_constraintTop_toBottomOf="@id/etv_outfit_desc" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tv_remix_allowed"
                    style="@style/MetaTextView.S12"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/ugc_asset_remix_allowed"
                    android:textColor="@color/color_4AB4FF"
                    app:layout_constraintBottom_toBottomOf="@id/v_bg_remix_allowed"
                    app:layout_constraintEnd_toStartOf="@id/iv_remix_allowed_tips"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="@id/v_bg_remix_allowed"
                    app:layout_constraintTop_toTopOf="@id/v_bg_remix_allowed" />

                <ImageView
                    android:id="@+id/iv_remix_allowed_tips"
                    android:layout_width="@dimen/dp_22"
                    android:layout_height="0dp"
                    android:paddingHorizontal="@dimen/dp_4"
                    android:src="@drawable/ic_asset_mixable_tips"
                    app:layout_constraintBottom_toBottomOf="@id/tv_remix_allowed"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tv_remix_allowed"
                    app:layout_constraintTop_toTopOf="@id/tv_remix_allowed" />

                <androidx.constraintlayout.helper.widget.Flow
                    android:id="@+id/flow_outfit_stat"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    app:constraint_referenced_ids="ll_outfit_pv, ll_try_on_times, ll_acquired_times, ll_like_times"
                    app:flow_horizontalBias="0"
                    app:flow_horizontalGap="@dimen/dp_12"
                    app:flow_horizontalStyle="packed"
                    app:flow_wrapMode="chain2"
                    app:layout_constraintEnd_toEndOf="@id/guide_outfit_right"
                    app:layout_constraintStart_toStartOf="@id/guide_outfit_left"
                    app:layout_constraintTop_toBottomOf="@id/v_bg_remix_allowed" />

                <LinearLayout
                    android:id="@+id/ll_outfit_pv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    tools:ignore="MissingConstraints">

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_outfit_pv"
                        style="@style/MetaTextView.S14.PoppinsSemiBold600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="9999999" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_outfit_pv_label"
                        style="@style/MetaTextView.S12"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_3"
                        android:text="@string/views"
                        android:textColor="@color/neutral_color_4" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_try_on_times"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    tools:ignore="MissingConstraints">

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_try_on_times"
                        style="@style/MetaTextView.S14.PoppinsSemiBold600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="9999999" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_try_on_times_label"
                        style="@style/MetaTextView.S12"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_3"
                        android:text="@string/try_ons"
                        android:textColor="@color/neutral_color_4" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_acquired_times"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    tools:ignore="MissingConstraints">

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_acquired_times"
                        style="@style/MetaTextView.S14.PoppinsSemiBold600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="99999" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_acquired_times_label"
                        style="@style/MetaTextView.S12"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_3"
                        android:text="@string/acquired"
                        android:textColor="@color/neutral_color_4" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_like_times"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    tools:ignore="MissingConstraints">

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_like_times"
                        style="@style/MetaTextView.S14.PoppinsSemiBold600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="99999" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_like_times_label"
                        style="@style/MetaTextView.S12"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_3"
                        android:text="@string/user_like"
                        android:textColor="@color/neutral_color_4" />
                </LinearLayout>

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tv_outfit_desc_update_time"
                    style="@style/MetaTextView.S12"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:textColor="@color/neutral_color_4"
                    app:layout_constraintEnd_toEndOf="@id/guide_outfit_right"
                    app:layout_constraintStart_toStartOf="@id/guide_outfit_left"
                    app:layout_constraintTop_toBottomOf="@id/flow_outfit_stat"
                    tools:text="Nov 12, 2022  14:12 AM" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tv_like_btn"
                    style="@style/MetaTextView.S14"
                    android:layout_width="@dimen/dp_50"
                    android:layout_height="@dimen/dp_48"
                    android:layout_marginTop="@dimen/dp_12"
                    android:paddingVertical="@dimen/dp_12"
                    android:paddingStart="@dimen/dp_38"
                    android:paddingEnd="@dimen/dp_14"
                    android:textColor="@color/color_757575"
                    app:layout_constraintStart_toStartOf="@id/guide_outfit_left"
                    app:layout_constraintTop_toBottomOf="@id/tv_outfit_desc_update_time" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tv_like_count"
                    style="@style/MetaTextView.S11.PoppinsMedium500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dp_4"
                    android:gravity="center"
                    android:textColor="@color/Gray_800"
                    app:layout_constraintBottom_toBottomOf="@id/tv_like_btn"
                    app:layout_constraintEnd_toEndOf="@id/tv_like_btn"
                    app:layout_constraintStart_toStartOf="@id/tv_like_btn"
                    tools:text="999.9M+" />

                <com.socialplay.gpark.ui.view.IconTextView
                    android:id="@+id/tvDress"
                    style="@style/Button.S15.PoppinsSemiBold600"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp_48"
                    android:layout_marginStart="@dimen/dp_8"
                    android:background="@drawable/bg_yellow_round_40_stoke"
                    android:gravity="center"
                    android:text="@string/try_ons"
                    android:textAllCaps="false"
                    android:textColor="@color/color_E59A29"
                    app:iconHeight="@dimen/dp_24"
                    app:iconPadding="@dimen/dp_8"
                    app:iconPosition="left"
                    app:iconSrc="@drawable/ic_profile_copy_white"
                    app:iconTint="@color/color_E59A29"
                    app:iconWidth="@dimen/dp_24"
                    app:layout_constraintBottom_toBottomOf="@id/tv_like_btn"
                    app:layout_constraintEnd_toStartOf="@id/tv_get_btn"
                    app:layout_constraintStart_toEndOf="@id/tv_like_btn" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tv_get_btn"
                    style="@style/MetaTextView.S15.PoppinsMedium600"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp_48"
                    android:layout_marginStart="@dimen/dp_8"
                    android:background="@drawable/bg_ffdc1c_round_40"
                    android:gravity="center"
                    android:text="@string/claim_cap"
                    app:layout_constraintBottom_toBottomOf="@id/tv_like_btn"
                    app:layout_constraintEnd_toEndOf="@id/guide_outfit_right"
                    app:layout_constraintStart_toEndOf="@id/tvDress"
                    app:layout_constraintTop_toTopOf="@id/tv_like_btn" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/group_guest_views"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="@dimen/dp_0"
                    app:constraint_referenced_ids="tv_like_btn, tv_like_count
                        , tvDress, tv_get_btn, iv_like_count
                        , lav_like_anim" />

                <View
                    android:id="@+id/vSplitOutfitBottom"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_8"
                    android:layout_marginTop="@dimen/dp_12"
                    android:background="@color/color_F6F6F6"
                    app:layout_constraintTop_toBottomOf="@id/tv_like_btn" />

                <ImageView
                    android:id="@+id/iv_like_count"
                    android:layout_width="@dimen/dp_22"
                    android:layout_height="@dimen/dp_22"
                    android:layout_marginTop="@dimen/dp_6"
                    android:src="@drawable/ic_ugc_design_like"
                    app:layout_constraintEnd_toEndOf="@id/tv_like_btn"
                    app:layout_constraintStart_toStartOf="@id/tv_like_btn"
                    app:layout_constraintTop_toTopOf="@id/tv_like_btn" />

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/lav_like_anim"
                    android:layout_width="@dimen/dp_60"
                    android:layout_height="@dimen/dp_60"
                    android:padding="@dimen/dp_14"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/iv_like_count"
                    app:layout_constraintEnd_toEndOf="@id/iv_like_count"
                    app:layout_constraintStart_toStartOf="@id/iv_like_count"
                    app:layout_constraintTop_toTopOf="@id/iv_like_count"
                    app:lottie_autoPlay="false"
                    app:lottie_fileName="ugc_like.zip"
                    app:lottie_progress="1"
                    tools:visibility="visible" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <FrameLayout
                android:id="@+id/fl_pin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tv_comment_count"
                    style="@style/MetaTextView.S14.PoppinsSemiBold600"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="@dimen/dp_16"
                    android:paddingVertical="@dimen/dp_11"
                    tools:text="@string/comment_count_postfix" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvSortBtn"
                    style="@style/MetaTextView.S14"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableEnd="@drawable/ic_game_detail_common_sort_arrow"
                    android:drawablePadding="@dimen/dp_6"
                    android:paddingHorizontal="@dimen/dp_16"
                    android:paddingVertical="@dimen/dp_11"
                    android:text="@string/sort_default"
                    android:textColor="@color/color_757575"
                    android:visibility="gone"
                    android:layout_gravity="end"
                    tools:visibility="visible" />

            </FrameLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/rvComment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"
            tools:listitem="@layout/item_ugc_comment" />

    </com.socialplay.gpark.ui.view.VerticalCoordinatorLayout>

    <com.socialplay.gpark.ui.view.BottomCommentInputLayout
        android:id="@+id/bottomCommentInput"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_54"
        app:enableComment="true"
        app:enableLightUp="false"
        app:enableLike="true"
        app:enableSendFlower="false"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="gone" />

    <View
        android:id="@+id/v_bg_top_toolbar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:alpha="0"
        android:background="@color/white"
        android:clickable="true"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@id/sbphv" />

    <ImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:layout_marginStart="@dimen/dp_48"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintStart_toStartOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_username"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toStartOf="@id/ulv"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        app:layout_goneMarginEnd="@dimen/dp_16"
        tools:text="longlonglonglonglonglonglonglonglonglonglonglonglonglonglong" />

    <com.socialplay.gpark.ui.view.UserLabelView
        android:id="@+id/ulv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toStartOf="@id/tv_follow_btn"
        app:layout_constraintStart_toEndOf="@id/tv_username"
        app:layout_constraintTop_toTopOf="@id/iv_avatar" />

    <View
        android:id="@+id/v_user_click"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="@id/tv_username"
        app:layout_constraintStart_toStartOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_follow_btn"
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/bg_ffdc1c_round_24"
        android:drawableStart="@drawable/icon_send_flower_dialog_coins_add"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center"
        android:paddingHorizontal="12dp"
        android:paddingVertical="6.5dp"
        android:textColor="@color/color_212121"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toStartOf="@id/iv_share_btn"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        tools:text="@string/follow"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_share_btn"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:layout_marginEnd="@dimen/dp_16"
        android:padding="@dimen/dp_4"
        android:src="@drawable/ic_feat_24_1a1a1a_share"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        tools:visibility="visible" />

    <View
        android:id="@+id/vEditRedDot"
        android:layout_width="@dimen/dp_10"
        android:layout_height="@dimen/dp_10"
        android:background="@drawable/sp_red_dot"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/iv_share_btn"
        app:layout_constraintTop_toTopOf="@id/iv_share_btn"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MaskingLayout
        android:id="@+id/ml_guide"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_60" />

    <ImageView
        android:id="@+id/iv_guide_tri"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_ugc_design_tips_tri"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_guide"
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_round_32"
        android:drawableStart="@drawable/ic_ugc_design_tips_eye"
        android:drawablePadding="@dimen/dp_8"
        android:gravity="center_vertical"
        android:paddingVertical="@dimen/dp_6"
        android:paddingStart="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_16"
        android:text="@string/ugc_design_acquire_tips"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_guide_views"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:visibility="gone"
        app:constraint_referenced_ids="ml_guide, iv_guide_tri, tv_guide" />

</androidx.constraintlayout.widget.ConstraintLayout>