<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_FFDC1C">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="@dimen/dp_16" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sbphv_placeholder">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.socialplay.gpark.ui.view.TitleBarLayout
                android:id="@+id/tbl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:showBackIcon="true"
                app:background_color="@color/transparent"
                app:isDividerVisible="false"
                app:layout_constraintTop_toTopOf="parent"
                app:title_text="" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/ivTitle"
                style="@style/MetaTextView.S28.PoppinsBlack900"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/welcome_to_app"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"

                app:layout_constraintTop_toBottomOf="@id/tbl" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/input_name"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:layout_marginLeft="@dimen/dp_32"
                android:layout_marginTop="@dimen/dp_30"
                android:layout_marginRight="@dimen/dp_32"
                app:boxStrokeWidth="0dp"
                app:boxStrokeWidthFocused="0dp"
                app:hintAnimationEnabled="true"
                app:hintTextAppearance="@style/hintAppearence"
                app:hintTextColor="@color/color_B3B3B3"
                android:hint="@string/login_name_hint_long"
                android:textColorHint="@color/color_B3B3B3"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ivTitle">

              <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
                    android:id="@+id/et_name"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_login_fouces"
                    android:fontFamily="@font/poppins_regular_400"
                    android:maxLines="1"
                    android:inputType="textNoSuggestions"
                    android:textColorHint="@color/color_B3B3B3"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14" />
            </com.google.android.material.textfield.TextInputLayout>


            <ImageView
                android:id="@+id/ivClearName"
                android:layout_width="@dimen/dp_32"
                android:layout_height="0dp"
                android:paddingHorizontal="@dimen/dp_8"
                android:src="@drawable/icon_search_clear_input_grey"
                app:layout_constraintBottom_toBottomOf="@id/input_name"
                app:layout_constraintEnd_toStartOf="@id/vNameDivider"
                app:layout_constraintTop_toTopOf="@id/input_name" />

            <View
                android:id="@+id/vNameDivider"
                android:layout_width="@dimen/dp_1"
                android:layout_height="@dimen/dp_12"
                android:background="@color/neutral_color_8"
                app:layout_constraintBottom_toBottomOf="@id/input_name"
                app:layout_constraintEnd_toStartOf="@id/lottieRandomName"
                app:layout_constraintTop_toTopOf="@id/input_name" />

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/lottieRandomName"
                android:layout_width="@dimen/dp_56"
                android:layout_height="0dp"
                android:paddingHorizontal="@dimen/dp_16"
                app:layout_constraintBottom_toBottomOf="@id/input_name"
                app:layout_constraintEnd_toEndOf="@id/input_name"
                app:layout_constraintTop_toTopOf="@id/input_name"
                app:lottie_loop="true"
                app:lottie_fileName="random_rolling.zip" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvError"
                style="@style/MetaTextView.S12.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_10"
                android:visibility="gone"
                android:background="@drawable/bg_error_tips"
                android:gravity="center"
                android:paddingHorizontal="20dp"
                android:paddingVertical="3dp"
                android:textColor="@color/white"
                app:layout_constraintBottom_toTopOf="@id/input_birthday"
                app:layout_constraintEnd_toEndOf="@id/input_name"
                app:layout_constraintStart_toStartOf="@id/input_name"
                app:layout_constraintTop_toBottomOf="@id/input_name"
                app:layout_constraintVertical_chainStyle="packed"
                app:layout_constraintWidth_max="@dimen/dp_300"
                tools:text="dddddddddddddddddd" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/input_birthday"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:layout_marginLeft="@dimen/dp_32"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="@dimen/dp_32"
                android:background="@drawable/bg_white_corner_12"
                app:boxStrokeWidth="0dp"
                app:boxStrokeWidthFocused="0dp"
                app:hintAnimationEnabled="true"
                app:hintTextAppearance="@style/hintAppearence"
                app:hintTextColor="@color/color_B3B3B3"
                android:hint="@string/choose_birthday"
                android:textColorHint="@color/color_B3B3B3"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvError">

                <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
                    android:id="@+id/tv_birthday"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#00FFFFFF"
                    android:enabled="false"
                    android:fontFamily="@font/poppins_regular_400"
                    android:textColorHint="@color/color_B3B3B3"
                    android:textColor="@color/color_1A1A1A"
                    android:inputType="textNoSuggestions"
                    android:maxLines="1"
                    android:textSize="@dimen/sp_14" />
            </com.google.android.material.textfield.TextInputLayout>

            <ImageView
                android:id="@+id/iv_birthday"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:src="@drawable/icon_arrow_down"
                app:layout_constraintBottom_toBottomOf="@id/input_birthday"
                app:layout_constraintEnd_toEndOf="@id/input_birthday"
                app:layout_constraintTop_toTopOf="@id/input_birthday"
                app:tint="@color/textColorPrimary" />

            <View
                android:id="@+id/vBirthday"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/input_birthday"
                app:layout_constraintEnd_toEndOf="@id/input_birthday"
                app:layout_constraintStart_toStartOf="@id/input_birthday"
                app:layout_constraintTop_toTopOf="@id/input_birthday" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_birthday_format_err_tip"
                style="@style/MetaTextView.S12.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_10"
                android:background="@drawable/bg_error_tips"
                android:gravity="center"
                android:paddingHorizontal="20dp"
                android:paddingVertical="3dp"
                android:textColor="@color/white"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/input_birthday"
                app:layout_constraintStart_toStartOf="@id/input_birthday"
                app:layout_constraintTop_toBottomOf="@+id/input_birthday"
                app:layout_constraintVertical_chainStyle="packed"
                app:layout_constraintWidth_max="@dimen/dp_300"
                tools:text="ddddddddddddd" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/input_gender"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:layout_marginLeft="@dimen/dp_32"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="@dimen/dp_32"
                android:background="@drawable/bg_white_corner_12"
                app:boxStrokeWidth="0dp"
                app:boxStrokeWidthFocused="0dp"
                app:hintAnimationEnabled="true"
                android:textColorHint="@color/color_B3B3B3"
                android:hint="@string/login_gender_hint_long"
                app:hintTextAppearance="@style/hintAppearence"
                app:hintTextColor="@color/color_B3B3B3"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_birthday_format_err_tip">

                <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
                    android:id="@+id/tv_gender"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#00FFFFFF"
                    android:enabled="false"
                    android:fontFamily="@font/poppins_regular_400"
                    android:textColorHint="@color/color_B3B3B3"
                    android:textColor="@color/color_1A1A1A"
                    android:inputType="textNoSuggestions"
                    android:maxLines="1"
                    android:textSize="@dimen/sp_14" />
            </com.google.android.material.textfield.TextInputLayout>


            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_gender_format_err_tip"
                style="@style/MetaTextView.S12.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_10"
                android:background="@drawable/bg_error_tips"
                android:gravity="center"
                android:paddingHorizontal="20dp"
                android:paddingVertical="3dp"
                android:textColor="@color/white"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/tvSure"
                app:layout_constraintEnd_toEndOf="@id/input_gender"
                app:layout_constraintStart_toStartOf="@id/input_gender"
                app:layout_constraintTop_toBottomOf="@+id/input_gender"
                app:layout_constraintVertical_chainStyle="packed"
                app:layout_constraintWidth_max="@dimen/dp_300"
                tools:text="ddddddddddddddddddddddddddddd" />

            <ImageView
                android:id="@+id/iv_gender"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:src="@drawable/icon_arrow_down"
                app:layout_constraintBottom_toBottomOf="@id/input_gender"
                app:layout_constraintEnd_toEndOf="@id/input_gender"
                app:layout_constraintTop_toTopOf="@id/input_gender"
                app:tint="@color/textColorPrimary" />

            <View
                android:id="@+id/vGender"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/input_gender"
                app:layout_constraintEnd_toEndOf="@id/input_gender"
                app:layout_constraintStart_toStartOf="@id/input_gender"
                app:layout_constraintTop_toTopOf="@id/input_gender" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvSure"
                style="@style/Button.S18.PoppinsBlack900.Height48"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_72"
                android:layout_marginTop="@dimen/dp_40"
                android:background="@drawable/bg_c5b100_round"
                android:enabled="false"
                android:minHeight="@dimen/dp_56"
                android:text="@string/text_confirm_uppercase"
                android:textColor="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_gender_format_err_tip" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="100dp"
                app:layout_constraintTop_toBottomOf="@+id/tvSure" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_agreement"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_FFDC1C"
        android:orientation="horizontal"
        android:paddingBottom="@dimen/dp_32"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_agreement"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_40"
            android:gravity="center"
            android:textColor="#53535E"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:uiLineHeight="@dimen/dp_18"
            tools:ignore="RtlSymmetry"
            tools:text="I have read and agreed to the user agreement and privacy policy" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>