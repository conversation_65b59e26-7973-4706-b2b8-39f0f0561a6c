<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/dp_16"
    >

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTopicName"
        style="@style/MetaTextView.S18.PoppinsSemiBold600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvTopicFollow"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="#Communitylonggggggggggggggggggggggg" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvViews"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:maxLines="1"
        android:singleLine="true"
        android:text="@string/community_topic_item_view_flowing_count"
        android:textColor="@color/neutral_color_4"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTopicName"
        app:layout_constraintEnd_toStartOf="@id/tvTopicFollow"
        />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTopicFollow"
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        android:layout_width="@dimen/dp_99"
        android:layout_height="@dimen/dp_30"
        android:text="@string/follow"
        android:gravity="center"
        android:background="@drawable/bg_ffdc1c_round_100"
        android:textColor="@color/color_1A1A1A"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>