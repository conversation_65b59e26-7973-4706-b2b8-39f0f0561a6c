<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivPortrait"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:src="@drawable/icon_default_avatar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:scaleType="centerCrop"
        app:shapeAppearance="@style/circleStyle" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvName"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_12"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tvId"
        app:layout_constraintEnd_toStartOf="@id/label_group"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/ivPortrait"
        app:layout_constraintTop_toTopOf="@id/ivPortrait"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvId"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_12"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/neutral_color_5"
        app:layout_constraintBottom_toBottomOf="@id/ivPortrait"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivPortrait"
        app:layout_constraintTop_toBottomOf="@id/tvName"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="ID: ddd" />

    <com.socialplay.gpark.ui.view.UserLabelView
        android:id="@+id/label_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvName"
        app:layout_constraintTop_toTopOf="@id/tvName" />

</androidx.constraintlayout.widget.ConstraintLayout>