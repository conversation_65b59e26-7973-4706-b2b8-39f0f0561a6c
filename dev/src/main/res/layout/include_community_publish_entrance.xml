<?xml version="1.0" encoding="utf-8"?>
<com.lihang.ShadowLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:hl_cornerRadius="@dimen/dp_100"
    app:hl_shadowColor="@color/black"
    app:hl_shadowLimit="@dimen/dp_16"
    app:hl_shadowOffsetY="@dimen/dp_6"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clClick"
        android:layout_height="@dimen/dp_64"
        android:layout_width="@dimen/dp_64"
        android:background="@color/color_FFDC1C"
        >
        <ImageView
            android:id="@+id/ivPublish"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_community_post"
            app:layout_constraintBottom_toTopOf="@id/tvPublish"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvPublish"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:layout_gravity="center"
            android:text="@string/post"
            app:layout_constraintTop_toBottomOf="@id/ivPublish"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>


</com.lihang.ShadowLayout>
