<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder_corner_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/round_corner_8dp" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_title"
        style="@style/MetaTextView.S14"
        tools:text="Night FuseNight FuseNight FuseNight FuseNight Fuse"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constraintBottom_toTopOf="@id/tv_game_author"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="@id/iv_icon"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_author"
        style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
        tools:text="Creator: Zucca"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_2"
        android:layout_marginEnd="@dimen/dp_8"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:minHeight="@dimen/sp_22"
        android:singleLine="true"
        app:layout_constraintBottom_toTopOf="@id/ll_game_info"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_game_title"
        app:layout_constraintTop_toBottomOf="@id/tv_game_title"
        app:uiLineHeight="@dimen/sp_22" />

    <LinearLayout
        android:id="@+id/ll_game_info"
        android:layout_width="0dp"
        android:layout_height="@dimen/sp_22"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_game_title"
        app:layout_constraintTop_toBottomOf="@id/tv_game_author">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_game_like"
            style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
            tools:text="24"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_post_game_like"
            android:drawablePadding="@dimen/dp_3"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:minHeight="@dimen/sp_22"
            android:singleLine="true"
            app:uiLineHeight="@dimen/sp_22" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_game_score"
            style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
            tools:text="6.8k Players"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:drawableStart="@drawable/ic_post_game_score"
            android:drawablePadding="@dimen/dp_3"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:minHeight="@dimen/sp_22"
            android:singleLine="true"
            app:uiLineHeight="@dimen/sp_22" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_game_people"
            style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
            tools:text="6.8k Players"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_post_game_people"
            android:drawablePadding="@dimen/dp_3"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:minHeight="@dimen/sp_22"
            android:singleLine="true"
            app:uiLineHeight="@dimen/sp_22" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>