<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/cl_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_video_feed_info"
        android:maxHeight="445dp"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp_12"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_max="445dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|bottom"
            android:layout_marginEnd="@dimen/dp_35"
            android:layout_weight="1"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_game_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_video_feed_game_info_bar"
                android:padding="@dimen/dp_4">

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/iv_game_icon"
                    android:layout_width="@dimen/dp_30"
                    android:layout_height="@dimen/dp_30"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:scaleType="centerCrop"
                    app:shapeAppearance="@style/round_corner_6dp"
                    tools:src="@color/black" />

                <TextView
                    android:id="@+id/tv_game_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_6"
                    android:layout_marginEnd="@dimen/dp_6"
                    android:ellipsize="end"
                    android:maxLength="12"
                    android:maxLines="1"
                    android:textColor="@color/white"
                    style="@style/MetaTextView.S12.PoppinsSemiBold600"
                    app:layout_constraintEnd_toStartOf="@id/dpn_play_game"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/iv_game_icon"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintWidth_default="wrap"
                    tools:text="Enchilada" />

                <TextView
                    android:id="@+id/dpn_play_game"
                    style="@style/MetaTextView.S12.PoppinsSemiBold600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_6"
                    android:background="@drawable/bg_video_feed_game_enter"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp_8"
                    android:paddingVertical="@dimen/dp_4"
                    android:text="@string/video_feed_enter_game"
                    android:textColor="@color/black"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="@id/tv_game_name"
                    app:layout_constraintTop_toTopOf="parent" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_14"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_author"
                    android:layout_width="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/white"
                    style="@style/MetaTextView.S16.PoppinsSemiBold600"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@id/iv_author_official"
                    tools:text="\@YanTuo007" />

                <ImageView
                    android:id="@+id/iv_author_official"
                    android:layout_marginStart="@dimen/dp_4"
                    app:layout_constraintStart_toEndOf="@+id/tv_author"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:src="@drawable/ic_video_feed_author_official" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <ScrollView
                android:id="@+id/sv_content_container"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/dp_9"
                android:layout_weight="1"
                tools:ignore="NestedWeights">

                <com.socialplay.gpark.ui.view.ExpandableTextView
                    android:id="@+id/tv_content"
                    style="@style/MetaTextView.S14.PoppinsMedium500"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineSpacingMultiplier="1.2"
                    android:textColor="@color/white"
                    app:etv_EnableToggleClick="true"
                    app:etv_InitState="shrink"
                    app:etv_MaxLinesOnShrink="2"
                    app:etv_ToExpandHint="@string/more_with_space"
                    app:etv_ToExpandHintColor="@color/white_60"
                    app:etv_ToExpandHintColorBgPressed="@color/transparent"
                    app:etv_ToExpandHintShow="true"
                    app:etv_ToShrinkHint="@string/collapse_with_space"
                    app:etv_ToShrinkHintColor="@color/white_60"
                    app:etv_ToShrinkHintColorBgPressed="@color/transparent"
                    app:etv_ToShrinkHintShow="true"
                    tools:text="How to optimize images in WordPress for faster loading to ...How to optimize images in WordPress for faster loading to ...How to optimize images in WordPress for faster loading to ...How to optimize images in WordPress for faster loading to ...How to optimize images in WordPress for faster loading to ...How to optimize images in WordPress for faster loading to ...How to optimize images in WordPress for faster loading to ...How to optimize images in WordPress for faster loading to ..." />
            </ScrollView>


        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginEnd="-12dp"
            android:layout_marginBottom="@dimen/dp_20"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:gravity="center_horizontal"
            android:orientation="vertical">

        <LinearLayout
                android:layout_width="wrap_content"
                android:orientation="vertical"
                android:gravity="center_horizontal"
                android:layout_height="wrap_content">

                <FrameLayout
                    android:id="@+id/fl_author_avatar_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dp_8"
                    android:background="@drawable/bg_white_circle"
                    android:padding="1dp">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/siv_author_avatar"
                        android:layout_width="@dimen/dp_46"
                        android:layout_height="@dimen/dp_46"
                        android:src="@color/black"
                        app:shapeAppearance="@style/circleStyle" />
                </FrameLayout>

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/lav_follow_anim"
                android:layout_width="@dimen/dp_32"
                android:layout_height="@dimen/dp_32"
                android:layout_marginTop="-25dp"
                app:lottie_progress="0"
                app:lottie_fileName="anim_video_feed_follow.zip" />
        </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_like_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal|bottom"
                android:clipChildren="false"
                android:gravity="center"
                android:orientation="vertical">

                <FrameLayout
                    android:id="@+id/fl_like_animation_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:translationZ="1dp"
                    android:clipChildren="false">

                    <ImageView
                        android:id="@+id/iv_like"
                        android:layout_width="@dimen/dp_44"
                        android:layout_height="@dimen/dp_44"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_video_feed_like" />

                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/lav_like_anim"
                        android:layout_width="@dimen/dp_64"
                        android:layout_height="@dimen/dp_64"
                        app:lottie_autoPlay="false"
                        app:lottie_loop="false"
                        app:lottie_progress="1"
                        tools:alpha="0.5"
                        app:lottie_fileName="anim_video_feed_like.zip" />
                </FrameLayout>

                <TextView
                    android:id="@+id/tv_like_cnt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-8dp"
                    android:textColor="@color/white"
                    style="@style/MetaTextView.S12.PoppinsMedium500"
                    tools:text="373" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_comment_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal|bottom"
                android:layout_marginTop="@dimen/dp_14"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_comment"
                    android:layout_width="@dimen/dp_44"
                    android:layout_height="@dimen/dp_44"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_video_feed_comment" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_comment_cnt"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/sp_18"
                    android:textColor="@color/white"
                    app:autoSizeTextType="uniform"
                    android:maxWidth="@dimen/dp_64"
                    android:paddingHorizontal="@dimen/dp_4"
                    app:autoSizeMinTextSize="@dimen/sp_1"
                    app:autoSizeStepGranularity="0.01sp"
                    android:gravity="center"
                    app:autoSizeMaxTextSize="@dimen/sp_12"
                    style="@style/MetaTextView.S12.PoppinsMedium500"
                    tools:text="Comment" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_share_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal|bottom"
                android:layout_marginTop="@dimen/dp_14"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/iv_share"
                    android:layout_width="@dimen/dp_44"
                    android:layout_height="@dimen/dp_44"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_share_s44_white" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_share_cnt"
                    style="@style/MetaTextView.S12.PoppinsMedium500"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/sp_18"
                    android:gravity="center"
                    android:maxWidth="@dimen/dp_64"
                    android:paddingHorizontal="@dimen/dp_4"
                    android:textColor="@color/white"
                    app:autoSizeMaxTextSize="@dimen/sp_12"
                    app:autoSizeMinTextSize="@dimen/sp_1"
                    app:autoSizeStepGranularity="0.01sp"
                    app:autoSizeTextType="uniform"
                    tools:text="Share" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>


    <ImageView
        android:id="@+id/iv_play"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_video_feed_play"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />


</androidx.constraintlayout.widget.ConstraintLayout>