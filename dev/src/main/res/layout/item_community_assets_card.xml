<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guidelineAvatar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="@dimen/dp_56" />

    <androidx.cardview.widget.CardView
        android:id="@+id/bgCard"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_62"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_16"
        app:cardCornerRadius="@dimen/dp_12"
        app:cardElevation="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/guidelineAvatar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_max="@dimen/dp_288">

        <View
            android:id="@+id/vMainColor"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/color_97BCDE" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_linear_transparent_black15" />
    </androidx.cardview.widget.CardView>

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivIcon"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_4"
        android:layout_marginBottom="@dimen/dp_4"
        android:background="@color/color_D0D9F0"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="@id/bgCard"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintStart_toStartOf="@id/bgCard"
        app:layout_constraintTop_toTopOf="@id/bgCard"
        app:shapeAppearance="@style/round_corner_9dp" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTitle"
        style="@style/MetaTextView.S13.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/white"
        app:layout_constraintBottom_toTopOf="@id/layoutMetaLike"
        app:layout_constraintEnd_toStartOf="@id/ivDelBtn"
        app:layout_constraintStart_toEndOf="@id/ivIcon"
        app:layout_constraintTop_toTopOf="@id/bgCard"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Night FuseNight FuseNight FuseNight FuseNight Fuse" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutMetaLike"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/bgCard"
        app:layout_constraintStart_toEndOf="@id/ivIcon"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <com.socialplay.gpark.ui.view.MetaLikeView
            android:id="@+id/mlvLike"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:likeIcon="@drawable/recommend_iconvideo_like"
            app:likeText="12.3K" />

        <com.socialplay.gpark.ui.view.MetaLikeView
            android:id="@+id/mlvPlayers"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="-4dp"
            app:layout_constraintStart_toEndOf="@id/mlvLike"
            app:layout_constraintTop_toTopOf="parent"
            app:likeIcon="@drawable/icon_item_player"
            app:likeText="12.3K" />

        <com.socialplay.gpark.ui.view.MetaLikeView
            android:id="@+id/mlvTryOn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:likeIcon="@drawable/ic_cloth_white_size_12"
            app:likeText="1 Try on" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/ivDelBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_8"
        android:src="@drawable/ic_post_del_card"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/bgCard"
        app:layout_constraintEnd_toEndOf="@id/bgCard"
        app:layout_constraintTop_toTopOf="@id/bgCard"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>