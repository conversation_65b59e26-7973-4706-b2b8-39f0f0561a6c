<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:clipChildren="false"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guidelineAvatar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="@dimen/dp_56" />

    <include
        android:id="@+id/includeCard"
        layout="@layout/adapter_publish_post_game"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/guidelineAvatar"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 暂不使用 includeTag -->
    <include
        android:id="@+id/include_moment_take"
        layout="@layout/item_post_moment_take"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_10"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/guidelineAvatar"
        app:layout_constraintTop_toBottomOf="@id/includeCard"
        tools:visibility="visible" />

    <include
        android:id="@+id/include_outfit"
        layout="@layout/item_post_outfit_card_try_on"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_10"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/guidelineAvatar"
        app:layout_constraintTop_toBottomOf="@id/include_moment_take"
        tools:visibility="visible" />

    <include
        android:id="@+id/include_ugc_Design"
        layout="@layout/item_post_ugc_design_card_edit"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_10"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/guidelineAvatar"
        app:layout_constraintTop_toBottomOf="@id/include_outfit"
        tools:visibility="visible" />

    <include
        android:id="@+id/includeTag"
        layout="@layout/adapter_publish_post_tag"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_30"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_14"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/guidelineAvatar"
        app:layout_constraintTop_toBottomOf="@id/include_ugc_Design" />

    <include
        android:id="@+id/includeAttitude"
        layout="@layout/adapter_community_post_attitude"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/guidelineAvatar"
        app:layout_constraintTop_toBottomOf="@id/includeTag" />

    <Space
        android:id="@+id/spaceBottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintTop_toBottomOf="@id/includeAttitude" />

    <View
        android:id="@+id/vLine"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/neutral_color_9"
        app:layout_constraintTop_toBottomOf="@id/spaceBottom" />

    <View
        android:id="@+id/vMask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clickable="true"
        app:layout_constraintBottom_toBottomOf="@id/vLine"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>