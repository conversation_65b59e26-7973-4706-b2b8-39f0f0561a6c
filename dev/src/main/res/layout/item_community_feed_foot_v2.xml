<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guidelineAvatar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="@dimen/dp_56" />

    <include
        android:id="@+id/includeAttitude"
        layout="@layout/adapter_community_post_attitude"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/guidelineAvatar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_max="@dimen/dp_288" />

    <Space
        android:id="@+id/spaceBottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintTop_toBottomOf="@id/includeAttitude" />

    <View
        android:id="@+id/vLine"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/neutral_color_9"
        app:layout_constraintTop_toBottomOf="@id/spaceBottom" />

    <View
        android:id="@+id/vMask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clickable="true"
        app:layout_constraintBottom_toBottomOf="@id/vLine"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>