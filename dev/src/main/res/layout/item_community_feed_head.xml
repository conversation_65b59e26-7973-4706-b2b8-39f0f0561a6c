<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:paddingHorizontal="@dimen/dp_16">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivPortrait"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_16"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_default_avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/circleStyle" />

    <ImageView
        android:id="@+id/ivFollow"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:src="@drawable/ic_follow_add_yellow_size_24"
        android:translationY="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="@id/ivPortrait"
        app:layout_constraintEnd_toEndOf="@id/ivPortrait"
        app:layout_constraintStart_toStartOf="@id/ivPortrait" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lavFollowAnim"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:translationY="@dimen/dp_15"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivPortrait"
        app:layout_constraintEnd_toEndOf="@id/ivPortrait"
        app:layout_constraintStart_toStartOf="@id/ivPortrait"
        app:lottie_autoPlay="false"
        app:lottie_fileName="community_follow_add.zip"
        app:lottie_progress="1"
        tools:visibility="visible" />

    <View
        android:id="@+id/vFollowClick"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:translationY="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/ivPortrait"
        app:layout_constraintEnd_toEndOf="@id/ivPortrait"
        app:layout_constraintStart_toStartOf="@id/ivPortrait" />

    <ImageView
        android:id="@+id/ivState"
        android:layout_width="@dimen/dp_10"
        android:layout_height="@dimen/dp_10"
        android:src="@drawable/shape_green_point_white_stroke"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/ivPortrait"
        app:layout_constraintTop_toTopOf="@id/ivPortrait"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvName"
        style="@style/MetaTextView.S14.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tvTime"
        app:layout_constraintEnd_toStartOf="@id/ivCertification"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/ivPortrait"
        app:layout_constraintTop_toTopOf="@id/ivPortrait"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="AAAAAAAAAAAAAAAAAAAAAAAAAA" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTime"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:drawablePadding="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/neutral_color_3"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/ivPortrait"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/ivPortrait"
        app:layout_constraintTop_toBottomOf="@id/tvName"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Nov 12, 2022  14:12 AM" />

    <com.socialplay.gpark.ui.view.UserLabelView
        android:id="@+id/ivCertification"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvName"
        app:layout_constraintTop_toTopOf="@id/tvName"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.ExpandableTextView
        android:id="@+id/tvContent"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_4"
        android:textColor="@color/neutral_color_2"
        app:etv_EnableToggleClick="false"
        app:etv_ExtendClickScope="true"
        app:etv_MaxLinesOnShrink="5"
        app:etv_ToExpandHint="@string/more_cap"
        app:etv_ToExpandHintBold="false"
        app:etv_ToExpandHintColor="@color/secondary_color_1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/ivPortrait"
        app:layout_constraintTop_toBottomOf="@id/ivPortrait"
        tools:text="aaaaaaaaaaaaaaa" />

    <View
        android:id="@+id/layerUser"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/ivPortrait"
        app:layout_constraintEnd_toEndOf="@id/tvName"
        app:layout_constraintStart_toStartOf="@id/ivPortrait"
        app:layout_constraintTop_toTopOf="@id/ivPortrait" />

    <View
        android:id="@+id/vMask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clickable="true"
        app:layout_constraintBottom_toBottomOf="@id/tvContent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>