<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guidelineAvatar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="@dimen/dp_72" />

    <View
        android:id="@+id/vBg"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@drawable/bg_f6f6f6_round_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/guidelineAvatar"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivPortrait"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_16"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_default_avatar"
        app:layout_constraintStart_toStartOf="@id/vBg"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/circleStyle" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvName"
        style="@style/MetaTextView.S14.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_6"
        android:ellipsize="end"
        android:maxLines="1"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/labelView"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/ivPortrait"
        app:layout_constraintTop_toTopOf="@id/ivPortrait"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="AAAAAAAAAAAAAAAAAAAAAAAAAA" />

    <com.socialplay.gpark.ui.view.UserLabelView
        android:id="@+id/labelView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvName"
        app:layout_constraintEnd_toStartOf="@id/ivLike"
        app:layout_constraintStart_toEndOf="@id/tvName"
        app:layout_constraintTop_toTopOf="@id/tvName"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivLike"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_4"
        android:src="@drawable/ic_ugc_design_like"
        app:layout_constraintEnd_toStartOf="@id/tvLike"
        app:layout_constraintTop_toTopOf="@id/ivPortrait" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lavLikeAnim"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivLike"
        app:layout_constraintEnd_toEndOf="@id/ivLike"
        app:layout_constraintStart_toStartOf="@id/ivLike"
        app:layout_constraintTop_toTopOf="@id/ivLike"
        app:lottie_autoPlay="false"
        app:lottie_fileName="community_like.zip"
        app:lottie_progress="1"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvLike"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_12"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:minWidth="@dimen/dp_27"
        android:textColor="@color/color_666666"
        app:layout_constraintBottom_toBottomOf="@id/ivLike"
        app:layout_constraintEnd_toEndOf="@id/vBg"
        app:layout_constraintTop_toTopOf="@id/ivLike"
        tools:text="13.6K" />

    <View
        android:id="@+id/layerLike"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/tvLike"
        app:layout_constraintEnd_toEndOf="@id/tvLike"
        app:layout_constraintStart_toStartOf="@id/ivLike"
        app:layout_constraintTop_toTopOf="@id/tvLike" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvContent"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_12"
        android:ellipsize="end"
        android:maxLines="3"
        app:layout_constraintEnd_toEndOf="@id/vBg"
        app:layout_constraintStart_toStartOf="@id/tvName"
        app:layout_constraintTop_toBottomOf="@id/tvName"
        tools:text="Join a secret underground battle club for toys! Rebel davehicle combat game where disgruntleddag" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/imageTag"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_16"
        android:drawableStart="@drawable/ic_img_0070ce_size_20"
        android:drawablePadding="@dimen/dp_4"
        android:ellipsize="end"
        android:maxLines="3"
        android:textColor="@color/color_0070CE"
        app:layout_constraintStart_toStartOf="@id/tvName"
        app:layout_constraintTop_toBottomOf="@id/tvContent"
        app:layout_goneMarginTop="@dimen/dp_4"
        tools:text="View image" />

    <View
        android:id="@+id/vLineBottom"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="@dimen/dp_32"
        android:background="@color/neutral_color_9"
        app:layout_constraintTop_toBottomOf="@id/imageTag" />

</androidx.constraintlayout.widget.ConstraintLayout>