<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <Space
        android:id="@+id/spaceTop"
        android:layout_width="@dimen/dp_1"
        android:layout_height="@dimen/dp_6"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:id="@+id/spaceStart"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivImage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="fitStart"
        android:src="@drawable/placeholder_corner_6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/spaceStart"
        app:layout_constraintTop_toBottomOf="@id/spaceTop"
        app:shapeAppearance="@style/round_corner_12dp"
        tools:layout_height="@dimen/dp_217"
        tools:layout_width="@dimen/dp_163" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvLongImage"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_21"
        android:layout_marginEnd="@dimen/dp_4"
        android:layout_marginBottom="@dimen/dp_4"
        android:background="@drawable/bg_black65_5"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_4"
        android:text="@string/long_scroll"
        android:textColor="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivImage"
        app:layout_constraintEnd_toEndOf="@id/ivImage"
        tools:visibility="visible" />

    <View
        android:id="@+id/vMask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clickable="true"
        app:layout_constraintBottom_toBottomOf="@id/ivImage"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>