<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:maxWidth="389dp"
    android:paddingVertical="@dimen/dp_16">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvName"
        style="@style/MetaTextView.S16.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        android:text="@string/community_suggested_users"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_16"
        android:max="100"
        android:progressDrawable="@drawable/progress_ffdc1c_bg_f0f0f0_width_radius_2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:progress="50" />

</androidx.constraintlayout.widget.ConstraintLayout>