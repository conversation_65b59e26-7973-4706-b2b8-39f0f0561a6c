<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/dp_8">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivIcon"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_marginStart="@dimen/dp_16"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_topics_square_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/round_corner_12dp" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTopicTitle"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/ivTopicHot"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/ivIcon"
        app:layout_constraintTop_toTopOf="@id/ivIcon"
        tools:text="# Monster bug relesage" />

    <ImageView
        android:id="@+id/ivTopicHot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_2"
        android:layout_marginEnd="@dimen/dp_8"
        android:src="@drawable/ic_topic_hot"
        app:layout_constraintBottom_toBottomOf="@id/tvTopicTitle"
        app:layout_constraintEnd_toStartOf="@id/tvTopicDiscuss"
        app:layout_constraintStart_toEndOf="@id/tvTopicTitle"
        app:layout_constraintTop_toTopOf="@id/tvTopicTitle" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTopicDesc"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constraintEnd_toStartOf="@id/tvTopicDiscuss"
        app:layout_constraintStart_toStartOf="@id/tvTopicTitle"
        app:layout_constraintTop_toBottomOf="@id/tvTopicTitle"
        tools:text="@string/community_topic_item_view_flowing_count" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTopicDiscuss"
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        android:layout_width="@dimen/dp_88"
        android:layout_height="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/bg_corner_100_f49d0c"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:singleLine="true"
        android:text="@string/community_topic_item_discuss"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/ivIcon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/ivIcon" />

</androidx.constraintlayout.widget.ConstraintLayout>