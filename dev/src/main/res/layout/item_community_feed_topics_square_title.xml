<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/ivTopicsSquareCardTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:paddingHorizontal="@dimen/dp_8"
        android:paddingVertical="@dimen/dp_8"
        android:src="@drawable/img_topics_square_card_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivTopicsSquareCardArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_2"
        android:src="@drawable/icon_back_arrow_topics_square"
        app:layout_constraintBottom_toBottomOf="@id/ivTopicsSquareCardTitle"
        app:layout_constraintStart_toEndOf="@id/ivTopicsSquareCardTitle"
        app:layout_constraintTop_toTopOf="@id/ivTopicsSquareCardTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>