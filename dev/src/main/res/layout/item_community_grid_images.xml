<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/imageGrid"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dp_6"
    android:layout_marginStart="@dimen/dp_6"
    >

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivImage"
        android:layout_width="@dimen/dp_88"
        android:layout_height="@dimen/dp_88"
        android:scaleType="fitStart"
        android:src="@drawable/placeholder_corner_6"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:shapeAppearance="@style/round_corner_12dp" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvLongImageMarker"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_21"
        android:layout_marginEnd="@dimen/dp_4"
        android:layout_marginBottom="@dimen/dp_4"
        android:background="@drawable/bg_black65_5"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_4"
        android:text="@string/long_scroll"
        android:textColor="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivImage"
        app:layout_constraintEnd_toEndOf="@id/ivImage" />

    <View
        android:id="@+id/vMask"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:clickable="true"
        app:layout_constraintBottom_toBottomOf="@id/ivImage"
        app:layout_constraintEnd_toEndOf="@id/ivImage"
        app:layout_constraintStart_toStartOf="@id/ivImage"
        app:layout_constraintTop_toTopOf="@id/ivImage" />

</androidx.constraintlayout.widget.ConstraintLayout>