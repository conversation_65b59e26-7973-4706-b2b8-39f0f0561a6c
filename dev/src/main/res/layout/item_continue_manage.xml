<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv_game_icon"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginBottom="@dimen/dp_12"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:cornerRadii="@dimen/dp_12"
        tools:src="@mipmap/ic_launcher" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_name"
        style="@style/MetaTextView.S16.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_26"
        android:ellipsize="end"
        android:maxLines="2"
        app:layout_constraintBottom_toBottomOf="@id/iv_game_icon"
        app:layout_constraintEnd_toStartOf="@id/tv_game_delete"
        app:layout_constraintStart_toEndOf="@id/iv_game_icon"
        app:layout_constraintTop_toTopOf="@id/iv_game_icon"
        tools:text="AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_delete"
        style="@style/MetaTextView.S16.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/shape_black_1a_stroke_41"
        android:ellipsize="end"
        android:maxLines="2"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_4"
        android:text="@string/continue_gamedelete_delete"
        app:layout_constraintBottom_toBottomOf="@id/iv_game_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_game_icon" />

    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:background="@color/color_F0F0F0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>