<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="start"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp_16">

    <Space
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_video"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_175"
        android:layout_gravity="center"
        android:background="@color/neutral_color_9"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp">

        <!-- onRenderedFirstFrame 回调时间不准, 有些视频都已经播放了 1s, 才收到这个回调 -->
        <!-- 如果将视频封面放在视频下面, 并将PlayerView的背景设置为透明, 回调时间不准也没关系了 -->
        <ImageView
            android:id="@+id/cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:scaleType="centerCrop" />

        <com.google.android.exoplayer2.ui.StyledPlayerView
            android:id="@+id/player"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:contentDescription="@null"
            android:background="@color/transparent"
            app:played_color="@color/colorPrimary"
            app:resize_mode="zoom"
            app:player_layout_id="@layout/view_player_layout_id_fullscreen"
            app:surface_type="texture_view"
            app:unplayed_color="@color/white"
            app:use_controller="false" />

        <include
            android:id="@+id/control"
            layout="@layout/layout_feed_controller_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center" />
    </androidx.cardview.widget.CardView>
</LinearLayout>
