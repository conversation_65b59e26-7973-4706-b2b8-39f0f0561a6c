<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"

    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"


    android:paddingTop="6dp"
    android:paddingBottom="6dp">

    <RelativeLayout
        android:id="@+id/rl_notification"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">

        <TextView
            android:id="@+id/rc_msg"
            style="@style/MessageTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:textSize="@dimen/sp_12" />

        <TextView
            android:id="@+id/rc_edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/rc_msg"
            android:gravity="center"
            android:paddingLeft="4dp"
            android:paddingTop="4dp"
            android:paddingRight="2dp"
            android:paddingBottom="4dp"
            android:textSize="14sp"
            android:visibility="gone" />
    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rl_app_notification"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dp_8"
        android:paddingVertical="@dimen/dp_16"
        android:background="@drawable/shape_f5f5f5_corner_12"
        android:layout_centerInParent="true"
        android:layout_centerHorizontal="true">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottie_bell"
            android:layout_width="32dp"
            android:layout_height="32dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_fileName="bell.zip" />

        <LinearLayout
            android:id="@+id/ll_notification_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_8"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/lottie_bell"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/rc_app_notification"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:fontFamily="@font/poppins_medium_500"
                android:gravity="center"
                android:text="@string/notification_system_title"
                android:textColor="@color/color_0F0A21"
                android:textSize="@dimen/sp_12" />

            <TextView
                android:id="@+id/rc_app_notification_des"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:fontFamily="@font/poppins_medium_500"
                android:gravity="left"
                android:maxWidth="@dimen/dp_173"
                android:text="@string/notification_system_content"
                android:textColor="@color/color_BDBDBD"
                android:textSize="@dimen/sp_12" />
        </LinearLayout>

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/btnConfirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_16"
            android:background="@drawable/selector_button"
            android:fontFamily="@font/poppins_semi_bold_600"
            android:paddingHorizontal="@dimen/dp_18"
            android:paddingVertical="6dp"
            android:text="@string/open"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/ll_notification_tips"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</RelativeLayout>