<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/dp_12"
    android:paddingHorizontal="@dimen/dp_16"
    >

    <include
        android:id="@+id/includeItem"
        layout="@layout/include_common_user_list"
        android:layout_width="0dp"
        android:layout_marginEnd="@dimen/dp_88"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.socialplay.gpark.ui.view.FollowView
        android:id="@+id/followView"
        android:layout_height="@dimen/dp_30"
        android:layout_width="@dimen/dp_88"
        app:viewStyle="hollow"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvAction"
        style="@style/MetaTextView.S12"
        android:layout_width="@dimen/dp_88"
        android:layout_height="@dimen/dp_30"
        android:background="@drawable/bg_fbd24e_round_corner_100"
        android:gravity="center"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        android:textColor="@color/color_FBD24E"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/view_cap" />

</androidx.constraintlayout.widget.ConstraintLayout>