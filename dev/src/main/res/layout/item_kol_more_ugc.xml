<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <Space
        android:id="@+id/spaceTop"
        android:layout_width="@dimen/dp_1"
        android:layout_height="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_icon"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder"
        app:layout_constraintDimensionRatio="166:123"
        app:layout_constraintEnd_toStartOf="@id/spaceEnd"
        app:layout_constraintStart_toEndOf="@id/spaceStart"
        app:layout_constraintTop_toBottomOf="@id/spaceTop"
        app:shapeAppearance="@style/round_corner_12dp" />

    <!--  new: @drawable/bg_followed_work_new  @string/community_followed_work_tag_new-->
    <!--  hot: @drawable/bg_followed_work_hot  @string/community_followed_work_tag_hot-->
    <!--  recommend: @drawable/bg_followed_work_recommend  @string/community_followed_work_tag_recommend-->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTag"
        style="@style/MetaTextView.S10.PoppinsBlack900"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_17"
        android:background="@drawable/bg_followed_work_new"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_10"
        android:text="@string/community_followed_work_tag_new"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="@id/iv_icon"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_more_btn"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        android:padding="@dimen/dp_6"
        android:src="@drawable/ic_item_more"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="@id/iv_icon"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/lv_like"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        app:likeIcon="@drawable/recommend_iconvideo_like"
        app:likeText="1234574733735" />

    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/lv_play"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="-4dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintStart_toEndOf="@id/lv_like"
        app:likeIcon="@drawable/icon_item_player"
        app:likeText="1234574733735" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_name"
        style="@style/MetaTextView.S13.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/neutral_color_1"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        app:layout_constraintTop_toBottomOf="@id/iv_icon"
        tools:text="Leslie Alexander\n范德萨" />

    <com.socialplay.gpark.ui.view.TagContainerView
        android:id="@+id/tag_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_author_avatar"
        android:layout_width="@dimen/dp_18"
        android:layout_height="@dimen/dp_18"
        android:layout_marginTop="@dimen/dp_8"
        app:layout_constraintStart_toStartOf="@id/tv_name"
        app:layout_constraintTop_toBottomOf="@id/tag_container"
        app:shapeAppearance="@style/circleStyle"
        app:strokeColor="@color/white_60"
        app:strokeWidth="@dimen/dp_1"
        tools:src="@drawable/placeholder" />

    <TextView
        android:id="@+id/tv_author_name"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_757575"
        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
        app:layout_constraintEnd_toEndOf="@id/tv_name"
        app:layout_constraintStart_toEndOf="@+id/iv_author_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_author_avatar"
        tools:text="Leslie Alexander Alexander" />

    <Space
        android:id="@+id/spaceBottom"
        android:layout_width="@dimen/dp_1"
        android:layout_height="@dimen/dp_14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_author_avatar" />

    <Space
        android:id="@+id/spaceStart"
        android:layout_width="0dp"
        android:layout_height="1dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:id="@+id/spaceEnd"
        android:layout_width="0dp"
        android:layout_height="1dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>