<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >

    <!-- 封面图片 -->
    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/ivCover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_6"
        android:scaleType="centerCrop"
        app:cornerRadii="@dimen/dp_12"
        app:layout_constraintDimensionRatio="164:123"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@color/default_shadow_color" />

    <!-- Outstanding标识 -->
    <LinearLayout
        android:id="@+id/layoutOutstandingTag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="-5.5dp"
        android:background="@drawable/bg_home_template_child_item"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingEnd="@dimen/dp_4"
        android:visibility="visible"
        android:layout_marginStart="-2dp"
        app:layout_constraintStart_toStartOf="@id/ivCover"
        app:layout_constraintTop_toTopOf="@id/ivCover"
        tools:visibility="visible">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvOutstanding"
            style="@style/MetaTextView.S9.OutfitBlack900"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginStart="@dimen/dp_2"
            android:text="@string/game_creation"
            android:textColor="@color/white" />
    </LinearLayout>

    <!-- 点赞和播放量 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/ivCover"
        app:layout_constraintStart_toStartOf="@id/ivCover">

        <com.socialplay.gpark.ui.view.MetaLikeView
            android:id="@+id/likeView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:likeText="22.2k" />

        <com.socialplay.gpark.ui.view.MetaLikeView
            android:id="@+id/tvPlayCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="-4dp"
            app:likeText="22.2k Plays"
            app:likeIcon="@drawable/icon_item_player"/>
    </LinearLayout>

    <!-- 标题 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTitle"
        style="@style/MetaTextView.S13.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/Gray_1000"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivCover"
        tools:text="Enchilada Casserole Tutorial" />

    <!-- 作者信息 -->
    <LinearLayout
        android:id="@+id/layoutAuthor"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/sivAuthorAvatar"
            android:layout_width="@dimen/dp_18"
            android:layout_height="@dimen/dp_18"
            android:foreground="@drawable/avatar_inner_stroke_border"
            android:src="@color/color_8792B2"
            android:visibility="visible"
            app:shapeAppearance="@style/circleStyle" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvAuthorName"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/color_757575"
            tools:text="SEThomsont2" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 