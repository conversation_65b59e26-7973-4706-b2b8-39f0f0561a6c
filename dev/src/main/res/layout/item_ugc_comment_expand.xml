<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/startLine"
        android:layout_width="@dimen/dp_05"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_34"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@color/color_F0F0F0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvExpandBtn"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_57"
        android:layout_marginBottom="@dimen/dp_16"
        android:gravity="center_vertical"
        android:textColor="@color/color_4AB4FF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Expand More" />

    <Space
        android:id="@+id/spaceExpandBtn"
        android:layout_width="@dimen/dp_16"
        android:layout_height="@dimen/dp_1"
        app:layout_constraintStart_toEndOf="@id/tvExpandBtn"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvCollapseBtn"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_16"
        android:gravity="center_vertical"
        android:text="@string/collapse_cap"
        android:textColor="@color/color_4AB4FF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/spaceExpandBtn"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginStart="@dimen/dp_57" />

</androidx.constraintlayout.widget.ConstraintLayout>