<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/dp_60"
    android:layout_marginEnd="@dimen/dp_16"
    android:layout_marginBottom="@dimen/dp_16"
    android:minHeight="@dimen/dp_20">

    <FrameLayout
        android:id="@+id/fl_progress_container"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:layout_gravity="center_vertical">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lav_progress"
            android:layout_width="@dimen/dp_28"
            android:layout_height="@dimen/dp_28"
            android:layout_gravity="center"
            app:lottie_loop="true"
            app:lottie_fileName="circle_loading.zip" />

    </FrameLayout>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_loading"
        style="@style/MetaTextView.S12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp_24"
        android:gravity="center_vertical"
        android:text="@string/loading" />

</FrameLayout>