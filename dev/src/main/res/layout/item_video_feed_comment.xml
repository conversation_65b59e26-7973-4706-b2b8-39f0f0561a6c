<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_comment"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_16"
    android:paddingEnd="@dimen/dp_24"
   >

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/siv_user_avatar"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        android:scaleType="centerCrop"
        android:layout_marginTop="@dimen/dp_3"
        android:src="@drawable/icon_default_avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/circleStyle" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_username"
        app:uiLineHeight="@dimen/sp_18"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_6"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        tools:text="用户呢称显示用显示用"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:textColor="@color/neutral_color_5"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/hlv_honor"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/siv_user_avatar"
        app:layout_constraintTop_toTopOf="@id/siv_user_avatar" />

    <TextView
        android:id="@+id/hlv_honor"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="@id/tv_username"
        app:layout_constraintStart_toEndOf="@id/tv_username"
        app:layout_constraintTop_toTopOf="@id/tv_username" />

    <TextView
        android:id="@+id/tv_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:background="@drawable/bg_comment_user_label_self"
        android:maxLines="1"
        tools:text="Author"
        android:visibility="gone"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        tools:visibility="visible"
        android:paddingVertical="@dimen/dp_1"
        android:paddingHorizontal="@dimen/dp_6"
        android:textColor="@color/neutral_color_5"
        android:textSize="@dimen/sp_10"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/tv_username"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/hlv_honor"
        app:layout_constraintTop_toTopOf="@id/tv_username" />


    <ImageView
        android:id="@+id/iv_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_review_more"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.socialplay.gpark.ui.view.ExpandableTextView
        android:id="@+id/tv_content"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:paddingBottom="@dimen/dp_4"
        android:textColor="@color/neutral_color_2"
        android:textSize="@dimen/sp_14"
        app:etv_InitState="shrink"
        app:etv_EnableToggleClick="true"
        app:etv_MaxLinesOnShrink="4"
        app:etv_ToExpandHintOffset="@dimen/dp_100"
        app:etv_ToExpandHint="@string/more_with_space"
        app:etv_ToShrinkHint="@string/collapse_with_space"
        app:etv_ToExpandHintColor="@color/black_40"
        app:etv_ToExpandHintColorBgPressed="@color/transparent"
        app:etv_ToExpandHintShow="true"
        app:etv_ToShrinkHintColor="@color/black_40"
        app:etv_ToShrinkHintColorBgPressed="@color/transparent"
        app:etv_ToShrinkHintShow="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="@id/tv_username"
        app:layout_constraintTop_toBottomOf="@id/tv_username"
        tools:maxLength="500"
        tools:text="平台为咱们的会员用户推出了云存档服务平台为咱们的会员用户推出了云存档服务平台为咱们的会员用户推出了云存档服务平台为咱们的会员用户推出了云存档服务平台为咱们的会员用户推出了云存档服务" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:layout_marginTop="@dimen/dp_8"
        android:textColor="@color/neutral_color_5"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_username"
        app:layout_constraintTop_toBottomOf="@id/tv_content"
        app:layout_goneMarginStart="0dp"
        tools:text="10分钟" />


    <ImageView
        android:id="@+id/iv_like"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_ugc_comment_like_selected"
        android:paddingRight="@dimen/dp_2"
        app:layout_constraintBottom_toBottomOf="@id/tv_like_count"
        app:layout_constraintEnd_toStartOf="@id/tv_like_count"
        app:layout_constraintTop_toTopOf="@id/tv_like_count"/>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_like_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:includeFontPadding="false"
        android:text="@string/zero_int"
        android:gravity="center"
        android:textColor="@color/neutral_color_5"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/tv_time"
        app:layout_constraintTop_toTopOf="@id/tv_time"
        app:layout_goneMarginStart="0dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/ry_view"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_65"
        android:layout_marginEnd="16dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_username"
        app:layout_constraintTop_toBottomOf="@id/tv_content" />


    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lav_like_count"
        android:layout_width="@dimen/dp_57"
        android:layout_height="@dimen/dp_57"
        android:translationX="38.4dp"
        android:translationY="37.5dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/iv_like"
        app:layout_constraintEnd_toStartOf="@id/iv_like"
        app:lottie_autoPlay="false"
        app:lottie_loop="false"
        app:lottie_fileName="anim_video_feed_like.zip"
        tools:lottie_progress="0.4"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>