<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_54">

    <View
        android:id="@+id/vDivider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_05"
        android:background="@color/color_EEEEEE"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvReplyHint"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_38"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_8"
        android:background="@drawable/shape_f5f5f5_corner_360"
        android:drawableStart="@drawable/icon_game_detail_comments"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_9"
        android:text="@string/post_reply"
        android:textColor="@color/color_BDBDBD"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ivLike"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivImageBtn"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:paddingStart="@dimen/dp_6"
        android:paddingEnd="@dimen/dp_6"
        android:src="@drawable/community_icon_photo_sel"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint"
        app:layout_constraintEnd_toStartOf="@id/ivEmojiBtn"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivEmojiBtn"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/dp_10"
        android:paddingStart="@dimen/dp_6"
        android:paddingEnd="@dimen/dp_6"
        android:src="@drawable/community_comment_icon_emoji"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint"
        app:layout_constraintEnd_toEndOf="@id/tvReplyHint"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivLike"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_20"
        android:src="@drawable/ic_heart_like_size_24"
        app:layout_constraintBottom_toTopOf="@id/tvLike"
        app:layout_constraintStart_toEndOf="@id/tvReplyHint"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvLike"
        style="@style/MetaTextView.S10.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_10"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint"
        app:layout_constraintEnd_toEndOf="@id/ivLike"
        app:layout_constraintStart_toStartOf="@id/ivLike"
        app:layout_constraintTop_toBottomOf="@id/ivLike"
        tools:text="24" />

    <View
        android:id="@+id/layerLike"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="-10dp"
        android:layout_marginEnd="-10dp"
        app:constraint_referenced_ids="ivLike, tvLike"
        app:layout_constraintBottom_toBottomOf="@id/tvLike"
        app:layout_constraintEnd_toEndOf="@id/ivLike"
        app:layout_constraintStart_toStartOf="@id/ivLike"
        app:layout_constraintTop_toTopOf="@id/ivLike"
        tools:visibility="gone" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lavLikeAnim"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivLike"
        app:layout_constraintEnd_toEndOf="@id/ivLike"
        app:layout_constraintStart_toStartOf="@id/ivLike"
        app:layout_constraintTop_toTopOf="@id/ivLike"
        app:lottie_autoPlay="false"
        app:lottie_fileName="community_like.zip"
        app:lottie_progress="1"
        tools:visibility="visible"/>

    <ImageView
        android:id="@+id/ivLightUp"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_20"
        android:src="@drawable/ic_light_up"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/tvLightUp"
        app:layout_constraintStart_toEndOf="@id/ivLike"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint"
        app:layout_constraintVertical_chainStyle="packed"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvLightUp"
        style="@style/MetaTextView.S10.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_10"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint"
        app:layout_constraintEnd_toEndOf="@id/ivLightUp"
        app:layout_constraintStart_toStartOf="@id/ivLightUp"
        app:layout_constraintTop_toBottomOf="@id/ivLightUp"
        tools:text="24"
        tools:visibility="visible" />

    <View
        android:id="@+id/layerLightUp"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="-10dp"
        android:layout_marginEnd="-10dp"
        android:visibility="gone"
        app:constraint_referenced_ids="ivLightUp, tvLightUp"
        app:layout_constraintBottom_toBottomOf="@id/tvLightUp"
        app:layout_constraintEnd_toEndOf="@id/ivLightUp"
        app:layout_constraintStart_toStartOf="@id/ivLightUp"
        app:layout_constraintTop_toTopOf="@id/ivLightUp"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/ivComment"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_20"
        android:src="@drawable/ic_game_detail_common_comment"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@id/tvComment"
        app:layout_constraintStart_toEndOf="@id/ivLightUp"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint"
        app:layout_constraintVertical_chainStyle="packed"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvComment"
        style="@style/MetaTextView.S10.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint"
        app:layout_constraintEnd_toEndOf="@id/ivComment"
        app:layout_constraintStart_toStartOf="@id/ivComment"
        app:layout_constraintTop_toBottomOf="@id/ivComment"
        tools:text="24"
        tools:visibility="visible" />

    <View
        android:id="@+id/layerComment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="-10dp"
        android:layout_marginEnd="-10dp"
        android:visibility="visible"
        app:constraint_referenced_ids="ivComment, tvComment"
        app:layout_constraintBottom_toBottomOf="@id/tvComment"
        app:layout_constraintEnd_toEndOf="@id/ivComment"
        app:layout_constraintStart_toStartOf="@id/ivComment"
        app:layout_constraintTop_toTopOf="@id/ivComment"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivSendFlowers"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_20"
        android:src="@drawable/ic_game_detail_common_send_flower"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/tvSendFlowers"
        app:layout_constraintEnd_toStartOf="@id/spaceEnd"
        app:layout_constraintStart_toEndOf="@id/ivComment"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint"
        app:layout_constraintVertical_chainStyle="packed"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSendFlowers"
        style="@style/MetaTextView.S10.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint"
        app:layout_constraintEnd_toEndOf="@id/ivSendFlowers"
        app:layout_constraintStart_toStartOf="@id/ivSendFlowers"
        app:layout_constraintTop_toBottomOf="@id/ivSendFlowers"
        tools:text="24"
        tools:visibility="visible" />

    <View
        android:id="@+id/layerSendFlowers"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="-10dp"
        android:layout_marginEnd="-10dp"
        android:visibility="gone"
        app:constraint_referenced_ids="ivSendFlowers, tvSendFlowers"
        app:layout_constraintBottom_toBottomOf="@id/tvSendFlowers"
        app:layout_constraintEnd_toEndOf="@id/ivSendFlowers"
        app:layout_constraintStart_toStartOf="@id/ivSendFlowers"
        app:layout_constraintTop_toTopOf="@id/ivSendFlowers"
        tools:visibility="visible" />

    <Space
        android:id="@+id/spaceEnd"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint" />

</androidx.constraintlayout.widget.ConstraintLayout>