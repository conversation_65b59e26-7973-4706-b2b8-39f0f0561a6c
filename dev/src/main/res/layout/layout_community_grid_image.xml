<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <Space
        android:id="@+id/spaceStart"
        android:layout_height="@dimen/dp_0"
        android:layout_width="@dimen/dp_72"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <include
        android:id="@+id/icImage1"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        layout="@layout/item_community_grid_images"
        android:layout_marginTop="@dimen/dp_6"
        app:layout_constraintStart_toEndOf="@id/spaceStart"
        app:layout_constraintTop_toBottomOf="@id/spaceStart"
        />

    <include
        android:id="@+id/icImage2"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        layout="@layout/item_community_grid_images"
        app:layout_constraintStart_toEndOf="@id/icImage1"
        app:layout_constraintTop_toTopOf="@id/icImage1"
        android:layout_marginStart="@dimen/dp_6"
        />

    <include
        android:id="@+id/icImage3"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        layout="@layout/item_community_grid_images"
        app:layout_constraintStart_toEndOf="@id/icImage2"
        app:layout_constraintTop_toTopOf="@id/icImage1"
        android:layout_marginStart="@dimen/dp_6"
        />

    <include
        android:id="@+id/icImage4"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        layout="@layout/item_community_grid_images"
        app:layout_constraintTop_toBottomOf="@id/icImage1"
        app:layout_constraintStart_toEndOf="@id/spaceStart"
        android:layout_marginTop="@dimen/dp_6"
        />

    <include
        android:id="@+id/icImage5"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        layout="@layout/item_community_grid_images"
        app:layout_constraintTop_toTopOf="@id/icImage4"
        app:layout_constraintStart_toEndOf="@id/icImage4"
        android:layout_marginStart="@dimen/dp_6"
        />

    <include
        android:id="@+id/icImage6"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        layout="@layout/item_community_grid_images"
        app:layout_constraintTop_toTopOf="@id/icImage4"
        app:layout_constraintStart_toEndOf="@id/icImage5"
        android:layout_marginStart="@dimen/dp_6"
        />

    <include
        android:id="@+id/icImage7"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        layout="@layout/item_community_grid_images"
        app:layout_constraintTop_toBottomOf="@id/icImage4"
        app:layout_constraintStart_toEndOf="@id/spaceStart"
        android:layout_marginTop="@dimen/dp_6"
        />
    <include
        android:id="@+id/icImage8"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        layout="@layout/item_community_grid_images"
        app:layout_constraintTop_toTopOf="@id/icImage7"
        app:layout_constraintStart_toEndOf="@id/icImage7"
        android:layout_marginStart="@dimen/dp_6"
        />

    <include
        android:id="@+id/icImage9"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        layout="@layout/item_community_grid_images"
        app:layout_constraintTop_toTopOf="@id/icImage7"
        app:layout_constraintStart_toEndOf="@id/icImage8"
        android:layout_marginStart="@dimen/dp_6"
        />
</androidx.constraintlayout.widget.ConstraintLayout>