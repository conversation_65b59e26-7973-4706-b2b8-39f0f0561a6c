<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ProgressBar
        android:id="@+id/loadingIndicator"
        android:layout_width="47dp"
        android:layout_height="47dp"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:indeterminateTint="@color/white"
        android:visibility="visible" />

    <ImageView
        android:id="@+id/iv_volume"
        android:layout_width="@dimen/dp_42"
        android:layout_height="@dimen/dp_42"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:padding="@dimen/dp_8"
        android:src="@drawable/selector_volume" />

    <ImageView
        android:id="@+id/tvReplay"
        android:layout_width="@dimen/dp_47"
        android:layout_height="@dimen/dp_47"
        android:layout_centerInParent="true"
        android:src="@drawable/icon_video_play"
        android:visibility="gone"
        tools:visibility="visible" />
</RelativeLayout>
