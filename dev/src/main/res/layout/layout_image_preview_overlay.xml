<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/previewOverlay"
    android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/ib_back"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/title_bar_height"
        android:paddingHorizontal="@dimen/dp_16"
        android:src="@drawable/ic_back_arrow_v2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/white" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bottomBar"
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        >
        <com.zhpan.indicator.IndicatorView
            android:id="@+id/indicatorImg"
            android:layout_width="wrap_content"
            app:layout_constrainedWidth="true"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_50"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:vpi_orientation="horizontal"
            app:vpi_slide_mode="normal"
            app:vpi_slider_checked_color="@color/white"
            app:vpi_slider_normal_color="@color/neutral_color_4"
            app:vpi_style="circle" />

        <TextView
            android:id="@+id/tvImgAction"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_70"
            android:background="@drawable/bg_img_dialog_action"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_24"
            android:text="@string/image_dialog_change_avatar"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            tools:text="aaaaaaaaaaaaaaaaa"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/progressIndicator"
        android:layout_height="@dimen/dp_40"
        android:layout_width="@dimen/dp_40"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />
</androidx.constraintlayout.widget.ConstraintLayout>