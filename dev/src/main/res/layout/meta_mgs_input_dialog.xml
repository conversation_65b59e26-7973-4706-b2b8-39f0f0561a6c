<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_f5f5f5"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingVertical="@dimen/dp_5"
    android:paddingEnd="@dimen/dp_5">

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_mgs_message"
        style="@style/EditText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_50"
        android:layout_marginVertical="@dimen/dp_3"
        android:background="@drawable/bg_input_et_white"
        android:hint="@string/meta_mgs_send_message"
        android:imeOptions="actionSend|flagNoExtractUi"
        android:maxHeight="@dimen/dp_100"
        android:maxLength="145"
        android:paddingStart="@dimen/dp_6"
        android:paddingVertical="@dimen/dp_5"
        android:singleLine="true"
        android:textColorHint="@color/color_999999"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn_send_message"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <Button
        android:id="@+id/btn_send_message"
        style="@style/Button.S14.PoppinsBlack900"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:minHeight="@dimen/dp_28"
        android:minWidth="@dimen/dp_58"
        android:layout_marginStart="@dimen/dp_6"
        android:gravity="center_vertical"
        android:text="@string/submit"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn_cancel"
        app:layout_constraintStart_toEndOf="@id/et_mgs_message"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btn_cancel"
        style="@style/Button.S14.PoppinsRegular400.CancelPrimary"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:minHeight="@dimen/dp_28"
        android:minWidth="@dimen/dp_58"
        android:layout_marginStart="@dimen/dp_6"
        android:gravity="center_vertical"
        android:text="@string/cancel"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_send_message"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:background="@color/black_60"
        android:visibility="gone"
        app:loadingProgressSize="@dimen/dp_60"
        tools:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>