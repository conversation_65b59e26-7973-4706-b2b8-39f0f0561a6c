<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dp_20">

    <com.youth.banner.Banner
        android:id="@+id/banner"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:banner_auto_loop="true"
        app:banner_infinite_loop="true"
        app:banner_orientation="horizontal"
        app:layout_constraintDimensionRatio="343:142"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@color/black_50" />

    <com.zhpan.indicator.IndicatorView
        android:id="@+id/indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/banner"
        app:vpi_orientation="horizontal"
        app:vpi_slide_mode="smooth"
        app:vpi_slider_checked_color="@color/color_F49D0C"
        app:vpi_slider_normal_color="@color/color_FDE58A"
        app:vpi_style="round_rect" />

</androidx.constraintlayout.widget.ConstraintLayout>
