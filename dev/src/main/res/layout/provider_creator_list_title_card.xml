<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dp_16">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTitle"
        style="@style/MetaTextView.S16.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_toStartOf="@id/tvMore"
        android:paddingVertical="4.5dp"
        tools:text="@string/kol_hot" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvMore"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/tvTitle"
        android:layout_alignBottom="@id/tvTitle"
        android:layout_alignParentEnd="true"
        android:drawableEnd="@drawable/ic_right_arrow_666666_18"
        android:drawablePadding="@dimen/dp_2"
        android:gravity="center"
        android:paddingVertical="@dimen/dp_4"
        android:paddingStart="@dimen/dp_8"
        android:paddingEnd="@dimen/dp_16"
        android:text="@string/see_all"
        android:textColor="@color/textColorPrimaryLight" />

    <com.socialplay.gpark.ui.view.WrapRecyclerView
        android:id="@+id/rvHor"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tvTitle"
        android:layout_marginTop="@dimen/dp_8"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

</RelativeLayout>