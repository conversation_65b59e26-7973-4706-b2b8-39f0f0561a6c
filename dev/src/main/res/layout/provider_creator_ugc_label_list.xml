<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dp_20"
    android:paddingBottom="@dimen/dp_12">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTitle"
        style="@style/MetaTextView.S16.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/kol_hot" />

    <com.socialplay.gpark.ui.view.RecyclerTabLayout
        android:id="@+id/tabLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/ivMore"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle" />

    <View
        android:id="@+id/vMore"
        android:layout_width="@dimen/dp_44"
        android:layout_height="@dimen/dp_0"
        android:background="@drawable/bg_ai_bot_line_bg"
        app:layout_constraintBottom_toBottomOf="@id/ivMore"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/ivMore" />

    <ImageView
        android:id="@+id/ivMore"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingStart="@dimen/dp_4"
        android:paddingEnd="@dimen/dp_16"
        android:src="@drawable/icon_ai_bot_tag_more"
        app:layout_constraintBottom_toBottomOf="@id/tabLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tabLayout" />

</androidx.constraintlayout.widget.ConstraintLayout>