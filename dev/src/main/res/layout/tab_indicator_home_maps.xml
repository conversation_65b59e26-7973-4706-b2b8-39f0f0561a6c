<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_44">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvNormal"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_44"
        android:layout_gravity="center"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:minWidth="@dimen/dp_38"
        android:singleLine="true"
        android:textColor="@color/textColorPrimary"
        tools:text="Add Friend" />

    <ImageView
        android:id="@+id/ivNormal"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_21"
        android:layout_gravity="center"
        android:adjustViewBounds="true"
        android:maxWidth="@dimen/dp_104"
        android:minWidth="@dimen/dp_38"
        android:scaleType="fitCenter" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSelected"
        style="@style/MetaTextView.S16.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_44"
        android:layout_gravity="center"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:minWidth="@dimen/dp_38"
        android:singleLine="true"
        android:textColor="@color/textColorPrimary"
        android:visibility="invisible"
        tools:text="Add Friend"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivSelected"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_24"
        android:layout_gravity="center"
        android:adjustViewBounds="true"
        android:maxWidth="@dimen/dp_104"
        android:minWidth="@dimen/dp_38"
        android:scaleType="fitCenter" />

</FrameLayout>




