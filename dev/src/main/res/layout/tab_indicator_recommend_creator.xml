<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_26">

    <Space
        android:layout_width="@dimen/dp_1"
        android:layout_height="@dimen/dp_26" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvNormal"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:minHeight="@dimen/dp_21"
        android:textColor="@color/color_666666"
        app:uiLineHeight="@dimen/dp_21"
        tools:text="All" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSelected"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:minHeight="@dimen/dp_21"
        android:textColor="@color/color_1A1A1A"
        android:visibility="invisible"
        app:uiLineHeight="@dimen/dp_21"
        tools:text="All" />

</FrameLayout>

