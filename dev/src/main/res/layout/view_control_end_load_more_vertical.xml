<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/load_more_loading_view"
        android:layout_width="@dimen/dp_64"
        android:layout_height="match_parent">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/ivProgress"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_gravity="center"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_fileName="lottie_loading.zip"
            tools:lottie_progress="0.3" />

    </FrameLayout>

    <FrameLayout
        android:id="@+id/load_more_load_fail_view"
        android:layout_width="@dimen/dp_64"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_gravity="center"
            android:src="@drawable/icon_net_error" />

    </FrameLayout>

    <FrameLayout
        android:id="@+id/load_more_load_complete_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_gravity="center"
            android:src="@drawable/icon_black_arrow_right" />

    </FrameLayout>

    <FrameLayout
        android:id="@+id/load_more_load_end_view"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:visibility="gone" />

</FrameLayout>