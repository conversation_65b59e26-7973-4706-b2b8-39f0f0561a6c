<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/tab_layout_height">

    <!-- Lottie动画视图 -->
    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lav_icon"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="@dimen/dp_4"
        app:layout_constraintRight_toRightOf="parent"
        app:lottie_autoPlay="false"
        app:lottie_loop="false"
        app:lottie_speed="1.2"
        app:layout_constraintTop_toTopOf="parent"
        tools:lottie_fileName="main_bottom_navigation_map.zip" />

    <!-- 红点通知 -->
    <View
        android:id="@+id/viewUnReadCount"
        android:layout_width="@dimen/dp_10"
        android:layout_height="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_3"
        android:layout_marginEnd="@dimen/dp_2"
        android:background="@drawable/sp_red_dot"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="@+id/lav_icon"
        app:layout_constraintTop_toTopOf="@+id/lav_icon"
        tools:visibility="visible" />


    <!-- 标题文本 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/Gray_900"
        app:layout_constraintLeft_toLeftOf="@+id/lav_icon"
        app:layout_constraintRight_toRightOf="@+id/lav_icon"
        app:layout_constraintTop_toBottomOf="@+id/lav_icon"
        tools:text="首页" />

</androidx.constraintlayout.widget.ConstraintLayout> 