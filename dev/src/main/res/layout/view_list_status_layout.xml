<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


<!--    <androidx.constraintlayout.widget.ConstraintLayout-->
<!--        android:id="@+id/v_empty_layout"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        android:visibility="visible">-->

<!--        <ImageView-->
<!--            android:id="@+id/iv_empty_tip_img"-->
<!--            android:layout_width="@dimen/dp_150"-->
<!--            android:layout_height="@dimen/dp_150"-->
<!--            android:src="@drawable/icon_no_messages"-->
<!--            app:layout_constraintBottom_toTopOf="@+id/tv_empty_tip_text"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintVertical_chainStyle="packed" />-->

<!--        <com.socialplay.gpark.ui.view.MetaTextView-->
<!--            android:id="@+id/tv_empty_tip_text"-->
<!--            style="@style/MetaTextView.S15.PoppinsRegular400.CenterVertical.Secondary"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginBottom="@dimen/dp_80"-->
<!--            android:text="@string/no_messages"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintHorizontal_bias="0.5"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintTop_toBottomOf="@+id/iv_empty_tip_img" />-->

<!--    </androidx.constraintlayout.widget.ConstraintLayout>-->

<!--    <androidx.constraintlayout.widget.ConstraintLayout-->
<!--        android:id="@+id/v_error_layout"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        android:background="@color/white"-->
<!--        android:clickable="true"-->
<!--        android:focusable="true"-->
<!--        android:visibility="gone">-->

<!--        <ImageView-->
<!--            android:id="@+id/iv_error_tip_img"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:src="@drawable/icon_no_friends_tip"-->
<!--            app:layout_constraintBottom_toTopOf="@+id/tv_error_tip_text"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintVertical_chainStyle="packed" />-->

<!--        <com.socialplay.gpark.ui.view.MetaTextView-->
<!--            android:id="@+id/tv_error_tip_text"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="@dimen/dp_12"-->
<!--            android:layout_marginBottom="@dimen/dp_80"-->
<!--            android:text="@string/loading_failed_click_to_retry"-->
<!--            android:textColor="@color/color_666666"-->
<!--            android:textSize="@dimen/sp_13"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintHorizontal_bias="0.5"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintTop_toBottomOf="@+id/iv_error_tip_img" />-->

<!--    </androidx.constraintlayout.widget.ConstraintLayout>-->

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loadingView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</FrameLayout>