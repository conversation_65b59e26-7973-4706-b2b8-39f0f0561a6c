<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:clickable="true"
    tools:layout_height="match_parent"
    tools:layout_width="match_parent"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">


    <LinearLayout
        android:id="@+id/ll_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_80"
        android:maxWidth="@dimen/dp_282"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottie_placeholder"
            android:layout_width="@dimen/dp_200"
            android:layout_height="@dimen/dp_200"
            android:layout_gravity="center_horizontal"
            app:lottie_autoPlay="true"
            app:lottie_fileName="lottie_empty.zip"
            app:lottie_loop="true" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_tips"
            style="@style/MetaTextView.S15.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_24"
            android:gravity="center"
            android:textColor="@color/Gray_900"
            tools:text="Tech enthusiast passionate about AI  innovation. Skilled in coding " />

        <Button
            android:id="@+id/btn_loading"
            style="@style/Button.S16.PoppinsBold600"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginHorizontal="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_16"
            android:maxWidth="@dimen/dp_240"
            android:textColor="@color/Gray_1000"
            android:visibility="invisible"
            tools:text="Retry"
            tools:visibility="visible" />

        <Button
            android:id="@+id/btn_empty"
            style="@style/Button.S16.PoppinsBold600"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginHorizontal="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_16"
            android:maxWidth="@dimen/dp_240"
            android:textColor="@color/Gray_1000"
            android:visibility="gone"
            tools:text="go"
            tools:visibility="visible" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_loading"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_80"
        android:layout_marginBottom="@dimen/dp_80"
        android:background="@drawable/bg_corner_8_black_70"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottie_loading"
            android:layout_width="@dimen/dp_32"
            android:layout_height="@dimen/dp_32"
            android:layout_gravity="center"
            app:lottie_autoPlay="true"
            app:lottie_fileName="lottie_loading.zip"
            app:lottie_loop="true" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_loading"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_2"
            android:gravity="center"
            android:text="@string/loading"
            android:textColor="@color/white" />
    </LinearLayout>

</merge>