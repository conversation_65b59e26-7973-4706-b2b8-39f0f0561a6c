<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    tools:parentTag="android.widget.LinearLayout">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvAuthorLabel"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:background="@drawable/shape_fef3c7_corner_19"
        android:gravity="center_vertical"
        android:minHeight="@dimen/dp_15"
        android:paddingHorizontal="@dimen/dp_6"
        android:paddingVertical="@dimen/dp_1"
        android:text="@string/comment_label_author"
        android:textColor="@color/color_D87607"
        android:visibility="gone"
        tools:visibility="visible"/>

    <ImageView
        android:id="@+id/iv_label_official"
        android:layout_width="@dimen/dp_14"
        android:layout_height="@dimen/dp_14"
        android:layout_marginStart="@dimen/dp_4"
        android:src="@drawable/official_certification"
        android:visibility="visible" />

    <ImageView
        android:id="@+id/iv_label_net"
        android:layout_width="@dimen/dp_14"
        android:layout_height="@dimen/dp_14"
        android:layout_marginStart="@dimen/dp_2"
        android:visibility="gone"
        tools:src="@drawable/ic_label_kol_default"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_label_creator"
        android:layout_width="@dimen/dp_14"
        android:layout_height="@dimen/dp_14"
        android:layout_marginStart="@dimen/dp_2"
        android:src="@drawable/ic_label_creator"
        android:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_label_me"
        style="@style/MetaTextView.S10"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:background="@drawable/shape_f5f5f5_corner_360"
        android:gravity="center_vertical"
        android:minHeight="@dimen/dp_15"
        android:paddingHorizontal="@dimen/dp_6"
        android:paddingVertical="@dimen/dp_1"
        android:text="@string/comment_label_self"
        android:textColor="@color/color_757575"
        android:textSize="@dimen/dp_10"
        android:visibility="gone"
        app:uiLineHeight="@dimen/dp_15" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_label_under_review"
        style="@style/MetaTextView.S10"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:background="@drawable/shape_f5f5f5_corner_360"
        android:gravity="center_vertical"
        android:minHeight="@dimen/dp_15"
        android:paddingHorizontal="@dimen/dp_6"
        android:paddingVertical="@dimen/dp_1"
        android:text="@string/under_review"
        android:textColor="@color/color_757575"
        android:textSize="@dimen/dp_10"
        android:visibility="gone"
        app:uiLineHeight="@dimen/dp_15" />

</merge>