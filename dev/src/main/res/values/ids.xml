<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item name="banner_data_key" type="id"/>
    <item name="banner_pos_key" type="id"/>
    <item name="devShowEvent" type="id" />
    <item name="meta_epoxy_model_text_view_text_watcher" type="id" />
    <item name="meta_epoxy_model_viewbinding_tag" type="id" />
    <item name="tag_binding_data" type="id" />
    <item name="tag_animation_listener" type="id" />
    <item name="tag_dark_mode" type="id" />
    <item name="rv_item_show_listener" type="id" />
    <item name="meta_epoxy_model_glide_request_manager" type="id" />
    <item name="meta_epoxy_model_parent_view" type="id" />
    <item name="tag_item_visibility_listener" type="id" />
    <item name="tag_shown_feedback_tip" type="id" />
    <item name="v_configuration_detect_holder" type="id" />
    <item name="tag_configuration_detector_callback" type="id" />
    <item name="tag_binding" type="id" />
    <item name="left_overlay" type="id" />
    <item name="right_overlay" type="id" />
    <item name="top_overlay" type="id" />
    <item name="bottom_overlay" type="id" />
    <item name="base_adapter_pre_load_tag" type="id" />
    <item name="base_adapter_pre_load_tag_bind" type="id" />
    <item name="enable_all_with_alpha" type="id" />
    <item name="set_all_alpha" type="id" />
    <item name="view_lifecycle_owner" type="id" />
</resources>