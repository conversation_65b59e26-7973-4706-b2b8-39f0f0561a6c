<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="MetaTextView">
        <item name="android:textSize">@dimen/textSizeSmall</item>
        <item name="android:fontFamily">@font/poppins_regular_400</item>
        <item name="android:includeFontPadding">false</item>
        <item name="uiLineHeight">@dimen/dp_20</item>
        <item name="android:minHeight">@dimen/dp_20</item>
        <item name="android:textColor">@color/colorPrimaryDark</item>
    </style>

    <!--字号-->
    <style name="MetaTextView.S8">
        <item name="android:textSize">@dimen/sp_8</item>
        <item name="uiLineHeight">@dimen/sp_12</item>
        <item name="android:minHeight">@dimen/sp_12</item>
    </style>

    <style name="MetaTextView.S8.PoppinsRegular400">
        <item name="android:fontFamily">@font/poppins_regular_400</item>
    </style>

    <style name="MetaTextView.S8.PoppinsBlack900">
        <item name="android:textSize">@dimen/sp_8</item>
        <item name="uiLineHeight">@dimen/sp_12</item>
        <item name="android:minHeight">@dimen/sp_12</item>
        <item name="android:fontFamily">@font/poppins_black_900</item>
    </style>
    <style name="MetaTextView.S9">
        <item name="android:textSize">@dimen/sp_9</item>
        <item name="uiLineHeight">@dimen/sp_14</item>
        <item name="android:minHeight">@dimen/sp_14</item>
    </style>

    <style name="MetaTextView.S9.OutfitBlack900">
        <item name="android:fontFamily">@font/outfit_black_900</item>
    </style>

    <style name="MetaTextView.S10">
        <item name="android:textSize">@dimen/sp_10</item>
        <item name="uiLineHeight">@dimen/sp_15</item>
        <item name="android:minHeight">@dimen/sp_15</item>
    </style>

    <style name="MetaTextView.S11">
        <item name="android:textSize">@dimen/sp_11</item>
        <item name="uiLineHeight">@dimen/sp_16</item>
        <item name="android:minHeight">@dimen/sp_16</item>
    </style>

    <style name="MetaTextView.S12">
        <item name="android:textSize">@dimen/textSizeSmaller</item>
        <item name="uiLineHeight">@dimen/sp_18</item>
        <item name="android:minHeight">@dimen/sp_18</item>
    </style>

    <style name="MetaTextView.S12_TBMC">
        <item name="android:fontFamily">@font/outfit_tbmc_black_500</item>
        <item name="android:textSize">@dimen/sp_9</item>
    </style>

    <style name="MetaTextView.S12.OutfitBlack900">
        <item name="android:fontFamily">@font/outfit_black_900</item>
    </style>

    <style name="MetaTextView.S13">
        <item name="android:textSize">@dimen/textSize13</item>
        <item name="uiLineHeight">@dimen/sp_20</item>
        <item name="android:minHeight">@dimen/sp_20</item>
    </style>

    <style name="MetaTextView.S14">
        <item name="android:textSize">@dimen/textSizeSmall</item>
        <item name="uiLineHeight">@dimen/sp_21</item>
        <item name="android:minHeight">@dimen/sp_21</item>
    </style>

    <style name="MetaTextView.S15">
        <item name="android:textSize">@dimen/textSizeNormal</item>
        <item name="uiLineHeight">@dimen/sp_23</item>
        <item name="android:minHeight">@dimen/sp_23</item>
    </style>

    <style name="MetaTextView.S16">
        <item name="android:textSize">@dimen/textSize16</item>
        <item name="uiLineHeight">@dimen/sp_24</item>
        <item name="android:minHeight">@dimen/sp_24</item>
    </style>

    <style name="MetaTextView.S18">
        <item name="android:textSize">@dimen/textSizeBig</item>
        <item name="uiLineHeight">@dimen/sp_27</item>
        <item name="android:minHeight">@dimen/sp_27</item>
    </style>

    <style name="MetaTextView.S19">
        <item name="android:textSize">@dimen/textSize19</item>
        <item name="uiLineHeight">@dimen/sp_22</item>
        <item name="android:minHeight">@dimen/sp_22</item>
    </style>

    <style name="MetaTextView.S20">
        <item name="android:textSize">@dimen/sp_20</item>
        <item name="uiLineHeight">@dimen/sp_30</item>
        <item name="android:minHeight">@dimen/sp_30</item>
    </style>

    <style name="MetaTextView.S22">
        <item name="android:textSize">@dimen/sp_22</item>
        <item name="uiLineHeight">@dimen/sp_33</item>
        <item name="android:minHeight">@dimen/sp_33</item>
    </style>

    <style name="MetaTextView.S24">
        <item name="android:textSize">@dimen/sp_24</item>
        <item name="uiLineHeight">@dimen/sp_36</item>
        <item name="android:minHeight">@dimen/sp_36</item>
    </style>

    <style name="MetaTextView.S25">
        <item name="android:textSize">@dimen/sp_25</item>
        <item name="uiLineHeight">@dimen/sp_38</item>
        <item name="android:minHeight">@dimen/sp_38</item>
    </style>

    <style name="MetaTextView.S26">
        <item name="android:textSize">@dimen/sp_26</item>
        <item name="uiLineHeight">@dimen/sp_39</item>
        <item name="android:minHeight">@dimen/sp_39</item>
    </style>

    <style name="MetaTextView.S28">
        <item name="android:textSize">@dimen/sp_28</item>
        <item name="uiLineHeight">@dimen/sp_42</item>
        <item name="android:minHeight">@dimen/sp_42</item>
    </style>

    <style name="MetaTextView.S30">
        <item name="android:textSize">@dimen/sp_30</item>
        <item name="uiLineHeight">@dimen/sp_45</item>
        <item name="android:minHeight">@dimen/sp_45</item>
    </style>

    <style name="MetaTextView.S32">
        <item name="android:textSize">@dimen/sp_32</item>
        <item name="uiLineHeight">@dimen/sp_48</item>
        <item name="android:minHeight">@dimen/sp_48</item>
    </style>

    <!--字体字重-->
    <style name="MetaTextView.S9.PoppinsRegular400">
        <item name="android:fontFamily">@font/poppins_regular_400</item>
    </style>

    <style name="MetaTextView.S9.PoppinsMedium500">
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>

    <style name="MetaTextView.S10.PoppinsRegular400">
        <item name="android:fontFamily">@font/poppins_regular_400</item>
    </style>

    <style name="MetaTextView.S10.PoppinsSemiBold600">
        <item name="android:fontFamily">@font/poppins_semi_bold_600</item>
    </style>

    <style name="MetaTextView.S10.PoppinsMedium500">
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>

    <style name="MetaTextView.S10.PoppinsExtraBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="MetaTextView.S10.PoppinsBlack900">
        <item name="android:fontFamily">@font/poppins_black_900</item>
    </style>

    <style name="MetaTextView.S10.OutfitBlack900">
        <item name="android:fontFamily">@font/outfit_black_900</item>
    </style>

    <style name="MetaTextView.S11.PoppinsRegular400">
        <item name="android:fontFamily">@font/poppins_regular_400</item>
    </style>

    <style name="MetaTextView.S11.PoppinsMedium500">
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>

    <style name="MetaTextView.S11.PoppinsExtraBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="MetaTextView.S12.PoppinsLight300">
        <item name="android:fontFamily">@font/poppins_light_300</item>
    </style>

    <style name="MetaTextView.S12.PoppinsRegular400">
        <item name="android:fontFamily">@font/poppins_regular_400</item>
    </style>

    <style name="MetaTextView.S12.PoppinsMedium500">
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>

    <style name="MetaTextView.S12.PoppinsMedium500.Secondary">
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/textColorSecondary</item>
    </style>

    <style name="MetaTextView.S12.PoppinsSemiBold600">
        <item name="android:fontFamily">@font/poppins_semi_bold_600</item>
    </style>

    <style name="MetaTextView.S12.PoppinsBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="MetaTextView.S12.PoppinsExtraBold800">
        <item name="android:fontFamily">@font/poppins_extra_bold_800</item>
    </style>

    <style name="MetaTextView.S12.PoppinsBlack900">
        <item name="android:fontFamily">@font/poppins_black_900</item>
    </style>

    <style name="MetaTextView.S13.PoppinsRegular400">
        <item name="android:fontFamily">@font/poppins_regular_400</item>
    </style>

    <style name="MetaTextView.S13.PoppinsMedium500">
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>

    <style name="MetaTextView.S13.PoppinsSemiBold600">
        <item name="android:fontFamily">@font/poppins_semi_bold_600</item>
    </style>

    <style name="MetaTextView.S14.PoppinsRegular400">
        <item name="android:fontFamily">@font/poppins_regular_400</item>
    </style>

    <style name="MetaTextView.S14.PoppinsMedium500">
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>

    <style name="MetaTextView.S14.PoppinsSemiBold600">
        <item name="android:fontFamily">@font/poppins_semi_bold_600</item>
    </style>

    <style name="MetaTextView.S14.PoppinsBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="MetaTextView.S14.PoppinsBold800">
        <item name="android:fontFamily">@font/poppins_extra_bold_800</item>
    </style>

    <style name="MetaTextView.S14.PoppinsBlack900">
        <item name="android:fontFamily">@font/poppins_black_900</item>
    </style>

    <style name="MetaTextView.S15.PoppinsRegular400">
        <item name="android:fontFamily">@font/poppins_regular_400</item>
    </style>

    <style name="MetaTextView.S15.PoppinsMedium500">
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>

    <style name="MetaTextView.S15.PoppinsMedium600">
        <item name="android:fontFamily">@font/poppins_semi_bold_600</item>
    </style>

    <style name="MetaTextView.S15.PoppinsBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="MetaTextView.S15.PoppinsBlack900">
        <item name="android:fontFamily">@font/poppins_black_900</item>
    </style>

    <style name="MetaTextView.S16.PoppinsRegular400">
        <item name="android:fontFamily">@font/poppins_regular_400</item>
    </style>

    <style name="MetaTextView.S16.PoppinsMedium500">
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>

    <style name="MetaTextView.S16.PoppinsSemiBold600">
        <item name="android:fontFamily">@font/poppins_semi_bold_600</item>
    </style>

    <style name="MetaTextView.S16.PoppinsSemiBold600.TitleBar">
        <item name="android:gravity">center</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="MetaTextView.S16.PoppinsBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="MetaTextView.S16.PoppinsBold700.TitleBar">
        <item name="android:gravity">center</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="MetaTextView.S18.PoppinsSemiBold600.TitleBar">
        <item name="android:gravity">center</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="MetaTextView.S16.PoppinsBlack900">
        <item name="android:fontFamily">@font/poppins_black_900</item>
    </style>

    <style name="MetaTextView.S16.PoppinsExtraBold800">
        <item name="android:fontFamily">@font/poppins_extra_bold_800</item>
    </style>

    <style name="MetaTextView.S18.PoppinsSemiBold600">
        <item name="android:fontFamily">@font/poppins_semi_bold_600</item>
    </style>

    <style name="MetaTextView.S18.PoppinsExtraBold800">
        <item name="android:fontFamily">@font/poppins_extra_bold_800</item>
    </style>

    <style name="MetaTextView.S18.PoppinsBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="MetaTextView.S18.PoppinsMedium500">
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>

    <style name="MetaTextView.S18.PoppinsBlack900">
        <item name="android:fontFamily">@font/poppins_black_900</item>
    </style>

    <style name="MetaTextView.S19.PoppinsSemiBold600">
        <item name="android:fontFamily">@font/poppins_semi_bold_600</item>
    </style>


    <style name="MetaTextView.S20.PoppinsBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="MetaTextView.S20.PoppinsSemiBold600">
        <item name="android:fontFamily">@font/poppins_semi_bold_600</item>
    </style>

    <style name="MetaTextView.S24.PoppinsMedium500">
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>

    <style name="MetaTextView.S24.PoppinsSemiBold600">
        <item name="android:fontFamily">@font/poppins_semi_bold_600</item>
    </style>

    <style name="MetaTextView.S24.PoppinsBlack900">
        <item name="android:fontFamily">@font/poppins_black_900</item>
    </style>

    <style name="MetaTextView.S24.PoppinsBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="MetaTextView.S25.PoppinsBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="MetaTextView.S26.PoppinsBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="MetaTextView.S28.PoppinsBlack900">
        <item name="android:fontFamily">@font/poppins_black_900</item>
    </style>

    <style name="MetaTextView.S32.PoppinsBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <!-- 定制化 -->
    <style name="MetaTextView.S12.PoppinsMedium500.Tag">
        <item name="android:gravity">center</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:textColor">@color/textColorPrimaryLight</item>
    </style>

    <style name="MetaTextView.S12.PoppinsBold700.EditorCard">
        <item name="android:gravity">left</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:textColor">@color/textColorPrimaryLight</item>
    </style>

    <style name="MetaTextView.S18.PoppinsExtraBold800.LeftTitle">
        <item name="android:gravity">left</item>
    </style>

    <style name="MetaTextView.S16.PoppinsExtraBold800.LeftTitle">
        <item name="android:gravity">left</item>
    </style>

    <style name="MetaTextView.S16.PoppinsBlack900.CenterTitle">
        <item name="android:gravity">center</item>
    </style>

    <style name="MetaTextView.S15.PoppinsBold700.LeftTitle">
        <item name="android:gravity">left</item>
    </style>

    <style name="MetaTextView.S14.PoppinsBold700.Popup">
        <item name="android:gravity">left|center_vertical</item>
    </style>

    <style name="MetaTextView.S14.PoppinsMedium500.Popup">
        <item name="android:gravity">left|center_vertical</item>
    </style>

    <style name="MetaTextView.S14.PoppinsRegular400.Popup">
        <item name="android:gravity">left|center_vertical</item>
    </style>

    <style name="MetaTextView.S14.PoppinsMedium500.Popup.Danger">
        <item name="android:textColor">@color/color_FC596E</item>
    </style>

    <style name="MetaTextView.S18.PoppinsMedium500.CenterDanger">
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/color_FC596E</item>
    </style>

    <style name="MetaTextView.S18.PoppinsMedium500.ButtonOther">
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/textColorPrimary</item>
    </style>

    <style name="MetaTextView.S12.PoppinsRegular400.White">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="MetaTextView.S12.PoppinsLight300.secondary">
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/textColorSecondary</item>
    </style>

    <style name="MetaTextView.S12.PoppinsMedium500.Third">
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/textColorThird</item>
    </style>

    <style name="MetaTextView.S12.PoppinsRegular400.secondary">
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/textColorSecondary</item>
    </style>

    <style name="MetaTextView.S15.PoppinsBlack900.Center">
        <item name="android:gravity">center</item>
    </style>

    <style name="MetaTextView.S10.PoppinsRegular400.CenterVertical14">
        <item name="android:gravity">center_vertical</item>
        <item name="minHeight">@dimen/dp_14</item>
    </style>

    <style name="MetaTextView.S15.PoppinsRegular400.CenterVertical">
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="MetaTextView.S15.PoppinsMedium500.CenterVertical">
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="MetaTextView.S15.PoppinsRegular400.CenterVertical.Secondary">
        <item name="android:textColor">@color/textColorSecondary</item>
    </style>

    <style name="MetaTextView.S14.PoppinsRegular400.CenterVertical">
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="MetaTextView.S12.PoppinsRegular400.CenterVertical">
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="MetaTextView.S15.PoppinsBold700.CenterVertical">
        <item name="android:gravity">center_vertical</item>
    </style>
</resources>