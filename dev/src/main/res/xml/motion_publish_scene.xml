<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ConstraintSet android:id="@+id/start">
        <Constraint android:id="@+id/vBackground">
            <CustomAttribute
                app:attributeName="Alpha"
                app:customFloatValue="0.0" />
        </Constraint>

        <Constraint android:id="@+id/clPublishPlaceHolder">
            <CustomAttribute
                app:attributeName="Alpha"
                app:customFloatValue="1.0" />
            <!-- 添加缩放属性 -->
            <CustomAttribute
                app:attributeName="ScaleX"
                app:customFloatValue="1" />
            <CustomAttribute
                app:attributeName="ScaleY"
                app:customFloatValue="1" />
        </Constraint>

        <Constraint
            android:id="@+id/ivPublish"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@id/slChoosePublishScene"
            app:layout_constraintEnd_toEndOf="@id/slChoosePublishScene"
            app:layout_constraintTop_toTopOf="@id/slChoosePublishScene"
            app:layout_constraintStart_toStartOf="@id/slChoosePublishScene">
            <CustomAttribute
                app:attributeName="Rotation"
                app:customFloatValue="0" />
        </Constraint>

        <Constraint
            android:id="@+id/tvTemplate"
            android:layout_width="@dimen/dp_248"
            android:layout_height="@dimen/dp_60"
            app:visibilityMode="ignore"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_12"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/slChoosePublishScene"
            app:layout_constraintEnd_toEndOf="parent">
            <CustomAttribute
                app:attributeName="TranslationY"
                app:customDimension="@dimen/dp_100" />
            <CustomAttribute
                app:attributeName="Alpha"
                app:customFloatValue="0" />
        </Constraint>

        <Constraint
            android:id="@+id/tvPost"
            android:layout_width="@dimen/dp_248"
            android:layout_height="@dimen/dp_60"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_13"
            app:layout_constraintBottom_toTopOf="@id/slChoosePublishScene"
            app:layout_constraintEnd_toEndOf="parent">
            <CustomAttribute
                app:attributeName="TranslationY"
                app:customDimension="@dimen/dp_120" />
            <CustomAttribute
                app:attributeName="Alpha"
                app:customFloatValue="0" />
        </Constraint>
    </ConstraintSet>

    <ConstraintSet android:id="@+id/end">
        <Constraint android:id="@+id/vBackground">
            <CustomAttribute
                app:attributeName="Alpha"
                app:customFloatValue="0.3" />
        </Constraint>

        <Constraint android:id="@+id/clPublishPlaceHolder">
            <CustomAttribute
                app:attributeName="Alpha"
                app:customFloatValue="0.0" />
            <!-- 添加缩放属性 -->
            <CustomAttribute
                app:attributeName="ScaleX"
                app:customFloatValue="0.6" />
            <CustomAttribute
                app:attributeName="ScaleY"
                app:customFloatValue="0.6" />
        </Constraint>

        <Constraint
            android:id="@+id/ivPublish"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@id/slChoosePublishScene"
            app:layout_constraintEnd_toEndOf="@id/slChoosePublishScene"
            app:layout_constraintStart_toStartOf="@id/slChoosePublishScene"
            app:layout_constraintTop_toTopOf="@id/slChoosePublishScene">
            <CustomAttribute
                app:attributeName="Rotation"
                app:customFloatValue="-45" />
            <CustomAttribute
                app:attributeName="ScaleX"
                app:customFloatValue="1" />
            <CustomAttribute
                app:attributeName="ScaleY"
                app:customFloatValue="1" />
        </Constraint>

        <Constraint
            android:id="@+id/tvTemplate"
            android:layout_width="@dimen/dp_248"
            android:layout_height="@dimen/dp_60"
            app:visibilityMode="ignore"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:visibility="gone"
            android:layout_marginBottom="@dimen/dp_12"
            app:layout_constraintBottom_toTopOf="@id/tvPost"
            app:layout_constraintEnd_toEndOf="parent">
            <CustomAttribute
                app:attributeName="TranslationY"
                app:customDimension="0dp" />
            <CustomAttribute
                app:attributeName="Alpha"
                app:customFloatValue="1" />
        </Constraint>

        <Constraint
            android:id="@+id/tvPost"
            android:layout_width="@dimen/dp_248"
            android:layout_height="@dimen/dp_60"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_32"
            app:layout_constraintBottom_toTopOf="@id/ivPublish"
            app:layout_constraintEnd_toEndOf="parent">
            <CustomAttribute
                app:attributeName="TranslationY"
                app:customDimension="0dp" />
            <CustomAttribute
                app:attributeName="Alpha"
                app:customFloatValue="1" />
            <CustomAttribute
                app:attributeName="ScaleX"
                app:customFloatValue="1" />
            <CustomAttribute
                app:attributeName="ScaleY"
                app:customFloatValue="1" />
        </Constraint>
    </ConstraintSet>

    <Transition
        app:constraintSetEnd="@id/end"
        app:constraintSetStart="@id/start"
        app:duration="350"
        app:motionInterpolator="overshoot">
    </Transition>
</MotionScene>